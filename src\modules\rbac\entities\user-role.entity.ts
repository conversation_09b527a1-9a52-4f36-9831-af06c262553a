import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Role } from './role.entity';

/**
 * UserRole Entity - Gán vai trò cho người dùng
 * UserRole Entity - Assigns roles to users
 */
@Entity('user_roles')
@Unique('UQ_user_role', ['userId', 'roleId'])
@Index(['userId'])
@Index(['roleId'])
@Index(['isActive'])
export class UserRole {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID người dùng - User ID
   */
  @Column({ type: 'uuid', name: 'user_id' })
  userId: string;

  /**
   * ID vai trò - Role ID
   */
  @Column({ type: 'uuid', name: 'role_id' })
  roleId: string;

  /**
   * ID người gán vai trò - Assigner ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'assigned_by' })
  assignedBy?: string;

  @CreateDateColumn({ name: 'assigned_at' })
  assignedAt: Date;

  /**
   * Thời hạn vai trò (tùy chọn) - Role expiration (optional)
   */
  @Column({ type: 'timestamp', nullable: true, name: 'expires_at' })
  expiresAt?: Date;

  /**
   * Trạng thái hoạt động - Active status
   */
  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  // Relations

  /**
   * Người dùng - User
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  /**
   * Vai trò - Role
   */
  @ManyToOne(() => Role, (role) => role.userRoles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  /**
   * Người gán vai trò - Assigner
   */
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'assigned_by' })
  assigner?: User;

  // Virtual properties

  /**
   * Kiểm tra vai trò có hết hạn không - Check if role is expired
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  /**
   * Kiểm tra vai trò có hiệu lực không - Check if role is valid
   */
  get isValid(): boolean {
    return this.isActive && !this.isExpired;
  }

  /**
   * Lấy số ngày còn lại - Get remaining days
   */
  get remainingDays(): number | null {
    if (!this.expiresAt) return null;
    const now = new Date();
    const diffTime = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Kiểm tra có phải vai trò hệ thống không - Check if system role
   */
  get isSystemRole(): boolean {
    return this.role?.isSystemRole || false;
  }

  /**
   * Kiểm tra có thể thu hồi không - Check if revocable
   */
  get isRevocable(): boolean {
    return !this.isSystemRole && this.role?.name !== 'MASTER_ACCOUNT';
  }

  /**
   * Lấy thông tin tóm tắt - Get summary info
   */
  get summary(): string {
    const status = this.isValid ? 'Active' : this.isExpired ? 'Expired' : 'Inactive';
    return `User: ${this.user?.email} - Role: ${this.role?.name} - Status: ${status}`;
  }

  /**
   * Kiểm tra vai trò có sắp hết hạn không - Check if role is expiring soon
   */
  isExpiringSoon(days: number = 7): boolean {
    if (!this.expiresAt) return false;
    const remainingDays = this.remainingDays;
    return remainingDays !== null && remainingDays <= days && remainingDays > 0;
  }

  /**
   * Gia hạn vai trò - Extend role
   */
  extend(days: number): void {
    if (this.expiresAt) {
      this.expiresAt = new Date(this.expiresAt.getTime() + days * 24 * 60 * 60 * 1000);
    } else {
      this.expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Vô hiệu hóa vai trò - Deactivate role
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * Kích hoạt vai trò - Activate role
   */
  activate(): void {
    this.isActive = true;
  }
}
