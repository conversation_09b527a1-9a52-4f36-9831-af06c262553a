# Tài Liệu Cache Module

## Tổng Quan

Cache Module cung cấp giải pháp caching toàn diện dựa trên Redis cho Delify Platform, đư<PERSON><PERSON> thiết kế đặc biệt để hỗ trợ quản lý session SSO (Single Sign-On), blacklisting token, và các nhu cầu caching chung của ứng dụng.

## Tính Năng

- **Tích Hợp Redis**: Hỗ trợ Redis đầy đủ với connection pooling
- **Hỗ Trợ SSO**: Tối ưu cho lưu trữ session SSO và quản lý token
- **Decorators**: Decorators caching dễ sử dụng cho methods
- **Giám Sát Sức Khỏe**: Health checks và thống kê tích hợp
- **Global Module**: Có sẵn trong toàn bộ ứng dụng
- **Hỗ Trợ TypeScript**: Type safety và IntelliSense đầy đủ

## Cài Đặt

Cache module đã đượ<PERSON> cấu hình và sẵn sàng sử dụng. Dependencies cần thiết:

```json
{
  "@nestjs/cache-manager": "^2.1.0",
  "cache-manager": "^5.2.0",
  "cache-manager-redis-store": "^2.0.0",
  "redis": "^4.6.0"
}
```

## Cấu Hình

### Biến Môi Trường

```env
# Cấu Hình Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# Cấu Hình Cache
SSO_CACHE_TTL=900
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
```

### Import Module

Cache module được tự động import như global module trong `app.module.ts`:

```typescript
import { CacheModule } from './modules/cache/cache.module';

@Module({
  imports: [
    CacheModule.forRoot(), // Global cache module
    // ... other modules
  ],
})
export class AppModule {}
```

## Sử Dụng

### Cache Service Cơ Bản

```typescript
import { Injectable } from '@nestjs/common';
import { CacheService } from '../cache/services/cache.service';

@Injectable()
export class UserService {
  constructor(private readonly cacheService: CacheService) {}

  async getUserProfile(userId: string) {
    // Thử lấy từ cache trước
    const cached = await this.cacheService.get(`user:profile:${userId}`);
    if (cached) {
      return cached;
    }

    // Lấy từ database và cache
    const profile = await this.userRepository.findOne(userId);
    await this.cacheService.set(`user:profile:${userId}`, profile, { ttl: 300 });
    
    return profile;
  }
}
```

### Sử Dụng Cache Decorators

```typescript
import { Injectable } from '@nestjs/common';
import { Cacheable, CacheEvict } from '../cache/decorators/cache.decorator';

@Injectable()
export class UserService {
  // Cache kết quả method trong 5 phút
  @Cacheable('user-profile-{0}', 300)
  async getUserProfile(userId: string) {
    return this.userRepository.findOne(userId);
  }

  // Xóa cache khi user được cập nhật
  @CacheEvict('user-profile-{0}')
  async updateUser(userId: string, data: UpdateUserDto) {
    return this.userRepository.update(userId, data);
  }

  // Caching có điều kiện
  @CacheWhen(
    (userId: string) => userId !== 'admin',
    'user-profile-{0}',
    300
  )
  async getUserProfileConditional(userId: string) {
    return this.userRepository.findOne(userId);
  }
}
```

### Sử Dụng Cho SSO

```typescript
import { Injectable } from '@nestjs/common';
import { CacheService, CACHE_KEYS } from '../cache';

@Injectable()
export class SSOService {
  constructor(private readonly cacheService: CacheService) {}

  // Lưu trữ SSO session
  async storeSession(sessionId: string, sessionData: any) {
    const key = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;
    await this.cacheService.set(key, sessionData, { ttl: 28800 }); // 8 giờ
  }

  // Lấy SSO session
  async getSession(sessionId: string) {
    const key = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;
    return this.cacheService.get(key);
  }

  // Blacklist JWT token
  async blacklistToken(tokenId: string, expiresAt: Date) {
    const key = `${CACHE_KEYS.SSO_JWT_BLACKLIST}${tokenId}`;
    const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000);
    await this.cacheService.set(key, true, { ttl });
  }

  // Kiểm tra token có bị blacklist không
  async isTokenBlacklisted(tokenId: string): Promise<boolean> {
    const key = `${CACHE_KEYS.SSO_JWT_BLACKLIST}${tokenId}`;
    return this.cacheService.exists(key);
  }
}
```

## Tham Khảo API

### CacheService Methods

#### `set<T>(key: string, value: T, options?: CacheOptions): Promise<void>`
Lưu trữ dữ liệu trong cache.

```typescript
await cacheService.set('user:123', userData, { ttl: 300, prefix: 'api:' });
```

#### `get<T>(key: string, prefix?: string): Promise<T | undefined>`
Lấy dữ liệu từ cache.

```typescript
const userData = await cacheService.get<User>('user:123');
```

#### `del(key: string, prefix?: string): Promise<void>`
Xóa dữ liệu khỏi cache.

```typescript
await cacheService.del('user:123');
```

#### `exists(key: string, prefix?: string): Promise<boolean>`
Kiểm tra key có tồn tại trong cache không.

```typescript
const exists = await cacheService.exists('user:123');
```

#### `getOrSet<T>(key: string, factory: () => Promise<T>, options?: CacheOptions): Promise<T>`
Lấy từ cache hoặc thực thi factory function và cache kết quả.

```typescript
const userData = await cacheService.getOrSet(
  'user:123',
  () => this.userRepository.findOne('123'),
  { ttl: 300 }
);
```

### Cache Decorators

#### `@Cacheable(key: string, ttl?: number)`
Cache kết quả method.

```typescript
@Cacheable('user-{0}', 300)
async getUser(id: string) { /* ... */ }
```

#### `@CacheEvict(key: string | string[])`
Xóa cache entries.

```typescript
@CacheEvict(['user-{0}', 'user-profile-{0}'])
async updateUser(id: string, data: any) { /* ... */ }
```

## Cache Key Patterns

Module cung cấp các cache key patterns được định nghĩa trước:

```typescript
export const CACHE_KEYS = {
  // SSO patterns
  SSO_SESSION: 'sso:session:',
  SSO_JWT_BLACKLIST: 'sso:jwt_blacklist:',
  SSO_DEVICE_FINGERPRINT: 'sso:device:',
  SSO_APPLICATION: 'sso:app:',
  
  // RBAC patterns
  USER_PERMISSIONS: 'rbac:user_permissions:',
  USER_ROLES: 'rbac:user_roles:',
  ROLE_PERMISSIONS: 'rbac:role_permissions:',
  
  // General patterns
  USER_PROFILE: 'user:profile:',
  AI_RESPONSE: 'ai:response:',
};
```

## Giám Sát Sức Khỏe

### Health Check Endpoint

```http
GET /cache/health
```

Phản hồi:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "stats": {
    "hits": 150,
    "misses": 25,
    "keys": 0,
    "memory": "N/A"
  }
}
```

### Statistics Endpoint

```http
GET /cache/stats
```

## Thực Hành Tốt Nhất

### 1. Sử Dụng TTL Values Phù Hợp

```typescript
const CACHE_TTL = {
  SHORT: 300,    // 5 phút - dữ liệu thay đổi thường xuyên
  MEDIUM: 900,   // 15 phút - user sessions
  LONG: 3600,    // 1 giờ - user profiles
  VERY_LONG: 86400, // 24 giờ - cấu hình tĩnh
};
```

### 2. Sử Dụng Key Patterns Nhất Quán

```typescript
// Tốt
const userKey = `${CACHE_KEYS.USER_PROFILE}${userId}`;
const sessionKey = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;

// Tránh
const userKey = `user_${userId}_profile`;
```

### 3. Xử Lý Cache Failures Một Cách Graceful

```typescript
async getUserProfile(userId: string) {
  try {
    const cached = await this.cacheService.get(`user:${userId}`);
    if (cached) return cached;
  } catch (error) {
    // Log error nhưng tiếp tục với database query
    this.logger.warn('Cache error:', error);
  }
  
  // Fallback to database
  return this.userRepository.findOne(userId);
}
```

## Khắc Phục Sự Cố

### Vấn Đề Thường Gặp

1. **Redis Connection Failed**
   - Kiểm tra Redis server đang chạy
   - Xác minh connection parameters trong `.env`
   - Kiểm tra network connectivity

2. **Cache Không Hoạt Động**
   - Xác minh cache module được import
   - Kiểm tra cấu hình Redis
   - Review cache key patterns

3. **Vấn Đề Hiệu Suất**
   - Giám sát cache hit/miss ratio
   - Điều chỉnh TTL values
   - Cân nhắc cache warming strategies

### Debug Mode

Bật debug logging:

```env
LOG_LEVEL=debug
```

Điều này sẽ hiển thị cache operations trong logs:

```
[CacheService] Cache HIT: user:profile:123
[CacheService] Cache MISS: user:profile:456
[CacheService] Cache SET: user:profile:456 (TTL: 300s)
```

## Tích Hợp Với SSO

Cache module được tối ưu đặc biệt cho các hoạt động SSO:

- **Session Storage**: Lưu trữ user sessions với automatic expiration
- **Token Blacklisting**: Duy trì blacklisted JWT tokens
- **Device Fingerprinting**: Cache thông tin thiết bị cho bảo mật
- **Application Configuration**: Cache cài đặt SSO application

Điều này đảm bảo hiệu suất tối ưu cho hệ thống SSO trong khi duy trì tính nhất quán dữ liệu và bảo mật.

---

Để biết thêm thông tin về triển khai SSO, xem [Hướng Dẫn Triển Khai SSO](../sso/SSO_IMPLEMENTATION_GUIDE_vi.md).
