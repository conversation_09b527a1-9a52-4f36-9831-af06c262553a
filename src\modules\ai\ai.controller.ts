import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, UploadedFile, UseInterceptors, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiConsumes, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { AiService } from './ai.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { AIProvider, AIModelName } from './entities/ai-model.entity';
import { AIMessage } from './providers/base-ai.provider';
import {
  GenerateTextDto,
  GenerateStreamDto,
  AnalyzeImageDto,
  SmartGenerateDto,
  CompareProvidersDto
} from './dto/generate-text.dto';

@ApiTags('AI')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AiController {
  constructor(private readonly aiService: AiService) {}

  @Post('content/optimize')
  async optimizeContent(@CurrentUser() user: User, @Body() data: { content: string; platform: string; target: string }) {
    return this.aiService.optimizeContent(user.id, data);
  }

  @Post('content/generate')
  async generateContent(@CurrentUser() user: User, @Body() data: { prompt: string; type: string; platform: string }) {
    return this.aiService.generateContent(user.id, data);
  }

  @Post('cv/analyze')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async analyzeCv(
    @CurrentUser() user: User,
    @UploadedFile() file: Express.Multer.File,
    @Body() data: { jobDescription?: string; requirements?: string }
  ) {
    return this.aiService.analyzeCv(user.id, file, data);
  }

  @Get('cv/analyses')
  async getCvAnalyses(@CurrentUser() user: User) {
    return this.aiService.getCvAnalyses(user.id);
  }

  @Post('chatbot/message')
  async sendChatbotMessage(@CurrentUser() user: User, @Body() data: { message: string; sessionId?: string; context?: any }) {
    return this.aiService.processChatbotMessage(user.id, data);
  }

  @Get('chatbot/sessions')
  async getChatbotSessions(@CurrentUser() user: User) {
    return this.aiService.getChatbotSessions(user.id);
  }

  @Post('comment/analyze')
  async analyzeComment(@CurrentUser() user: User, @Body() data: { comment: string; platform: string }) {
    return this.aiService.analyzeComment(user.id, data);
  }

  @Post('email/analyze')
  async analyzeEmail(@CurrentUser() user: User, @Body() data: { subject: string; content: string; sender: string }) {
    return this.aiService.analyzeEmail(user.id, data);
  }

  @Get('models')
  async getAvailableModels(@CurrentUser() user: User) {
    return this.aiService.getAvailableModels();
  }

  @Post('models/configure')
  async configureModel(@CurrentUser() user: User, @Body() data: { modelType: string; settings: any }) {
    return this.aiService.configureModel(user.id, data);
  }

  // New multi-provider endpoints
  @Post('generate/text')
  @ApiOperation({ summary: 'Generate text using specified AI provider' })
  @ApiResponse({ status: 200, description: 'Text generated successfully' })
  async generateText(
    @CurrentUser() user: User,
    @Body() data: GenerateTextDto
  ) {
    return this.aiService.generateText(data.provider, data.model, data.messages, data.options);
  }

  @Post('generate/stream')
  @ApiOperation({ summary: 'Generate streaming text using specified AI provider' })
  async generateStream(
    @CurrentUser() user: User,
    @Body() data: GenerateStreamDto
  ) {
    // Note: In a real implementation, you'd want to use Server-Sent Events (SSE)
    // This is a simplified version that collects all chunks
    const generator = await this.aiService.generateStream(data.provider, data.model, data.messages, data.options);

    let fullResponse = '';
    for await (const chunk of generator) {
      fullResponse += chunk;
    }

    return { content: fullResponse };
  }

  @Post('analyze/image')
  @ApiOperation({ summary: 'Analyze image using AI vision models' })
  @ApiResponse({ status: 200, description: 'Image analyzed successfully' })
  async analyzeImage(
    @CurrentUser() user: User,
    @Body() data: AnalyzeImageDto
  ) {
    return this.aiService.analyzeImage(data.provider, data.model, data.imageUrl, data.prompt, data.options);
  }

  @Get('providers')
  @ApiOperation({ summary: 'Get available AI providers and their capabilities' })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getAvailableProviders() {
    return this.aiService.getAvailableProviders();
  }

  @Post('providers/:provider/validate')
  @ApiOperation({ summary: 'Validate AI provider configuration' })
  @ApiResponse({ status: 200, description: 'Provider validation completed' })
  async validateProvider(@Param('provider') provider: AIProvider) {
    return this.aiService.validateProvider(provider);
  }

  @Get('providers/best')
  @ApiOperation({ summary: 'Get best provider for specific task' })
  @ApiResponse({ status: 200, description: 'Best provider recommendation retrieved' })
  async getBestProvider(@Query('task') task: 'text' | 'image' | 'code' | 'embedding' | 'creative') {
    return this.aiService.getBestProviderForTask(task);
  }

  @Post('smart/generate')
  @ApiOperation({ summary: 'Smart text generation - automatically selects best provider' })
  @ApiResponse({ status: 200, description: 'Text generated using optimal provider' })
  async smartGenerate(
    @CurrentUser() user: User,
    @Body() data: SmartGenerateDto
  ) {
    const taskType = data.task || 'text';
    const bestProvider = await this.aiService.getBestProviderForTask(taskType);

    return {
      ...await this.aiService.generateText(
        bestProvider.provider,
        bestProvider.model,
        data.messages,
        data.options
      ),
      providerUsed: bestProvider,
    };
  }

  @Post('compare/providers')
  @ApiOperation({ summary: 'Compare responses from multiple providers' })
  @ApiResponse({ status: 200, description: 'Provider comparison completed' })
  async compareProviders(
    @CurrentUser() user: User,
    @Body() data: CompareProvidersDto
  ) {
    const results = [];

    for (const providerConfig of data.providers) {
      try {
        const startTime = Date.now();
        const response = await this.aiService.generateText(
          providerConfig.provider,
          providerConfig.model,
          data.messages,
          data.options
        );
        const endTime = Date.now();

        results.push({
          provider: providerConfig.provider,
          model: providerConfig.model,
          response,
          responseTime: endTime - startTime,
          success: true,
        });
      } catch (error) {
        results.push({
          provider: providerConfig.provider,
          model: providerConfig.model,
          error: error.message,
          success: false,
        });
      }
    }

    return {
      results,
      comparison: {
        fastest: results
          .filter(r => r.success)
          .sort((a, b) => a.responseTime - b.responseTime)[0],
        mostTokens: results
          .filter(r => r.success)
          .sort((a, b) => (b.response?.usage?.totalTokens || 0) - (a.response?.usage?.totalTokens || 0))[0],
      },
    };
  }

  @Get('providers/best-local')
  @ApiOperation({ summary: 'Get best local provider (OLLAMA) for privacy-focused tasks' })
  @ApiResponse({ status: 200, description: 'Best local provider recommendation' })
  async getBestLocalProvider(@Query('task') task: 'text' | 'code' | 'embedding') {
    // Always recommend OLLAMA for local/privacy tasks
    const modelMap = {
      text: AIModelName.LLAMA_2_7B,
      code: AIModelName.CODE_LLAMA_7B,
      embedding: 'nomic-embed-text:latest',
    };

    return {
      provider: AIProvider.OLLAMA,
      model: modelMap[task] || AIModelName.LLAMA_2_7B,
      reason: 'Local processing ensures data privacy and no API costs',
      benefits: [
        'Complete data privacy - nothing leaves your server',
        'No API costs or rate limits',
        'Works offline',
        'Customizable and fine-tunable',
      ],
    };
  }
}
