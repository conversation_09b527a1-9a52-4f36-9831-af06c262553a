import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { OllamaController } from '../ollama.controller';
import { AIProviderFactory } from '../../providers/ai-provider.factory';
import { OllamaProvider } from '../../providers/ollama.provider';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { testConfig, TestUtils, mockOllamaModels } from '../../../../../test/test.config';

describe('OllamaController (Integration)', () => {
  let app: INestApplication;
  let ollamaProvider: jest.Mocked<OllamaProvider>;
  let authToken: string;

  beforeAll(async () => {
    // Mock OLLAMA provider
    const mockOllamaProvider = {
      checkServerHealth: jest.fn(),
      listModels: jest.fn(),
      pullModel: jest.fn(),
      deleteModel: jest.fn(),
      getModelInfo: jest.fn(),
      generateText: jest.fn(),
      generateStream: jest.fn(),
      generateCode: jest.fn(),
      explainCode: jest.fn(),
      embedText: jest.fn(),
      analyzeImage: jest.fn(),
    };

    // Mock AI Provider Factory
    const mockAIProviderFactory = {
      createProviderFromEnv: jest.fn().mockReturnValue(mockOllamaProvider),
    };

    // Mock JWT Auth Guard
    const mockJwtAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register(testConfig.jwt),
      ],
      controllers: [OllamaController],
      providers: [
        {
          provide: AIProviderFactory,
          useValue: mockAIProviderFactory,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    ollamaProvider = mockAIProviderFactory.createProviderFromEnv() as jest.Mocked<OllamaProvider>;
    authToken = TestUtils.createMockJwtToken();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /ai/ollama/status', () => {
    it('should return server health status', async () => {
      const mockHealth = {
        isHealthy: true,
        version: '0.1.0',
        models: 2,
      };

      ollamaProvider.checkServerHealth.mockResolvedValue(mockHealth);

      const response = await request(app.getHttpServer())
        .get('/ai/ollama/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual(mockHealth);
      expect(ollamaProvider.checkServerHealth).toHaveBeenCalled();
    });

    it('should return unhealthy status when server is down', async () => {
      const mockHealth = {
        isHealthy: false,
        error: 'OLLAMA server not accessible',
      };

      ollamaProvider.checkServerHealth.mockResolvedValue(mockHealth);

      const response = await request(app.getHttpServer())
        .get('/ai/ollama/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual(mockHealth);
    });
  });

  describe('GET /ai/ollama/models', () => {
    it('should list available models', async () => {
      ollamaProvider.listModels.mockResolvedValue(mockOllamaModels);

      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual(mockOllamaModels);
      expect(ollamaProvider.listModels).toHaveBeenCalled();
    });

    it('should handle empty model list', async () => {
      ollamaProvider.listModels.mockResolvedValue([]);

      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual([]);
    });
  });

  describe('POST /ai/ollama/models/pull', () => {
    it('should pull model successfully', async () => {
      const mockResult = {
        success: true,
        status: 'success',
      };

      ollamaProvider.pullModel.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .post('/ai/ollama/models/pull')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ modelName: 'mistral:7b' })
        .expect(201);

      expect(response.body).toEqual(mockResult);
      expect(ollamaProvider.pullModel).toHaveBeenCalledWith('mistral:7b');
    });

    it('should handle pull errors', async () => {
      const mockResult = {
        success: false,
        status: 'Model not found',
      };

      ollamaProvider.pullModel.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .post('/ai/ollama/models/pull')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ modelName: 'invalid-model' })
        .expect(201);

      expect(response.body).toEqual(mockResult);
    });

    it('should validate request body', async () => {
      await request(app.getHttpServer())
        .post('/ai/ollama/models/pull')
        .set('Authorization', `Bearer ${authToken}`)
        .send({}) // Missing modelName
        .expect(400);
    });
  });

  describe('DELETE /ai/ollama/models/:modelName', () => {
    it('should delete model successfully', async () => {
      const mockResult = {
        success: true,
        message: 'Model llama2:7b deleted successfully',
      };

      ollamaProvider.deleteModel.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .delete('/ai/ollama/models/llama2:7b')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toEqual(mockResult);
      expect(ollamaProvider.deleteModel).toHaveBeenCalledWith('llama2:7b');
    });
  });

  describe('POST /ai/ollama/generate', () => {
    it('should generate text successfully', async () => {
      const mockResponse = {
        content: 'Hello! How can I help you today?',
        usage: {
          promptTokens: 5,
          completionTokens: 10,
          totalTokens: 15,
        },
        model: 'llama2:7b',
        finishReason: 'stop',
        responseTime: 1000,
      };

      ollamaProvider.generateText.mockResolvedValue(mockResponse);

      const requestBody = {
        model: 'llama2:7b',
        prompt: 'Hello',
        system: 'You are a helpful assistant',
        options: {
          temperature: 0.7,
          num_predict: 100,
        },
      };

      const response = await request(app.getHttpServer())
        .post('/ai/ollama/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(requestBody)
        .expect(201);

      expect(response.body).toEqual(mockResponse);
      expect(ollamaProvider.generateText).toHaveBeenCalledWith(
        [
          { role: 'system', content: 'You are a helpful assistant' },
          { role: 'user', content: 'Hello' },
        ],
        expect.objectContaining({
          model: 'llama2:7b',
          temperature: 0.7,
          maxTokens: 100,
        })
      );
    });

    it('should validate request body', async () => {
      await request(app.getHttpServer())
        .post('/ai/ollama/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
          prompt: 'Hello',
        })
        .expect(400);
    });
  });

  describe('POST /ai/ollama/code/generate', () => {
    it('should generate code successfully', async () => {
      const mockResponse = {
        content: 'def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)',
        usage: {
          promptTokens: 15,
          completionTokens: 25,
          totalTokens: 40,
        },
        model: 'codellama:7b',
        finishReason: 'stop',
        responseTime: 2000,
      };

      ollamaProvider.generateCode.mockResolvedValue(mockResponse);

      const requestBody = {
        model: 'codellama:7b',
        prompt: 'Create a fibonacci function',
        language: 'python',
        options: {
          temperature: 0.2,
          num_predict: 200,
        },
      };

      const response = await request(app.getHttpServer())
        .post('/ai/ollama/code/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(requestBody)
        .expect(201);

      expect(response.body).toEqual(mockResponse);
      expect(ollamaProvider.generateCode).toHaveBeenCalledWith(
        'Create a fibonacci function',
        'python',
        expect.objectContaining({
          model: 'codellama:7b',
          temperature: 0.2,
          maxTokens: 200,
        })
      );
    });
  });

  describe('GET /ai/ollama/models/recommended', () => {
    it('should return recommended models for text task', async () => {
      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models/recommended?task=text')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('task', 'text');
      expect(response.body).toHaveProperty('recommended');
      expect(Array.isArray(response.body.recommended)).toBe(true);
      expect(response.body.recommended.length).toBeGreaterThan(0);
    });

    it('should return recommended models for code task', async () => {
      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models/recommended?task=code')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('task', 'code');
      expect(response.body.recommended[0]).toHaveProperty('model', 'codellama:7b');
    });

    it('should default to text task when no task specified', async () => {
      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models/recommended')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('task', 'text');
    });
  });

  describe('Authentication', () => {
    it('should require authentication', async () => {
      // Override the mock to return false
      const moduleRef = app.get(JwtAuthGuard);
      jest.spyOn(moduleRef, 'canActivate').mockReturnValue(false);

      await request(app.getHttpServer())
        .get('/ai/ollama/status')
        .expect(403);
    });

    it('should accept valid JWT token', async () => {
      ollamaProvider.checkServerHealth.mockResolvedValue({
        isHealthy: true,
        version: '0.1.0',
        models: 0,
      });

      await request(app.getHttpServer())
        .get('/ai/ollama/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });
  });
});
