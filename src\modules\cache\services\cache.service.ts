import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';

/**
 * Interface cho cache options
 * Interface for cache options
 */
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string; // Cache key prefix
}

/**
 * Interface cho cache statistics
 * Interface for cache statistics
 */
export interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  memory: string;
}

/**
 * CacheService - Dịch vụ quản lý cache với Redis
 * CacheService - Cache management service with Redis
 */
@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTTL: number;
  private readonly defaultPrefix: string;

  // Statistics tracking
  private stats = {
    hits: 0,
    misses: 0,
  };

  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private configService: ConfigService,
  ) {
    this.defaultTTL = this.configService.get('SSO_CACHE_TTL', 900); // 15 minutes
    this.defaultPrefix = 'delify:';
  }

  /**
   * Lưu dữ liệu vào cache
   * Store data in cache
   */
  async set<T>(
    key: string, 
    value: T, 
    options?: CacheOptions
  ): Promise<void> {
    try {
      const cacheKey = this.buildKey(key, options?.prefix);
      const ttl = options?.ttl || this.defaultTTL;
      
      await this.cacheManager.set(cacheKey, value, ttl);
      
      this.logger.debug(`Cache SET: ${cacheKey} (TTL: ${ttl}s)`);
    } catch (error) {
      this.logger.error(`Cache SET error for key ${key}:`, error);
      // Don't throw error to prevent cache failures from breaking the application
    }
  }

  /**
   * Lấy dữ liệu từ cache
   * Get data from cache
   */
  async get<T>(key: string, prefix?: string): Promise<T | undefined> {
    try {
      const cacheKey = this.buildKey(key, prefix);
      const value = await this.cacheManager.get<T>(cacheKey);
      
      if (value !== undefined) {
        this.stats.hits++;
        this.logger.debug(`Cache HIT: ${cacheKey}`);
      } else {
        this.stats.misses++;
        this.logger.debug(`Cache MISS: ${cacheKey}`);
      }
      
      return value;
    } catch (error) {
      this.logger.error(`Cache GET error for key ${key}:`, error);
      this.stats.misses++;
      return undefined;
    }
  }

  /**
   * Xóa dữ liệu khỏi cache
   * Delete data from cache
   */
  async del(key: string, prefix?: string): Promise<void> {
    try {
      const cacheKey = this.buildKey(key, prefix);
      await this.cacheManager.del(cacheKey);
      
      this.logger.debug(`Cache DEL: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Cache DEL error for key ${key}:`, error);
    }
  }

  /**
   * Xóa nhiều keys theo pattern
   * Delete multiple keys by pattern
   */
  async delPattern(pattern: string): Promise<void> {
    try {
      // Note: This is a simplified implementation
      // In production, you might want to use Redis SCAN for better performance
      const fullPattern = this.buildKey(pattern);
      
      // For cache-manager-redis-store, we need to access the Redis client directly
      const redisClient = (this.cacheManager.store as any).getClient();
      
      if (redisClient && redisClient.keys) {
        const keys = await redisClient.keys(fullPattern);
        if (keys.length > 0) {
          await redisClient.del(...keys);
          this.logger.debug(`Cache DEL PATTERN: ${fullPattern} (${keys.length} keys)`);
        }
      } else {
        this.logger.warn(`Pattern deletion not supported for pattern: ${fullPattern}`);
      }
    } catch (error) {
      this.logger.error(`Cache DEL PATTERN error for pattern ${pattern}:`, error);
    }
  }

  /**
   * Kiểm tra key có tồn tại không
   * Check if key exists
   */
  async exists(key: string, prefix?: string): Promise<boolean> {
    try {
      const cacheKey = this.buildKey(key, prefix);
      const value = await this.cacheManager.get(cacheKey);
      return value !== undefined;
    } catch (error) {
      this.logger.error(`Cache EXISTS error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Đặt TTL cho key
   * Set TTL for key
   */
  async expire(key: string, ttl: number, prefix?: string): Promise<void> {
    try {
      const cacheKey = this.buildKey(key, prefix);
      
      // Get current value and reset with new TTL
      const value = await this.cacheManager.get(cacheKey);
      if (value !== undefined) {
        await this.cacheManager.set(cacheKey, value, ttl);
        this.logger.debug(`Cache EXPIRE: ${cacheKey} (TTL: ${ttl}s)`);
      }
    } catch (error) {
      this.logger.error(`Cache EXPIRE error for key ${key}:`, error);
    }
  }

  /**
   * Lấy hoặc đặt cache (cache-aside pattern)
   * Get or set cache (cache-aside pattern)
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await this.get<T>(key, options?.prefix);
      if (cached !== undefined) {
        return cached;
      }

      // If not in cache, get from factory and cache it
      const value = await factory();
      await this.set(key, value, options);
      
      return value;
    } catch (error) {
      this.logger.error(`Cache GET_OR_SET error for key ${key}:`, error);
      // Fallback to factory function
      return factory();
    }
  }

  /**
   * Xóa tất cả cache
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      await this.cacheManager.reset();
      this.logger.warn('All cache cleared');
    } catch (error) {
      this.logger.error('Cache CLEAR error:', error);
    }
  }

  /**
   * Lấy thống kê cache
   * Get cache statistics
   */
  getStats(): CacheStats {
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      keys: 0, // Would need Redis client access to get accurate count
      memory: 'N/A', // Would need Redis client access to get memory usage
    };
  }

  /**
   * Reset thống kê cache
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.logger.debug('Cache statistics reset');
  }

  /**
   * Tạo cache key với prefix
   * Build cache key with prefix
   */
  private buildKey(key: string, prefix?: string): string {
    const finalPrefix = prefix || this.defaultPrefix;
    return `${finalPrefix}${key}`;
  }

  /**
   * Kiểm tra kết nối cache
   * Check cache connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const testKey = 'health_check';
      const testValue = Date.now().toString();
      
      await this.set(testKey, testValue, { ttl: 10 });
      const retrieved = await this.get(testKey);
      await this.del(testKey);
      
      return retrieved === testValue;
    } catch (error) {
      this.logger.error('Cache health check failed:', error);
      return false;
    }
  }

  /**
   * Lấy thông tin cache manager
   * Get cache manager info
   */
  getCacheManager(): Cache {
    return this.cacheManager;
  }
}
