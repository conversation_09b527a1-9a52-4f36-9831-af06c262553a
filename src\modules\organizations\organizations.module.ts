import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { OrganizationsController } from './organizations.controller';
import { OrganizationsService } from './organizations.service';
import { OrganizationMembersService } from './services/organization-members.service';
import { RolesService } from './services/roles.service';
import { PermissionsService } from './services/permissions.service';
import { InvitationService } from './services/invitation.service';
import { Organization } from './entities/organization.entity';
import { OrganizationMember } from './entities/organization-member.entity';
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Organization,
      OrganizationMember,
      Role,
      Permission,
      User,
    ]),
    BullModule.registerQueue({
      name: 'email',
    }),
  ],
  controllers: [OrganizationsController],
  providers: [
    OrganizationsService,
    OrganizationMembersService,
    RolesService,
    PermissionsService,
    InvitationService,
  ],
  exports: [
    OrganizationsService,
    OrganizationMembersService,
    RolesService,
    PermissionsService,
    InvitationService,
  ],
})
export class OrganizationsModule {}
