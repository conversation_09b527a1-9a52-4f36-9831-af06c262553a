const { Client } = require('pg');
require('dotenv').config();

async function testConnection() {
  const client = new Client({
    host: process.env.DATABASE_HOST,
    port: process.env.DATABASE_PORT,
    database: process.env.DATABASE_NAME,
    user: process.env.DATABASE_USER,
    password: process.env.DATABASE_PASSWORD,
    connectionTimeoutMillis: 10000,
  });

  try {
    console.log('🔄 Testing connection to PostgreSQL...');
    console.log(`Host: ${process.env.DATABASE_HOST}`);
    console.log(`Port: ${process.env.DATABASE_PORT}`);
    console.log(`Database: ${process.env.DATABASE_NAME}`);
    console.log(`User: ${process.env.DATABASE_USER}`);
    
    await client.connect();
    console.log('✅ Successfully connected to PostgreSQL!');

    // Test query
    const result = await client.query('SELECT version()');
    console.log('📊 PostgreSQL Version:', result.rows[0].version);

    // Check if database exists
    const dbCheck = await client.query('SELECT current_database()');
    console.log('🗄️  Current Database:', dbCheck.rows[0].current_database);

    // List existing tables
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    if (tablesResult.rows.length > 0) {
      console.log('📋 Existing tables:');
      tablesResult.rows.forEach(row => {
        console.log(`  - ${row.table_name}`);
      });
    } else {
      console.log('📋 No tables found in database (this is normal for a new database)');
    }

  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.error('💡 Host not found. Please check:');
      console.error('   - Host IP address is correct');
      console.error('   - Network connectivity to the host');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Connection refused. Please check:');
      console.error('   - PostgreSQL is running on the remote host');
      console.error('   - Port 5432 is open and accessible');
      console.error('   - Firewall allows connections');
    } else if (error.code === '28P01') {
      console.error('💡 Authentication failed. Please check:');
      console.error('   - Username and password are correct');
      console.error('   - User has permission to connect');
    } else if (error.code === '3D000') {
      console.error('💡 Database does not exist. Please check:');
      console.error('   - Database name is correct');
      console.error('   - Database exists on the server');
    }
    
    process.exit(1);
  } finally {
    await client.end();
  }
}

testConnection();
