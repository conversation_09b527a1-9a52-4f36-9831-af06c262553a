import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { WorkflowsController } from './workflows.controller';
import { WorkflowsService } from './workflows.service';
import { WorkflowExecutionService } from './services/workflow-execution.service';
import { WorkflowBuilderService } from './services/workflow-builder.service';
import { Workflow } from './entities/workflow.entity';
import { WorkflowNode } from './entities/workflow-node.entity';
import { WorkflowExecution } from './entities/workflow-execution.entity';
import { WorkflowProcessor } from './processors/workflow.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      WorkflowNode,
      WorkflowExecution,
    ]),
    BullModule.registerQueue({
      name: 'workflow',
    }),
  ],
  controllers: [WorkflowsController],
  providers: [
    WorkflowsService,
    WorkflowExecutionService,
    WorkflowBuilderService,
    WorkflowProcessor,
  ],
  exports: [
    WorkflowsService,
    WorkflowExecutionService,
    WorkflowBuilderService,
  ],
})
export class WorkflowsModule {}
