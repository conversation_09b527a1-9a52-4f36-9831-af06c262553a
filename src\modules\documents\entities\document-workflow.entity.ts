import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Document } from './document.entity';

export enum WorkflowStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
}

export enum WorkflowStepType {
  REVIEW = 'review',
  APPROVE = 'approve',
  SIGN = 'sign',
  NOTIFY = 'notify',
  ARCHIVE = 'archive',
}

export enum WorkflowStepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FAILED = 'failed',
}

@Entity('document_workflows')
@Index(['userId', 'status'])
@Index(['documentId', 'status'])
export class DocumentWorkflow {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ nullable: true })
  documentId: string;

  @ManyToOne(() => Document, { nullable: true })
  @JoinColumn({ name: 'documentId' })
  document: Document;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    default: WorkflowStatus.ACTIVE,
  })
  status: WorkflowStatus;

  @Column({ type: 'jsonb' })
  steps: Array<{
    id: string;
    name: string;
    type: WorkflowStepType;
    status: WorkflowStepStatus;
    assignee: {
      email: string;
      name: string;
      role?: string;
    };
    order: number;
    dueDate?: string;
    completedAt?: string;
    completedBy?: string;
    comments?: string;
    settings?: {
      required?: boolean;
      allowDelegation?: boolean;
      reminderFrequency?: number;
      escalationAfter?: number;
    };
  }>;

  @Column({ default: 0 })
  currentStepIndex: number;

  @Column({ nullable: true })
  startedAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ nullable: true })
  dueDate: Date;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    autoAdvance?: boolean;
    allowParallelSteps?: boolean;
    requireAllApprovals?: boolean;
    notifyOnCompletion?: boolean;
    archiveOnCompletion?: boolean;
  };

  @Column({ type: 'jsonb', nullable: true })
  history: Array<{
    stepId: string;
    action: string;
    performedBy: string;
    timestamp: string;
    comments?: string;
    metadata?: any;
  }>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === WorkflowStatus.ACTIVE;
  }

  get isCompleted(): boolean {
    return this.status === WorkflowStatus.COMPLETED;
  }

  get currentStep(): any {
    return this.steps?.[this.currentStepIndex] || null;
  }

  get completedSteps(): any[] {
    return this.steps?.filter(step => step.status === WorkflowStepStatus.COMPLETED) || [];
  }

  get pendingSteps(): any[] {
    return this.steps?.filter(step => step.status === WorkflowStepStatus.PENDING) || [];
  }

  get progress(): number {
    if (!this.steps || this.steps.length === 0) return 0;
    const completed = this.completedSteps.length;
    return Math.round((completed / this.steps.length) * 100);
  }
}
