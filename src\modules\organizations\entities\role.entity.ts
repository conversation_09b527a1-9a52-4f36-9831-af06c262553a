import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  Index,
} from 'typeorm';
import { Permission } from './permission.entity';

export enum RoleType {
  SYSTEM = 'system', // Predefined system roles
  CUSTOM = 'custom', // Custom roles created by organization
}

export enum SystemRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

@Entity('roles')
@Index(['name', 'organizationId'], { unique: true })
@Index(['type'])
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: RoleType,
    default: RoleType.CUSTOM,
  })
  type: RoleType;

  @Column({ nullable: true })
  organizationId: string; // null for system roles

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isDefault: boolean; // Default role for new members

  @Column({ default: 0 })
  priority: number; // Higher number = higher priority

  @ManyToMany(() => Permission, permission => permission.roles, { cascade: true })
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'roleId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permissionId', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    color?: string; // UI color for role badge
    icon?: string; // UI icon for role
    canBeDeleted?: boolean;
    canBeModified?: boolean;
    maxMembers?: number; // Limit number of users with this role
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isSystemRole(): boolean {
    return this.type === RoleType.SYSTEM;
  }

  get isCustomRole(): boolean {
    return this.type === RoleType.CUSTOM;
  }

  get canBeDeleted(): boolean {
    return this.isCustomRole && (this.metadata?.canBeDeleted !== false);
  }

  get canBeModified(): boolean {
    return this.isCustomRole && (this.metadata?.canBeModified !== false);
  }

  hasPermission(permissionName: string): boolean {
    return this.permissions?.some(permission => 
      permission.name === permissionName && permission.isActive
    ) || false;
  }

  getPermissionNames(): string[] {
    return this.permissions?.map(permission => permission.name) || [];
  }

  get displayName(): string {
    return this.name.charAt(0).toUpperCase() + this.name.slice(1);
  }

  get badgeColor(): string {
    return this.metadata?.color || this.getDefaultColor();
  }

  private getDefaultColor(): string {
    const colorMap = {
      [SystemRole.OWNER]: '#dc2626', // red
      [SystemRole.ADMIN]: '#ea580c', // orange
      [SystemRole.MANAGER]: '#ca8a04', // yellow
      [SystemRole.MEMBER]: '#16a34a', // green
      [SystemRole.VIEWER]: '#6b7280', // gray
    };
    return colorMap[this.name as SystemRole] || '#6366f1'; // indigo default
  }
}
