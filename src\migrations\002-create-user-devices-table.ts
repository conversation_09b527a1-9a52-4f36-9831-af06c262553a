import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateUserDevicesTable1700000002 implements MigrationInterface {
  name = 'CreateUserDevicesTable1700000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'user_devices',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'userId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'sessionToken',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'deviceName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'deviceType',
            type: 'enum',
            enum: ['desktop', 'mobile', 'tablet', 'unknown'],
            default: "'unknown'",
          },
          {
            name: 'deviceId',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'userAgent',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'ipAddress',
            type: 'varchar',
            length: '45',
            isNullable: false,
          },
          {
            name: 'location',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['active', 'inactive', 'revoked'],
            default: "'active'",
          },
          {
            name: 'deviceInfo',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'loginAt',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'lastActiveAt',
            type: 'timestamp',
            isNullable: false,
          },
          {
            name: 'logoutAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'expiresAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'isCurrentDevice',
            type: 'boolean',
            default: false,
          },
          {
            name: 'securityInfo',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_user_devices_userId_status" ON "user_devices" ("userId", "status")
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_user_devices_sessionToken" ON "user_devices" ("sessionToken")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_user_devices_lastActiveAt_status" ON "user_devices" ("lastActiveAt", "status")
    `);

    // Create foreign key
    await queryRunner.query(`
      ALTER TABLE "user_devices" ADD CONSTRAINT "FK_user_devices_userId"
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('user_devices');
  }
}
