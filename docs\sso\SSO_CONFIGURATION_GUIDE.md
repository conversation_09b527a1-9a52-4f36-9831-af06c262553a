# SSO Configuration Guide

## Overview

This guide provides comprehensive configuration instructions for the Single Sign-On (SSO) system, covering environment variables, database setup, security settings, and deployment configurations.

## Environment Configuration

### Required Environment Variables

```env
# Core SSO Configuration
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ISSUER=auth.yourcompany.com

# Application Configuration
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com

# Session Configuration
SSO_SESSION_TIMEOUT=480
SSO_MAX_CONCURRENT_SESSIONS=5
SSO_REQUIRE_DEVICE_VERIFICATION=false

# Security Configuration
SSO_ENABLE_AUDIT_LOGGING=true
SSO_TOKEN_REVOCATION_ENABLED=true
SSO_CROSS_DOMAIN_COOKIES=true
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=15m

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/your_database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_db_user
DATABASE_PASSWORD=your_db_password
DATABASE_NAME=your_database

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
```

### Optional Environment Variables

```env
# Advanced Security
SSO_DEVICE_FINGERPRINT_ENABLED=true
SSO_IP_VALIDATION_ENABLED=false
SSO_GEOLOCATION_TRACKING=true
SSO_SUSPICIOUS_ACTIVITY_THRESHOLD=3

# Performance Tuning
SSO_CACHE_TTL=900
SSO_SESSION_CLEANUP_INTERVAL=3600
SSO_TOKEN_CLEANUP_INTERVAL=1800

# Logging Configuration
SSO_LOG_LEVEL=info
SSO_AUDIT_LOG_RETENTION_DAYS=90
SSO_ENABLE_PERFORMANCE_LOGGING=false

# Rate Limiting
SSO_RATE_LIMIT_AUTH=10
SSO_RATE_LIMIT_SESSION=60
SSO_RATE_LIMIT_ADMIN=100

# Development Settings
NODE_ENV=production
NODE_TLS_REJECT_UNAUTHORIZED=1
```

## Domain Configuration

### Single Domain Setup

For a single domain deployment:

```env
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,admin.yourcompany.com
```

### Multi-Domain Setup

For multiple domain deployment:

```env
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com,analytics.yourcompany.com
```

### Custom Domain Configuration

For custom domain names:

```env
SSO_BASE_DOMAIN=mycompany.io
SSO_COOKIE_DOMAIN=.mycompany.io
SSO_ISSUER=auth.mycompany.io
SSO_ALLOWED_APPLICATIONS=dashboard.mycompany.io,mail.mycompany.io,api.mycompany.io
```

## Database Configuration

### PostgreSQL Setup

1. **Create Database:**
```sql
CREATE DATABASE sso_production;
CREATE USER sso_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE sso_production TO sso_user;
```

2. **Connection Configuration:**
```env
DATABASE_URL=postgresql://sso_user:secure_password@localhost:5432/sso_production
DATABASE_SSL=true
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=60000
```

3. **SSL Configuration:**
```env
DATABASE_SSL_CA=/path/to/ca-certificate.crt
DATABASE_SSL_CERT=/path/to/client-certificate.crt
DATABASE_SSL_KEY=/path/to/client-key.key
DATABASE_SSL_REJECT_UNAUTHORIZED=true
```

### Migration and Seeding

```bash
# Run migrations
npm run migration:run

# Seed initial data
npm run seed:sso

# Verify setup
npm run migration:show
```

## Redis Configuration

### Basic Redis Setup

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
```

### Redis Cluster Configuration

```env
REDIS_CLUSTER_ENABLED=true
REDIS_CLUSTER_NODES=redis1.yourcompany.com:6379,redis2.yourcompany.com:6379,redis3.yourcompany.com:6379
REDIS_CLUSTER_PASSWORD=cluster_password
```

### Redis Sentinel Configuration

```env
REDIS_SENTINEL_ENABLED=true
REDIS_SENTINEL_MASTER_NAME=mymaster
REDIS_SENTINEL_NODES=sentinel1:26379,sentinel2:26379,sentinel3:26379
REDIS_SENTINEL_PASSWORD=sentinel_password
```

## Security Configuration

### Cookie Security Settings

```env
# Production Settings
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax
SSO_CROSS_DOMAIN_COOKIES=true

# Development Settings
SSO_SECURE_ONLY=false
SSO_SAME_SITE=none
```

### JWT Security Configuration

```env
# Strong JWT Secret (minimum 32 characters)
JWT_SECRET=your_super_secure_jwt_secret_key_with_at_least_32_characters

# Token Expiration
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Algorithm Configuration
JWT_ALGORITHM=HS256
JWT_ISSUER=auth.yourcompany.com
JWT_AUDIENCE=yourcompany.com
```

### Device Security Settings

```env
# Device Fingerprinting
SSO_DEVICE_FINGERPRINT_ENABLED=true
SSO_REQUIRE_DEVICE_VERIFICATION=false
SSO_DEVICE_TRUST_THRESHOLD=80

# Suspicious Activity Detection
SSO_SUSPICIOUS_ACTIVITY_THRESHOLD=3
SSO_IP_CHANGE_DETECTION=true
SSO_USER_AGENT_VALIDATION=true
```

## Application Registration

### Automatic Registration

Applications can be automatically registered via environment variables:

```env
SSO_AUTO_REGISTER_APPS=true
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com
```

### Manual Registration

Register applications via API or database:

```sql
INSERT INTO sso_applications (name, subdomain, display_name, base_url, allowed_origins, is_active)
VALUES 
  ('MAIN_APP', 'app', 'Main Application', 'https://app.yourcompany.com', ARRAY['https://app.yourcompany.com'], true),
  ('MAIL_APP', 'mail', 'Email Service', 'https://mail.yourcompany.com', ARRAY['https://mail.yourcompany.com'], true);
```

### Application-Specific Configuration

```typescript
// Application configuration object
const applicationConfig = {
  name: 'ANALYTICS_APP',
  subdomain: 'analytics',
  displayName: 'Analytics Dashboard',
  description: 'Business intelligence platform',
  baseUrl: 'https://analytics.yourcompany.com',
  allowedOrigins: [
    'https://analytics.yourcompany.com',
    'https://app.yourcompany.com'
  ],
  corsSettings: {
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    maxAge: 86400
  }
};
```

## CORS Configuration

### Basic CORS Setup

```typescript
// CORS configuration
const corsConfig = {
  origin: [
    'https://app.yourcompany.com',
    'https://mail.yourcompany.com',
    'https://core.yourcompany.com',
    'https://api.yourcompany.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Session-ID',
    'X-Device-ID',
    'X-Application'
  ],
  exposedHeaders: [
    'X-Session-ID',
    'X-Token-Expires',
    'X-Refresh-Token'
  ],
  maxAge: 86400
};
```

### Dynamic CORS Configuration

```env
# Environment-based CORS
CORS_ORIGINS=https://app.yourcompany.com,https://mail.yourcompany.com
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400
```

## Load Balancer Configuration

### Nginx Configuration

```nginx
# /etc/nginx/sites-available/sso
upstream sso_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    listen 443 ssl http2;
    server_name auth.yourcompany.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://sso_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSO-specific headers
        proxy_set_header X-Session-ID $http_x_session_id;
        proxy_set_header X-Device-ID $http_x_device_id;
        
        # Cookie handling
        proxy_cookie_domain localhost yourcompany.com;
        proxy_cookie_path / /;
    }
}

# Subdomain configuration
server {
    listen 443 ssl http2;
    server_name *.yourcompany.com;

    ssl_certificate /path/to/ssl/wildcard.crt;
    ssl_certificate_key /path/to/ssl/wildcard.key;

    location /auth/ {
        proxy_pass http://sso_backend/auth/;
        proxy_set_header Host auth.yourcompany.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Apache Configuration

```apache
# /etc/apache2/sites-available/sso.conf
<VirtualHost *:443>
    ServerName auth.yourcompany.com
    
    SSLEngine on
    SSLCertificateFile /path/to/ssl/certificate.crt
    SSLCertificateKeyFile /path/to/ssl/private.key
    
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # SSO-specific headers
    ProxyPassReverse / http://localhost:3000/
    ProxyPassReverseMatch ^(/.*) http://localhost:3000$1
    
    # Cookie configuration
    Header edit Set-Cookie "^(.*; )Domain=localhost(.*)$" "$1Domain=.yourcompany.com$2"
</VirtualHost>
```

## SSL/TLS Configuration

### SSL Certificate Setup

```bash
# Generate SSL certificate (Let's Encrypt)
certbot certonly --nginx -d auth.yourcompany.com -d *.yourcompany.com

# Or use existing certificates
cp /path/to/certificate.crt /etc/ssl/certs/yourcompany.crt
cp /path/to/private.key /etc/ssl/private/yourcompany.key
```

### SSL Configuration in Application

```env
# SSL Settings
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/yourcompany.crt
SSL_KEY_PATH=/etc/ssl/private/yourcompany.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# HTTPS Redirect
FORCE_HTTPS=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
```

## Monitoring Configuration

### Health Check Endpoints

```typescript
// Health check configuration
const healthConfig = {
  endpoints: {
    '/health': 'basic',
    '/health/detailed': 'detailed',
    '/health/sso': 'sso-specific'
  },
  checks: [
    'database',
    'redis',
    'jwt-service',
    'sso-config'
  ]
};
```

### Logging Configuration

```env
# Logging Settings
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/sso/application.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_PATH=/var/log/sso/audit.log
AUDIT_LOG_RETENTION_DAYS=90
```

### Metrics Configuration

```env
# Metrics Collection
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Performance Monitoring
PERFORMANCE_MONITORING=true
RESPONSE_TIME_THRESHOLD=1000
ERROR_RATE_THRESHOLD=5
```

## Deployment Configurations

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  sso-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - SSO_ENABLED=true
      - SSO_BASE_DOMAIN=yourcompany.com
      - DATABASE_URL=******************************/sso
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: sso
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass your_redis_password

volumes:
  postgres_data:
```

### Kubernetes Configuration

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sso-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sso-app
  template:
    metadata:
      labels:
        app: sso-app
    spec:
      containers:
      - name: sso-app
        image: yourcompany/sso-app:latest
        ports:
        - containerPort: 3000
        env:
        - name: SSO_ENABLED
          value: "true"
        - name: SSO_BASE_DOMAIN
          value: "yourcompany.com"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sso-secrets
              key: database-url
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## Environment-Specific Configurations

### Development Environment

```env
# Development overrides
NODE_ENV=development
SSO_SECURE_ONLY=false
SSO_SAME_SITE=none
LOG_LEVEL=debug
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Staging Environment

```env
# Staging configuration
NODE_ENV=staging
SSO_BASE_DOMAIN=staging.yourcompany.com
SSO_COOKIE_DOMAIN=.staging.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.staging.yourcompany.com,mail.staging.yourcompany.com
```

### Production Environment

```env
# Production configuration
NODE_ENV=production
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax
LOG_LEVEL=warn
METRICS_ENABLED=true
AUDIT_LOG_ENABLED=true
```

## Configuration Validation

### Startup Validation

```typescript
// Configuration validation on startup
const validateConfig = () => {
  const required = [
    'SSO_BASE_DOMAIN',
    'SSO_COOKIE_DOMAIN',
    'JWT_SECRET',
    'DATABASE_URL',
    'REDIS_HOST'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};
```

### Runtime Configuration Check

```bash
# Configuration check script
#!/bin/bash

echo "Checking SSO configuration..."

# Check environment variables
if [ -z "$SSO_ENABLED" ]; then
  echo "ERROR: SSO_ENABLED not set"
  exit 1
fi

# Check database connection
npm run migration:show > /dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "ERROR: Database connection failed"
  exit 1
fi

# Check Redis connection
redis-cli -h $REDIS_HOST ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "ERROR: Redis connection failed"
  exit 1
fi

echo "Configuration check passed!"
```

---

**For implementation details, refer to [Implementation Guide](./SSO_IMPLEMENTATION_GUIDE.md) and [API Documentation](./SSO_API_DOCUMENTATION.md).**
