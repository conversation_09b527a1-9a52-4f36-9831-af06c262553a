# Hệ Thống Xác Thực Nâng Cao

Tài liệu này mô tả hệ thống xác thực nâng cao với các phương thức xác minh bảo mật có thể cấu hình được triển khai trong nền tảng Delify.

## Tổng Quan

Hệ thống xác thực nâng cao cung cấp cho người dùng các phương thức xác minh bảo mật có thể cấu hình để thêm một lớp bảo mật bổ sung cho tài khoản của họ. Người dùng có thể chọn từ bốn phương thức bảo mật:

1. **DISABLED** - Không yêu cầu xác minh bổ sung (mặc định)
2. **EMAIL_VERIFICATION** - G<PERSON><PERSON> mã xác minh đến email của người dùng
3. **TWO_FACTOR_AUTH** - Sử dụng 2FA dựa trên TOTP/ứng dụng xác thực
4. **FIXED_CODE** - Mã xác minh cố định do người dùng tạo có thể tái sử dụng

## Tính Năng

### 1. Cấu Hình Phương Thức Bảo Mật Người Dùng

- Người dùng có thể xem cấu hình phương thức bảo mật hiện tại
- Người dùng có thể cập nhật tùy chọn phương thức bảo mật
- Hỗ trợ xác minh email, 2FA dựa trên TOTP, và mã cố định
- Tạo mã QR để thiết lập 2FA dễ dàng
- Validation mã cố định với yêu cầu bảo mật (6-8 chữ số, không có mẫu yếu)

### 2. Luồng Đăng Nhập Nâng Cao
- Xác minh mật khẩu theo sau bởi kiểm tra phương thức bảo mật
- Phản hồi đăng nhập một phần cho xác minh bổ sung
- Endpoints gửi mã xác minh
- Theo dõi xác minh dựa trên session

### 3. Luồng Đổi Mật Khẩu Nâng Cao
- Yêu cầu mã xác minh cho người dùng có phương thức bảo mật được bật
- Validation nhập đôi (xác nhận mật khẩu mới)
- Thông báo email cho việc thay đổi mật khẩu

## API Endpoints

### Endpoints Xác Thực

#### Đăng Nhập
```http
POST /auth/login
```
Trả về phản hồi đăng nhập hoàn chỉnh hoặc đăng nhập một phần yêu cầu xác minh.

#### Xác Minh Đăng Nhập
```http
POST /auth/verify-login
```
Hoàn thành đăng nhập sau khi gửi mã xác minh.

#### Gửi Lại Mã Xác Minh
```http
POST /auth/resend-verification-code
```
Gửi lại mã xác minh cho đăng nhập.

### Quản Lý Phương Thức Bảo Mật

#### Lấy Phương Thức Bảo Mật
```http
GET /auth/security-method
Authorization: Bearer <token>
```

#### Cập Nhật Phương Thức Bảo Mật
```http
POST /auth/security-method
Authorization: Bearer <token>
```

### Quản Lý Mật Khẩu

#### Gửi Mã Xác Minh Đổi Mật Khẩu
```http
POST /auth/send-password-change-code
Authorization: Bearer <token>
```

#### Đổi Mật Khẩu (Nâng Cao)
```http
POST /auth/change-password
Authorization: Bearer <token>
```

#### Đổi Mã Cố Định
```http
POST /auth/change-fixed-code
Authorization: Bearer <token>
```

### Quản Lý Token

#### Làm Mới Token
```http
POST /auth/refresh
```

#### Thu Hồi Token (Đăng Xuất)
```http
POST /auth/revoke
```

#### Thu Hồi Tất Cả Tokens
```http
POST /auth/revoke-all
Authorization: Bearer <token>
```

## Ví Dụ Sử Dụng

### 1. Bật Xác Minh Email

```javascript
// Bước 1: Cập nhật phương thức bảo mật
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'email_verification'
  })
});
```

### 2. Bật Xác Thực Hai Yếu Tố

```javascript
// Bước 1: Khởi tạo thiết lập 2FA
const setupResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth'
  })
});

const { qrCodeUrl, manualEntryKey } = await setupResponse.json();

// Bước 2: Người dùng quét mã QR hoặc nhập key thủ công trong ứng dụng xác thực

// Bước 3: Xác minh và bật 2FA
const verifyResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth',
    verificationCode: '123456' // Mã từ ứng dụng xác thực
  })
});
```

### 3. Đăng Nhập với Xác Minh Bảo Mật

```javascript
// Bước 1: Đăng nhập ban đầu
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.sessionId) {
  // Yêu cầu xác minh bổ sung
  const verificationCode = prompt('Nhập mã xác minh:');

  // Bước 2: Gửi mã xác minh
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: verificationCode
    })
  });

  const { user, accessToken } = await verifyResponse.json();
  // Đăng nhập hoàn tất
} else {
  // Đăng nhập trực tiếp (không có phương thức bảo mật được bật)
  const { user, accessToken } = loginData;
}
```

### 4. Bật Xác Minh Mã Cố Định

```javascript
// Bật xác minh mã cố định
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'fixed_code',
    fixedCode: '789123' // Mã 6-8 chữ số do người dùng chọn
  })
});

const result = await response.json();
// { "securityMethod": "fixed_code", "twoFactorEnabled": false, "hasFixedCode": true }
```

### 5. Đổi Mã Cố Định

```javascript
// Đổi mã cố định hiện có
const response = await fetch('/auth/change-fixed-code', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    currentCode: '789123', // Mã cố định hiện tại
    newCode: '456789'      // Mã cố định mới
  })
});

const result = await response.json();
// { "message": "Fixed code changed successfully" }
```

### 6. Đăng Nhập với Mã Cố Định

```javascript
// Bước 1: Đăng nhập ban đầu
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.sessionId && loginData.requiredVerification === 'fixed_code') {
  // Yêu cầu xác minh mã cố định
  const fixedCode = prompt('Nhập mã xác minh cố định của bạn:');

  // Bước 2: Gửi mã cố định
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: fixedCode
    })
  });

  const { user, accessToken } = await verifyResponse.json();
  // Đăng nhập hoàn tất
}
```

### 7. Sử Dụng Refresh Token

```javascript
// Lưu trữ tokens sau đăng nhập
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const { accessToken, refreshToken, expiresIn } = await loginResponse.json();

// Lưu trữ tokens an toàn
localStorage.setItem('accessToken', accessToken);
localStorage.setItem('refreshToken', refreshToken);

// Làm mới access token khi hết hạn
const refreshResponse = await fetch('/auth/refresh', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    refreshToken: localStorage.getItem('refreshToken')
  })
});

const newTokens = await refreshResponse.json();
localStorage.setItem('accessToken', newTokens.accessToken);
localStorage.setItem('refreshToken', newTokens.refreshToken);
```

### 8. Đăng Xuất (Thu Hồi Tokens)

```javascript
// Đăng xuất - thu hồi refresh token
const logoutResponse = await fetch('/auth/revoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    refreshToken: localStorage.getItem('refreshToken')
  })
});

// Xóa tokens đã lưu trữ
localStorage.removeItem('accessToken');
localStorage.removeItem('refreshToken');

// Thu hồi tất cả tokens (đăng xuất khỏi tất cả thiết bị)
const revokeAllResponse = await fetch('/auth/revoke-all', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + accessToken }
});
```

### 9. Làm Mới Token Tự Động

```javascript
// Axios interceptor cho việc làm mới token tự động
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken })
        });

        const { accessToken, refreshToken: newRefreshToken } = await response.json();

        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', newRefreshToken);

        // Thử lại request ban đầu với token mới
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return axios(originalRequest);
      } catch (refreshError) {
        // Làm mới thất bại, chuyển hướng đến đăng nhập
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);
```

### 10. Đổi Mật Khẩu với Xác Minh

```javascript
// Bước 1: Yêu cầu mã xác minh (nếu phương thức bảo mật được bật)
await fetch('/auth/send-password-change-code', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + token }
});

// Bước 2: Đổi mật khẩu với xác minh
const response = await fetch('/auth/change-password', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    currentPassword: 'oldPassword123',
    newPassword: 'newPassword123',
    confirmNewPassword: 'newPassword123',
    verificationCode: '123456' // Bắt buộc nếu phương thức bảo mật được bật
  })
});
```

## Thay Đổi Lược Đồ Cơ Sở Dữ Liệu

Các trường sau đã được thêm vào bảng `users`:

- `securityMethod` (enum): Phương thức xác minh bảo mật đã chọn
- `twoFactorSecret` (varchar): TOTP secret cho 2FA (được mã hóa)
- `twoFactorEnabled` (boolean): Liệu 2FA có được bật hay không
- `verificationCode` (varchar): Mã xác minh tạm thời
- `verificationCodeExpires` (timestamp): Thời gian hết hạn mã xác minh
- `fixedCode` (varchar): Mã xác minh cố định đã hash
- `refreshToken` (text): JWT refresh token để gia hạn token
- `refreshTokenExpires` (timestamp): Thời gian hết hạn refresh token

## Cân Nhắc Bảo Mật

1. **Access tokens có thời gian sống ngắn (15 phút)**
2. **Refresh tokens có thời gian sống dài hơn (7 ngày)**
3. **Mã xác minh hết hạn sau 5 phút**
4. **2FA secrets được loại trừ khỏi API responses**
5. **Mã xác minh được loại trừ khỏi API responses**
6. **Mã cố định được hash và loại trừ khỏi API responses**
7. **Refresh tokens được lưu trữ an toàn trong cơ sở dữ liệu**
8. **Validation mã cố định ngăn chặn mẫu yếu (tuần tự, chữ số lặp lại)**
9. **Thông báo email cho các thay đổi bảo mật**
10. **Theo dõi xác minh dựa trên session**
11. **Token rotation khi làm mới (refresh token mới được phát hành)**
12. **Tương thích ngược được duy trì**

## Dependencies

Các packages sau đã được thêm:
- `speakeasy`: Cho việc tạo và xác minh TOTP
- `qrcode`: Cho việc tạo mã QR
- `@types/speakeasy`: TypeScript definitions
- `@types/qrcode`: TypeScript definitions

## Migration

Chạy database migrations để thêm các trường bảo mật mới:

```bash
npm run migration:run
```

Các file migration sẽ thêm các cột và indexes cần thiết vào bảng users:
- `003-add-security-fields-to-users.ts` - Các trường bảo mật ban đầu
- `004-add-fixed-code-to-users.ts` - Hỗ trợ mã cố định
- `005-add-refresh-token-to-users.ts` - Hỗ trợ refresh token

## Khắc Phục Sự Cố

### Vấn Đề Thường Gặp và Giải Pháp

#### 1. Vấn Đề Xác Minh Email

**Vấn đề**: Không nhận được mã xác minh
- **Giải pháp**: Kiểm tra thư mục spam email, xác minh cấu hình dịch vụ email
- **Debug**: Kiểm tra server logs để tìm lỗi gửi email

**Vấn đề**: Lỗi "Invalid verification code"
- **Giải pháp**: Đảm bảo mã được nhập trong vòng 5 phút sau khi tạo
- **Debug**: Kiểm tra xem mã xác minh đã hết hạn chưa

#### 2. Vấn Đề Xác Thực Hai Yếu Tố

**Vấn đề**: Mã QR không hiển thị
- **Giải pháp**: Đảm bảo package `qrcode` được cài đặt và hoạt động
- **Debug**: Kiểm tra browser console để tìm lỗi JavaScript

**Vấn đề**: "Invalid verification code" cho 2FA
- **Giải pháp**: Đảm bảo thời gian thiết bị được đồng bộ (TOTP dựa trên thời gian)
- **Debug**: Kiểm tra xem 2FA secret có được lưu trữ đúng trong cơ sở dữ liệu không

#### 3. Vấn Đề Mã Cố Định

**Vấn đề**: Lỗi "Fixed code is too weak"
- **Giải pháp**: Sử dụng mã không chứa:
  - Số tuần tự (123456, 654321)
  - Chữ số lặp lại (111111, 222222)
  - Mẫu thông thường (000000, 123123)
- **Khuyến nghị**: Sử dụng tổ hợp 6-8 chữ số ngẫu nhiên

**Vấn đề**: "Current fixed code is incorrect"
- **Giải pháp**: Xác minh bạn đang nhập đúng mã hiện tại
- **Debug**: Nếu quên, vô hiệu hóa và bật lại phương thức mã cố định

#### 4. Vấn Đề Luồng Đăng Nhập

**Vấn đề**: Bị kẹt trong trạng thái đăng nhập một phần
- **Giải pháp**: Hoàn thành xác minh trong thời gian timeout session (5 phút)
- **Thay thế**: Bắt đầu lại quy trình đăng nhập

**Vấn đề**: Session hết hạn trong quá trình xác minh
- **Giải pháp**: Khởi động lại quy trình đăng nhập từ đầu
- **Phòng ngừa**: Hoàn thành xác minh kịp thời

**Hệ thống xác thực nâng cao này cung cấp bảo mật linh hoạt và mạnh mẽ cho nền tảng Delify với trải nghiệm người dùng mượt mà.** 🔐✨
