/**
 * Cache Module Exports
 * Xuất tất cả components của Cache Module
 */

// Module
export { CacheModule } from './cache.module';

// Services
export { CacheService } from './services/cache.service';
export type { CacheOptions } from './services/cache.service';

// Interfaces
export * from './interfaces/cache.interface';

// Decorators
export * from './decorators/cache.decorator';

// Interceptors
export {
  CacheInterceptor,
  CacheEvictInterceptor,
  CacheManagerInterceptor,
} from './interceptors/cache.interceptor';

// Constants
export const CACHE_MODULE_OPTIONS = 'CACHE_MODULE_OPTIONS';
export const CACHE_MANAGER_TOKEN = 'CACHE_MANAGER';

/**
 * Default cache configuration
 * Cấu hình cache mặc định
 */
export const DEFAULT_CACHE_CONFIG = {
  ttl: 900, // 15 minutes
  max: 1000, // Maximum items in cache
  prefix: 'delify:',
  connectTimeout: 10000,
  commandTimeout: 5000,
};

/**
 * Cache key prefixes for different modules
 * Prefix cache key cho các module khác nhau
 */
export const CACHE_PREFIXES = {
  SSO: 'sso:',
  RBAC: 'rbac:',
  USER: 'user:',
  AI: 'ai:',
  ORGANIZATION: 'org:',
  SESSION: 'session:',
  AUTH: 'auth:',
} as const;

/**
 * Cache TTL constants (in seconds)
 * Hằng số TTL cache (tính bằng giây)
 */
export const CACHE_TTL = {
  SHORT: 300,    // 5 minutes
  MEDIUM: 900,   // 15 minutes
  LONG: 3600,    // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const;

/**
 * Type definitions for cache module
 * Định nghĩa type cho cache module
 */
export type CachePrefix = typeof CACHE_PREFIXES[keyof typeof CACHE_PREFIXES];
export type CacheTTLValue = typeof CACHE_TTL[keyof typeof CACHE_TTL];
