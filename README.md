# Delify Platform

Nền tảng web toàn diện sử dụng kiến trúc monolithic của NestJS cùng với cơ sở dữ liệu PostgreSQL, cung cấp các tiện ích hợp lý về chi phí cho các startup và doanh nghiệp nhỏ.

## 🚀 Tính năng chính

### 🎯 Tự động hóa Marketing
- Tự động đăng bài lên Facebook (tường và nhóm)
- Đăng bài theo lịch trình với tùy chọn thời gian
- Tự động bình luận trên Facebook và TikTok
- Lọc bình luận bằng AI để phát hiện thông tin liên hệ
- Tối ưu nội dung bằng AI

### 🤖 Tính năng AI
- Chatbot AI hỗ trợ khách hàng
- Đánh giá CV và chấm điểm hồ sơ tự động
- Phân tích email thông minh
- Tối ưu hóa nội dung cho social media

### 📧 Vận hành Doanh nghiệp
- Hệ thống email tên miền riêng
- Chiến dịch email marketing hàng loạt
- Hệ thống hóa đơn điện tử
- Quản lý tài liệu với chữ ký số
- Hệ thống đặt lịch hỗ trợ kỹ thuật

### 🔧 Workflow Builder
- Hệ thống kéo thả chức năng như n8n
- Tự động hóa quy trình kinh doanh
- Tích hợp API Facebook và TikTok

## 🏗️ Kiến trúc

### Tech Stack
- **Backend**: NestJS (Node.js + TypeScript)
- **Database**: PostgreSQL
- **Cache & Queue**: Redis + Bull
- **Authentication**: JWT + Passport
- **API Documentation**: Swagger/OpenAPI
- **Containerization**: Docker + Docker Compose

### Cấu trúc Module
```
src/
├── modules/
│   ├── auth/           # Authentication & Authorization
│   ├── users/          # User management
│   ├── marketing/      # Facebook/TikTok automation
│   ├── ai/             # AI services
│   ├── email/          # Email system
│   ├── documents/      # Document management
│   ├── workflows/      # Workflow builder
│   ├── invoices/       # Electronic invoicing
│   └── scheduling/     # Support scheduling
├── common/             # Shared services
└── config/             # Configuration
```

## 🚀 Cài đặt và Chạy

### Yêu cầu hệ thống
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. Clone repository
```bash
git clone <repository-url>
cd delify
```

### 2. Cấu hình environment
```bash
cp .env.example .env
# Chỉnh sửa file .env với thông tin cấu hình của bạn
```

### 3. Chạy với Docker Compose
```bash
# Development mode
docker-compose up -d

# Production mode
docker-compose --profile production up -d
```

### 4. Cài đặt dependencies (nếu chạy local)
```bash
npm install
```

### 5. Chạy migrations
```bash
npm run migration:run
```

### 6. Khởi động ứng dụng
```bash
# Development
npm run start:dev

# Production
npm run start:prod
```

## 📚 API Documentation

Sau khi khởi động ứng dụng, truy cập Swagger UI tại:
- Development: http://localhost:3000/api/v1/docs
- Production: http://localhost:3001/api/v1/docs

## 🔧 Cấu hình

### Environment Variables

Xem file `.env.example` để biết danh sách đầy đủ các biến môi trường cần thiết.

#### Cấu hình quan trọng:
- `DATABASE_*`: Thông tin kết nối PostgreSQL
- `REDIS_*`: Thông tin kết nối Redis
- `JWT_SECRET`: Secret key cho JWT
- `FACEBOOK_*`: API keys cho Facebook
- `TIKTOK_*`: API keys cho TikTok
- `OPENAI_API_KEY`: API key cho OpenAI

### Facebook Integration
1. Tạo Facebook App tại https://developers.facebook.com/
2. Cấu hình permissions: `pages_manage_posts`, `pages_read_engagement`, `groups_access_member_info`
3. Thêm Facebook App ID và Secret vào `.env`

### TikTok Integration
1. Đăng ký TikTok for Business API
2. Tạo app và lấy Client Key/Secret
3. Cấu hình webhook endpoints

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 📦 Deployment

### Docker Production
```bash
# Build production image
docker build -t delify-platform --target production .

# Run with docker-compose
docker-compose --profile production up -d
```

### Manual Deployment
```bash
# Build application
npm run build

# Start production server
npm run start:prod
```

## 🔒 Bảo mật

- JWT authentication với refresh tokens
- Rate limiting
- Input validation với class-validator
- SQL injection protection với TypeORM
- CORS configuration
- Environment-based configuration

## 📈 Monitoring & Logging

- Structured logging với custom LoggerService
- Request/response logging
- Error tracking
- Performance monitoring
- Database query logging

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

Nếu bạn gặp vấn đề hoặc có câu hỏi, vui lòng:
1. Kiểm tra [Issues](../../issues) hiện có
2. Tạo issue mới với mô tả chi tiết
3. Liên hệ team phát triển

## 🗺️ Roadmap

- [ ] Tích hợp thêm social platforms (Instagram, LinkedIn)
- [ ] Mobile app (React Native)
- [ ] Advanced analytics dashboard
- [ ] Multi-tenant support
- [ ] Marketplace for workflow templates
- [ ] Advanced AI features (GPT-4, Claude)
- [ ] Real-time collaboration
- [ ] Advanced reporting system
