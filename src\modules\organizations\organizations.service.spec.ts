import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationsService } from './organizations.service';
import { Organization, OrganizationStatus, OrganizationPlan } from './entities/organization.entity';
import { OrganizationMember, MemberStatus } from './entities/organization-member.entity';
import { RolesService } from './services/roles.service';
import { LoggerService } from '../../common/services/logger.service';
import { UtilsService } from '../../common/services/utils.service';

describe('OrganizationsService', () => {
  let service: OrganizationsService;
  let organizationRepository: Repository<Organization>;
  let memberRepository: Repository<OrganizationMember>;
  let rolesService: RolesService;

  const mockOrganizationRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    increment: jest.fn(),
    decrement: jest.fn(),
  };

  const mockMemberRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  const mockRolesService = {
    getOwnerRole: jest.fn(),
  };

  const mockLoggerService = {
    logWithContext: jest.fn(),
    logError: jest.fn(),
  };

  const mockUtilsService = {
    slugify: jest.fn(),
    generateUuid: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationsService,
        {
          provide: getRepositoryToken(Organization),
          useValue: mockOrganizationRepository,
        },
        {
          provide: getRepositoryToken(OrganizationMember),
          useValue: mockMemberRepository,
        },
        {
          provide: RolesService,
          useValue: mockRolesService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: UtilsService,
          useValue: mockUtilsService,
        },
      ],
    }).compile();

    service = module.get<OrganizationsService>(OrganizationsService);
    organizationRepository = module.get<Repository<Organization>>(getRepositoryToken(Organization));
    memberRepository = module.get<Repository<OrganizationMember>>(getRepositoryToken(OrganizationMember));
    rolesService = module.get<RolesService>(RolesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createOrganization', () => {
    it('should create a new organization successfully', async () => {
      const userId = 'user-123';
      const createOrgDto = {
        name: 'Test Organization',
        description: 'Test description',
        website: 'https://test.com',
        industry: 'Technology',
      };

      const mockOwnerRole = { id: 'owner-role-id', name: 'owner' };
      const mockOrganization = {
        id: 'org-123',
        ...createOrgDto,
        slug: 'test-organization',
        ownerId: userId,
        status: OrganizationStatus.ACTIVE,
        plan: OrganizationPlan.FREE,
      };

      mockUtilsService.slugify.mockReturnValue('test-organization');
      mockOrganizationRepository.findOne.mockResolvedValue(null); // Slug not taken
      mockOrganizationRepository.create.mockReturnValue(mockOrganization);
      mockOrganizationRepository.save.mockResolvedValue(mockOrganization);
      mockRolesService.getOwnerRole.mockResolvedValue(mockOwnerRole);
      mockMemberRepository.create.mockReturnValue({});
      mockMemberRepository.save.mockResolvedValue({});

      const result = await service.createOrganization(userId, createOrgDto);

      expect(result).toEqual(mockOrganization);
      expect(mockOrganizationRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: createOrgDto.name,
          slug: 'test-organization',
          ownerId: userId,
          status: OrganizationStatus.ACTIVE,
          plan: OrganizationPlan.FREE,
        })
      );
      expect(mockLoggerService.logWithContext).toHaveBeenCalledWith(
        `Organization created: ${mockOrganization.id}`,
        'OrganizationsService'
      );
    });

    it('should generate unique slug when name conflicts', async () => {
      const userId = 'user-123';
      const createOrgDto = { name: 'Test Organization' };

      mockUtilsService.slugify.mockReturnValue('test-organization');
      mockOrganizationRepository.findOne
        .mockResolvedValueOnce({ id: 'existing-org' }) // First slug taken
        .mockResolvedValueOnce(null); // Second slug available

      const mockOrganization = { id: 'org-123', slug: 'test-organization-1' };
      mockOrganizationRepository.create.mockReturnValue(mockOrganization);
      mockOrganizationRepository.save.mockResolvedValue(mockOrganization);
      mockRolesService.getOwnerRole.mockResolvedValue({ id: 'owner-role-id' });
      mockMemberRepository.create.mockReturnValue({});
      mockMemberRepository.save.mockResolvedValue({});

      await service.createOrganization(userId, createOrgDto);

      expect(mockOrganizationRepository.findOne).toHaveBeenCalledTimes(2);
    });
  });

  describe('getUserOrganizations', () => {
    it('should return user organizations', async () => {
      const userId = 'user-123';
      const mockMembers = [
        {
          organization: { id: 'org-1', name: 'Org 1' },
          role: { name: 'owner' },
        },
        {
          organization: { id: 'org-2', name: 'Org 2' },
          role: { name: 'member' },
        },
      ];

      mockMemberRepository.find.mockResolvedValue(mockMembers);

      const result = await service.getUserOrganizations(userId);

      expect(result).toEqual([
        { id: 'org-1', name: 'Org 1' },
        { id: 'org-2', name: 'Org 2' },
      ]);
      expect(mockMemberRepository.find).toHaveBeenCalledWith({
        where: { userId, status: MemberStatus.ACTIVE },
        relations: ['organization', 'role'],
      });
    });
  });

  describe('getOrganizationById', () => {
    it('should return organization when found', async () => {
      const organizationId = 'org-123';
      const mockOrganization = {
        id: organizationId,
        name: 'Test Organization',
        members: [],
      };

      mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);

      const result = await service.getOrganizationById(organizationId);

      expect(result).toEqual(mockOrganization);
      expect(mockOrganizationRepository.findOne).toHaveBeenCalledWith({
        where: { id: organizationId },
        relations: ['owner', 'members', 'members.user', 'members.role'],
      });
    });

    it('should throw NotFoundException when organization not found', async () => {
      const organizationId = 'non-existent';
      mockOrganizationRepository.findOne.mockResolvedValue(null);

      await expect(service.getOrganizationById(organizationId)).rejects.toThrow(
        'Organization not found'
      );
    });
  });

  describe('updateOrganization', () => {
    it('should update organization successfully', async () => {
      const organizationId = 'org-123';
      const updateDto = { name: 'Updated Name' };
      const existingOrg = { id: organizationId, name: 'Old Name' };
      const updatedOrg = { ...existingOrg, ...updateDto };

      mockOrganizationRepository.findOne
        .mockResolvedValueOnce(existingOrg) // getOrganizationById call
        .mockResolvedValueOnce(updatedOrg); // final getOrganizationById call
      mockOrganizationRepository.update.mockResolvedValue({ affected: 1 });
      mockUtilsService.slugify.mockReturnValue('updated-name');
      mockOrganizationRepository.findOne.mockResolvedValue(null); // Slug check

      const result = await service.updateOrganization(organizationId, updateDto);

      expect(result).toEqual(updatedOrg);
      expect(mockOrganizationRepository.update).toHaveBeenCalledWith(
        organizationId,
        expect.objectContaining({
          name: 'Updated Name',
          slug: 'updated-name',
        })
      );
    });
  });

  describe('checkOrganizationLimits', () => {
    it('should return true when within limits', async () => {
      const organizationId = 'org-123';
      const mockOrganization = {
        limits: { maxMembers: 10 },
        usage: { currentMembers: 5 },
      };

      mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);

      const result = await service.checkOrganizationLimits(organizationId, 'members', 2);

      expect(result).toBe(true);
    });

    it('should return false when exceeding limits', async () => {
      const organizationId = 'org-123';
      const mockOrganization = {
        limits: { maxMembers: 10 },
        usage: { currentMembers: 9 },
      };

      mockOrganizationRepository.findOne.mockResolvedValue(mockOrganization);

      const result = await service.checkOrganizationLimits(organizationId, 'members', 2);

      expect(result).toBe(false);
    });
  });
});
