import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { DigitalSignatureService } from './services/digital-signature.service';
import { DocumentWorkflowService } from './services/document-workflow.service';
import { Document } from './entities/document.entity';
import { DocumentSignature } from './entities/document-signature.entity';
import { DocumentWorkflow } from './entities/document-workflow.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Document,
      DocumentSignature,
      DocumentWorkflow,
    ]),
  ],
  controllers: [DocumentsController],
  providers: [
    DocumentsService,
    DigitalSignatureService,
    DocumentWorkflowService,
  ],
  exports: [
    DocumentsService,
    DigitalSignatureService,
    DocumentWorkflowService,
  ],
})
export class DocumentsModule {}
