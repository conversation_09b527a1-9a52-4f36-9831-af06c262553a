import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { UserDeviceService } from '../users/services/user-device.service';
import { UtilsService } from '../../common/services/utils.service';
import { LoggerService } from '../../common/services/logger.service';
import { VerificationService } from './services/verification.service';
import { EmailVerificationService } from './services/email-verification.service';
import { SecurityMethod, User } from '../users/entities/user.entity';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';

describe('AuthService - Enhanced Security', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let verificationService: jest.Mocked<VerificationService>;
  let emailVerificationService: jest.Mocked<EmailVerificationService>;

  const mockUser: Partial<User> = {
    id: 'user-id',
    email: '<EMAIL>',
    username: 'testuser',
    password: 'hashedpassword',
    securityMethod: SecurityMethod.DISABLED,
    twoFactorEnabled: false,
    hasSecurityMethodEnabled: jest.fn().mockReturnValue(false),
    requiresEmailVerification: jest.fn().mockReturnValue(false),
    requiresTwoFactorAuth: jest.fn().mockReturnValue(false),
    isVerificationCodeValid: jest.fn().mockReturnValue(false),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByEmailOrUsername: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            updateLastLogin: jest.fn(),
            setVerificationCode: jest.fn(),
            clearVerificationCode: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn().mockReturnValue('mock-jwt-token'),
          },
        },
        {
          provide: UserDeviceService,
          useValue: {
            createDeviceSession: jest.fn().mockResolvedValue({
              sessionToken: 'mock-session-token',
            }),
          },
        },
        {
          provide: UtilsService,
          useValue: {
            comparePassword: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            logWithContext: jest.fn(),
          },
        },
        {
          provide: VerificationService,
          useValue: {
            generateVerificationCode: jest.fn().mockReturnValue('123456'),
            createVerificationSession: jest.fn().mockReturnValue('session-id'),
            getVerificationSession: jest.fn(),
            removeVerificationSession: jest.fn(),
            verifyTwoFactorCode: jest.fn(),
            generateTwoFactorSecret: jest.fn(),
            validateFixedCodeFormat: jest.fn(),
            hashFixedCode: jest.fn(),
            verifyFixedCode: jest.fn(),
          },
        },
        {
          provide: EmailVerificationService,
          useValue: {
            sendVerificationCode: jest.fn(),
            sendPasswordChangeNotification: jest.fn(),
            sendSecurityMethodChangeNotification: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    verificationService = module.get(VerificationService);
    emailVerificationService = module.get(EmailVerificationService);
  });

  describe('login with security method disabled', () => {
    it('should complete login directly when security method is disabled', async () => {
      const loginDto = { emailOrUsername: '<EMAIL>', password: 'password' };
      const deviceInfo = { ip: '127.0.0.1', userAgent: 'test-agent' };

      usersService.findByEmailOrUsername.mockResolvedValue(mockUser as User);
      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser as User);

      const result = await service.login(loginDto, deviceInfo);

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(result).not.toHaveProperty('sessionId');
    });
  });

  describe('login with email verification enabled', () => {
    it('should return partial login response when email verification is enabled', async () => {
      const userWithEmailVerification = {
        ...mockUser,
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        hasSecurityMethodEnabled: jest.fn().mockReturnValue(true),
        requiresEmailVerification: jest.fn().mockReturnValue(true),
      };

      const loginDto = { emailOrUsername: '<EMAIL>', password: 'password' };
      const deviceInfo = { ip: '127.0.0.1', userAgent: 'test-agent' };

      usersService.findByEmailOrUsername.mockResolvedValue(userWithEmailVerification as User);
      jest.spyOn(service, 'validateUser').mockResolvedValue(userWithEmailVerification as User);

      const result = await service.login(loginDto, deviceInfo);

      expect(result).toHaveProperty('sessionId');
      expect(result).toHaveProperty('requiredVerification', SecurityMethod.EMAIL_VERIFICATION);
      expect(result).toHaveProperty('message');
      expect(verificationService.createVerificationSession).toHaveBeenCalled();
      expect(emailVerificationService.sendVerificationCode).toHaveBeenCalled();
    });
  });

  describe('verifyLogin', () => {
    it('should complete login after successful email verification', async () => {
      const verificationDto = { sessionId: 'session-id', verificationCode: '123456' };
      const deviceInfo = { ip: '127.0.0.1', userAgent: 'test-agent' };

      const userWithEmailVerification = {
        ...mockUser,
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        requiresEmailVerification: jest.fn().mockReturnValue(true),
        isVerificationCodeValid: jest.fn().mockReturnValue(true),
      };

      verificationService.getVerificationSession.mockReturnValue({
        userId: 'user-id',
        sessionId: 'session-id',
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        expiresAt: new Date(Date.now() + 300000),
        deviceInfo,
      });

      usersService.findOne.mockResolvedValue(userWithEmailVerification as User);

      const result = await service.verifyLogin(verificationDto, deviceInfo);

      expect(result).toHaveProperty('user');
      expect(result).toHaveProperty('accessToken');
      expect(usersService.clearVerificationCode).toHaveBeenCalledWith('user-id');
      expect(verificationService.removeVerificationSession).toHaveBeenCalledWith('session-id');
    });

    it('should throw error for invalid verification code', async () => {
      const verificationDto = { sessionId: 'session-id', verificationCode: 'invalid' };
      const deviceInfo = { ip: '127.0.0.1', userAgent: 'test-agent' };

      const userWithEmailVerification = {
        ...mockUser,
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        requiresEmailVerification: jest.fn().mockReturnValue(true),
        isVerificationCodeValid: jest.fn().mockReturnValue(false),
      };

      verificationService.getVerificationSession.mockReturnValue({
        userId: 'user-id',
        sessionId: 'session-id',
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        expiresAt: new Date(Date.now() + 300000),
        deviceInfo,
      });

      usersService.findOne.mockResolvedValue(userWithEmailVerification as User);

      await expect(service.verifyLogin(verificationDto, deviceInfo))
        .rejects.toThrow(UnauthorizedException);
    });
  });

  describe('updateSecurityMethod', () => {
    it('should enable email verification', async () => {
      const dto = { securityMethod: SecurityMethod.EMAIL_VERIFICATION };

      usersService.findOne
        .mockResolvedValueOnce(mockUser as User) // First call in updateSecurityMethod
        .mockResolvedValueOnce({ // Second call after update
          ...mockUser,
          securityMethod: SecurityMethod.EMAIL_VERIFICATION,
          hasFixedCodeSet: jest.fn().mockReturnValue(false),
        } as User);

      usersService.update.mockResolvedValue(undefined);

      const result = await service.updateSecurityMethod('user-id', dto);

      expect(result.securityMethod).toBe(SecurityMethod.EMAIL_VERIFICATION);
      expect(usersService.update).toHaveBeenCalledWith('user-id', {
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        twoFactorEnabled: false,
        twoFactorSecret: null,
      });
      expect(emailVerificationService.sendSecurityMethodChangeNotification)
        .toHaveBeenCalledWith('<EMAIL>', SecurityMethod.EMAIL_VERIFICATION);
    });

    it('should enable fixed code verification', async () => {
      const dto = {
        securityMethod: SecurityMethod.FIXED_CODE,
        fixedCode: '789123'
      };

      verificationService.validateFixedCodeFormat.mockReturnValue({ isValid: true });
      verificationService.hashFixedCode.mockResolvedValue('hashed-fixed-code');

      usersService.findOne
        .mockResolvedValueOnce(mockUser as User) // First call in updateSecurityMethod
        .mockResolvedValueOnce({ // Second call after update
          ...mockUser,
          securityMethod: SecurityMethod.FIXED_CODE,
          hasFixedCodeSet: jest.fn().mockReturnValue(true),
        } as User);

      usersService.update.mockResolvedValue(undefined);

      const result = await service.updateSecurityMethod('user-id', dto);

      expect(result.securityMethod).toBe(SecurityMethod.FIXED_CODE);
      expect(result.hasFixedCode).toBe(true);
      expect(verificationService.validateFixedCodeFormat).toHaveBeenCalledWith('789123');
      expect(verificationService.hashFixedCode).toHaveBeenCalledWith('789123');
      expect(usersService.update).toHaveBeenCalledWith('user-id', {
        securityMethod: SecurityMethod.FIXED_CODE,
        twoFactorEnabled: false,
        twoFactorSecret: null,
        fixedCode: 'hashed-fixed-code',
      });
    });

    it('should reject weak fixed code', async () => {
      const dto = {
        securityMethod: SecurityMethod.FIXED_CODE,
        fixedCode: '123456' // Sequential pattern
      };

      verificationService.validateFixedCodeFormat.mockReturnValue({
        isValid: false,
        error: 'Fixed code is too weak'
      });

      usersService.findOne.mockResolvedValue(mockUser as User);

      await expect(service.updateSecurityMethod('user-id', dto))
        .rejects.toThrow('Fixed code is too weak');
    });
  });
});
