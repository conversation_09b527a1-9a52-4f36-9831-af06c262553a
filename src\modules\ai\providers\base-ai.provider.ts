export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  responseTime: number;
}

export interface AIProviderConfig {
  apiKey: string;
  baseURL?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  timeout?: number;
}

export abstract class BaseAIProvider {
  protected config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
  }

  abstract generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;

  abstract generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown>;

  abstract analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;

  abstract embedText(text: string): Promise<number[]>;

  abstract validateConfig(): Promise<boolean>;

  protected mergeConfig(options?: Partial<AIProviderConfig>): AIProviderConfig {
    return {
      ...this.config,
      ...options,
    };
  }

  protected calculateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English
    return Math.ceil(text.length / 4);
  }

  protected handleError(error: any, provider: string): never {
    console.error(`${provider} API Error:`, error);
    
    if (error.response?.status === 401) {
      throw new Error(`${provider}: Invalid API key`);
    } else if (error.response?.status === 429) {
      throw new Error(`${provider}: Rate limit exceeded`);
    } else if (error.response?.status === 400) {
      throw new Error(`${provider}: Bad request - ${error.response?.data?.error?.message || 'Invalid parameters'}`);
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error(`${provider}: Connection refused - service unavailable`);
    } else if (error.code === 'ETIMEDOUT') {
      throw new Error(`${provider}: Request timeout`);
    }
    
    throw new Error(`${provider}: ${error.message || 'Unknown error'}`);
  }
}
