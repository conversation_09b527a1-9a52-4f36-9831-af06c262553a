import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationMember, MemberStatus } from '../entities/organization-member.entity';
import { PERMISSIONS_KEY } from '../decorators/require-permissions.decorator';
import { ORGANIZATION_KEY } from '../decorators/current-organization.decorator';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @InjectRepository(OrganizationMember)
    private memberRepository: Repository<OrganizationMember>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions) {
      return true; // No permissions required
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = request.params.id || request.params.organizationId;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (!organizationId) {
      throw new ForbiddenException('Organization context required');
    }

    // Get user's membership in the organization
    const membership = await this.memberRepository.findOne({
      where: { 
        userId: user.id, 
        organizationId,
        status: MemberStatus.ACTIVE,
      },
      relations: ['role', 'role.permissions'],
    });

    if (!membership) {
      throw new ForbiddenException('User is not a member of this organization');
    }

    // Check if user has required permissions
    const hasPermission = requiredPermissions.every(permission =>
      membership.hasPermission(permission)
    );

    if (!hasPermission) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Add organization context to request
    request.organization = membership.organization;
    request.membership = membership;

    return true;
  }
}
