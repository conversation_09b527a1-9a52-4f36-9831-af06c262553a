import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  Request,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { SSOApplication } from '../entities/sso-application.entity';
import { SSOAuditLog, SSOAction } from '../entities/sso-audit-log.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RBACGuard } from '../../rbac/guards/rbac.guard';
import { RequirePermission } from '../../rbac/decorators/rbac.decorators';

/**
 * DTOs for SSO Application Management
 */
export class CreateSSOApplicationDto {
  name: string;
  subdomain: string;
  displayName: string;
  description?: string;
  baseUrl: string;
  allowedOrigins?: string[];
}

export class UpdateSSOApplicationDto {
  displayName?: string;
  description?: string;
  baseUrl?: string;
  allowedOrigins?: string[];
  isActive?: boolean;
}

export class SSOApplicationQueryDto {
  search?: string;
  isActive?: boolean;
  page?: string;
  limit?: string;
}

/**
 * SSOApplicationController - Controller quản lý ứng dụng SSO
 * SSOApplicationController - SSO application management controller
 */
@ApiTags('SSO Applications - Quản lý ứng dụng SSO')
@Controller('sso/applications')
@UseGuards(JwtAuthGuard, RBACGuard)
@ApiBearerAuth()
export class SSOApplicationController {
  constructor(
    @InjectRepository(SSOApplication)
    private applicationRepository: Repository<SSOApplication>,
    @InjectRepository(SSOAuditLog)
    private auditRepository: Repository<SSOAuditLog>,
  ) {}

  /**
   * Tạo ứng dụng SSO mới
   * Create new SSO application
   */
  @Post()
  @RequirePermission('SSO_MANAGEMENT_CREATE')
  @ApiOperation({
    summary: 'Tạo ứng dụng SSO mới - Create new SSO application',
    description: 'Tạo một ứng dụng mới trong hệ thống SSO.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Ứng dụng được tạo thành công - Application created successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Tên hoặc subdomain đã tồn tại - Name or subdomain already exists',
  })
  @HttpCode(HttpStatus.CREATED)
  async createApplication(
    @Body() createDto: CreateSSOApplicationDto,
    @Request() req: any,
  ) {
    // Validate subdomain format
    if (!SSOApplication.isValidSubdomain(createDto.subdomain)) {
      throw new BadRequestException('Invalid subdomain format');
    }

    // Check for existing name or subdomain
    const existing = await this.applicationRepository.findOne({
      where: [
        { name: createDto.name },
        { subdomain: createDto.subdomain },
      ],
    });

    if (existing) {
      throw new ConflictException('Application name or subdomain already exists');
    }

    const application = new SSOApplication();
    application.name = createDto.name;
    application.subdomain = createDto.subdomain;
    application.displayName = createDto.displayName;
    application.description = createDto.description;
    application.baseUrl = createDto.baseUrl;
    application.allowedOrigins = createDto.allowedOrigins;

    // Validate configuration
    const validation = application.validateConfiguration();
    if (!validation.valid) {
      throw new BadRequestException(`Invalid configuration: ${validation.errors.join(', ')}`);
    }

    const savedApplication = await this.applicationRepository.save(application);

    // Log audit
    await this.logAudit(SSOAction.CONFIG_CHANGE, true, {
      userId: req.user.sub,
      metadata: {
        action: 'APPLICATION_CREATED',
        applicationId: savedApplication.id,
        applicationName: savedApplication.name,
      },
    });

    return savedApplication;
  }

  /**
   * Lấy danh sách ứng dụng SSO
   * Get SSO applications list
   */
  @Get()
  @RequirePermission('SSO_MANAGEMENT_READ')
  @ApiOperation({
    summary: 'Lấy danh sách ứng dụng SSO - Get SSO applications list',
    description: 'Lấy danh sách tất cả ứng dụng SSO với khả năng lọc và phân trang.',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo tên hoặc subdomain' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Lọc theo trạng thái hoạt động' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách ứng dụng SSO - SSO applications list',
  })
  async getApplications(@Query() query: SSOApplicationQueryDto) {
    const queryBuilder = this.applicationRepository.createQueryBuilder('app');

    // Apply filters
    if (query.search) {
      queryBuilder.where(
        '(app.name ILIKE :search OR app.subdomain ILIKE :search OR app.displayName ILIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    if (query.isActive !== undefined) {
      queryBuilder.andWhere('app.isActive = :isActive', { isActive: query.isActive });
    }

    // Apply pagination
    const page = parseInt(query.page) || 1;
    const limit = parseInt(query.limit) || 10;
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);
    queryBuilder.orderBy('app.createdAt', 'DESC');

    const [applications, total] = await queryBuilder.getManyAndCount();

    return {
      applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Lấy ứng dụng SSO theo ID
   * Get SSO application by ID
   */
  @Get(':id')
  @RequirePermission('SSO_MANAGEMENT_READ')
  @ApiOperation({
    summary: 'Lấy ứng dụng SSO theo ID - Get SSO application by ID',
    description: 'Lấy thông tin chi tiết của một ứng dụng SSO.',
  })
  @ApiParam({ name: 'id', description: 'ID ứng dụng SSO' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin ứng dụng SSO - SSO application information',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy ứng dụng - Application not found',
  })
  async getApplicationById(@Param('id', ParseUUIDPipe) id: string) {
    const application = await this.applicationRepository.findOne({
      where: { id },
    });

    if (!application) {
      throw new NotFoundException('SSO application not found');
    }

    return application;
  }

  /**
   * Cập nhật ứng dụng SSO
   * Update SSO application
   */
  @Put(':id')
  @RequirePermission('SSO_MANAGEMENT_UPDATE')
  @ApiOperation({
    summary: 'Cập nhật ứng dụng SSO - Update SSO application',
    description: 'Cập nhật thông tin của một ứng dụng SSO.',
  })
  @ApiParam({ name: 'id', description: 'ID ứng dụng SSO' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ứng dụng được cập nhật thành công - Application updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy ứng dụng - Application not found',
  })
  async updateApplication(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateSSOApplicationDto,
    @Request() req: any,
  ) {
    const application = await this.applicationRepository.findOne({
      where: { id },
    });

    if (!application) {
      throw new NotFoundException('SSO application not found');
    }

    // Update fields
    if (updateDto.displayName !== undefined) {
      application.displayName = updateDto.displayName;
    }
    if (updateDto.description !== undefined) {
      application.description = updateDto.description;
    }
    if (updateDto.baseUrl !== undefined) {
      application.baseUrl = updateDto.baseUrl;
    }
    if (updateDto.allowedOrigins !== undefined) {
      application.allowedOrigins = updateDto.allowedOrigins;
    }
    if (updateDto.isActive !== undefined) {
      application.isActive = updateDto.isActive;
    }

    // Validate configuration
    const validation = application.validateConfiguration();
    if (!validation.valid) {
      throw new BadRequestException(`Invalid configuration: ${validation.errors.join(', ')}`);
    }

    const updatedApplication = await this.applicationRepository.save(application);

    // Log audit
    await this.logAudit(SSOAction.CONFIG_CHANGE, true, {
      userId: req.user.sub,
      metadata: {
        action: 'APPLICATION_UPDATED',
        applicationId: updatedApplication.id,
        applicationName: updatedApplication.name,
        changes: updateDto,
      },
    });

    return updatedApplication;
  }

  /**
   * Xóa ứng dụng SSO
   * Delete SSO application
   */
  @Delete(':id')
  @RequirePermission('SSO_MANAGEMENT_DELETE')
  @ApiOperation({
    summary: 'Xóa ứng dụng SSO - Delete SSO application',
    description: 'Xóa một ứng dụng SSO khỏi hệ thống.',
  })
  @ApiParam({ name: 'id', description: 'ID ứng dụng SSO' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Ứng dụng được xóa thành công - Application deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy ứng dụng - Application not found',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteApplication(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const application = await this.applicationRepository.findOne({
      where: { id },
    });

    if (!application) {
      throw new NotFoundException('SSO application not found');
    }

    await this.applicationRepository.remove(application);

    // Log audit
    await this.logAudit(SSOAction.CONFIG_CHANGE, true, {
      userId: req.user.sub,
      metadata: {
        action: 'APPLICATION_DELETED',
        applicationId: id,
        applicationName: application.name,
      },
    });
  }

  /**
   * Kích hoạt/vô hiệu hóa ứng dụng SSO
   * Activate/deactivate SSO application
   */
  @Put(':id/toggle')
  @RequirePermission('SSO_MANAGEMENT_UPDATE')
  @ApiOperation({
    summary: 'Kích hoạt/vô hiệu hóa ứng dụng SSO - Toggle SSO application',
    description: 'Kích hoạt hoặc vô hiệu hóa một ứng dụng SSO.',
  })
  @ApiParam({ name: 'id', description: 'ID ứng dụng SSO' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trạng thái ứng dụng được thay đổi - Application status changed',
  })
  async toggleApplication(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const application = await this.applicationRepository.findOne({
      where: { id },
    });

    if (!application) {
      throw new NotFoundException('SSO application not found');
    }

    application.isActive = !application.isActive;
    const updatedApplication = await this.applicationRepository.save(application);

    // Log audit
    await this.logAudit(SSOAction.CONFIG_CHANGE, true, {
      userId: req.user.sub,
      metadata: {
        action: application.isActive ? 'APPLICATION_ACTIVATED' : 'APPLICATION_DEACTIVATED',
        applicationId: updatedApplication.id,
        applicationName: updatedApplication.name,
      },
    });

    return {
      message: `Application ${application.isActive ? 'activated' : 'deactivated'} successfully`,
      application: updatedApplication,
    };
  }

  /**
   * Log audit event
   * Log audit event
   */
  private async logAudit(
    action: SSOAction,
    success: boolean,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<void> {
    const auditLog = SSOAuditLog.create(action, success, options);
    await this.auditRepository.save(auditLog);
  }
}
