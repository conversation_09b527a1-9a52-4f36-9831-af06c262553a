# SSO Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting steps for common SSO issues, diagnostic procedures, and resolution strategies for administrators and developers.

## Quick Diagnostic Checklist

Before diving into specific issues, run through this quick checklist:

- [ ] SSO is enabled (`SSO_ENABLED=true`)
- [ ] Environment variables are correctly configured
- [ ] Database migrations have been run
- [ ] Redis is running and accessible
- [ ] JWT secrets are consistent across all services
- [ ] CORS is properly configured for all domains
- [ ] Browser cookies are enabled
- [ ] User has appropriate permissions

## Common Issues and Solutions

### 1. Authentication Issues

#### Issue: "SSO not enabled" Error

**Symptoms:**
- Users receive "SSO is not enabled" message
- API returns 400 Bad Request with SSO disabled message

**Diagnosis:**
```bash
# Check SSO configuration
curl -X GET http://localhost:3000/auth/sso/config

# Expected response should show "enabled": true
```

**Solutions:**
1. **Environment Configuration:**
   ```env
   SSO_ENABLED=true
   ```

2. **Restart Application:**
   ```bash
   npm run start:dev
   ```

3. **Verify Configuration Service:**
   ```typescript
   // Check SSOConfigService.isEnabled()
   const config = ssoConfigService.getConfig();
   console.log('SSO Enabled:', config.enabled);
   ```

#### Issue: Token Verification Fails

**Symptoms:**
- "Token is invalid" errors
- Cross-domain authentication not working
- Users redirected to login repeatedly

**Diagnosis:**
```bash
# Test token verification
curl -X POST http://localhost:3000/auth/sso/verify \
  -H "Content-Type: application/json" \
  -d '{"token":"your-jwt-token","application":"app.yourcompany.com"}'
```

**Solutions:**
1. **Check JWT Secret Consistency:**
   ```env
   # Ensure same JWT_SECRET across all services
   JWT_SECRET=your_consistent_secret_key
   ```

2. **Verify Token Format:**
   ```typescript
   // Decode token to check structure
   const decoded = jwt.decode(token);
   console.log('Token payload:', decoded);
   ```

3. **Check Token Blacklist:**
   ```bash
   # Check if token is blacklisted
   curl -X GET http://localhost:3000/auth/sso/blacklist/stats \
     -H "Authorization: Bearer admin-token"
   ```

#### Issue: Session Expired Immediately

**Symptoms:**
- Sessions expire right after login
- "Session expired" message appears immediately
- Users cannot stay logged in

**Diagnosis:**
```bash
# Check session information
curl -X GET http://localhost:3000/auth/sso/session \
  -H "Authorization: Bearer access-token"
```

**Solutions:**
1. **Check Session Timeout Configuration:**
   ```env
   SSO_SESSION_TIMEOUT=480  # 8 hours in minutes
   ```

2. **Verify System Clock:**
   ```bash
   # Ensure server time is correct
   date
   timedatectl status
   ```

3. **Check Redis Connection:**
   ```bash
   # Test Redis connectivity
   redis-cli ping
   # Should return PONG
   ```

### 2. Cross-Domain Issues

#### Issue: Cookies Not Shared Across Subdomains

**Symptoms:**
- Login works on main domain but not subdomains
- Users need to login separately for each subdomain
- Cross-domain API calls fail

**Diagnosis:**
```javascript
// Check cookies in browser developer tools
document.cookie
// Should show cookies with domain=.yourcompany.com
```

**Solutions:**
1. **Configure Cookie Domain:**
   ```env
   SSO_COOKIE_DOMAIN=.yourcompany.com
   ```

2. **Verify CORS Configuration:**
   ```typescript
   // Check CORS settings
   const corsConfig = ssoConfigService.getCORSConfig();
   console.log('CORS Origins:', corsConfig.origin);
   ```

3. **Browser Settings:**
   - Enable third-party cookies
   - Add `*.yourcompany.com` to trusted sites
   - Disable ad blockers for company domains

#### Issue: CORS Errors

**Symptoms:**
- "CORS policy" errors in browser console
- Cross-domain requests blocked
- Preflight requests failing

**Diagnosis:**
```bash
# Test CORS with curl
curl -X OPTIONS http://localhost:3000/auth/sso/login \
  -H "Origin: https://app.yourcompany.com" \
  -H "Access-Control-Request-Method: POST" \
  -v
```

**Solutions:**
1. **Update CORS Configuration:**
   ```env
   SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com
   ```

2. **Check Application Registration:**
   ```bash
   # Verify applications are registered
   curl -X GET http://localhost:3000/sso/applications \
     -H "Authorization: Bearer admin-token"
   ```

### 3. Device and Security Issues

#### Issue: Device Not Recognized

**Symptoms:**
- "Device verification required" messages
- Users blocked on familiar devices
- Excessive security warnings

**Diagnosis:**
```typescript
// Check device fingerprint generation
const deviceInfo = extractDeviceInfo(request);
const fingerprint = deviceFingerprintService.generateFingerprint(deviceInfo);
console.log('Device fingerprint:', fingerprint);
```

**Solutions:**
1. **Adjust Device Verification Settings:**
   ```env
   SSO_REQUIRE_DEVICE_VERIFICATION=false
   ```

2. **Clear Device Cache:**
   ```bash
   # Clear Redis device cache
   redis-cli FLUSHDB
   ```

3. **Review Device Fingerprinting Logic:**
   ```typescript
   // Check suspicious device detection
   const analysis = deviceFingerprintService.analyzeDevice(deviceInfo);
   console.log('Device analysis:', analysis);
   ```

#### Issue: Suspicious Activity False Positives

**Symptoms:**
- Legitimate users flagged as suspicious
- Frequent device verification requests
- Sessions terminated unexpectedly

**Diagnosis:**
```bash
# Check audit logs for suspicious activity
curl -X GET http://localhost:3000/auth/sso/audit/security \
  -H "Authorization: Bearer admin-token"
```

**Solutions:**
1. **Tune Security Thresholds:**
   ```typescript
   // Adjust trust score thresholds
   const trustThreshold = 70; // Lower for less strict checking
   ```

2. **Whitelist Known IP Ranges:**
   ```typescript
   // Add company IP ranges to whitelist
   const companyIpRanges = ['***********/24', '10.0.0.0/8'];
   ```

### 4. Performance Issues

#### Issue: Slow Authentication

**Symptoms:**
- Login takes longer than 5 seconds
- Token verification timeouts
- Poor user experience

**Diagnosis:**
```bash
# Check response times
time curl -X POST http://localhost:3000/auth/sso/login \
  -H "Content-Type: application/json" \
  -d '{"userId":"test","deviceInfo":{}}'
```

**Solutions:**
1. **Optimize Database Queries:**
   ```sql
   -- Check for missing indexes
   EXPLAIN ANALYZE SELECT * FROM user_sessions WHERE user_id = 'uuid';
   ```

2. **Redis Cache Optimization:**
   ```bash
   # Check Redis performance
   redis-cli --latency-history -i 1
   ```

3. **Connection Pool Tuning:**
   ```typescript
   // Increase database connection pool
   {
     type: 'postgres',
     poolSize: 20,
     extra: {
       connectionLimit: 20
     }
   }
   ```

#### Issue: High Memory Usage

**Symptoms:**
- Server memory consumption increases over time
- Out of memory errors
- Performance degradation

**Diagnosis:**
```bash
# Monitor memory usage
top -p $(pgrep node)
# Check Redis memory usage
redis-cli info memory
```

**Solutions:**
1. **Implement Cache Cleanup:**
   ```typescript
   // Schedule regular cleanup
   @Cron('0 */6 * * *') // Every 6 hours
   async cleanupExpiredData() {
     await sessionService.cleanupExpiredSessions();
     await blacklistService.cleanupExpiredEntries();
   }
   ```

2. **Optimize Cache TTL:**
   ```env
   # Reduce cache TTL if needed
   CACHE_TTL=900  # 15 minutes
   ```

### 5. Database Issues

#### Issue: Migration Failures

**Symptoms:**
- SSO tables not created
- Foreign key constraint errors
- Migration rollback failures

**Diagnosis:**
```bash
# Check migration status
npm run migration:show

# Check database connection
npm run migration:run -- --dry-run
```

**Solutions:**
1. **Manual Migration:**
   ```bash
   # Run specific migration
   npm run migration:run -- --transaction=each
   ```

2. **Check Database Permissions:**
   ```sql
   -- Verify user has necessary permissions
   GRANT CREATE, ALTER, DROP ON DATABASE your_db TO your_user;
   ```

3. **Rollback and Retry:**
   ```bash
   # Rollback failed migration
   npm run migration:revert
   # Retry migration
   npm run migration:run
   ```

#### Issue: Seed Data Problems

**Symptoms:**
- Default applications not created
- Permissions not assigned
- Role-permission mappings missing

**Diagnosis:**
```bash
# Check seed data
npm run seed:sso -- --dry-run
```

**Solutions:**
1. **Manual Seed Execution:**
   ```bash
   # Run seed with verbose output
   npm run seed:sso -- --verbose
   ```

2. **Verify Data Integrity:**
   ```sql
   -- Check created data
   SELECT * FROM sso_applications;
   SELECT * FROM permissions WHERE application != 'MAIN_APP';
   ```

## Diagnostic Tools and Commands

### Health Check Commands

```bash
# SSO System Health Check
curl -X GET http://localhost:3000/auth/sso/config

# Database Connectivity
npm run migration:show

# Redis Connectivity
redis-cli ping

# Application Status
curl -X GET http://localhost:3000/health
```

### Log Analysis

```bash
# Application Logs
tail -f logs/application.log | grep SSO

# Audit Logs
curl -X GET http://localhost:3000/auth/sso/audit/stats \
  -H "Authorization: Bearer admin-token"

# Error Logs
grep -i error logs/application.log | grep SSO
```

### Performance Monitoring

```bash
# Response Time Testing
ab -n 100 -c 10 http://localhost:3000/auth/sso/config

# Memory Usage
ps aux | grep node
free -h

# Database Performance
EXPLAIN ANALYZE SELECT * FROM user_sessions WHERE is_active = true;
```

## Environment-Specific Issues

### Development Environment

**Common Issues:**
- HTTPS requirements in development
- Self-signed certificate problems
- Local domain configuration

**Solutions:**
```env
# Development overrides
SSO_SECURE_ONLY=false
NODE_TLS_REJECT_UNAUTHORIZED=0
```

### Production Environment

**Common Issues:**
- Load balancer configuration
- SSL termination problems
- Environment variable synchronization

**Solutions:**
1. **Load Balancer Configuration:**
   ```nginx
   # Nginx configuration for SSO
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   ```

2. **SSL Configuration:**
   ```env
   SSO_SECURE_ONLY=true
   SSO_SAME_SITE=lax
   ```

## Emergency Procedures

### Complete SSO Reset

```bash
# 1. Stop application
pm2 stop all

# 2. Clear Redis cache
redis-cli FLUSHALL

# 3. Reset database (CAUTION: Data loss)
npm run migration:revert
npm run migration:run
npm run seed:sso

# 4. Restart application
pm2 start all
```

### User Account Recovery

```sql
-- Reset user sessions
DELETE FROM user_sessions WHERE user_id = 'user-uuid';

-- Clear blacklisted tokens for user
DELETE FROM jwt_blacklist WHERE user_id = 'user-uuid';

-- Reset device verification
UPDATE users SET require_device_verification = false WHERE id = 'user-uuid';
```

### Security Incident Response

```bash
# 1. Revoke all tokens for compromised user
curl -X POST http://localhost:3000/auth/sso/revoke-all \
  -H "Authorization: Bearer admin-token" \
  -d '{"userId":"compromised-user-id"}'

# 2. Check audit logs
curl -X GET http://localhost:3000/auth/sso/audit/security \
  -H "Authorization: Bearer admin-token"

# 3. Disable user account if necessary
curl -X PUT http://localhost:3000/users/compromised-user-id \
  -H "Authorization: Bearer admin-token" \
  -d '{"isActive":false}'
```

## Getting Additional Help

### Log Collection for Support

```bash
# Collect relevant logs
mkdir sso-debug-$(date +%Y%m%d)
cp logs/application.log sso-debug-$(date +%Y%m%d)/
curl -X GET http://localhost:3000/auth/sso/config > sso-debug-$(date +%Y%m%d)/config.json
curl -X GET http://localhost:3000/auth/sso/blacklist/stats \
  -H "Authorization: Bearer admin-token" > sso-debug-$(date +%Y%m%d)/stats.json
tar -czf sso-debug-$(date +%Y%m%d).tar.gz sso-debug-$(date +%Y%m%d)/
```

### Support Information Template

When contacting support, include:

1. **Environment Details:**
   - Node.js version
   - Database version
   - Redis version
   - Operating system

2. **Error Information:**
   - Exact error messages
   - Stack traces
   - Request/response examples

3. **Configuration:**
   - Relevant environment variables (redacted)
   - CORS settings
   - Domain configuration

4. **Steps to Reproduce:**
   - Detailed reproduction steps
   - Expected vs actual behavior
   - Frequency of occurrence

---

**For additional support, refer to [Implementation Guide](./SSO_IMPLEMENTATION_GUIDE.md) and [API Documentation](./SSO_API_DOCUMENTATION.md).**
