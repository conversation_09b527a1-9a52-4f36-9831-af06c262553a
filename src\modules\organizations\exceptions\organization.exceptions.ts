import { BadRequestException, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';

export class OrganizationNotFoundException extends NotFoundException {
  constructor(organizationId?: string) {
    super(
      organizationId 
        ? `Organization with ID ${organizationId} not found`
        : 'Organization not found'
    );
  }
}

export class OrganizationMemberNotFoundException extends NotFoundException {
  constructor(memberId?: string) {
    super(
      memberId 
        ? `Organization member with ID ${memberId} not found`
        : 'Organization member not found'
    );
  }
}

export class InvitationNotFoundException extends NotFoundException {
  constructor(token?: string) {
    super(
      token 
        ? `Invitation with token ${token} not found`
        : 'Invitation not found'
    );
  }
}

export class InvitationExpiredException extends BadRequestException {
  constructor() {
    super('Invitation has expired');
  }
}

export class InvitationAlreadyAcceptedException extends BadRequestException {
  constructor() {
    super('Invitation has already been accepted');
  }
}

export class UserAlreadyMemberException extends ConflictException {
  constructor(email: string, organizationName: string) {
    super(`User ${email} is already a member of ${organizationName}`);
  }
}

export class InvitationAlreadySentException extends ConflictException {
  constructor(email: string) {
    super(`Invitation already sent to ${email}`);
  }
}

export class MemberLimitExceededException extends BadRequestException {
  constructor(limit: number) {
    super(`Organization has reached the maximum member limit of ${limit}`);
  }
}

export class InsufficientPermissionsException extends ForbiddenException {
  constructor(permission: string) {
    super(`Insufficient permissions. Required: ${permission}`);
  }
}

export class CannotRemoveOwnerException extends BadRequestException {
  constructor() {
    super('Cannot remove organization owner');
  }
}

export class CannotModifySystemRoleException extends BadRequestException {
  constructor(roleName: string) {
    super(`Cannot modify system role: ${roleName}`);
  }
}

export class RoleNotFoundException extends NotFoundException {
  constructor(roleId?: string) {
    super(
      roleId 
        ? `Role with ID ${roleId} not found`
        : 'Role not found'
    );
  }
}

export class PermissionNotFoundException extends NotFoundException {
  constructor(permissionName?: string) {
    super(
      permissionName 
        ? `Permission ${permissionName} not found`
        : 'Permission not found'
    );
  }
}

export class InvalidRoleAssignmentException extends BadRequestException {
  constructor(reason: string) {
    super(`Invalid role assignment: ${reason}`);
  }
}

export class OrganizationSlugExistsException extends ConflictException {
  constructor(slug: string) {
    super(`Organization with slug '${slug}' already exists`);
  }
}

export class DeviceNotFoundException extends NotFoundException {
  constructor(deviceId?: string) {
    super(
      deviceId 
        ? `Device with ID ${deviceId} not found`
        : 'Device not found'
    );
  }
}

export class SessionExpiredException extends BadRequestException {
  constructor() {
    super('Session has expired');
  }
}

export class InvalidSessionException extends BadRequestException {
  constructor() {
    super('Invalid session');
  }
}

export class AccountTypeNotAllowedException extends ForbiddenException {
  constructor(action: string, requiredType: string) {
    super(`${action} requires ${requiredType} account type`);
  }
}

export class EmailMismatchException extends BadRequestException {
  constructor() {
    super('Email address does not match invitation');
  }
}

export class InvalidInvitationTokenException extends BadRequestException {
  constructor() {
    super('Invalid invitation token');
  }
}
