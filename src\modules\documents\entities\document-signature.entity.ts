import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Document } from './document.entity';

export enum SignatureType {
  ELECTRONIC = 'electronic',
  DIGITAL = 'digital',
  HANDWRITTEN = 'handwritten',
}

export enum SignatureStatus {
  PENDING = 'pending',
  SIGNED = 'signed',
  DECLINED = 'declined',
  EXPIRED = 'expired',
}

@Entity('document_signatures')
@Index(['documentId', 'signerEmail'])
@Index(['status', 'createdAt'])
export class DocumentSignature {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  documentId: string;

  @ManyToOne(() => Document)
  @JoinColumn({ name: 'documentId' })
  document: Document;

  @Column({ nullable: true })
  userId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  signerEmail: string;

  @Column()
  signerName: string;

  @Column({ nullable: true })
  signerRole: string;

  @Column({
    type: 'enum',
    enum: SignatureType,
    default: SignatureType.ELECTRONIC,
  })
  signatureType: SignatureType;

  @Column({
    type: 'enum',
    enum: SignatureStatus,
    default: SignatureStatus.PENDING,
  })
  status: SignatureStatus;

  @Column({ type: 'text', nullable: true })
  signatureData: string; // Base64 encoded signature image or digital signature

  @Column({ type: 'jsonb', nullable: true })
  signatureMetadata: {
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    timestamp?: string;
    certificate?: string; // For digital signatures
  };

  @Column({ nullable: true })
  signedAt: Date;

  @Column({ nullable: true })
  declinedAt: Date;

  @Column({ type: 'text', nullable: true })
  declineReason: string;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ nullable: true })
  accessToken: string; // Token for accessing signature page

  @Column({ nullable: true })
  accessTokenExpiresAt: Date;

  @Column({ default: 0 })
  remindersSent: number;

  @Column({ nullable: true })
  lastReminderSentAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isPending(): boolean {
    return this.status === SignatureStatus.PENDING;
  }

  get isSigned(): boolean {
    return this.status === SignatureStatus.SIGNED;
  }

  get isDeclined(): boolean {
    return this.status === SignatureStatus.DECLINED;
  }

  get isExpired(): boolean {
    return this.status === SignatureStatus.EXPIRED ||
           (this.accessTokenExpiresAt && new Date() > this.accessTokenExpiresAt);
  }

  get canSign(): boolean {
    return this.isPending && !this.isExpired;
  }
}
