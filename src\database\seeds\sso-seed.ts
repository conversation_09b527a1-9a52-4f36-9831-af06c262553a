import { DataSource } from 'typeorm';
import { SSOApplication } from '../../modules/sso/entities/sso-application.entity';
import { Permission, SystemModule, PermissionAction } from '../../modules/rbac/entities/permission.entity';
import { Role } from '../../modules/rbac/entities/role.entity';
import { RolePermission } from '../../modules/rbac/entities/role-permission.entity';

/**
 * SSO Seed Data - Dữ liệu khởi tạo cho hệ thống SSO
 * SSO Seed Data - Initial data for SSO system
 */
export class SSOSeed {
  
  /**
   * Chạy seed data cho SSO
   * Run SSO seed data
   */
  static async run(dataSource: DataSource): Promise<void> {
    console.log('🔄 Starting SSO seed data...');

    try {
      await this.seedSSOApplications(dataSource);
      await this.seedSSOPermissions(dataSource);
      await this.assignSSOPermissionsToRoles(dataSource);
      
      console.log('✅ SSO seed data completed successfully');
    } catch (error) {
      console.error('❌ SSO seed data failed:', error);
      throw error;
    }
  }

  /**
   * Seed SSO Applications
   * Seed SSO Applications
   */
  private static async seedSSOApplications(dataSource: DataSource): Promise<void> {
    console.log('📱 Seeding SSO applications...');

    const applicationRepository = dataSource.getRepository(SSOApplication);

    const applications = [
      {
        name: 'MAIN_APP',
        subdomain: 'app',
        displayName: 'Main Application',
        description: 'Primary application with full feature set',
        baseUrl: 'https://app.yourcompany.com',
        allowedOrigins: [
          'https://app.yourcompany.com',
          'https://*.yourcompany.com',
        ],
        isActive: true,
      },
      {
        name: 'MAIL_APP',
        subdomain: 'mail',
        displayName: 'Email Service',
        description: 'Email management and communication platform',
        baseUrl: 'https://mail.yourcompany.com',
        allowedOrigins: [
          'https://mail.yourcompany.com',
          'https://app.yourcompany.com',
        ],
        isActive: true,
      },
      {
        name: 'CORE_APP',
        subdomain: 'core',
        displayName: 'Core Services',
        description: 'Core business services and utilities',
        baseUrl: 'https://core.yourcompany.com',
        allowedOrigins: [
          'https://core.yourcompany.com',
          'https://app.yourcompany.com',
        ],
        isActive: true,
      },
      {
        name: 'API_GATEWAY',
        subdomain: 'api',
        displayName: 'API Gateway',
        description: 'Centralized API gateway and management',
        baseUrl: 'https://api.yourcompany.com',
        allowedOrigins: [
          'https://api.yourcompany.com',
          'https://app.yourcompany.com',
          'https://mail.yourcompany.com',
          'https://core.yourcompany.com',
        ],
        isActive: true,
      },
    ];

    for (const appData of applications) {
      const existingApp = await applicationRepository.findOne({
        where: { name: appData.name },
      });

      if (!existingApp) {
        const application = applicationRepository.create(appData);
        await applicationRepository.save(application);
        console.log(`  ✓ Created SSO application: ${appData.displayName}`);
      } else {
        console.log(`  ⚠ SSO application already exists: ${appData.displayName}`);
      }
    }
  }

  /**
   * Seed SSO Permissions
   * Seed SSO Permissions
   */
  private static async seedSSOPermissions(dataSource: DataSource): Promise<void> {
    console.log('🔐 Seeding SSO permissions...');

    const permissionRepository = dataSource.getRepository(Permission);

    // SSO-specific modules and their permissions
    const ssoModules = [
      {
        module: SystemModule.MAIL_MANAGEMENT,
        application: 'MAIL_APP',
        permissions: [
          { action: PermissionAction.READ, description: 'View emails and mailboxes' },
          { action: PermissionAction.CREATE, description: 'Compose and send emails' },
          { action: PermissionAction.UPDATE, description: 'Edit email settings and folders' },
          { action: PermissionAction.DELETE, description: 'Delete emails and folders' },
          { action: PermissionAction.EXPORT, description: 'Export email data' },
          { action: PermissionAction.MANAGE, description: 'Full email management access' },
        ],
      },
      {
        module: SystemModule.CORE_SERVICES,
        application: 'CORE_APP',
        permissions: [
          { action: PermissionAction.READ, description: 'View core services status' },
          { action: PermissionAction.CREATE, description: 'Create core service configurations' },
          { action: PermissionAction.UPDATE, description: 'Update core service settings' },
          { action: PermissionAction.DELETE, description: 'Remove core service configurations' },
          { action: PermissionAction.MANAGE, description: 'Full core services management' },
        ],
      },
      {
        module: SystemModule.API_GATEWAY,
        application: 'API_GATEWAY',
        permissions: [
          { action: PermissionAction.READ, description: 'View API gateway metrics and logs' },
          { action: PermissionAction.CREATE, description: 'Create API routes and configurations' },
          { action: PermissionAction.UPDATE, description: 'Update API gateway settings' },
          { action: PermissionAction.DELETE, description: 'Remove API configurations' },
          { action: PermissionAction.MANAGE, description: 'Full API gateway management' },
        ],
      },
      {
        module: SystemModule.SSO_MANAGEMENT,
        application: 'MAIN_APP',
        permissions: [
          { action: PermissionAction.READ, description: 'View SSO configurations and sessions' },
          { action: PermissionAction.CREATE, description: 'Create SSO applications and settings' },
          { action: PermissionAction.UPDATE, description: 'Update SSO configurations' },
          { action: PermissionAction.DELETE, description: 'Remove SSO applications' },
          { action: PermissionAction.MANAGE, description: 'Full SSO system management' },
          { action: PermissionAction.ASSIGN, description: 'Assign SSO permissions to users' },
        ],
      },
    ];

    for (const moduleData of ssoModules) {
      for (const permData of moduleData.permissions) {
        const code = `${moduleData.module}_${permData.action}`;
        
        const existingPermission = await permissionRepository.findOne({
          where: { code },
        });

        if (!existingPermission) {
          const permission = permissionRepository.create({
            code,
            module: moduleData.module,
            action: permData.action,
            application: moduleData.application,
            description: permData.description,
            isActive: true,
          });

          await permissionRepository.save(permission);
          console.log(`  ✓ Created permission: ${code}`);
        } else {
          console.log(`  ⚠ Permission already exists: ${code}`);
        }
      }
    }
  }

  /**
   * Assign SSO Permissions to Roles
   * Assign SSO Permissions to Roles
   */
  private static async assignSSOPermissionsToRoles(dataSource: DataSource): Promise<void> {
    console.log('👥 Assigning SSO permissions to roles...');

    const roleRepository = dataSource.getRepository(Role);
    const permissionRepository = dataSource.getRepository(Permission);
    const rolePermissionRepository = dataSource.getRepository(RolePermission);

    // Define role-permission mappings for SSO
    const rolePermissionMappings = [
      {
        roleName: 'MASTER_ACCOUNT',
        permissions: [
          // All SSO permissions
          'MAIL_MANAGEMENT_MANAGE',
          'CORE_SERVICES_MANAGE',
          'API_GATEWAY_MANAGE',
          'SSO_MANAGEMENT_MANAGE',
        ],
      },
      {
        roleName: 'ADMIN',
        permissions: [
          // Most SSO permissions except some sensitive ones
          'MAIL_MANAGEMENT_READ',
          'MAIL_MANAGEMENT_CREATE',
          'MAIL_MANAGEMENT_UPDATE',
          'MAIL_MANAGEMENT_DELETE',
          'CORE_SERVICES_READ',
          'CORE_SERVICES_UPDATE',
          'API_GATEWAY_READ',
          'API_GATEWAY_UPDATE',
          'SSO_MANAGEMENT_READ',
          'SSO_MANAGEMENT_CREATE',
          'SSO_MANAGEMENT_UPDATE',
          'SSO_MANAGEMENT_DELETE',
        ],
      },
      {
        roleName: 'MANAGER',
        permissions: [
          // Limited SSO permissions
          'MAIL_MANAGEMENT_READ',
          'MAIL_MANAGEMENT_CREATE',
          'MAIL_MANAGEMENT_UPDATE',
          'CORE_SERVICES_READ',
          'API_GATEWAY_READ',
          'SSO_MANAGEMENT_READ',
        ],
      },
      {
        roleName: 'MARKETING_LEAD',
        permissions: [
          'MAIL_MANAGEMENT_READ',
          'MAIL_MANAGEMENT_CREATE',
          'MAIL_MANAGEMENT_UPDATE',
        ],
      },
      {
        roleName: 'SALES_LEAD',
        permissions: [
          'MAIL_MANAGEMENT_READ',
          'MAIL_MANAGEMENT_CREATE',
          'CORE_SERVICES_READ',
        ],
      },
      {
        roleName: 'CONTENT_LEAD',
        permissions: [
          'MAIL_MANAGEMENT_READ',
          'MAIL_MANAGEMENT_CREATE',
        ],
      },
      {
        roleName: 'ANALYST',
        permissions: [
          'MAIL_MANAGEMENT_READ',
          'CORE_SERVICES_READ',
          'API_GATEWAY_READ',
          'SSO_MANAGEMENT_READ',
        ],
      },
      {
        roleName: 'VIEWER',
        permissions: [
          'MAIL_MANAGEMENT_READ',
          'CORE_SERVICES_READ',
          'API_GATEWAY_READ',
        ],
      },
    ];

    for (const mapping of rolePermissionMappings) {
      const role = await roleRepository.findOne({
        where: { name: mapping.roleName },
      });

      if (!role) {
        console.log(`  ⚠ Role not found: ${mapping.roleName}`);
        continue;
      }

      for (const permissionCode of mapping.permissions) {
        const permission = await permissionRepository.findOne({
          where: { code: permissionCode },
        });

        if (!permission) {
          console.log(`  ⚠ Permission not found: ${permissionCode}`);
          continue;
        }

        // Check if role-permission mapping already exists
        const existingMapping = await rolePermissionRepository.findOne({
          where: {
            roleId: role.id,
            permissionId: permission.id,
          },
        });

        if (!existingMapping) {
          const rolePermission = rolePermissionRepository.create({
            roleId: role.id,
            permissionId: permission.id,
            grantedBy: 'system',
            grantedAt: new Date(),
          });

          await rolePermissionRepository.save(rolePermission);
          console.log(`  ✓ Assigned ${permissionCode} to ${mapping.roleName}`);
        } else {
          console.log(`  ⚠ Permission already assigned: ${permissionCode} to ${mapping.roleName}`);
        }
      }
    }
  }
}

/**
 * Main seed function
 * Main seed function
 */
export async function seedSSO(dataSource: DataSource): Promise<void> {
  await SSOSeed.run(dataSource);
}
