import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SocialPlatform } from './social-post.entity';

export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  EXPIRED = 'expired',
}

@Entity('social_accounts')
@Index(['userId', 'platform'])
@Index(['platform', 'platformAccountId'], { unique: true })
export class SocialAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'enum',
    enum: SocialPlatform,
  })
  platform: SocialPlatform;

  @Column()
  platformAccountId: string;

  @Column()
  accountName: string;

  @Column({ nullable: true })
  accountUsername: string;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ type: 'text' })
  accessToken: string;

  @Column({ nullable: true })
  refreshToken: string;

  @Column({ nullable: true })
  tokenExpiresAt: Date;

  @Column({
    type: 'enum',
    enum: AccountStatus,
    default: AccountStatus.ACTIVE,
  })
  status: AccountStatus;

  @Column({ type: 'jsonb', nullable: true })
  permissions: string[]; // List of granted permissions

  @Column({ type: 'jsonb', nullable: true })
  pages: {
    id: string;
    name: string;
    accessToken: string;
    permissions: string[];
  }[]; // For Facebook pages

  @Column({ type: 'jsonb', nullable: true })
  groups: {
    id: string;
    name: string;
    privacy: string;
    canPost: boolean;
  }[]; // For Facebook groups

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    autoPost?: boolean;
    defaultPostTime?: string; // HH:mm format
    timezone?: string;
    enableCommentFiltering?: boolean;
    enableAutoComment?: boolean;
  };

  @Column({ nullable: true })
  lastSyncAt: Date;

  @Column({ type: 'text', nullable: true })
  lastError: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === AccountStatus.ACTIVE;
  }

  get isTokenExpired(): boolean {
    return this.tokenExpiresAt && new Date() > this.tokenExpiresAt;
  }

  get hasValidToken(): boolean {
    return this.isActive && !this.isTokenExpired;
  }

  get isFacebook(): boolean {
    return this.platform === SocialPlatform.FACEBOOK;
  }

  get isTikTok(): boolean {
    return this.platform === SocialPlatform.TIKTOK;
  }
}
