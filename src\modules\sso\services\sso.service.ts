import { Injectable, UnauthorizedException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Response } from 'express';
import { SSOApplication } from '../entities/sso-application.entity';
import { UserSession } from '../entities/user-session.entity';
import { SSOAuditLog, SSOAction } from '../entities/sso-audit-log.entity';
import { SessionManagementService } from './session-management.service';
import { JWTBlacklistService, RevokeTokenInfo } from './jwt-blacklist.service';
import { DeviceFingerprintService, DeviceInfo } from './device-fingerprint.service';
import { SSOConfigService } from './sso-config.service';
import { TokenType, RevocationReason } from '../entities/jwt-blacklist.entity';
import { JwtPayload } from '../../auth/auth.service';
import { LoggerService } from '../../../common/services/logger.service';

/**
 * Interface cho SSO login request
 * Interface for SSO login request
 */
export interface SSOLoginRequest {
  userId: string;
  deviceInfo: DeviceInfo;
  application?: string;
  deviceName?: string;
  location?: string;
  rememberDevice?: boolean;
}

/**
 * Interface cho SSO login response
 * Interface for SSO login response
 */
export interface SSOLoginResponse {
  accessToken: string;
  refreshToken: string;
  sessionId: string;
  expiresIn: number;
  tokenType: string;
  ssoEnabled: boolean;
  allowedApplications: string[];
}

/**
 * Interface cho token verification
 * Interface for token verification
 */
export interface TokenVerificationResult {
  valid: boolean;
  payload?: JwtPayload;
  reason?: string;
  sessionValid?: boolean;
}

/**
 * SSOService - Dịch vụ chính cho hệ thống SSO
 * SSOService - Main service for SSO system
 */
@Injectable()
export class SSOService {
  constructor(
    @InjectRepository(SSOApplication)
    private applicationRepository: Repository<SSOApplication>,
    @InjectRepository(SSOAuditLog)
    private auditRepository: Repository<SSOAuditLog>,
    private sessionService: SessionManagementService,
    private blacklistService: JWTBlacklistService,
    private deviceFingerprintService: DeviceFingerprintService,
    private ssoConfigService: SSOConfigService,
    private jwtService: JwtService,
    private logger: LoggerService,
  ) {}

  /**
   * SSO Login - Đăng nhập với SSO
   * SSO Login - Login with SSO
   */
  async ssoLogin(loginRequest: SSOLoginRequest, rbacInfo?: any): Promise<SSOLoginResponse> {
    const { userId, deviceInfo, application, deviceName, location } = loginRequest;

    if (!this.ssoConfigService.isEnabled()) {
      throw new BadRequestException('SSO is not enabled');
    }

    // Validate application if provided
    if (application && !this.ssoConfigService.isApplicationAllowed(application)) {
      throw new ForbiddenException(`Application not allowed: ${application}`);
    }

    // Create session
    const session = await this.sessionService.createSession({
      userId,
      deviceInfo,
      deviceName,
      location,
    });

    // Generate tokens
    const { accessToken, refreshToken } = await this.generateSSOTokens(
      userId,
      session.sessionId,
      deviceInfo,
      rbacInfo,
    );

    // Log successful login
    await this.logAudit(SSOAction.LOGIN, true, {
      userId,
      sessionId: session.sessionId,
      application,
      ipAddress: deviceInfo.ipAddress,
      userAgent: deviceInfo.userAgent,
      deviceId: session.deviceId,
    });

    this.logger.logWithContext(
      `SSO login successful for user ${userId}, session ${session.sessionId}`,
      'SSOService'
    );

    return {
      accessToken,
      refreshToken,
      sessionId: session.sessionId,
      expiresIn: 15 * 60, // 15 minutes
      tokenType: 'Bearer',
      ssoEnabled: true,
      allowedApplications: this.ssoConfigService.getAllowedApplications(),
    };
  }

  /**
   * SSO Logout - Đăng xuất SSO
   * SSO Logout - SSO logout
   */
  async ssoLogout(
    sessionId: string,
    userId: string,
    globalLogout: boolean = false,
    deviceInfo?: DeviceInfo,
  ): Promise<{ message: string; sessionsTerminated: number }> {
    let sessionsTerminated = 0;

    if (globalLogout) {
      // Terminate all user sessions
      sessionsTerminated = await this.sessionService.terminateAllUserSessions(userId);
      
      // Revoke all user tokens
      await this.blacklistService.revokeAllUserTokens(
        userId,
        RevocationReason.GLOBAL_LOGOUT,
        userId,
      );

      await this.logAudit(SSOAction.GLOBAL_LOGOUT, true, {
        userId,
        sessionId,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
        metadata: { sessionsTerminated },
      });

      this.logger.logWithContext(
        `Global logout for user ${userId}: ${sessionsTerminated} sessions terminated`,
        'SSOService'
      );
    } else {
      // Terminate specific session
      await this.sessionService.terminateSession(sessionId, 'User logout');
      
      // Revoke session tokens
      await this.blacklistService.revokeSessionTokens(
        sessionId,
        userId,
        RevocationReason.USER_LOGOUT,
        userId,
      );

      sessionsTerminated = 1;

      await this.logAudit(SSOAction.LOGOUT, true, {
        userId,
        sessionId,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
      });

      this.logger.logWithContext(
        `Logout for user ${userId}, session ${sessionId}`,
        'SSOService'
      );
    }

    return {
      message: globalLogout ? 'Global logout successful' : 'Logout successful',
      sessionsTerminated,
    };
  }

  /**
   * Verify SSO Token
   * Verify SSO Token
   */
  async verifyToken(
    token: string,
    application?: string,
    deviceInfo?: DeviceInfo,
  ): Promise<TokenVerificationResult> {
    try {
      // Decode token without verification first to get JTI
      const decoded = this.jwtService.decode(token) as JwtPayload;
      if (!decoded || !decoded.jti) {
        return { valid: false, reason: 'Invalid token format' };
      }

      // Check if token is blacklisted
      const isBlacklisted = await this.blacklistService.isTokenBlacklisted(decoded.jti);
      if (isBlacklisted) {
        await this.logAudit(SSOAction.TOKEN_VERIFY, false, {
          userId: decoded.sub,
          sessionId: decoded.sessionId,
          application,
          ipAddress: deviceInfo?.ipAddress,
          userAgent: deviceInfo?.userAgent,
          errorMessage: 'Token is blacklisted',
        });

        return { valid: false, reason: 'Token is blacklisted' };
      }

      // Verify token signature and expiration
      const payload = this.jwtService.verify(token) as JwtPayload;

      // Validate audience if application provided
      if (application && payload.aud && !payload.aud.includes(application)) {
        return { valid: false, reason: 'Token not valid for this application' };
      }

      // Validate session if sessionId present
      let sessionValid = true;
      if (payload.sessionId) {
        sessionValid = await this.sessionService.validateSession(payload.sessionId, deviceInfo);
        if (!sessionValid) {
          return { valid: false, reason: 'Session expired or invalid', sessionValid: false };
        }
      }

      // Log successful verification
      await this.logAudit(SSOAction.TOKEN_VERIFY, true, {
        userId: payload.sub,
        sessionId: payload.sessionId,
        application,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
      });

      return {
        valid: true,
        payload,
        sessionValid,
      };
    } catch (error) {
      await this.logAudit(SSOAction.TOKEN_VERIFY, false, {
        application,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
        errorMessage: error.message,
      });

      return { valid: false, reason: error.message };
    }
  }

  /**
   * Refresh SSO Token
   * Refresh SSO Token
   */
  async refreshToken(
    refreshToken: string,
    deviceInfo?: DeviceInfo,
  ): Promise<{ accessToken: string; expiresIn: number }> {
    try {
      const payload = this.jwtService.verify(refreshToken) as any;
      
      if (payload.type !== 'refresh') {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Check if refresh token is blacklisted
      if (payload.jti) {
        const isBlacklisted = await this.blacklistService.isTokenBlacklisted(payload.jti);
        if (isBlacklisted) {
          throw new UnauthorizedException('Refresh token is blacklisted');
        }
      }

      // Generate new access token
      const newAccessToken = await this.generateAccessToken(payload.sub, payload.sessionId);

      await this.logAudit(SSOAction.TOKEN_REFRESH, true, {
        userId: payload.sub,
        sessionId: payload.sessionId,
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
      });

      return {
        accessToken: newAccessToken,
        expiresIn: 15 * 60, // 15 minutes
      };
    } catch (error) {
      await this.logAudit(SSOAction.TOKEN_REFRESH, false, {
        ipAddress: deviceInfo?.ipAddress,
        userAgent: deviceInfo?.userAgent,
        errorMessage: error.message,
      });

      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Set SSO Cookies
   * Set SSO Cookies
   */
  setSSOCookies(
    response: Response,
    accessToken: string,
    refreshToken: string,
  ): void {
    const accessTokenConfig = this.ssoConfigService.getAccessTokenCookieConfig();
    const refreshTokenConfig = this.ssoConfigService.getRefreshTokenCookieConfig();

    response.cookie('sso_access_token', accessToken, accessTokenConfig);
    response.cookie('sso_refresh_token', refreshToken, refreshTokenConfig);
  }

  /**
   * Clear SSO Cookies
   * Clear SSO Cookies
   */
  clearSSOCookies(response: Response): void {
    const cookieDomain = this.ssoConfigService.getCookieDomain();

    response.clearCookie('sso_access_token', { domain: cookieDomain });
    response.clearCookie('sso_refresh_token', { domain: cookieDomain });
  }

  /**
   * Get User Sessions
   * Get User Sessions
   */
  async getUserSessions(userId: string) {
    return this.sessionService.getUserSessions(userId);
  }

  /**
   * Terminate User Session
   * Terminate User Session
   */
  async terminateUserSession(sessionId: string, userId: string, reason?: string) {
    await this.sessionService.terminateSession(sessionId, reason);
    await this.blacklistService.revokeSessionTokens(
      sessionId,
      userId,
      RevocationReason.ADMIN_REVOKE,
      userId,
    );
  }

  /**
   * Generate SSO Tokens
   * Generate SSO Tokens
   */
  private async generateSSOTokens(
    userId: string,
    sessionId: string,
    deviceInfo: DeviceInfo,
    rbacInfo?: any,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const deviceAnalysis = this.deviceFingerprintService.analyzeDevice(deviceInfo);
    const config = this.ssoConfigService.getConfig();

    // Generate JTIs
    const accessJti = `access_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    const refreshJti = `refresh_${Date.now()}_${Math.random().toString(36).substring(2)}`;

    // Create access token payload
    const accessPayload: JwtPayload = {
      sub: userId,
      email: rbacInfo?.email || '',
      username: rbacInfo?.username || '',
      role: rbacInfo?.role || '',
      sessionToken: sessionId,
      // RBAC fields
      roles: rbacInfo?.roles || [],
      permissions: rbacInfo?.permissions || [],
      roleLevel: rbacInfo?.roleLevel,
      isMasterAccount: rbacInfo?.isMasterAccount || false,
      permissionVersion: Date.now(),
      // SSO fields
      iss: config.issuer,
      aud: config.allowedApplications,
      sessionId,
      deviceId: deviceAnalysis.deviceId,
      jti: accessJti,
      domain: config.baseDomain,
      nbf: Math.floor(Date.now() / 1000),
      ssoEnabled: true,
      applications: config.allowedApplications,
    };

    // Create refresh token payload
    const refreshPayload = {
      sub: userId,
      type: 'refresh',
      sessionId,
      jti: refreshJti,
      iss: config.issuer,
      aud: config.allowedApplications,
    };

    // Generate tokens
    const accessToken = this.jwtService.sign(accessPayload, { expiresIn: '15m' });
    const refreshToken = this.jwtService.sign(refreshPayload, { expiresIn: '7d' });

    return { accessToken, refreshToken };
  }

  /**
   * Generate Access Token
   * Generate Access Token
   */
  private async generateAccessToken(userId: string, sessionId: string): Promise<string> {
    const config = this.ssoConfigService.getConfig();
    const accessJti = `access_${Date.now()}_${Math.random().toString(36).substring(2)}`;

    const payload: JwtPayload = {
      sub: userId,
      email: '', // Would need to fetch from user service
      username: '',
      role: '',
      sessionId,
      jti: accessJti,
      iss: config.issuer,
      aud: config.allowedApplications,
      domain: config.baseDomain,
      ssoEnabled: true,
      applications: config.allowedApplications,
    };

    return this.jwtService.sign(payload, { expiresIn: '15m' });
  }

  /**
   * Log audit event
   * Log audit event
   */
  private async logAudit(
    action: SSOAction,
    success: boolean,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<void> {
    if (!this.ssoConfigService.isAuditLoggingEnabled()) {
      return;
    }

    const auditLog = SSOAuditLog.create(action, success, options);
    await this.auditRepository.save(auditLog);
  }
}
