-- Initialize PostgreSQL database for Delify platform

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS marketing;
CREATE SCHEMA IF NOT EXISTS ai;
CREATE SCHEMA IF NOT EXISTS email;
CREATE SCHEMA IF NOT EXISTS documents;
CREATE SCHEMA IF NOT EXISTS workflows;

-- Set default search path
ALTER DATABASE delify_db SET search_path TO public, auth, marketing, ai, email, documents, workflows;

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('admin', 'user', 'manager');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE post_status AS ENUM ('draft', 'scheduled', 'published', 'failed');
CREATE TYPE workflow_status AS ENUM ('active', 'inactive', 'paused');
CREATE TYPE document_status AS ENUM ('draft', 'pending_signature', 'signed', 'expired');

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE delify_db TO delify_user;
GRANT ALL ON SCHEMA public TO delify_user;
GRANT ALL ON SCHEMA auth TO delify_user;
GRANT ALL ON SCHEMA marketing TO delify_user;
GRANT ALL ON SCHEMA ai TO delify_user;
GRANT ALL ON SCHEMA email TO delify_user;
GRANT ALL ON SCHEMA documents TO delify_user;
GRANT ALL ON SCHEMA workflows TO delify_user;
