import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { WorkflowNode } from './workflow-node.entity';
import { WorkflowExecution } from './workflow-execution.entity';

export enum WorkflowStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PAUSED = 'paused',
}

export enum WorkflowTrigger {
  MANUAL = 'manual',
  SCHEDULE = 'schedule',
  WEBHOOK = 'webhook',
  EMAIL_RECEIVED = 'email_received',
  FORM_SUBMITTED = 'form_submitted',
  FILE_UPLOADED = 'file_uploaded',
}

@Entity('workflows')
@Index(['userId', 'status'])
@Index(['trigger', 'status'])
export class Workflow {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => WorkflowNode, node => node.workflow, { cascade: true })
  nodes: WorkflowNode[];

  @OneToMany(() => WorkflowExecution, execution => execution.workflow)
  executions: WorkflowExecution[];

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    default: WorkflowStatus.INACTIVE,
  })
  status: WorkflowStatus;

  @Column({
    type: 'enum',
    enum: WorkflowTrigger,
    default: WorkflowTrigger.MANUAL,
  })
  trigger: WorkflowTrigger;

  @Column({ type: 'jsonb', nullable: true })
  triggerConfig: {
    schedule?: {
      cron: string;
      timezone: string;
    };
    webhook?: {
      url: string;
      secret: string;
    };
    email?: {
      fromEmail: string;
      subject: string;
    };
    form?: {
      formId: string;
    };
  };

  @Column({ type: 'jsonb' })
  canvas: {
    nodes: Array<{
      id: string;
      type: string;
      position: { x: number; y: number };
      data: any;
    }>;
    edges: Array<{
      id: string;
      source: string;
      target: string;
      type?: string;
    }>;
  };

  @Column({ type: 'jsonb', nullable: true })
  variables: Record<string, any>; // Global workflow variables

  @Column({ default: 0 })
  executionCount: number;

  @Column({ default: 0 })
  successCount: number;

  @Column({ default: 0 })
  failureCount: number;

  @Column({ nullable: true })
  lastExecutedAt: Date;

  @Column({ nullable: true })
  lastSuccessAt: Date;

  @Column({ nullable: true })
  lastFailureAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    timeout?: number; // seconds
    retryCount?: number;
    retryDelay?: number; // seconds
    enableLogging?: boolean;
    enableNotifications?: boolean;
    notificationEmail?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === WorkflowStatus.ACTIVE;
  }

  get successRate(): number {
    if (this.executionCount === 0) return 0;
    return Math.round((this.successCount / this.executionCount) * 100);
  }

  get canBeExecuted(): boolean {
    return this.isActive && this.nodes && this.nodes.length > 0;
  }
}
