# Hướng Dẫn Thiết Lập Biến Môi Trường SSO

## Tổng Quan

Hướng dẫn này giải thích cách cấu hình biến môi trường cho hệ thống Single Sign-On (SSO) trong Delify Platform. Hệ thống SSO cho phép xác thực liền mạch trên nhiều subdomain và ứng dụng.

## Biến Môi Trường Bắt Buộc

### Cấu Hình SSO Cốt Lõi

#### `SSO_ENABLED`
- **Loại**: Boolean
- **Mặc định**: `false`
- **Mô tả**: Bật hoặc tắt chức năng SSO
- **Ví dụ**: `SSO_ENABLED=true`

#### `SSO_BASE_DOMAIN`
- **Loại**: String
- **Mô tả**: Domain gốc cho tổ chức của bạn
- **Ví dụ**: `SSO_BASE_DOMAIN=yourcompany.com`
- **L<PERSON>u ý**: Không bao gồm protocol (http/https) hoặc subdomain

#### `SSO_COOKIE_DOMAIN`
- **Loại**: String
- **Mô tả**: Domain cho chia sẻ cookie cross-subdomain
- **Ví dụ**: `SSO_COOKIE_DOMAIN=.yourcompany.com`
- **Lưu ý**: Phải bắt đầu bằng dấu chấm (.) để chia sẻ subdomain

#### `SSO_ISSUER`
- **Loại**: String
- **Mô tả**: Domain issuer JWT để validation token
- **Ví dụ**: `SSO_ISSUER=auth.yourcompany.com`

### Cấu Hình Ứng Dụng

#### `SSO_ALLOWED_APPLICATIONS`
- **Loại**: Chuỗi phân tách bằng dấu phẩy
- **Mô tả**: Danh sách ứng dụng được phép cho SSO
- **Ví dụ**: `SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com`
- **Lưu ý**: Bao gồm tất cả subdomain nên tham gia SSO

### Cấu Hình Session

#### `SSO_SESSION_TIMEOUT`
- **Loại**: Integer (phút)
- **Mặc định**: `480`
- **Mô tả**: Timeout session tính bằng phút (mặc định 8 giờ)
- **Ví dụ**: `SSO_SESSION_TIMEOUT=480`

#### `SSO_MAX_CONCURRENT_SESSIONS`
- **Loại**: Integer
- **Mặc định**: `5`
- **Mô tả**: Số session đồng thời tối đa mỗi user
- **Ví dụ**: `SSO_MAX_CONCURRENT_SESSIONS=5`

#### `SSO_REQUIRE_DEVICE_VERIFICATION`
- **Loại**: Boolean
- **Mặc định**: `false`
- **Mô tả**: Yêu cầu xác minh thiết bị cho thiết bị mới
- **Ví dụ**: `SSO_REQUIRE_DEVICE_VERIFICATION=false`

### Cấu Hình Bảo Mật

#### `SSO_ENABLE_AUDIT_LOGGING`
- **Loại**: Boolean
- **Mặc định**: `true`
- **Mô tả**: Bật audit logging cho hoạt động SSO
- **Ví dụ**: `SSO_ENABLE_AUDIT_LOGGING=true`

#### `SSO_TOKEN_REVOCATION_ENABLED`
- **Loại**: Boolean
- **Mặc định**: `true`
- **Mô tả**: Bật khả năng thu hồi token
- **Ví dụ**: `SSO_TOKEN_REVOCATION_ENABLED=true`

#### `SSO_CROSS_DOMAIN_COOKIES`
- **Loại**: Boolean
- **Mặc định**: `true`
- **Mô tả**: Bật chia sẻ cookie cross-domain
- **Ví dụ**: `SSO_CROSS_DOMAIN_COOKIES=true`

#### `SSO_SECURE_ONLY`
- **Loại**: Boolean
- **Mặc định**: `true`
- **Mô tả**: Yêu cầu HTTPS cho cookies (đặt false cho development)
- **Ví dụ**: `SSO_SECURE_ONLY=true`

#### `SSO_SAME_SITE`
- **Loại**: String
- **Mặc định**: `lax`
- **Tùy chọn**: `strict`, `lax`, `none`
- **Mô tả**: Thuộc tính SameSite cookie
- **Ví dụ**: `SSO_SAME_SITE=lax`

## Cập Nhật Cấu Hình JWT

### `JWT_SECRET`
- **Loại**: String
- **Mô tả**: Secret key để ký JWT token
- **Độ dài tối thiểu**: 32 ký tự (khuyến nghị 64+ cho production)
- **Ví dụ**: `JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters`

### `JWT_EXPIRES_IN`
- **Loại**: String
- **Mặc định**: `15m`
- **Mô tả**: Thời gian hết hạn access token
- **Ví dụ**: `JWT_EXPIRES_IN=15m`
- **Lưu ý**: Đã thay đổi từ `7d` thành `15m` cho bảo mật SSO

### `JWT_REFRESH_EXPIRES_IN`
- **Loại**: String
- **Mặc định**: `7d`
- **Mô tả**: Thời gian hết hạn refresh token
- **Ví dụ**: `JWT_REFRESH_EXPIRES_IN=7d`

## Biến Hiệu Suất Tùy Chọn

### `SSO_CACHE_TTL`
- **Loại**: Integer (giây)
- **Mặc định**: `900`
- **Mô tả**: Cache TTL cho dữ liệu SSO (15 phút)

### `SSO_SESSION_CLEANUP_INTERVAL`
- **Loại**: Integer (giây)
- **Mặc định**: `3600`
- **Mô tả**: Khoảng thời gian dọn dẹp session hết hạn (1 giờ)

### `SSO_TOKEN_CLEANUP_INTERVAL`
- **Loại**: Integer (giây)
- **Mặc định**: `1800`
- **Mô tả**: Khoảng thời gian dọn dẹp token hết hạn (30 phút)

## Biến Logging Tùy Chọn

### `SSO_LOG_LEVEL`
- **Loại**: String
- **Mặc định**: `info`
- **Tùy chọn**: `debug`, `info`, `warn`, `error`
- **Mô tả**: Mức logging cho hoạt động SSO

### `SSO_AUDIT_LOG_RETENTION_DAYS`
- **Loại**: Integer
- **Mặc định**: `90`
- **Mô tả**: Số ngày giữ lại audit logs

### `SSO_ENABLE_PERFORMANCE_LOGGING`
- **Loại**: Boolean
- **Mặc định**: `false`
- **Mô tả**: Bật performance logging cho hoạt động SSO

## Biến Rate Limiting Tùy Chọn

### `SSO_RATE_LIMIT_AUTH`
- **Loại**: Integer
- **Mặc định**: `10`
- **Mô tả**: Rate limit cho authentication endpoints (requests mỗi phút)

### `SSO_RATE_LIMIT_SESSION`
- **Loại**: Integer
- **Mặc định**: `60`
- **Mô tả**: Rate limit cho session management endpoints

### `SSO_RATE_LIMIT_ADMIN`
- **Loại**: Integer
- **Mặc định**: `100`
- **Mô tả**: Rate limit cho admin endpoints

## Cấu Hình Theo Môi Trường

### Môi Trường Development
```env
SSO_ENABLED=true
SSO_BASE_DOMAIN=localhost
SSO_COOKIE_DOMAIN=.localhost
SSO_SECURE_ONLY=false
SSO_REQUIRE_DEVICE_VERIFICATION=false
SSO_LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

### Môi Trường Production
```env
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_SECURE_ONLY=true
SSO_REQUIRE_DEVICE_VERIFICATION=true
SSO_LOG_LEVEL=warn
CORS_ORIGIN=https://app.yourcompany.com,https://mail.yourcompany.com,https://core.yourcompany.com
```

## Cập Nhật Cấu Hình CORS

Cập nhật cấu hình CORS để bao gồm SSO domains:

```env
CORS_ORIGIN=http://localhost:3000,https://app.yourcompany.com,https://mail.yourcompany.com,https://core.yourcompany.com,https://api.yourcompany.com
CORS_CREDENTIALS=true
```

## Cấu Hình Redis

Đảm bảo Redis được cấu hình đúng cho SSO session storage:

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
```

## Validation và Testing

Sau khi cấu hình biến môi trường, test cấu hình SSO:

### 1. Kiểm Tra SSO Configuration Endpoint
```bash
curl http://localhost:3000/auth/sso/config
```

Phản hồi mong đợi:
```json
{
  "enabled": true,
  "baseDomain": "yourcompany.com",
  "allowedApplications": ["app.yourcompany.com", "mail.yourcompany.com"],
  "sessionTimeout": 480,
  "maxConcurrentSessions": 5
}
```

### 2. Xác Minh Database Tables
Đảm bảo các bảng liên quan đến SSO được tạo:
```bash
npm run migration:run
```

### 3. Test SSO Login Flow
Test SSO login endpoint:
```bash
curl -X POST http://localhost:3000/auth/sso/login \
  -H "Content-Type: application/json" \
  -d '{"userId":"test-user-id","application":"app.yourcompany.com"}'
```

## Khắc Phục Sự Cố

### Vấn Đề Thường Gặp

1. **SSO không được bật**: Kiểm tra `SSO_ENABLED=true`
2. **Cross-domain cookies không hoạt động**: Xác minh `SSO_COOKIE_DOMAIN` bắt đầu bằng dấu chấm
3. **Lỗi CORS**: Đảm bảo tất cả SSO domains có trong `CORS_ORIGIN`
4. **Token verification thất bại**: Kiểm tra tính nhất quán `JWT_SECRET`
5. **Vấn đề session timeout**: Xác minh giá trị `SSO_SESSION_TIMEOUT`

### Debug Mode
Bật debug logging để khắc phục sự cố:
```env
SSO_LOG_LEVEL=debug
SSO_ENABLE_PERFORMANCE_LOGGING=true
```

## Thực Hành Bảo Mật Tốt Nhất

1. **Sử dụng JWT secrets mạnh** (64+ ký tự trong production)
2. **Bật HTTPS-only cookies** trong production (`SSO_SECURE_ONLY=true`)
3. **Đặt session timeouts phù hợp** dựa trên yêu cầu bảo mật
4. **Bật audit logging** cho compliance và giám sát bảo mật
5. **Sử dụng device verification** trong môi trường production
6. **Thường xuyên rotate JWT secrets** và cập nhật trên tất cả services

---

Để biết thông tin chi tiết hơn, tham khảo [Hướng Dẫn Triển Khai SSO](../sso/SSO_IMPLEMENTATION_GUIDE_vi.md).
