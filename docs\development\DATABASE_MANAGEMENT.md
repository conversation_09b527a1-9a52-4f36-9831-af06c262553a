# 🗄️ Database Management Guide

## 📋 Tổng quan

Hướng dẫn chi tiết để **quản lý database** trong Delify Platform với TypeORM, bao gồm migrations, entities, relationships, và best practices.

## 🚀 Database Setup

### Initial Configuration
```typescript
// ormconfig.ts
import { DataSource } from 'typeorm';
import { ConfigService } from '@nestjs/config';

const configService = new ConfigService();

export default new DataSource({
  type: 'postgres',
  host: configService.get('DATABASE_HOST'),
  port: configService.get('DATABASE_PORT'),
  username: configService.get('DATABASE_USERNAME'),
  password: configService.get('DATABASE_PASSWORD'),
  database: configService.get('DATABASE_NAME'),
  entities: ['src/**/*.entity.ts'],
  migrations: ['src/migrations/*.ts'],
  synchronize: false, // Always false in production
  logging: configService.get('NODE_ENV') === 'development',
  ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
});
```

### Environment Variables
```bash
# .env
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=delify_user
DATABASE_PASSWORD=secure_password
DATABASE_NAME=delify_platform

# Production
DATABASE_SSL=true
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=60000
```

## 🔧 Migrations Management

### Creating Migrations
```bash
# Generate migration from entity changes
npm run migration:generate -- --name CreateUsersTable

# Create empty migration
npm run migration:create -- --name AddIndexesToUsersTable

# Run migrations
npm run migration:run

# Revert last migration
npm run migration:revert

# Show migration status
npm run migration:show
```

### Migration Scripts (package.json)
```json
{
  "scripts": {
    "migration:generate": "typeorm-ts-node-commonjs migration:generate",
    "migration:create": "typeorm-ts-node-commonjs migration:create",
    "migration:run": "typeorm-ts-node-commonjs migration:run",
    "migration:revert": "typeorm-ts-node-commonjs migration:revert",
    "migration:show": "typeorm-ts-node-commonjs migration:show",
    "schema:drop": "typeorm-ts-node-commonjs schema:drop",
    "schema:sync": "typeorm-ts-node-commonjs schema:sync"
  }
}
```

### Migration Example
```typescript
// src/migrations/*************-CreateUsersTable.ts
import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateUsersTable************* implements MigrationInterface {
  name = 'CreateUsersTable*************';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE account_type_enum AS ENUM ('personal', 'business')
    `);

    // Create users table
    await queryRunner.createTable(
      new Table({
        name: 'users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'email',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'password_hash',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'first_name',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'last_name',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'account_type',
            type: 'enum',
            enum: ['personal', 'business'],
            default: "'personal'",
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'email_verified',
            type: 'boolean',
            default: false,
          },
          {
            name: 'email_verified_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'last_login_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex(
      'users',
      new Index({
        name: 'idx_users_email',
        columnNames: ['email'],
      }),
    );

    await queryRunner.createIndex(
      'users',
      new Index({
        name: 'idx_users_account_type',
        columnNames: ['account_type'],
      }),
    );

    await queryRunner.createIndex(
      'users',
      new Index({
        name: 'idx_users_is_active',
        columnNames: ['is_active'],
      }),
    );

    // Create partial index for active users
    await queryRunner.query(`
      CREATE INDEX idx_users_active_email 
      ON users(email) 
      WHERE is_active = true
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('users');
    await queryRunner.query(`DROP TYPE account_type_enum`);
  }
}
```

## 🏗️ Entity Design

### Base Entity
```typescript
// src/common/entities/base.entity.ts
import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
} from 'typeorm';

export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ default: true })
  isActive: boolean;
}
```

### User Entity Example
```typescript
// src/modules/users/entities/user.entity.ts
import {
  Entity,
  Column,
  OneToMany,
  Index,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { BaseEntity } from '../../common/entities/base.entity';
import { OrganizationMember } from '../../organizations/entities/organization-member.entity';
import { Session } from '../../auth/entities/session.entity';
import { AccountType } from '../enums/account-type.enum';

@Entity('users')
@Index(['email', 'isActive'])
export class User extends BaseEntity {
  @Column({ unique: true })
  email: string;

  @Column()
  @Exclude({ toPlainOnly: true })
  passwordHash: string;

  @Column({ length: 100, nullable: true })
  firstName: string;

  @Column({ length: 100, nullable: true })
  lastName: string;

  @Column({
    type: 'enum',
    enum: AccountType,
    default: AccountType.PERSONAL,
  })
  accountType: AccountType;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ nullable: true })
  emailVerifiedAt: Date;

  @Column({ nullable: true })
  lastLoginAt: Date;

  // Relationships
  @OneToMany(() => OrganizationMember, member => member.user)
  organizationMemberships: OrganizationMember[];

  @OneToMany(() => Session, session => session.user)
  sessions: Session[];

  // Virtual properties
  get fullName(): string {
    return `${this.firstName || ''} ${this.lastName || ''}`.trim();
  }

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  normalizeEmail() {
    if (this.email) {
      this.email = this.email.toLowerCase().trim();
    }
  }
}
```

### Complex Entity with Relationships
```typescript
// src/modules/organizations/entities/organization.entity.ts
import {
  Entity,
  Column,
  OneToMany,
  ManyToOne,
  Index,
  Check,
} from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { OrganizationMember } from './organization-member.entity';
import { Team } from './team.entity';
import { Invitation } from './invitation.entity';
import { OrganizationType } from '../enums/organization-type.enum';

@Entity('organizations')
@Index(['name', 'isActive'])
@Check(`"name" <> ''`)
export class Organization extends BaseEntity {
  @Column({ unique: true })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OrganizationType,
    default: OrganizationType.COMPANY,
  })
  type: OrganizationType;

  @Column('jsonb', { nullable: true })
  settings: {
    timezone?: string;
    language?: string;
    currency?: string;
    features?: string[];
    billing?: {
      plan?: string;
      maxUsers?: number;
      maxTeams?: number;
    };
  };

  // Relationships
  @OneToMany(() => OrganizationMember, member => member.organization, {
    cascade: true,
  })
  members: OrganizationMember[];

  @OneToMany(() => Team, team => team.organization, {
    cascade: true,
  })
  teams: Team[];

  @OneToMany(() => Invitation, invitation => invitation.organization, {
    cascade: true,
  })
  invitations: Invitation[];

  // Virtual properties
  get memberCount(): number {
    return this.members?.filter(m => m.isActive).length || 0;
  }

  get teamCount(): number {
    return this.teams?.filter(t => t.isActive).length || 0;
  }
}
```

## 🔗 Relationships Management

### One-to-Many Relationship
```typescript
// Parent entity
@Entity('organizations')
export class Organization extends BaseEntity {
  @OneToMany(() => Team, team => team.organization, {
    cascade: true,
    eager: false, // Don't load by default
  })
  teams: Team[];
}

// Child entity
@Entity('teams')
export class Team extends BaseEntity {
  @ManyToOne(() => Organization, org => org.teams, {
    onDelete: 'CASCADE',
  })
  organization: Organization;

  @Column()
  organizationId: string; // Foreign key column
}
```

### Many-to-Many Relationship
```typescript
// Junction table approach
@Entity('team_members')
export class TeamMember extends BaseEntity {
  @ManyToOne(() => Team, team => team.teamMembers)
  team: Team;

  @Column()
  teamId: string;

  @ManyToOne(() => OrganizationMember, member => member.teamMemberships)
  member: OrganizationMember;

  @Column()
  memberId: string;

  @Column({ nullable: true })
  joinedAt: Date;

  @Column({ default: false })
  isLead: boolean;
}

// Using @JoinTable (simpler approach)
@Entity('teams')
export class Team extends BaseEntity {
  @ManyToMany(() => OrganizationMember, member => member.teams)
  @JoinTable({
    name: 'team_members',
    joinColumn: { name: 'team_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'member_id', referencedColumnName: 'id' },
  })
  members: OrganizationMember[];
}
```

### Self-Referencing Relationship
```typescript
@Entity('categories')
export class Category extends BaseEntity {
  @Column()
  name: string;

  @ManyToOne(() => Category, category => category.children, {
    nullable: true,
  })
  parent: Category;

  @Column({ nullable: true })
  parentId: string;

  @OneToMany(() => Category, category => category.parent)
  children: Category[];
}
```

## 📊 Advanced Querying

### Repository Pattern
```typescript
// src/modules/users/repositories/users.repository.ts
import { Injectable } from '@nestjs/common';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { QueryUsersDto } from '../dto/query-users.dto';

@Injectable()
export class UsersRepository {
  constructor(
    @InjectRepository(User)
    private repository: Repository<User>,
  ) {}

  async findWithPagination(query: QueryUsersDto) {
    const queryBuilder = this.createQueryBuilder(query);
    
    const total = await queryBuilder.getCount();
    const users = await queryBuilder
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .getMany();

    return { users, total };
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({
      where: { email: email.toLowerCase() },
      relations: ['organizationMemberships'],
    });
  }

  async findActiveUsersInOrganization(organizationId: string): Promise<User[]> {
    return this.repository
      .createQueryBuilder('user')
      .innerJoin('user.organizationMemberships', 'membership')
      .where('membership.organizationId = :organizationId', { organizationId })
      .andWhere('membership.isActive = :isActive', { isActive: true })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .getMany();
  }

  private createQueryBuilder(query: QueryUsersDto): SelectQueryBuilder<User> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.organizationMemberships', 'membership')
      .leftJoinAndSelect('membership.organization', 'organization');

    // Apply filters
    if (query.search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    if (query.accountType) {
      queryBuilder.andWhere('user.accountType = :accountType', {
        accountType: query.accountType,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`user.${query.sortBy}`, query.sortOrder);

    return queryBuilder;
  }

  async getUserStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    byAccountType: Record<string, number>;
  }> {
    const [total, active, verified, byAccountType] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { isActive: true } }),
      this.repository.count({ where: { emailVerified: true } }),
      this.repository
        .createQueryBuilder('user')
        .select('user.accountType', 'accountType')
        .addSelect('COUNT(*)', 'count')
        .groupBy('user.accountType')
        .getRawMany(),
    ]);

    return {
      total,
      active,
      verified,
      byAccountType: byAccountType.reduce((acc, item) => {
        acc[item.accountType] = parseInt(item.count);
        return acc;
      }, {}),
    };
  }
}
```

### Custom Repository Methods
```typescript
// src/modules/organizations/repositories/organizations.repository.ts
@Injectable()
export class OrganizationsRepository {
  constructor(
    @InjectRepository(Organization)
    private repository: Repository<Organization>,
  ) {}

  async findWithMemberCount(): Promise<Array<Organization & { memberCount: number }>> {
    return this.repository
      .createQueryBuilder('org')
      .leftJoin('org.members', 'member')
      .addSelect('COUNT(member.id)', 'memberCount')
      .where('org.isActive = :isActive', { isActive: true })
      .groupBy('org.id')
      .getRawAndEntities()
      .then(result => 
        result.entities.map((org, index) => ({
          ...org,
          memberCount: parseInt(result.raw[index].memberCount),
        }))
      );
  }

  async findUserOrganizations(userId: string): Promise<Organization[]> {
    return this.repository
      .createQueryBuilder('org')
      .innerJoin('org.members', 'member')
      .where('member.userId = :userId', { userId })
      .andWhere('member.isActive = :isActive', { isActive: true })
      .andWhere('org.isActive = :isActive', { isActive: true })
      .orderBy('member.createdAt', 'DESC')
      .getMany();
  }
}
```

## 🔄 Database Seeding

### Seed Structure
```typescript
// src/database/seeds/initial-data.seed.ts
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import { User } from '../../modules/users/entities/user.entity';
import { Organization } from '../../modules/organizations/entities/organization.entity';
import { Role } from '../../modules/auth/entities/role.entity';
import { Permission } from '../../modules/auth/entities/permission.entity';

export class InitialDataSeed implements Seeder {
  async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    // Create system roles
    await this.createSystemRoles(dataSource);
    
    // Create system permissions
    await this.createSystemPermissions(dataSource);
    
    // Create admin user
    await this.createAdminUser(dataSource);
    
    // Create sample organization
    await this.createSampleOrganization(dataSource);
  }

  private async createSystemRoles(dataSource: DataSource): Promise<void> {
    const roleRepository = dataSource.getRepository(Role);
    
    const roles = [
      { name: 'owner', description: 'Organization owner', level: 5, isSystemRole: true },
      { name: 'admin', description: 'Administrator', level: 4, isSystemRole: true },
      { name: 'manager', description: 'Manager', level: 3, isSystemRole: true },
      { name: 'member', description: 'Member', level: 2, isSystemRole: true },
      { name: 'viewer', description: 'Viewer', level: 1, isSystemRole: true },
    ];

    for (const roleData of roles) {
      const existingRole = await roleRepository.findOne({
        where: { name: roleData.name },
      });

      if (!existingRole) {
        const role = roleRepository.create(roleData);
        await roleRepository.save(role);
      }
    }
  }

  private async createSystemPermissions(dataSource: DataSource): Promise<void> {
    const permissionRepository = dataSource.getRepository(Permission);
    
    const permissions = [
      { name: 'organization:delete', description: 'Delete organization', resource: 'organization', action: 'delete' },
      { name: 'members:manage', description: 'Manage members', resource: 'members', action: 'manage' },
      { name: 'teams:manage', description: 'Manage teams', resource: 'teams', action: 'manage' },
      { name: 'ai:use', description: 'Use AI features', resource: 'ai', action: 'use' },
      { name: 'reports:view', description: 'View reports', resource: 'reports', action: 'view' },
    ];

    for (const permissionData of permissions) {
      const existingPermission = await permissionRepository.findOne({
        where: { name: permissionData.name },
      });

      if (!existingPermission) {
        const permission = permissionRepository.create(permissionData);
        await permissionRepository.save(permission);
      }
    }
  }
}
```

### Factory Pattern for Testing
```typescript
// src/database/factories/user.factory.ts
import { setSeederFactory } from 'typeorm-extension';
import { User } from '../../modules/users/entities/user.entity';
import { AccountType } from '../../modules/users/enums/account-type.enum';

export default setSeederFactory(User, (faker) => {
  const user = new User();
  user.email = faker.internet.email().toLowerCase();
  user.firstName = faker.person.firstName();
  user.lastName = faker.person.lastName();
  user.accountType = faker.helpers.enumValue(AccountType);
  user.emailVerified = faker.datatype.boolean();
  user.isActive = true;
  
  return user;
});
```

### Running Seeds
```bash
# Run all seeds
npm run seed:run

# Run specific seed
npm run seed:run -- --seed=InitialDataSeed

# Create seed file
npm run seed:create -- --name=CreateSampleData
```

## 🔧 Database Optimization

### Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX CONCURRENTLY idx_users_email_active 
ON users(email) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_org_members_org_role 
ON organization_members(organization_id, role) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_sessions_user_active 
ON sessions(user_id) WHERE is_active = true;

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_users_name_search 
ON users(first_name, last_name) WHERE is_active = true;

-- JSON indexes for JSONB columns
CREATE INDEX CONCURRENTLY idx_organizations_settings_gin 
ON organizations USING GIN(settings);
```

### Query Optimization
```typescript
// Efficient pagination with cursor-based approach
async findWithCursor(cursor?: string, limit: number = 20) {
  const queryBuilder = this.repository
    .createQueryBuilder('user')
    .orderBy('user.createdAt', 'DESC')
    .limit(limit);

  if (cursor) {
    queryBuilder.where('user.createdAt < :cursor', { cursor });
  }

  return queryBuilder.getMany();
}

// Batch operations
async updateMultiple(ids: string[], updateData: Partial<User>) {
  return this.repository.update(ids, updateData);
}

// Efficient counting with EXISTS
async hasActiveMembers(organizationId: string): Promise<boolean> {
  const result = await this.repository
    .createQueryBuilder()
    .select('1')
    .from(OrganizationMember, 'member')
    .where('member.organizationId = :organizationId', { organizationId })
    .andWhere('member.isActive = :isActive', { isActive: true })
    .limit(1)
    .getRawOne();

  return !!result;
}
```

## 🔄 Backup & Recovery

### Backup Scripts
```bash
#!/bin/bash
# backup-database.sh

DB_NAME="delify_platform"
DB_USER="delify_user"
DB_HOST="localhost"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME \
  --no-password --verbose --clean --no-owner --no-privileges \
  --file="$BACKUP_DIR/delify_backup_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/delify_backup_$DATE.sql"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "delify_backup_*.sql.gz" -mtime +7 -delete
```

### Recovery Process
```bash
# Restore from backup
gunzip delify_backup_20240101_120000.sql.gz
psql -h localhost -U delify_user -d delify_platform_new < delify_backup_20240101_120000.sql

# Migration rollback strategy
npm run migration:revert  # Rollback last migration
npm run migration:show    # Check current state
```

## 🧪 Database Testing

### Test Database Setup
```typescript
// test/database-test.setup.ts
export const testDatabaseConfig = {
  type: 'postgres' as const,
  host: 'localhost',
  port: 5433,
  username: 'test_user',
  password: 'test_password',
  database: 'delify_test',
  entities: ['src/**/*.entity.ts'],
  synchronize: true,
  dropSchema: true,
  logging: false,
};

export async function setupTestDatabase() {
  const dataSource = new DataSource(testDatabaseConfig);
  await dataSource.initialize();
  return dataSource;
}
```

## 🎯 Best Practices

### 1. Migration Best Practices
- **Small, incremental changes** - One logical change per migration
- **Always provide rollback** - Implement down() method
- **Test migrations** - Test on copy of production data
- **Backup before migration** - Always backup before running migrations

### 2. Entity Design
- **Use base entities** - Common fields in base class
- **Proper indexing** - Index frequently queried columns
- **Validation** - Use class-validator decorators
- **Relationships** - Define proper cascade options

### 3. Performance
- **Lazy loading** - Don't eager load unless necessary
- **Query optimization** - Use query builder for complex queries
- **Connection pooling** - Configure appropriate pool size
- **Monitoring** - Track slow queries and performance

**Following this guide ensures robust, scalable, và maintainable database management trong Delify Platform.** 🗄️✨
