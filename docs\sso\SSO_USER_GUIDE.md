# SSO User Guide

## Overview

This guide provides comprehensive instructions for end users on how to use the Single Sign-On (SSO) system across multiple applications and subdomains.

## What is SSO?

Single Sign-On (SSO) allows you to log in once and access multiple applications without having to enter your credentials again. Our SSO system works across the following applications:

- **Main Application** (`app.yourcompany.com`) - Primary business application
- **Email Service** (`mail.yourcompany.com`) - Email management platform
- **Core Services** (`core.yourcompany.com`) - Core business utilities
- **API Gateway** (`api.yourcompany.com`) - Developer and integration tools

## Getting Started

### First-Time Login

1. **Navigate to Login Page**
   - Go to any of the supported applications
   - You'll be redirected to the login page if not authenticated

2. **Enter Your Credentials**
   - Email or username
   - Password
   - Complete any additional security verification if required

3. **Device Recognition**
   - The system will remember your device for future logins
   - You may be asked to provide a device name (e.g., "Work Laptop", "Personal Phone")

4. **Automatic Access**
   - Once logged in, you can access all other applications without re-entering credentials
   - Simply navigate to any supported subdomain

### Subsequent Logins

After your first login, the SSO system will:
- Automatically log you in when visiting any supported application
- Maintain your session across all applications
- Remember your device for faster authentication

## Using SSO Features

### Accessing Different Applications

**From Main Application to Email:**
1. Click on email links or navigate directly to `mail.yourcompany.com`
2. You'll be automatically logged in without entering credentials
3. Your permissions will be applied based on your role

**Cross-Application Navigation:**
- Use the application switcher (if available in the UI)
- Bookmark different applications - you'll stay logged in
- Open multiple applications in different browser tabs

### Session Management

#### Viewing Active Sessions

1. Go to your **Account Settings** or **Security** section
2. Look for **Active Sessions** or **Device Management**
3. You'll see a list of all your active sessions including:
   - Device name and type
   - Location (approximate)
   - Last activity time
   - Session status

#### Managing Sessions

**Terminate Specific Session:**
1. In the Active Sessions list
2. Click **"Terminate"** next to the session you want to end
3. That device will be logged out immediately

**Global Logout (All Devices):**
1. Use the **"Logout from all devices"** option
2. This will end all your sessions across all applications
3. You'll need to log in again on all devices

### Security Features

#### Device Recognition

The system tracks your devices for security:
- **Trusted Devices**: Devices you've used before
- **New Devices**: First-time devices may require additional verification
- **Suspicious Activity**: Unusual login patterns trigger security alerts

#### Session Timeout

- **Automatic Timeout**: Sessions expire after 8 hours of inactivity
- **Extension**: Activity on any application extends your session across all applications
- **Warning**: You'll receive warnings before session expiration

## Troubleshooting

### Common Issues

#### "Session Expired" Message

**Cause**: Your session has timed out due to inactivity
**Solution**: 
1. Click "Login Again" or refresh the page
2. Enter your credentials to start a new session

#### Cannot Access Application

**Cause**: Insufficient permissions for the specific application
**Solution**:
1. Contact your administrator to request access
2. Verify you have the correct role assigned

#### "Device Not Recognized" Warning

**Cause**: Logging in from a new or different device
**Solution**:
1. Complete the device verification process
2. Check your email for verification codes if required
3. Contact IT support if you cannot verify the device

#### Automatic Logout Issues

**Possible Causes**:
- Browser cookies disabled
- Private/incognito browsing mode
- Browser security settings blocking cross-domain cookies

**Solutions**:
1. Enable cookies in your browser settings
2. Add `*.yourcompany.com` to your trusted sites
3. Disable ad blockers for company domains
4. Use a standard browsing mode (not private/incognito)

### Browser Compatibility

#### Supported Browsers
- **Chrome** 80+ (Recommended)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

#### Browser Settings
1. **Enable Cookies**: Required for SSO functionality
2. **JavaScript**: Must be enabled
3. **Third-party Cookies**: Allow for `*.yourcompany.com`

### Mobile Devices

#### Mobile Browser Support
- iOS Safari 13+
- Android Chrome 80+
- Samsung Internet 12+

#### Mobile App Integration
- Native mobile apps may have separate authentication
- Contact your IT department for mobile app setup

## Security Best Practices

### Account Security

1. **Strong Passwords**
   - Use unique, complex passwords
   - Enable two-factor authentication if available
   - Change passwords regularly

2. **Device Management**
   - Regularly review active sessions
   - Terminate sessions on devices you no longer use
   - Report suspicious activity immediately

3. **Safe Browsing**
   - Always log out on shared computers
   - Use private browsing on public computers
   - Verify the URL before entering credentials

### Privacy Considerations

#### Data Collection
The SSO system collects:
- Device information for security purposes
- Login times and locations
- Application usage patterns

#### Data Usage
- Information is used only for security and system improvement
- Data is not shared with third parties
- You can request data deletion by contacting support

## Advanced Features

### Application Switching

**Quick Switcher** (if available):
1. Look for the application switcher icon in the top navigation
2. Click to see all available applications
3. Select the application you want to access

**Bookmarks**:
- Bookmark frequently used applications
- SSO will automatically authenticate you when visiting bookmarked links

### Integration with External Tools

#### API Access
- Developers can use SSO tokens for API authentication
- Contact your development team for API integration details

#### Third-party Integrations
- Some external tools may support SSO integration
- Check with your administrator for available integrations

## Getting Help

### Self-Service Options

1. **Password Reset**
   - Use the "Forgot Password" link on the login page
   - Follow the email instructions to reset your password

2. **Account Recovery**
   - Contact your administrator if locked out
   - Provide your username and employee ID for verification

### Contact Support

#### IT Help Desk
- **Email**: <EMAIL>
- **Phone**: +1-XXX-XXX-XXXX
- **Hours**: Monday-Friday, 9 AM - 5 PM

#### Emergency Access
- For urgent access issues outside business hours
- Use the emergency contact information provided by your administrator

### Reporting Issues

When reporting SSO issues, please provide:
1. **Error Message**: Exact text of any error messages
2. **Browser Information**: Browser type and version
3. **Device Information**: Operating system and device type
4. **Steps to Reproduce**: What you were doing when the issue occurred
5. **Time of Issue**: When the problem occurred

## Frequently Asked Questions

### General Questions

**Q: Do I need to log in to each application separately?**
A: No, SSO allows you to log in once and access all applications automatically.

**Q: How long do sessions last?**
A: Sessions last for 8 hours of inactivity. Activity on any application extends the session.

**Q: Can I use SSO on mobile devices?**
A: Yes, SSO works on mobile browsers. Native mobile apps may have separate authentication.

### Technical Questions

**Q: Why am I being asked to log in again?**
A: This can happen due to session expiration, security policies, or browser settings.

**Q: Can I use SSO with multiple browser profiles?**
A: Each browser profile maintains separate sessions. You'll need to log in to each profile.

**Q: What happens if I clear my browser cookies?**
A: Clearing cookies will log you out of all applications. You'll need to log in again.

### Security Questions

**Q: Is SSO secure?**
A: Yes, our SSO system uses enterprise-grade security including device tracking and session monitoring.

**Q: What if someone else uses my computer?**
A: Always log out on shared computers. Use the "Logout from all devices" option if needed.

**Q: How do I know if someone else accessed my account?**
A: Check your active sessions regularly for unfamiliar devices or locations.

---

**For technical documentation, see [Implementation Guide](./SSO_IMPLEMENTATION_GUIDE.md) and [API Documentation](./SSO_API_DOCUMENTATION.md).**
