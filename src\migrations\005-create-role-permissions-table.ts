import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateRolePermissionsTable1700000005 implements MigrationInterface {
  name = 'CreateRolePermissionsTable1700000005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'role_permissions',
        columns: [
          {
            name: 'roleId',
            type: 'uuid',
            isPrimary: true,
          },
          {
            name: 'permissionId',
            type: 'uuid',
            isPrimary: true,
          },
        ],
      }),
      true
    );

    // Create foreign keys
    await queryRunner.query(`
      ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_role_permissions_roleId"
      FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_role_permissions_permissionId"
      FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE CASCADE
    `);

    // Assign permissions to system roles
    await this.assignPermissionsToSystemRoles(queryRunner);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('role_permissions');
  }

  private async assignPermissionsToSystemRoles(queryRunner: QueryRunner): Promise<void> {
    // Get all permissions
    const permissions = await queryRunner.query('SELECT id, name FROM permissions');
    const permissionMap = new Map(permissions.map(p => [p.name, p.id]));

    // Get system roles
    const roles = await queryRunner.query('SELECT id, name FROM roles WHERE type = \'system\'');
    const roleMap = new Map(roles.map(r => [r.name, r.id]));

    // Define role permissions
    const rolePermissions = {
      owner: permissions.map(p => p.name), // Owner gets all permissions

      admin: permissions
        .filter(p => !p.name.includes('organization:delete'))
        .map(p => p.name),

      manager: permissions
        .filter(p =>
          !p.name.includes('organization:') &&
          !p.name.includes('settings:manage') &&
          !p.name.includes('users:delete')
        )
        .map(p => p.name),

      member: permissions
        .filter(p =>
          p.name.includes(':read') ||
          p.name.includes(':create') ||
          p.name.includes(':execute') ||
          p.name === 'marketing:campaigns:update' ||
          p.name === 'email:campaigns:update'
        )
        .map(p => p.name),

      viewer: permissions
        .filter(p => p.name.includes(':read'))
        .map(p => p.name),
    };

    // Insert role permissions
    for (const [roleName, permissionNames] of Object.entries(rolePermissions)) {
      const roleId = roleMap.get(roleName);
      if (!roleId) continue;

      for (const permissionName of permissionNames) {
        const permissionId = permissionMap.get(permissionName);
        if (!permissionId) continue;

        await queryRunner.query(`
          INSERT INTO role_permissions ("roleId", "permissionId")
          VALUES ($1, $2)
          ON CONFLICT DO NOTHING
        `, [roleId, permissionId]);
      }
    }
  }
}
