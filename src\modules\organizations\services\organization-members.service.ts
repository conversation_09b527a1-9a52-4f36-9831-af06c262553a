import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationMember, MemberStatus } from '../entities/organization-member.entity';
import { Organization } from '../entities/organization.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class OrganizationMembersService {
  constructor(
    @InjectRepository(OrganizationMember)
    private memberRepository: Repository<OrganizationMember>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    private logger: LoggerService,
  ) {}

  async getOrganizationMembers(organizationId: string): Promise<OrganizationMember[]> {
    return this.memberRepository.find({
      where: { organizationId },
      relations: ['user', 'role', 'inviter'],
      order: { createdAt: 'DESC' },
    });
  }

  async getMemberById(memberId: string): Promise<OrganizationMember> {
    const member = await this.memberRepository.findOne({
      where: { id: memberId },
      relations: ['user', 'role', 'organization'],
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    return member;
  }

  async getUserMembership(userId: string, organizationId: string): Promise<OrganizationMember | null> {
    return this.memberRepository.findOne({
      where: { userId, organizationId },
      relations: ['role', 'organization'],
    });
  }

  async updateMemberRole(organizationId: string, memberId: string, roleId: string): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    if (member.organizationId !== organizationId) {
      throw new BadRequestException('Member does not belong to this organization');
    }

    await this.memberRepository.update(memberId, { roleId });

    this.logger.logWithContext(`Member role updated: ${memberId}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async updateMemberStatus(memberId: string, status: MemberStatus): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    await this.memberRepository.update(memberId, {
      status,
      lastActiveAt: new Date(),
    });

    // Update organization member count
    if (status === MemberStatus.ACTIVE && member.status !== MemberStatus.ACTIVE) {
      await this.organizationRepository.increment(
        { id: member.organizationId },
        'memberCount',
        1
      );
    } else if (status !== MemberStatus.ACTIVE && member.status === MemberStatus.ACTIVE) {
      await this.organizationRepository.decrement(
        { id: member.organizationId },
        'memberCount',
        1
      );
    }

    this.logger.logWithContext(`Member status updated: ${memberId} to ${status}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async removeMember(organizationId: string, memberId: string): Promise<void> {
    const member = await this.getMemberById(memberId);

    if (member.organizationId !== organizationId) {
      throw new BadRequestException('Member does not belong to this organization');
    }

    // Check if member is the owner
    const organization = await this.organizationRepository.findOne({
      where: { id: organizationId },
    });

    if (organization?.ownerId === member.userId) {
      throw new BadRequestException('Cannot remove organization owner');
    }

    // Soft delete by updating status
    await this.updateMemberStatus(memberId, MemberStatus.LEFT);

    this.logger.logWithContext(`Member removed: ${memberId}`, 'OrganizationMembersService');
  }

  async suspendMember(organizationId: string, memberId: string, reason?: string): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    if (member.organizationId !== organizationId) {
      throw new BadRequestException('Member does not belong to this organization');
    }

    const updatedMetadata = {
      ...member.metadata,
      suspensionReason: reason,
      suspendedAt: new Date().toISOString(),
    };

    await this.memberRepository.update(memberId, {
      status: MemberStatus.SUSPENDED,
      metadata: updatedMetadata as any,
    });

    this.logger.logWithContext(`Member suspended: ${memberId}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async reactivateMember(organizationId: string, memberId: string): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    if (member.organizationId !== organizationId) {
      throw new BadRequestException('Member does not belong to this organization');
    }

    return this.updateMemberStatus(memberId, MemberStatus.ACTIVE);
  }

  async updateMemberPermissions(memberId: string, permissions: string[]): Promise<OrganizationMember> {
    await this.memberRepository.update(memberId, { permissions });

    this.logger.logWithContext(`Member permissions updated: ${memberId}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async updateMemberRestrictions(memberId: string, restrictions: any): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    await this.memberRepository.update(memberId, {
      restrictions: {
        ...member.restrictions,
        ...restrictions,
      },
    });

    this.logger.logWithContext(`Member restrictions updated: ${memberId}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async updateMemberMetadata(memberId: string, metadata: any): Promise<OrganizationMember> {
    const member = await this.getMemberById(memberId);

    await this.memberRepository.update(memberId, {
      metadata: {
        ...member.metadata,
        ...metadata,
      },
    });

    this.logger.logWithContext(`Member metadata updated: ${memberId}`, 'OrganizationMembersService');

    return this.getMemberById(memberId);
  }

  async recordMemberActivity(userId: string, organizationId: string): Promise<void> {
    await this.memberRepository.update(
      { userId, organizationId },
      {
        lastActiveAt: new Date(),
        loginCount: () => 'login_count + 1',
      }
    );
  }

  async getOrganizationMemberStats(organizationId: string): Promise<any> {
    const members = await this.getOrganizationMembers(organizationId);

    const stats = {
      total: members.length,
      active: members.filter(m => m.isActive).length,
      pending: members.filter(m => m.isPending).length,
      suspended: members.filter(m => m.isSuspended).length,
      byRole: {},
      recentActivity: members
        .filter(m => m.lastActiveAt)
        .sort((a, b) => b.lastActiveAt.getTime() - a.lastActiveAt.getTime())
        .slice(0, 10),
    };

    // Group by role
    members.forEach(member => {
      const roleName = member.role?.name || 'Unknown';
      stats.byRole[roleName] = (stats.byRole[roleName] || 0) + 1;
    });

    return stats;
  }

  async getMemberActivity(memberId: string, days: number = 30): Promise<any> {
    const member = await this.getMemberById(memberId);

    // In a real implementation, this would query activity logs
    return {
      member,
      activity: {
        loginCount: member.loginCount,
        lastActive: member.lastActiveAt,
        memberSince: member.acceptedAt || member.createdAt,
        daysSinceMember: member.daysSinceMember,
        // Additional activity data would be fetched from activity logs
        recentActions: [],
        timeSpent: 0,
        featuresUsed: [],
      },
    };
  }
}
