# 🧪 Testing Strategies Guide

## 📋 Tổng quan

Comprehensive testing strategy cho Delify Platform, bao gồm **unit testing**, **integration testing**, **E2E testing**, và **performance testing** với focus đặc biệt vào AI multi-provider system.

## 🎯 Testing Pyramid

```
        🔺 E2E Tests (10%)
       🔺🔺 Integration Tests (20%)
      🔺🔺🔺 Unit Tests (70%)
```

### Test Distribution
- **Unit Tests (70%)**: Fast, isolated component testing
- **Integration Tests (20%)**: Module interaction testing
- **E2E Tests (10%)**: Complete user workflow testing

## 🔬 Unit Testing Strategy

### Test Structure
```typescript
// Standard test file structure
describe('ServiceName', () => {
  let service: ServiceName;
  let mockDependency: jest.Mocked<DependencyType>;

  beforeEach(async () => {
    // Setup test module
  });

  afterEach(() => {
    // Cleanup
    jest.clearAllMocks();
  });

  describe('methodName', () => {
    it('should handle success case', async () => {
      // Arrange
      // Act
      // Assert
    });

    it('should handle error case', async () => {
      // Arrange
      // Act
      // Assert
    });
  });
});
```

### Service Testing Example
```typescript
// src/modules/users/users.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { ConflictException, NotFoundException } from '@nestjs/common';

describe('UsersService', () => {
  let service: UsersService;
  let repository: jest.Mocked<Repository<User>>;

  beforeEach(async () => {
    const mockRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get(getRepositoryToken(User));
  });

  describe('create', () => {
    it('should create user successfully', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      const savedUser = {
        id: 'user-id',
        ...createUserDto,
        passwordHash: 'hashed-password',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      repository.findOne.mockResolvedValue(null); // Email not exists
      repository.create.mockReturnValue(savedUser as any);
      repository.save.mockResolvedValue(savedUser as any);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          email: createUserDto.email,
          firstName: createUserDto.firstName,
          lastName: createUserDto.lastName,
        })
      );
      expect(repository.save).toHaveBeenCalledWith(savedUser);
      expect(result).toEqual(savedUser);
    });

    it('should throw ConflictException when email exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      repository.findOne.mockResolvedValue({ id: 'existing-id' } as User);

      // Act & Assert
      await expect(service.create(createUserDto)).rejects.toThrow(
        ConflictException
      );
      expect(repository.create).not.toHaveBeenCalled();
      expect(repository.save).not.toHaveBeenCalled();
    });
  });

  describe('findById', () => {
    it('should return user when found', async () => {
      // Arrange
      const userId = 'user-id';
      const user = { id: userId, email: '<EMAIL>' } as User;
      repository.findOne.mockResolvedValue(user);

      // Act
      const result = await service.findById(userId);

      // Assert
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(result).toEqual(user);
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const userId = 'non-existent-id';
      repository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(userId)).rejects.toThrow(
        NotFoundException
      );
    });
  });
});
```

### AI Provider Testing
```typescript
// src/modules/ai/providers/ollama.provider.spec.ts
describe('OllamaProvider', () => {
  let provider: OllamaProvider;
  let mockAxios: jest.Mocked<any>;

  beforeEach(() => {
    mockAxios = {
      get: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
    };

    jest.doMock('axios', () => ({
      create: () => mockAxios,
    }));

    provider = new OllamaProvider({
      baseURL: 'http://localhost:11434',
      model: 'llama2:7b',
    });
  });

  describe('generateText', () => {
    it('should generate text successfully', async () => {
      // Arrange
      const messages = [{ role: 'user' as const, content: 'Hello' }];
      const mockResponse = {
        data: {
          message: { content: 'Hello! How can I help you?' },
          done: true,
        },
      };
      mockAxios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await provider.generateText(messages);

      // Assert
      expect(mockAxios.post).toHaveBeenCalledWith('/api/chat', {
        model: 'llama2:7b',
        messages,
        stream: false,
        options: expect.any(Object),
      });
      expect(result.content).toBe('Hello! How can I help you?');
      expect(result.model).toBe('llama2:7b');
    });

    it('should handle server unavailable error', async () => {
      // Arrange
      const messages = [{ role: 'user' as const, content: 'Hello' }];
      mockAxios.post.mockRejectedValue({
        code: 'ECONNREFUSED',
        message: 'Connection refused',
      });

      // Act & Assert
      await expect(provider.generateText(messages)).rejects.toThrow(
        'OLLAMA: Server not running'
      );
    });
  });

  describe('listModels', () => {
    it('should list available models', async () => {
      // Arrange
      const mockModels = [
        { name: 'llama2:7b', size: ********** },
        { name: 'codellama:7b', size: ********** },
      ];
      mockAxios.get.mockResolvedValue({ data: { models: mockModels } });

      // Act
      const result = await provider.listModels();

      // Assert
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tags');
      expect(result).toEqual(mockModels);
    });
  });
});
```

## 🔗 Integration Testing

### Controller Integration Tests
```typescript
// src/modules/users/users.controller.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from './users.module';
import { AuthModule } from '../auth/auth.module';
import { testDatabaseConfig } from '../../test/test-database.config';

describe('UsersController (Integration)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot(testDatabaseConfig),
        UsersModule,
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get authentication token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.tokens.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/users (POST)', () => {
    it('should create user with valid data', () => {
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'New',
          lastName: 'User',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.email).toBe('<EMAIL>');
          expect(res.body.data.passwordHash).toBeUndefined();
        });
    });

    it('should return 400 for invalid email', () => {
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: 'invalid-email',
          password: 'SecurePass123!',
          firstName: 'Test',
          lastName: 'User',
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.errors).toBeDefined();
        });
    });

    it('should return 409 for duplicate email', async () => {
      // First create user
      await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'First',
          lastName: 'User',
        });

      // Try to create duplicate
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          firstName: 'Second',
          lastName: 'User',
        })
        .expect(409);
    });
  });

  describe('/users (GET)', () => {
    it('should return paginated users', () => {
      return request(app.getHttpServer())
        .get('/users?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('meta');
          expect(Array.isArray(res.body.data.data)).toBe(true);
        });
    });

    it('should filter users by search term', () => {
      return request(app.getHttpServer())
        .get('/users?search=admin')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data.data.length).toBeGreaterThan(0);
        });
    });
  });

  describe('Authentication', () => {
    it('should require authentication', () => {
      return request(app.getHttpServer())
        .get('/users')
        .expect(401);
    });

    it('should reject invalid token', () => {
      return request(app.getHttpServer())
        .get('/users')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });
});
```

### Database Integration Tests
```typescript
// src/modules/organizations/organizations.integration.spec.ts
describe('Organizations Integration', () => {
  let dataSource: DataSource;
  let organizationsService: OrganizationsService;
  let usersService: UsersService;

  beforeAll(async () => {
    dataSource = await createTestDataSource();
    // Initialize services with real database
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  beforeEach(async () => {
    // Clean database before each test
    await dataSource.synchronize(true);
  });

  describe('Organization Creation Flow', () => {
    it('should create organization with owner membership', async () => {
      // Arrange
      const user = await usersService.create({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Owner',
        lastName: 'User',
      });

      // Act
      const organization = await organizationsService.create(user.id, {
        name: 'Test Organization',
        type: OrganizationType.COMPANY,
      });

      // Assert
      expect(organization.name).toBe('Test Organization');
      
      const membership = await organizationsService.getMembership(
        organization.id,
        user.id
      );
      expect(membership.role).toBe(MemberRole.OWNER);
    });

    it('should handle organization member invitation flow', async () => {
      // Create organization and owner
      const owner = await usersService.create({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Owner',
        lastName: 'User',
      });

      const organization = await organizationsService.create(owner.id, {
        name: 'Test Org',
        type: OrganizationType.COMPANY,
      });

      // Send invitation
      const invitation = await organizationsService.inviteUser(
        organization.id,
        {
          email: '<EMAIL>',
          role: MemberRole.MEMBER,
        },
        owner.id
      );

      expect(invitation.status).toBe(InvitationStatus.PENDING);

      // Create invited user
      const member = await usersService.create({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Member',
        lastName: 'User',
      });

      // Accept invitation
      const membership = await organizationsService.acceptInvitation(
        invitation.token,
        member.id
      );

      expect(membership.role).toBe(MemberRole.MEMBER);
      expect(membership.organizationId).toBe(organization.id);
    });
  });
});
```

## 🌐 End-to-End Testing

### Complete User Workflows
```typescript
// test/auth-flow.e2e-spec.ts
describe('Authentication Flow (E2E)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Complete User Registration and Login Flow', () => {
    const userEmail = `test-${Date.now()}@example.com`;
    let userId: string;
    let accessToken: string;

    it('should register new user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send({
          email: userEmail,
          password: 'SecurePass123!',
          firstName: 'Test',
          lastName: 'User',
          accountType: 'business',
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userEmail);
      expect(response.body.data.tokens.accessToken).toBeDefined();
      
      userId = response.body.data.user.id;
      accessToken = response.body.data.tokens.accessToken;
    });

    it('should login with registered credentials', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: userEmail,
          password: 'SecurePass123!',
        })
        .expect(200);

      expect(response.body.data.user.id).toBe(userId);
      expect(response.body.data.tokens.accessToken).toBeDefined();
    });

    it('should access protected profile endpoint', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.data.id).toBe(userId);
      expect(response.body.data.email).toBe(userEmail);
    });

    it('should create organization', async () => {
      const response = await request(app.getHttpServer())
        .post('/organizations')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'Test Organization',
          type: 'company',
          description: 'E2E Test Organization',
        })
        .expect(201);

      expect(response.body.data.name).toBe('Test Organization');
    });
  });
});
```

### AI Multi-Provider E2E Tests
```typescript
// test/ai-system.e2e-spec.ts
describe('AI Multi-Provider System (E2E)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    // Setup app and authentication
  });

  describe('Smart AI Generation Flow', () => {
    it('should use smart provider selection for text generation', async () => {
      const response = await request(app.getHttpServer())
        .post('/ai/smart/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          messages: [
            {
              role: 'user',
              content: 'Explain artificial intelligence in simple terms',
            },
          ],
          task: 'text',
          options: {
            temperature: 0.7,
            maxTokens: 200,
          },
        })
        .expect(201);

      expect(response.body.data.content).toBeDefined();
      expect(response.body.data.providerUsed).toBeDefined();
      expect(response.body.data.providerUsed.provider).toMatch(
        /^(openai|grok|gemini|ollama)$/
      );
    });

    it('should compare responses from multiple providers', async () => {
      const response = await request(app.getHttpServer())
        .post('/ai/compare/providers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          messages: [
            {
              role: 'user',
              content: 'Write a short poem about technology',
            },
          ],
          providers: [
            { provider: 'openai', model: 'gpt-3.5-turbo' },
            { provider: 'ollama', model: 'llama2:7b' },
          ],
          options: {
            temperature: 0.8,
            maxTokens: 150,
          },
        })
        .expect(201);

      expect(response.body.data.results).toHaveLength(2);
      expect(response.body.data.comparison).toBeDefined();
    });
  });

  describe('OLLAMA Integration Flow', () => {
    it('should check OLLAMA server status', async () => {
      const response = await request(app.getHttpServer())
        .get('/ai/ollama/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('isHealthy');
    });

    it('should list OLLAMA models', async () => {
      const response = await request(app.getHttpServer())
        .get('/ai/ollama/models')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should generate code with OLLAMA', async () => {
      const response = await request(app.getHttpServer())
        .post('/ai/ollama/code/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          model: 'codellama:7b',
          prompt: 'Create a simple hello world function in Python',
          language: 'python',
          options: {
            temperature: 0.2,
            num_predict: 100,
          },
        });

      // Response might be 201 (success) or error if OLLAMA not available
      if (response.status === 201) {
        expect(response.body.data.content).toBeDefined();
        expect(response.body.data.content).toContain('def');
      }
    });
  });
});
```

## ⚡ Performance Testing

### Load Testing with Artillery
```yaml
# artillery-config.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 120
      arrivalRate: 50
      name: "Load test"
    - duration: 60
      arrivalRate: 100
      name: "Stress test"
  variables:
    baseUrl: 'http://localhost:3000/api/v1'

scenarios:
  - name: "Authentication Flow"
    weight: 30
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.data.tokens.accessToken"
              as: "authToken"
      - get:
          url: "/users/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"

  - name: "AI Generation"
    weight: 40
    flow:
      - post:
          url: "/auth/login"
          json:
            email: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.data.tokens.accessToken"
              as: "authToken"
      - post:
          url: "/ai/smart/generate"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            messages:
              - role: "user"
                content: "Hello, how are you?"
            task: "text"
            options:
              temperature: 0.7
              maxTokens: 50
```

### Memory Leak Testing
```typescript
// test/memory-leak.spec.ts
describe('Memory Leak Tests', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Setup app
  });

  it('should not leak memory during AI generation', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // Perform 100 AI generations
    for (let i = 0; i < 100; i++) {
      await request(app.getHttpServer())
        .post('/ai/smart/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          messages: [{ role: 'user', content: `Test ${i}` }],
          task: 'text',
        });
    }

    // Force garbage collection
    if (global.gc) {
      global.gc();
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;

    // Memory increase should be reasonable (less than 50MB)
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
  });
});
```

## 🎯 Test Coverage & Quality

### Coverage Configuration
```javascript
// jest.config.js
module.exports = {
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    // Higher thresholds for critical modules
    'src/modules/auth/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    'src/modules/ai/': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  collectCoverageFrom: [
    'src/**/*.(t|j)s',
    '!src/**/*.spec.ts',
    '!src/**/*.e2e-spec.ts',
    '!src/main.ts',
    '!src/**/*.module.ts',
    '!src/**/*.interface.ts',
  ],
};
```

### Quality Gates
```bash
# Pre-commit hooks
#!/bin/sh
# .husky/pre-commit

# Run tests
npm run test:unit
if [ $? -ne 0 ]; then
  echo "Unit tests failed"
  exit 1
fi

# Check coverage
npm run test:cov
if [ $? -ne 0 ]; then
  echo "Coverage threshold not met"
  exit 1
fi

# Lint code
npm run lint
if [ $? -ne 0 ]; then
  echo "Linting failed"
  exit 1
fi
```

## 🔧 Test Utilities

### Test Data Factories
```typescript
// test/factories/user.factory.ts
export class UserFactory {
  static create(overrides: Partial<CreateUserDto> = {}): CreateUserDto {
    return {
      email: faker.internet.email(),
      password: 'SecurePass123!',
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      accountType: faker.helpers.enumValue(AccountType),
      ...overrides,
    };
  }

  static createMany(count: number, overrides: Partial<CreateUserDto> = {}): CreateUserDto[] {
    return Array.from({ length: count }, () => this.create(overrides));
  }
}
```

### Custom Matchers
```typescript
// test/matchers/api-response.matcher.ts
expect.extend({
  toBeValidApiResponse(received) {
    const pass = received &&
      typeof received.success === 'boolean' &&
      received.data !== undefined;

    return {
      message: () => `expected ${received} to be a valid API response`,
      pass,
    };
  },

  toBeValidAIResponse(received) {
    const pass = received &&
      typeof received.content === 'string' &&
      received.usage &&
      typeof received.usage.totalTokens === 'number' &&
      typeof received.model === 'string';

    return {
      message: () => `expected ${received} to be a valid AI response`,
      pass,
    };
  },
});
```

## 🎯 Best Practices

### 1. Test Organization
- **Descriptive test names** - Clear what is being tested
- **AAA pattern** - Arrange, Act, Assert
- **One assertion per test** - Focus on single behavior
- **Test isolation** - Tests should not depend on each other

### 2. Mock Strategy
- **Mock external dependencies** - APIs, databases, file systems
- **Use real objects for integration tests**
- **Verify mock interactions** - Ensure mocks are called correctly
- **Reset mocks between tests**

### 3. Performance Testing
- **Test realistic scenarios** - Use production-like data
- **Monitor key metrics** - Response time, memory usage
- **Set performance budgets** - Define acceptable limits
- **Regular performance testing** - Include in CI/CD

### 4. Maintenance
- **Keep tests up to date** - Update with code changes
- **Remove obsolete tests** - Clean up unused tests
- **Refactor test code** - Apply same quality standards
- **Document complex test scenarios**

**Following this testing strategy ensures high-quality, reliable code trong Delify Platform với comprehensive coverage across all layers.** 🧪✨
