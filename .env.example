# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=delify_db
DATABASE_USER=delify_user
DATABASE_PASSWORD=delify_password

# Redis Configuration (Required for SSO session storage)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# JWT Configuration (Updated for SSO compatibility)
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters_change_in_production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# ============================================================================
# SSO (Single Sign-On) Configuration
# ============================================================================

# Core SSO Configuration
SSO_ENABLED=true
SSO_BASE_DOMAIN=delify.vn
SSO_COOKIE_DOMAIN=.delify.vn
SSO_ISSUER=auth.delify.vn

# SSO Application Configuration
# Comma-separated list of allowed applications for cross-subdomain SSO
SSO_ALLOWED_APPLICATIONS=app.delify.vn,mail.delify.vn,api.delify.vn

# SSO Session Configuration
SSO_SESSION_TIMEOUT=480
SSO_MAX_CONCURRENT_SESSIONS=5
SSO_REQUIRE_DEVICE_VERIFICATION=false

# SSO Security Configuration
SSO_ENABLE_AUDIT_LOGGING=true
SSO_TOKEN_REVOCATION_ENABLED=true
SSO_CROSS_DOMAIN_COOKIES=true
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax

# SSO Performance Tuning (Optional)
SSO_CACHE_TTL=900
SSO_SESSION_CLEANUP_INTERVAL=3600
SSO_TOKEN_CLEANUP_INTERVAL=1800

# SSO Logging Configuration (Optional)
SSO_LOG_LEVEL=info
SSO_AUDIT_LOG_RETENTION_DAYS=90
SSO_ENABLE_PERFORMANCE_LOGGING=false

# SSO Rate Limiting (Optional)
SSO_RATE_LIMIT_AUTH=10
SSO_RATE_LIMIT_SESSION=60
SSO_RATE_LIMIT_ADMIN=100

# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# CORS Configuration (Updated for SSO support)
# Include all SSO-enabled applications for cross-domain requests
CORS_ORIGIN=http://localhost:3000,app.delify.vn,mail.delify.vn,api.delify.vn
CORS_CREDENTIALS=true

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key
SESSION_MAX_AGE=86400000

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Facebook API Configuration
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_GRAPH_API_VERSION=v18.0

# TikTok API Configuration
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# AI Provider Configuration
# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Grok (X.AI)
GROK_API_KEY=your-grok-api-key
GROK_BASE_URL=https://api.x.ai/v1

# Google Gemini
GEMINI_API_KEY=your-gemini-api-key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# Anthropic Claude
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_BASE_URL=https://api.anthropic.com

# OLLAMA (Local AI)
OLLAMA_BASE_URL=http://localhost:11434

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DEST=./uploads

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Logging
LOG_LEVEL=debug
LOG_FILE=true

# Digital Signature
DIGITAL_SIGNATURE_PROVIDER=docusign
DOCUSIGN_INTEGRATION_KEY=your-docusign-integration-key
DOCUSIGN_USER_ID=your-docusign-user-id
DOCUSIGN_ACCOUNT_ID=your-docusign-account-id
DOCUSIGN_PRIVATE_KEY_PATH=./keys/docusign-private.key

# Webhook URLs
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your-facebook-webhook-verify-token
TIKTOK_WEBHOOK_SECRET=your-tiktok-webhook-secret

# ============================================================================
# Environment-Specific SSO Configuration Notes
# ============================================================================

# Development Environment:
# - Set SSO_SECURE_ONLY=false (allows HTTP cookies)
# - Set SSO_REQUIRE_DEVICE_VERIFICATION=false
# - Use localhost or IP addresses in CORS_ORIGIN
# - Set SSO_LOG_LEVEL=debug for detailed logging

# Production Environment:
# - Set SSO_SECURE_ONLY=true (HTTPS-only cookies)
# - Set SSO_REQUIRE_DEVICE_VERIFICATION=true
# - Use proper domain names (yourcompany.com)
# - Set SSO_LOG_LEVEL=warn or error
# - Use strong, unique secrets (64+ characters)
# - Enable SSL/TLS configuration:
#   SSL_ENABLED=true
#   FORCE_HTTPS=true
#   HSTS_ENABLED=true

# SSO Domain Configuration Examples:
# For delify.vn:
#   SSO_BASE_DOMAIN=delify.vn
#   SSO_COOKIE_DOMAIN=.delify.vn
#   SSO_ALLOWED_APPLICATIONS=app.delify.vn,mail.delify.vn,core.delify.vn,api.delify.vn

# For custom domains:
#   SSO_BASE_DOMAIN=mycompany.io
#   SSO_COOKIE_DOMAIN=.mycompany.io
#   SSO_ALLOWED_APPLICATIONS=dashboard.mycompany.io,mail.mycompany.io,api.mycompany.io
