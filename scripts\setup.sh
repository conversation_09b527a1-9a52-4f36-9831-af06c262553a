#!/bin/bash

# Delify Platform Setup Script

echo "🚀 Setting up Delify Platform..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your configuration."
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads/images
mkdir -p uploads/documents
mkdir -p uploads/temp
mkdir -p logs
mkdir -p keys

echo "✅ Directories created"

# Start Docker containers
echo "🐳 Starting Docker containers..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is ready
until docker-compose exec postgres pg_isready -U delify_user -d delify_db; do
    echo "⏳ PostgreSQL is not ready yet, waiting..."
    sleep 5
done

echo "✅ PostgreSQL is ready"

# Run database migrations
echo "🗄️ Running database migrations..."
npm run migration:run

if [ $? -ne 0 ]; then
    echo "❌ Failed to run migrations"
    exit 1
fi

echo "✅ Database migrations completed"

# Start the application in development mode
echo "🚀 Starting application in development mode..."
docker-compose up -d

# Wait for application to start
echo "⏳ Waiting for application to start..."
sleep 15

# Check if application is running
if curl -f http://localhost:3000/api/v1/health > /dev/null 2>&1; then
    echo "✅ Application is running successfully!"
    echo ""
    echo "🎉 Setup completed!"
    echo ""
    echo "📍 Application URLs:"
    echo "   API: http://localhost:3000/api/v1"
    echo "   Swagger Docs: http://localhost:3000/api/v1/docs"
    echo ""
    echo "🔧 Next steps:"
    echo "   1. Edit .env file with your API keys and configuration"
    echo "   2. Restart the application: docker-compose restart app"
    echo "   3. Visit the Swagger docs to explore the API"
    echo ""
    echo "📚 Useful commands:"
    echo "   make dev     - Start development environment"
    echo "   make logs    - View application logs"
    echo "   make stop    - Stop all containers"
    echo "   make clean   - Clean up containers and volumes"
else
    echo "❌ Application failed to start. Check logs with: docker-compose logs app"
    exit 1
fi
