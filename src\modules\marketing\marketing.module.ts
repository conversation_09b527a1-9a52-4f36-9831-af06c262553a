import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { MarketingController } from './marketing.controller';
import { MarketingService } from './marketing.service';
import { FacebookService } from './services/facebook.service';
import { TikTokService } from './services/tiktok.service';
import { SocialPost } from './entities/social-post.entity';
import { SocialAccount } from './entities/social-account.entity';
import { PostSchedule } from './entities/post-schedule.entity';
import { CommentFilter } from './entities/comment-filter.entity';
import { MarketingProcessor } from './processors/marketing.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SocialPost,
      SocialAccount,
      PostSchedule,
      CommentFilter,
    ]),
    BullModule.registerQueue({
      name: 'marketing',
    }),
  ],
  controllers: [MarketingController],
  providers: [
    MarketingService,
    FacebookService,
    TikTokService,
    MarketingProcessor,
  ],
  exports: [MarketingService, FacebookService, TikTokService],
})
export class MarketingModule {}
