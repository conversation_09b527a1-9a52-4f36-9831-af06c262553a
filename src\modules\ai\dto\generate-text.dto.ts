import { <PERSON>Enum, IsArray, IsOptional, IsNumber, IsString, ValidateNested, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AIProvider, AIModelName } from '../entities/ai-model.entity';

export class AIMessageDto {
  @ApiProperty({ 
    enum: ['system', 'user', 'assistant'],
    example: 'user'
  })
  @IsEnum(['system', 'user', 'assistant'])
  role: 'system' | 'user' | 'assistant';

  @ApiProperty({ example: 'Hello, how can you help me today?' })
  @IsString()
  content: string;
}

export class GenerationOptionsDto {
  @ApiPropertyOptional({ 
    example: 0.7,
    description: 'Controls randomness (0.0 to 2.0)',
    minimum: 0,
    maximum: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({ 
    example: 1000,
    description: 'Maximum tokens to generate',
    minimum: 1,
    maximum: 8192
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(8192)
  maxTokens?: number;

  @ApiPropertyOptional({ 
    example: 1,
    description: 'Top-p sampling (0.0 to 1.0)',
    minimum: 0,
    maximum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  topP?: number;

  @ApiPropertyOptional({ 
    example: 0,
    description: 'Frequency penalty (-2.0 to 2.0)',
    minimum: -2,
    maximum: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  frequencyPenalty?: number;

  @ApiPropertyOptional({ 
    example: 0,
    description: 'Presence penalty (-2.0 to 2.0)',
    minimum: -2,
    maximum: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  presencePenalty?: number;
}

export class GenerateTextDto {
  @ApiProperty({ 
    enum: AIProvider,
    example: AIProvider.OPENAI,
    description: 'AI provider to use'
  })
  @IsEnum(AIProvider)
  provider: AIProvider;

  @ApiProperty({ 
    enum: AIModelName,
    example: AIModelName.GPT_4_TURBO,
    description: 'AI model to use'
  })
  @IsEnum(AIModelName)
  model: AIModelName;

  @ApiProperty({ 
    type: [AIMessageDto],
    description: 'Conversation messages'
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIMessageDto)
  messages: AIMessageDto[];

  @ApiPropertyOptional({ 
    type: GenerationOptionsDto,
    description: 'Generation options'
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerationOptionsDto)
  options?: GenerationOptionsDto;
}

export class GenerateStreamDto {
  @ApiProperty({ 
    enum: AIProvider,
    example: AIProvider.GROK
  })
  @IsEnum(AIProvider)
  provider: AIProvider;

  @ApiProperty({ 
    enum: AIModelName,
    example: AIModelName.GROK_1_5
  })
  @IsEnum(AIModelName)
  model: AIModelName;

  @ApiProperty({ type: [AIMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIMessageDto)
  messages: AIMessageDto[];

  @ApiPropertyOptional({ type: GenerationOptionsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerationOptionsDto)
  options?: Omit<GenerationOptionsDto, 'frequencyPenalty' | 'presencePenalty'>;
}

export class AnalyzeImageDto {
  @ApiProperty({ 
    enum: AIProvider,
    example: AIProvider.GEMINI
  })
  @IsEnum(AIProvider)
  provider: AIProvider;

  @ApiProperty({ 
    enum: AIModelName,
    example: AIModelName.GEMINI_PRO_VISION
  })
  @IsEnum(AIModelName)
  model: AIModelName;

  @ApiProperty({ 
    example: 'https://example.com/image.jpg',
    description: 'URL of the image to analyze'
  })
  @IsString()
  imageUrl: string;

  @ApiProperty({ 
    example: 'Describe what you see in this image',
    description: 'Prompt for image analysis'
  })
  @IsString()
  prompt: string;

  @ApiPropertyOptional({ type: GenerationOptionsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerationOptionsDto)
  options?: Pick<GenerationOptionsDto, 'temperature' | 'maxTokens'>;
}

export class SmartGenerateDto {
  @ApiProperty({ type: [AIMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIMessageDto)
  messages: AIMessageDto[];

  @ApiPropertyOptional({ 
    enum: ['text', 'code', 'creative'],
    example: 'text',
    description: 'Type of task for optimal provider selection'
  })
  @IsOptional()
  @IsEnum(['text', 'code', 'creative'])
  task?: 'text' | 'code' | 'creative';

  @ApiPropertyOptional({ type: GenerationOptionsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerationOptionsDto)
  options?: Pick<GenerationOptionsDto, 'temperature' | 'maxTokens'>;
}

export class ProviderConfigDto {
  @ApiProperty({ enum: AIProvider })
  @IsEnum(AIProvider)
  provider: AIProvider;

  @ApiProperty({ enum: AIModelName })
  @IsEnum(AIModelName)
  model: AIModelName;
}

export class CompareProvidersDto {
  @ApiProperty({ type: [AIMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AIMessageDto)
  messages: AIMessageDto[];

  @ApiProperty({ 
    type: [ProviderConfigDto],
    description: 'List of providers to compare'
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProviderConfigDto)
  providers: ProviderConfigDto[];

  @ApiPropertyOptional({ type: GenerationOptionsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerationOptionsDto)
  options?: Pick<GenerationOptionsDto, 'temperature' | 'maxTokens'>;
}
