import { Module, Global } from '@nestjs/common';
import { LoggerService } from './services/logger.service';
import { UtilsService } from './services/utils.service';
import { FileUploadService } from './services/file-upload.service';
import { HealthController } from './controllers/health.controller';

@Global()
@Module({
  controllers: [HealthController],
  providers: [
    LoggerService,
    UtilsService,
    FileUploadService,
  ],
  exports: [
    LoggerService,
    UtilsService,
    FileUploadService,
  ],
})
export class CommonModule {}
