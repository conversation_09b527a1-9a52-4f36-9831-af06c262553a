# 🏗️ Delify Platform - System Architecture

## 📋 Tổng quan

Delify Platform được thiết kế theo **modular monolithic architecture** với khả năng scale thành microservices. Hệ thống tập trung vào **enterprise-grade features** với AI integration và comprehensive team management.

## 🎯 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  React/Vue/Angular + TypeScript + State Management         │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/REST API
┌─────────────────────▼───────────────────────────────────────┐
│                  API Gateway Layer                         │
│     NestJS Controllers + Guards + Interceptors             │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Business Logic Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Auth     │ │     AI      │ │    Org      │          │
│  │   Module    │ │   Module    │ │   Module    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Users     │ │   Common    │ │   Files     │          │
│  │   Module    │ │   Module    │ │   Module    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Data Access Layer                         │
│              TypeORM + Repository Pattern                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Infrastructure Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ PostgreSQL  │ │    Redis    │ │   File      │          │
│  │  Database   │ │    Cache    │ │  Storage    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │    Grok     │ │   Gemini    │          │
│  │     API     │ │     API     │ │     API     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐                                          │
│  │   OLLAMA    │                                          │
│  │ Local AI    │                                          │
│  └─────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## 🏛️ Core Modules

### 1. Authentication Module (`src/modules/auth/`)
```typescript
auth/
├── controllers/
│   └── auth.controller.ts          # Login, register, refresh endpoints
├── services/
│   ├── auth.service.ts             # Authentication logic
│   ├── jwt.service.ts              # JWT token management
│   └── session.service.ts          # Session management
├── guards/
│   ├── jwt-auth.guard.ts           # JWT authentication guard
│   ├── roles.guard.ts              # Role-based authorization
│   └── organization.guard.ts       # Organization-level access
├── strategies/
│   ├── jwt.strategy.ts             # Passport JWT strategy
│   └── local.strategy.ts           # Local authentication
├── decorators/
│   ├── current-user.decorator.ts   # Get current user
│   ├── roles.decorator.ts          # Role requirements
│   └── public.decorator.ts         # Public endpoints
└── entities/
    ├── session.entity.ts           # User sessions
    └── refresh-token.entity.ts     # Refresh tokens
```

**Key Features:**
- JWT-based authentication với refresh tokens
- Session management với device tracking
- Role-based access control (RBAC)
- Organization-level permissions
- Password hashing với bcrypt
- Rate limiting cho security

### 2. Users Module (`src/modules/users/`)
```typescript
users/
├── controllers/
│   └── users.controller.ts         # User CRUD operations
├── services/
│   ├── users.service.ts            # User business logic
│   └── profile.service.ts          # Profile management
├── entities/
│   ├── user.entity.ts              # User entity
│   └── user-profile.entity.ts      # Extended profile info
├── dto/
│   ├── create-user.dto.ts          # User creation validation
│   ├── update-user.dto.ts          # User update validation
│   └── change-password.dto.ts      # Password change validation
└── enums/
    ├── account-type.enum.ts        # Business/Personal accounts
    └── user-status.enum.ts         # Active/Inactive/Suspended
```

**Key Features:**
- User profile management
- Account types (Business/Personal)
- Email verification
- Password management
- User preferences
- Activity tracking

### 3. Organizations Module (`src/modules/organizations/`)
```typescript
organizations/
├── controllers/
│   ├── organizations.controller.ts  # Organization CRUD
│   ├── teams.controller.ts          # Team management
│   ├── invitations.controller.ts    # Invitation system
│   └── roles.controller.ts          # Role management
├── services/
│   ├── organizations.service.ts     # Organization logic
│   ├── teams.service.ts             # Team operations
│   ├── invitations.service.ts       # Invitation handling
│   ├── permissions.service.ts       # Permission management
│   └── roles.service.ts             # Role operations
├── entities/
│   ├── organization.entity.ts       # Organization entity
│   ├── team.entity.ts               # Team entity
│   ├── organization-member.entity.ts # Membership
│   ├── invitation.entity.ts         # Invitations
│   ├── role.entity.ts               # Roles
│   └── permission.entity.ts         # Permissions
└── enums/
    ├── organization-type.enum.ts    # Company/Agency/Freelancer
    ├── member-role.enum.ts          # Owner/Admin/Manager/Member/Viewer
    └── invitation-status.enum.ts    # Pending/Accepted/Declined
```

**Key Features:**
- Multi-tenant organization support
- Team-based collaboration
- Invitation system với email notifications
- 5-tier role system (Owner → Viewer)
- Custom permissions
- Organization settings management

### 4. AI Module (`src/modules/ai/`)
```typescript
ai/
├── controllers/
│   ├── ai.controller.ts             # Main AI endpoints
│   └── ollama.controller.ts         # OLLAMA-specific endpoints
├── services/
│   ├── ai.service.ts                # AI orchestration
│   ├── openai.service.ts            # OpenAI integration
│   ├── content-optimization.service.ts
│   ├── cv-scoring.service.ts
│   └── chatbot.service.ts
├── providers/
│   ├── base-ai.provider.ts          # Abstract provider
│   ├── openai.provider.ts           # OpenAI implementation
│   ├── grok.provider.ts             # Grok implementation
│   ├── gemini.provider.ts           # Gemini implementation
│   ├── ollama.provider.ts           # OLLAMA implementation
│   └── ai-provider.factory.ts       # Provider factory
├── entities/
│   ├── ai-model.entity.ts           # AI model configurations
│   ├── chat-session.entity.ts       # Chat sessions
│   └── cv-analysis.entity.ts        # CV analysis results
└── dto/
    ├── generate-text.dto.ts         # Text generation requests
    └── ollama.dto.ts                # OLLAMA-specific DTOs
```

**Key Features:**
- 4 AI providers (OpenAI, Grok, Gemini, OLLAMA)
- Smart provider selection
- Local AI với OLLAMA
- Streaming responses
- Model management
- Usage tracking

### 5. Common Module (`src/common/`)
```typescript
common/
├── services/
│   ├── logger.service.ts            # Centralized logging
│   ├── email.service.ts             # Email notifications
│   └── file.service.ts              # File operations
├── guards/
│   ├── throttler.guard.ts           # Rate limiting
│   └── validation.guard.ts          # Input validation
├── interceptors/
│   ├── logging.interceptor.ts       # Request/response logging
│   ├── transform.interceptor.ts     # Response transformation
│   └── timeout.interceptor.ts       # Request timeout
├── filters/
│   ├── http-exception.filter.ts     # Error handling
│   └── validation.filter.ts         # Validation errors
├── decorators/
│   └── api-paginated-response.decorator.ts
└── utils/
    ├── pagination.util.ts           # Pagination helpers
    ├── validation.util.ts           # Validation utilities
    └── crypto.util.ts               # Cryptographic functions
```

## 🔄 Request Flow

### 1. Authentication Flow
```
Client Request
     ↓
API Gateway (NestJS)
     ↓
JWT Auth Guard
     ↓
Role Guard (if needed)
     ↓
Organization Guard (if needed)
     ↓
Controller Method
     ↓
Service Layer
     ↓
Repository/Database
     ↓
Response Transformation
     ↓
Client Response
```

### 2. AI Processing Flow
```
AI Request
     ↓
AI Controller
     ↓
AI Service (Smart Provider Selection)
     ↓
Provider Factory
     ↓
Specific Provider (OpenAI/Grok/Gemini/OLLAMA)
     ↓
External API / Local Processing
     ↓
Response Processing
     ↓
Usage Tracking
     ↓
Client Response
```

## 🗄️ Database Architecture

### Entity Relationships
```
User ──────────────── UserProfile
  │
  ├── OrganizationMember ──── Organization
  │                              │
  │                              ├── Team
  │                              └── Invitation
  │
  ├── Session
  ├── RefreshToken
  └── ChatSession ──── AIModel
```

### Key Design Patterns
1. **Repository Pattern** - Data access abstraction
2. **Factory Pattern** - AI provider creation
3. **Strategy Pattern** - Different AI providers
4. **Decorator Pattern** - Authentication và authorization
5. **Observer Pattern** - Event-driven notifications

## 🔒 Security Architecture

### Authentication Layers
1. **JWT Tokens** - Stateless authentication
2. **Refresh Tokens** - Secure token renewal
3. **Session Management** - Device tracking
4. **Rate Limiting** - DDoS protection
5. **Input Validation** - Data sanitization

### Authorization Levels
1. **Public Endpoints** - No authentication required
2. **Authenticated Endpoints** - Valid JWT required
3. **Role-Based Endpoints** - Specific roles required
4. **Organization Endpoints** - Organization membership required
5. **Owner-Only Endpoints** - Organization owner only

## 🚀 Performance Considerations

### Caching Strategy
```typescript
// Redis caching for frequently accessed data
@Cacheable('user-profile', 300) // 5 minutes
async getUserProfile(userId: string) {
  return this.userRepository.findOne(userId);
}

// AI provider response caching
@Cacheable('ai-response', 3600) // 1 hour
async generateText(prompt: string, provider: string) {
  return this.aiService.generate(prompt, provider);
}
```

### Database Optimization
- **Indexing** trên frequently queried columns
- **Connection Pooling** cho database connections
- **Query Optimization** với TypeORM query builder
- **Pagination** cho large datasets
- **Lazy Loading** cho relationships

### AI Performance
- **Provider Selection** based on task type và performance
- **Local Processing** với OLLAMA cho privacy
- **Response Caching** cho repeated requests
- **Streaming** cho real-time responses
- **Load Balancing** across multiple providers

## 🔧 Configuration Management

### Environment-Based Config
```typescript
// config/configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
  },
  ai: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    },
    ollama: {
      baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    },
  },
});
```

## 📊 Monitoring & Logging

### Logging Strategy
```typescript
// Structured logging với context
this.logger.logWithContext(
  'User authenticated successfully',
  'AuthService',
  { userId, organizationId, timestamp }
);

// Error logging với stack traces
this.logger.logError(
  'AI provider request failed',
  error,
  { provider, model, requestId }
);
```

### Metrics Collection
- **Request/Response times**
- **Error rates by endpoint**
- **AI provider performance**
- **Database query performance**
- **Memory và CPU usage**

## 🔮 Scalability Considerations

### Horizontal Scaling
- **Stateless design** cho easy scaling
- **Database connection pooling**
- **Redis session storage**
- **Load balancer ready**
- **Container orchestration** với Kubernetes

### Microservice Migration Path
```
Current Monolith
     ↓
Extract AI Service
     ↓
Extract Auth Service
     ↓
Extract Organization Service
     ↓
Full Microservices Architecture
```

## 🎯 Next Steps

### Immediate Improvements
1. **API Rate Limiting** per user/organization
2. **Advanced Caching** với Redis
3. **Real-time Features** với WebSockets
4. **File Upload** handling
5. **Advanced Monitoring** với metrics

### Future Architecture
1. **Event-Driven Architecture** với message queues
2. **CQRS Pattern** cho complex queries
3. **Microservices** extraction
4. **GraphQL** API layer
5. **Advanced AI** features với RAG

**This architecture provides a solid foundation for enterprise-grade applications với room for growth và optimization.** 🏗️✨
