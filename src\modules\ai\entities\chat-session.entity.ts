import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum SessionStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ABANDONED = 'abandoned',
}

export enum SessionType {
  CUSTOMER_SUPPORT = 'customer_support',
  SALES_INQUIRY = 'sales_inquiry',
  GENERAL_CHAT = 'general_chat',
  FACEBOOK_PAGE = 'facebook_page',
  WEBSITE_CHAT = 'website_chat',
}

@Entity('chat_sessions')
@Index(['userId', 'status'])
@Index(['sessionType', 'status'])
export class ChatSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'enum',
    enum: SessionType,
    default: SessionType.GENERAL_CHAT,
  })
  sessionType: SessionType;

  @Column({
    type: 'enum',
    enum: SessionStatus,
    default: SessionStatus.ACTIVE,
  })
  status: SessionStatus;

  @Column({ nullable: true })
  customerName: string;

  @Column({ nullable: true })
  customerEmail: string;

  @Column({ nullable: true })
  customerPhone: string;

  @Column({ nullable: true })
  platformId: string; // Facebook page ID, website ID, etc.

  @Column({ nullable: true })
  platformUserId: string; // Customer's ID on the platform

  @Column({ type: 'jsonb' })
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    metadata?: any;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  context: {
    companyInfo?: any;
    productInfo?: any;
    previousInteractions?: any;
    customerData?: any;
    intent?: string;
    sentiment?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    messageCount?: number;
    averageResponseTime?: number;
    customerSatisfaction?: number;
    resolvedIssues?: string[];
    leadGenerated?: boolean;
    conversionValue?: number;
  };

  @Column({ nullable: true })
  handoffToHuman: boolean;

  @Column({ nullable: true })
  handoffReason: string;

  @Column({ nullable: true })
  assignedAgent: string;

  @Column({ nullable: true })
  startedAt: Date;

  @Column({ nullable: true })
  endedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === SessionStatus.ACTIVE;
  }

  get duration(): number | null {
    if (!this.startedAt) return null;
    const endTime = this.endedAt || new Date();
    return Math.floor((endTime.getTime() - this.startedAt.getTime()) / 1000);
  }

  get messageCount(): number {
    return this.messages?.length || 0;
  }

  get lastMessage(): any {
    return this.messages?.[this.messages.length - 1] || null;
  }
}
