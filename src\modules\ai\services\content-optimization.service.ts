import { Injectable } from '@nestjs/common';
import { OpenAiService } from './openai.service';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class ContentOptimizationService {
  constructor(
    private openAiService: OpenAiService,
    private logger: LoggerService,
  ) {}

  async optimizeContent(content: string, platform: string, target: string): Promise<{
    optimizedContent: string;
    suggestions: string[];
    hashtags: string[];
    bestPostTime: string;
    engagementPrediction: number;
    improvements: {
      readability: string;
      seo: string;
      engagement: string;
    };
  }> {
    const systemPrompt = `You are a social media content optimization expert. Your job is to improve content for maximum engagement and reach.

    Consider:
    - Platform-specific best practices (${platform})
    - Target audience (${target})
    - Engagement optimization
    - Readability and clarity
    - Call-to-action effectiveness
    - Hashtag strategy
    - Optimal posting times

    Provide optimized content and detailed recommendations in JSON format.`;

    const prompt = `Platform: ${platform}
Target Audience: ${target}
Original Content: "${content}"

Please optimize this content for maximum engagement and provide comprehensive recommendations.`;

    try {
      const response = await this.openAiService.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.7,
        maxTokens: 1000,
      });

      const result = JSON.parse(response);
      
      this.logger.logWithContext(`Content optimized for platform: ${platform}`, 'ContentOptimizationService');
      
      return result;
    } catch (error) {
      this.logger.logError(error, 'ContentOptimizationService - optimizeContent');
      return {
        optimizedContent: content,
        suggestions: ['Unable to optimize content at this time'],
        hashtags: [],
        bestPostTime: '12:00 PM',
        engagementPrediction: 0,
        improvements: {
          readability: 'No analysis available',
          seo: 'No analysis available',
          engagement: 'No analysis available',
        },
      };
    }
  }

  async generateContent(prompt: string, type: string, platform: string): Promise<{
    content: string;
    variations: string[];
    hashtags: string[];
    callToAction: string;
    targetAudience: string;
    contentPillars: string[];
  }> {
    const systemPrompt = `You are a creative content generator specializing in ${platform} content. 

    Generate engaging ${type} content based on the user's prompt. Consider:
    - Platform-specific formats and limitations
    - Audience engagement strategies
    - Brand voice and tone
    - Visual content suggestions
    - Hashtag optimization
    - Call-to-action effectiveness

    Provide multiple variations and comprehensive content strategy in JSON format.`;

    const contentPrompt = `Content Type: ${type}
Platform: ${platform}
Brief: ${prompt}

Generate compelling content with multiple variations and strategic recommendations.`;

    try {
      const response = await this.openAiService.generateCompletion(contentPrompt, {
        systemPrompt,
        temperature: 0.8,
        maxTokens: 1200,
      });

      const result = JSON.parse(response);
      
      this.logger.logWithContext(`Content generated for type: ${type}, platform: ${platform}`, 'ContentOptimizationService');
      
      return result;
    } catch (error) {
      this.logger.logError(error, 'ContentOptimizationService - generateContent');
      return {
        content: 'Unable to generate content at this time.',
        variations: [],
        hashtags: [],
        callToAction: '',
        targetAudience: '',
        contentPillars: [],
      };
    }
  }

  async generateHashtags(content: string, platform: string, niche: string): Promise<{
    trending: string[];
    niche: string[];
    branded: string[];
    community: string[];
    recommendations: {
      primary: string[];
      secondary: string[];
      avoid: string[];
    };
  }> {
    const systemPrompt = `You are a hashtag strategy expert. Generate relevant, trending, and effective hashtags for social media content.

    Consider:
    - Platform-specific hashtag best practices
    - Trending hashtags in the niche
    - Community hashtags
    - Branded hashtags
    - Hashtag volume and competition
    - Content relevance

    Provide categorized hashtags and strategic recommendations in JSON format.`;

    const prompt = `Platform: ${platform}
Niche: ${niche}
Content: "${content}"

Generate a comprehensive hashtag strategy with categorized recommendations.`;

    try {
      const response = await this.openAiService.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.6,
        maxTokens: 800,
      });

      return JSON.parse(response);
    } catch (error) {
      this.logger.logError(error, 'ContentOptimizationService - generateHashtags');
      return {
        trending: [],
        niche: [],
        branded: [],
        community: [],
        recommendations: {
          primary: [],
          secondary: [],
          avoid: [],
        },
      };
    }
  }

  async analyzeContentPerformance(content: string, metrics: any): Promise<{
    score: number;
    insights: string[];
    improvements: string[];
    nextSteps: string[];
    benchmarkComparison: {
      industry: string;
      performance: 'above' | 'average' | 'below';
    };
  }> {
    const systemPrompt = `You are a content performance analyst. Analyze content performance based on engagement metrics and provide actionable insights.

    Consider:
    - Engagement rates and patterns
    - Content quality indicators
    - Audience response analysis
    - Performance benchmarks
    - Improvement opportunities

    Provide detailed analysis and recommendations in JSON format.`;

    const prompt = `Content: "${content}"
Metrics: ${JSON.stringify(metrics)}

Analyze the performance and provide comprehensive insights and recommendations.`;

    try {
      const response = await this.openAiService.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.5,
        maxTokens: 800,
      });

      return JSON.parse(response);
    } catch (error) {
      this.logger.logError(error, 'ContentOptimizationService - analyzeContentPerformance');
      return {
        score: 0,
        insights: ['Unable to analyze performance at this time'],
        improvements: [],
        nextSteps: [],
        benchmarkComparison: {
          industry: 'unknown',
          performance: 'average',
        },
      };
    }
  }
}
