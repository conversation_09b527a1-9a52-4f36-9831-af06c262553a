# 📚 API Reference Guide

## 📋 Tổng quan

Complete API reference cho Delify Platform với **detailed endpoints**, **request/response examples**, và **authentication requirements**.

## 🔗 Base Information

### API Base URL
```
Development: http://localhost:3000/api/v1
Staging:     https://staging-api.delify.com/api/v1
Production:  https://api.delify.com/api/v1
```

### API Documentation
```
Swagger UI: {BASE_URL}/docs
OpenAPI JSON: {BASE_URL}/docs-json
```

### Authentication
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

## 🔐 Authentication Endpoints

### POST /auth/register
Register new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "accountType": "business"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "accountType": "business",
      "isActive": true,
      "emailVerified": false,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900
    }
  }
}
```

### POST /auth/login
Authenticate user and get tokens.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "accountType": "business",
      "lastLoginAt": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900
    },
    "session": {
      "id": "session-uuid",
      "deviceInfo": {
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "***********"
      },
      "lastActivity": "2024-01-01T00:00:00Z"
    }
  }
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 900
  }
}
```

### POST /auth/logout
Logout user and invalidate tokens.

**Headers:** `Authorization: Bearer {token}`

**Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## 👥 Users Endpoints

### GET /users/profile
Get current user profile.

**Headers:** `Authorization: Bearer {token}`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "accountType": "business",
    "isActive": true,
    "emailVerified": true,
    "lastLoginAt": "2024-01-01T00:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "organizationMemberships": [
      {
        "id": "membership-uuid",
        "role": "owner",
        "organization": {
          "id": "org-uuid",
          "name": "Acme Corp",
          "type": "company"
        }
      }
    ]
  }
}
```

### PUT /users/profile
Update current user profile.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "firstName": "John",
  "lastName": "Smith"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Smith",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### GET /users
Get all users (Admin only).

**Headers:** `Authorization: Bearer {token}`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `search` (string): Search term for name or email
- `accountType` (string): Filter by account type
- `sortBy` (string): Sort field (default: createdAt)
- `sortOrder` (string): Sort order ASC/DESC (default: DESC)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "accountType": "business",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "total": 150,
      "page": 1,
      "limit": 20,
      "totalPages": 8,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

## 🏢 Organizations Endpoints

### POST /organizations
Create new organization.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "name": "Acme Corporation",
  "description": "Leading technology company",
  "type": "company",
  "settings": {
    "timezone": "UTC",
    "language": "en",
    "currency": "USD"
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "name": "Acme Corporation",
    "description": "Leading technology company",
    "type": "company",
    "settings": {
      "timezone": "UTC",
      "language": "en",
      "currency": "USD"
    },
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### GET /organizations
Get user's organizations.

**Headers:** `Authorization: Bearer {token}`

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "org-uuid",
      "name": "Acme Corporation",
      "type": "company",
      "memberRole": "owner",
      "memberCount": 15,
      "teamCount": 3,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### GET /organizations/:id
Get organization details.

**Headers:** `Authorization: Bearer {token}`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "name": "Acme Corporation",
    "description": "Leading technology company",
    "type": "company",
    "settings": {
      "timezone": "UTC",
      "language": "en",
      "currency": "USD"
    },
    "members": [
      {
        "id": "member-uuid",
        "role": "owner",
        "user": {
          "id": "user-uuid",
          "firstName": "John",
          "lastName": "Doe",
          "email": "<EMAIL>"
        },
        "joinedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "teams": [
      {
        "id": "team-uuid",
        "name": "Development Team",
        "memberCount": 5
      }
    ]
  }
}
```

### POST /organizations/:id/invitations
Send invitation to join organization.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "email": "<EMAIL>",
  "role": "member",
  "message": "Welcome to our team!",
  "expiresIn": "7d"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "invitation-uuid",
    "email": "<EMAIL>",
    "role": "member",
    "status": "pending",
    "token": "invitation-token",
    "expiresAt": "2024-01-08T00:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

## 🤖 AI Endpoints

### POST /ai/smart/generate
Smart AI text generation with automatic provider selection.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Explain artificial intelligence in simple terms"
    }
  ],
  "task": "text",
  "options": {
    "temperature": 0.7,
    "maxTokens": 500,
    "privacyLevel": "public"
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "content": "Artificial intelligence (AI) is like giving computers the ability to think and learn...",
    "usage": {
      "promptTokens": 25,
      "completionTokens": 150,
      "totalTokens": 175
    },
    "model": "gpt-3.5-turbo",
    "providerUsed": {
      "provider": "openai",
      "model": "gpt-3.5-turbo",
      "reason": "Best performance for text generation tasks"
    },
    "responseTime": 1200,
    "cost": 0.0003
  }
}
```

### POST /ai/compare/providers
Compare responses from multiple AI providers.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Write a creative story about AI"
    }
  ],
  "providers": [
    {
      "provider": "openai",
      "model": "gpt-4-turbo"
    },
    {
      "provider": "grok",
      "model": "grok-1.5"
    },
    {
      "provider": "ollama",
      "model": "llama2:7b"
    }
  ],
  "options": {
    "temperature": 0.8,
    "maxTokens": 300
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "provider": "openai",
        "model": "gpt-4-turbo",
        "response": {
          "content": "In a world where artificial intelligence...",
          "usage": {
            "totalTokens": 250
          }
        },
        "responseTime": 1500,
        "cost": 0.005,
        "success": true
      },
      {
        "provider": "grok",
        "model": "grok-1.5",
        "response": {
          "content": "Once upon a time, in the digital realm...",
          "usage": {
            "totalTokens": 280
          }
        },
        "responseTime": 800,
        "cost": 0.0008,
        "success": true
      },
      {
        "provider": "ollama",
        "model": "llama2:7b",
        "response": {
          "content": "The year was 2045, and AI had become...",
          "usage": {
            "totalTokens": 220
          }
        },
        "responseTime": 2000,
        "cost": 0,
        "success": true
      }
    ],
    "comparison": {
      "fastest": {
        "provider": "grok",
        "responseTime": 800
      },
      "mostTokens": {
        "provider": "grok",
        "totalTokens": 280
      },
      "cheapest": {
        "provider": "ollama",
        "cost": 0
      }
    }
  }
}
```

### GET /ai/ollama/models
Get available OLLAMA models.

**Headers:** `Authorization: Bearer {token}`

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "name": "llama2:7b",
      "size": **********,
      "digest": "sha256:...",
      "details": {
        "format": "gguf",
        "family": "llama",
        "families": ["llama"],
        "parameter_size": "7B",
        "quantization_level": "Q4_0"
      },
      "modified_at": "2024-01-01T00:00:00Z"
    },
    {
      "name": "codellama:7b",
      "size": **********,
      "digest": "sha256:...",
      "details": {
        "format": "gguf",
        "family": "llama",
        "families": ["llama"],
        "parameter_size": "7B",
        "quantization_level": "Q4_0"
      },
      "modified_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### POST /ai/ollama/models/pull
Pull new OLLAMA model.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "modelName": "mistral:7b"
}
```

**Response (202):**
```json
{
  "success": true,
  "data": {
    "status": "pulling",
    "modelName": "mistral:7b",
    "message": "Model pull started"
  }
}
```

### POST /ai/ollama/code/generate
Generate code using OLLAMA.

**Headers:** `Authorization: Bearer {token}`

**Request:**
```json
{
  "model": "codellama:7b",
  "prompt": "Create a REST API endpoint for user authentication in TypeScript",
  "language": "typescript",
  "options": {
    "temperature": 0.2,
    "num_predict": 200
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "content": "```typescript\n@Controller('auth')\nexport class AuthController {\n  @Post('login')\n  async login(@Body() loginDto: LoginDto) {\n    // Implementation\n  }\n}\n```",
    "usage": {
      "promptTokens": 20,
      "completionTokens": 80,
      "totalTokens": 100
    },
    "model": "codellama:7b",
    "responseTime": 3000
  }
}
```

## 📊 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "statusCode": 400,
  "message": "Validation failed",
  "errors": {
    "email": ["Please provide a valid email address"],
    "password": ["Password must be at least 8 characters"]
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "path": "/api/v1/auth/register"
}
```

### Common Error Codes
- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource already exists
- **422 Unprocessable Entity**: Validation errors
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

## 🔧 Rate Limiting

### Rate Limits
- **General API**: 100 requests per minute per IP
- **Authentication**: 5 requests per minute per IP
- **AI Generation**: 20 requests per minute per user
- **File Upload**: 10 requests per minute per user

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 📝 Request/Response Examples

### cURL Examples
```bash
# Register user
curl -X POST https://api.delify.com/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "John",
    "lastName": "Doe"
  }'

# Login
curl -X POST https://api.delify.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Get profile
curl -X GET https://api.delify.com/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# AI generation
curl -X POST https://api.delify.com/api/v1/ai/smart/generate \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "task": "text"
  }'
```

## 🎯 Best Practices

### API Usage Guidelines
1. **Always use HTTPS** in production
2. **Include proper headers** (Content-Type, Authorization)
3. **Handle rate limits** gracefully
4. **Validate responses** before processing
5. **Implement proper error handling**
6. **Use pagination** for large datasets
7. **Cache responses** when appropriate
8. **Monitor API usage** and performance

### Security Considerations
- Store JWT tokens securely
- Implement token refresh logic
- Validate all inputs on client side
- Use HTTPS for all requests
- Implement proper CORS policies
- Monitor for suspicious activity

**This API reference provides complete documentation cho all available endpoints trong Delify Platform.** 📚✨
