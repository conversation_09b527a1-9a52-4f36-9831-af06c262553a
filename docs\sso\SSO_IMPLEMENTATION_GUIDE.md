# Single Sign-On (SSO) Implementation Guide

## Overview

The Single Sign-On (SSO) system enables users to log in once and access multiple applications across different subdomains. The system is fully integrated with the existing RBAC and authentication infrastructure.

## 🚀 Key Features

- **Cross-Subdomain Authentication**: Login once, access all subdomains
- **Global Session Management**: Centralized session management with device fingerprinting
- **Token Revocation**: JWT token blacklist system with Redis caching
- **Device Security**: Suspicious device detection and device verification
- **Comprehensive Audit Logging**: Full logging for all SSO activities
- **Application Management**: Manage allowed SSO applications
- **RBAC Integration**: Seamless integration with role-based access control

## 📋 System Configuration

### 1. Environment Variables

```env
# SSO Configuration
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ISSUER=auth.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com

# Session Configuration
SSO_SESSION_TIMEOUT=480
SSO_MAX_CONCURRENT_SESSIONS=5
SSO_REQUIRE_DEVICE_VERIFICATION=false

# Security Configuration
SSO_ENABLE_AUDIT_LOGGING=true
SSO_TOKEN_REVOCATION_ENABLED=true
SSO_CROSS_DOMAIN_COOKIES=true
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax

# JWT Configuration (existing)
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=15m

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

### 2. Database Migration

Run migration to create SSO tables:

```bash
npm run migration:run
```

### 3. Seed Data

Run seed to create default data:

```bash
npm run seed:sso
```

## 🏗️ System Architecture

### Database Schema

```sql
-- User session management
user_sessions (
  id, session_id, user_id, device_id, device_fingerprint,
  device_name, device_type, ip_address, user_agent, location,
  is_active, last_activity_at, expires_at, created_at, updated_at
)

-- SSO application management
sso_applications (
  id, name, subdomain, display_name, description,
  base_url, allowed_origins, is_active, created_at, updated_at
)

-- JWT token blacklist
jwt_blacklist (
  id, jti, user_id, session_id, token_type,
  expires_at, revoked_at, revoked_by, reason
)

-- Audit logging
sso_audit_logs (
  id, user_id, session_id, application, action, resource,
  ip_address, user_agent, device_id, success, error_message,
  metadata, created_at
)
```

### Extended JWT Payload

```typescript
interface SSOJwtPayload extends JwtPayload {
  // Existing fields
  sub: string;
  email: string;
  username: string;
  roles: string[];
  permissions: string[];
  
  // SSO fields
  iss: string; // auth.yourcompany.com
  aud: string[]; // ['app.yourcompany.com', 'mail.yourcompany.com']
  sessionId: string; // Global session ID
  deviceId: string; // Device fingerprint
  jti: string; // JWT ID for revocation
  domain: string; // yourcompany.com
  ssoEnabled: boolean;
  applications: string[];
}
```

## 🔧 API Usage

### 1. SSO Login

```typescript
POST /auth/sso/login
{
  "userId": "user-uuid",
  "application": "app.yourcompany.com",
  "deviceName": "Chrome on Windows",
  "location": "New York, USA",
  "rbacInfo": {
    "email": "<EMAIL>",
    "username": "user123",
    "role": "MANAGER",
    "roles": ["MANAGER", "USER"],
    "permissions": ["USER_MANAGEMENT_READ", "CONTENT_MANAGEMENT_CREATE"]
  }
}

Response:
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "sessionId": "sso_1703000000_abc123",
  "expiresIn": 900,
  "tokenType": "Bearer",
  "ssoEnabled": true,
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ]
}
```

### 2. Token Verification

```typescript
POST /auth/sso/verify
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "application": "mail.yourcompany.com"
}

Response:
{
  "valid": true,
  "payload": {
    "sub": "user-uuid",
    "sessionId": "sso_1703000000_abc123",
    "permissions": ["MAIL_MANAGEMENT_READ", "MAIL_MANAGEMENT_CREATE"],
    "aud": ["app.yourcompany.com", "mail.yourcompany.com"]
  },
  "sessionValid": true
}
```

### 3. Session Management

```typescript
// Get current session info
GET /auth/sso/session
Authorization: Bearer <access_token>

// Get all user sessions
GET /auth/sso/sessions
Authorization: Bearer <access_token>

// Terminate specific session
DELETE /auth/sso/sessions/{sessionId}
Authorization: Bearer <access_token>

// Terminate all other sessions
DELETE /auth/sso/sessions/all
Authorization: Bearer <access_token>
```

### 4. SSO Logout

```typescript
POST /auth/sso/logout
Authorization: Bearer <access_token>
{
  "globalLogout": true // Logout from all devices
}

Response:
{
  "message": "Global logout successful",
  "sessionsTerminated": 3
}
```

## 🔒 Security

### Cookie Configuration

```typescript
// Access Token Cookie
{
  domain: '.yourcompany.com',
  path: '/',
  httpOnly: true,
  secure: true,
  sameSite: 'lax',
  maxAge: 15 * 60 * 1000 // 15 minutes
}

// Refresh Token Cookie
{
  domain: '.yourcompany.com',
  path: '/',
  httpOnly: true,
  secure: true,
  sameSite: 'lax',
  maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
}
```

### Device Fingerprinting

```typescript
const deviceInfo = {
  userAgent: req.headers['user-agent'],
  ipAddress: req.ip,
  acceptLanguage: req.headers['accept-language'],
  acceptEncoding: req.headers['accept-encoding'],
  viewport: { width: 1920, height: 1080 },
  timezone: 'America/New_York',
  platform: 'Win32',
  cookieEnabled: true,
  javaEnabled: false
};

const fingerprint = deviceFingerprintService.generateFingerprint(deviceInfo);
```

### Token Revocation

```typescript
// Revoke specific token
await jwtBlacklistService.revokeToken({
  jti: 'token-id',
  userId: 'user-uuid',
  tokenType: TokenType.ACCESS,
  expiresAt: new Date(),
  reason: RevocationReason.USER_LOGOUT,
  sessionId: 'session-id'
});

// Revoke all user tokens
await jwtBlacklistService.revokeAllUserTokens(
  'user-uuid',
  RevocationReason.GLOBAL_LOGOUT,
  'admin-user-id'
);
```

## 🎯 Frontend Integration

### 1. Cookie-Based Authentication

```javascript
// Frontend doesn't need to handle tokens manually
// Cookies are set automatically by server

// Check authentication status
fetch('/auth/sso/session', {
  credentials: 'include' // Important: send cookies
})
.then(response => {
  if (response.ok) {
    // User is authenticated
    return response.json();
  } else {
    // Redirect to login
    window.location.href = '/login';
  }
});
```

### 2. Cross-Domain Requests

```javascript
// From mail.yourcompany.com calling app.yourcompany.com API
fetch('https://app.yourcompany.com/api/users', {
  credentials: 'include', // Send SSO cookies
  headers: {
    'Content-Type': 'application/json',
    'X-Application': 'mail.yourcompany.com'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### 3. Global Logout

```javascript
// Logout from all devices
fetch('/auth/sso/logout', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    globalLogout: true
  })
})
.then(() => {
  // Redirect to login page
  window.location.href = '/login';
});
```

## 📊 Monitoring and Analytics

### 1. Audit Logs

```typescript
// Get security logs
GET /auth/sso/audit/security
Authorization: Bearer <admin_token>

// Get login statistics
GET /auth/sso/audit/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin_token>
```

### 2. Session Statistics

```typescript
// Session statistics
GET /auth/sso/sessions/stats
Authorization: Bearer <admin_token>

Response:
{
  "totalActiveSessions": 1250,
  "deviceTypeBreakdown": {
    "DESKTOP": 800,
    "MOBILE": 350,
    "TABLET": 100
  },
  "applicationBreakdown": {
    "app.yourcompany.com": 600,
    "mail.yourcompany.com": 400,
    "core.yourcompany.com": 250
  }
}
```

### 3. Blacklist Statistics

```typescript
GET /auth/sso/blacklist/stats
Authorization: Bearer <admin_token>

Response:
{
  "totalBlacklisted": 150,
  "byTokenType": {
    "ACCESS": 120,
    "REFRESH": 30
  },
  "byReason": {
    "USER_LOGOUT": 80,
    "GLOBAL_LOGOUT": 40,
    "SECURITY_BREACH": 20,
    "ADMIN_REVOKE": 10
  },
  "recentRevocations": 25,
  "expiredTokens": 50
}
```

## 🔧 Troubleshooting

### Common Issues

**1. Cookies not being set:**
- Check `SSO_COOKIE_DOMAIN` configuration
- Ensure `credentials: 'include'` in fetch requests
- Verify CORS configuration

**2. Token verification fails:**
- Check JWT secret consistency
- Verify token is not blacklisted
- Check audience (aud) claim

**3. Session expired:**
- Check `SSO_SESSION_TIMEOUT` configuration
- Verify session cleanup job is running
- Check Redis connection

**4. Device fingerprint mismatch:**
- Check `SSO_REQUIRE_DEVICE_VERIFICATION` setting
- Review device fingerprinting logic
- Check for browser/device changes

### Debug Commands

```bash
# Check SSO configuration
curl -X GET http://localhost:3000/auth/sso/config

# Verify token
curl -X POST http://localhost:3000/auth/sso/verify \
  -H "Content-Type: application/json" \
  -d '{"token":"your-jwt-token"}'

# Check session
curl -X GET http://localhost:3000/auth/sso/session \
  -H "Authorization: Bearer your-jwt-token"

# Cleanup expired sessions
curl -X POST http://localhost:3000/auth/sso/cleanup/sessions \
  -H "Authorization: Bearer admin-token"
```

## 📈 Performance Optimization

### 1. Redis Caching

```typescript
// Cache configuration
const CACHE_TTL = 15 * 60; // 15 minutes
const CACHE_PREFIX = 'sso:';

// Cache keys
user_permissions:${userId}
jwt_blacklist:${jti}
session:${sessionId}
device_fingerprint:${deviceId}
```

### 2. Database Indexing

```sql
-- Performance indexes
CREATE INDEX idx_user_sessions_user_active ON user_sessions(user_id, is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_jwt_blacklist_jti ON jwt_blacklist(jti);
CREATE INDEX idx_jwt_blacklist_expires ON jwt_blacklist(expires_at);
CREATE INDEX idx_sso_audit_created ON sso_audit_logs(created_at);
```

### 3. Cleanup Jobs

```typescript
// Scheduled cleanup (runs every hour)
@Cron('0 0 * * * *')
async cleanupExpiredData() {
  const expiredSessions = await sessionService.cleanupExpiredSessions();
  const expiredTokens = await blacklistService.cleanupExpiredEntries();
  const oldLogs = await auditService.cleanupOldLogs(90); // 90 days retention
  
  logger.log(`Cleanup completed: ${expiredSessions} sessions, ${expiredTokens} tokens, ${oldLogs} logs`);
}
```

---

**For more details, refer to [API Documentation](./SSO_API_DOCUMENTATION.md) and [User Guide](./SSO_USER_GUIDE.md).**
