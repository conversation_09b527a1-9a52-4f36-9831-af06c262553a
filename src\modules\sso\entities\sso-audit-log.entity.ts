import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyTo<PERSON><PERSON>,
  Join<PERSON>olumn,
  Index,
  In,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/**
 * Enum định nghĩa các hành động SSO
 * Enum defining SSO actions
 */
export enum SSOAction {
  // Authentication actions
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  GLOBAL_LOGOUT = 'GLOBAL_LOGOUT',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  TOKEN_VERIFY = 'TOKEN_VERIFY',
  
  // Session actions
  SESSION_CREATE = 'SESSION_CREATE',
  SESSION_EXTEND = 'SESSION_EXTEND',
  SESSION_TERMINATE = 'SESSION_TERMINATE',
  SESSION_CLEANUP = 'SESSION_CLEANUP',
  
  // Permission actions
  PERMISSION_CHECK = 'PERMISSION_CHECK',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  ROLE_ASSIGN = 'ROLE_ASSIGN',
  ROLE_REVOKE = 'ROLE_REVOKE',
  
  // Security actions
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  DEVICE_VERIFICATION = 'DEVICE_VERIFICATION',
  IP_CHANGE = 'IP_CHANGE',
  MULTIPLE_SESSIONS = 'MULTIPLE_SESSIONS',
  
  // Application actions
  APP_ACCESS = 'APP_ACCESS',
  APP_SWITCH = 'APP_SWITCH',
  CROSS_DOMAIN_REQUEST = 'CROSS_DOMAIN_REQUEST',
  
  // Administrative actions
  ADMIN_ACTION = 'ADMIN_ACTION',
  CONFIG_CHANGE = 'CONFIG_CHANGE',
  USER_IMPERSONATE = 'USER_IMPERSONATE',
}

/**
 * SSOAuditLog Entity - Ghi log audit cho hệ thống SSO
 * SSOAuditLog Entity - Audit logging for SSO system
 */
@Entity('sso_audit_logs')
@Index(['userId'])
@Index(['sessionId'])
@Index(['application'])
@Index(['action'])
@Index(['createdAt'])
export class SSOAuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID người dùng - User ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'user_id' })
  userId?: string;

  /**
   * ID phiên - Session ID
   */
  @Column({ type: 'varchar', length: 255, nullable: true, name: 'session_id' })
  sessionId?: string;

  /**
   * Ứng dụng - Application
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  application?: string;

  /**
   * Hành động - Action
   */
  @Column({ 
    type: 'varchar', 
    length: 100,
    enum: SSOAction 
  })
  action: SSOAction;

  /**
   * Tài nguyên - Resource
   */
  @Column({ type: 'varchar', length: 200, nullable: true })
  resource?: string;

  /**
   * Địa chỉ IP - IP address
   */
  @Column({ type: 'varchar', length: 45, nullable: true, name: 'ip_address' })
  ipAddress?: string;

  /**
   * User agent
   */
  @Column({ type: 'text', nullable: true, name: 'user_agent' })
  userAgent?: string;

  /**
   * ID thiết bị - Device ID
   */
  @Column({ type: 'varchar', length: 255, nullable: true, name: 'device_id' })
  deviceId?: string;

  /**
   * Trạng thái thành công - Success status
   */
  @Column({ type: 'boolean' })
  success: boolean;

  /**
   * Thông báo lỗi - Error message
   */
  @Column({ type: 'text', nullable: true, name: 'error_message' })
  errorMessage?: string;

  /**
   * Metadata bổ sung - Additional metadata
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations

  /**
   * Người dùng - User
   */
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  // Virtual properties

  /**
   * Kiểm tra có phải hành động bảo mật không - Check if security action
   */
  get isSecurityAction(): boolean {
    return [
      SSOAction.SUSPICIOUS_ACTIVITY,
      SSOAction.DEVICE_VERIFICATION,
      SSOAction.IP_CHANGE,
      SSOAction.MULTIPLE_SESSIONS,
      SSOAction.PERMISSION_DENIED,
    ].includes(this.action);
  }

  /**
   * Kiểm tra có phải hành động xác thực không - Check if authentication action
   */
  get isAuthAction(): boolean {
    return [
      SSOAction.LOGIN,
      SSOAction.LOGOUT,
      SSOAction.GLOBAL_LOGOUT,
      SSOAction.TOKEN_REFRESH,
      SSOAction.TOKEN_VERIFY,
    ].includes(this.action);
  }

  /**
   * Kiểm tra có phải hành động quản trị không - Check if admin action
   */
  get isAdminAction(): boolean {
    return [
      SSOAction.ADMIN_ACTION,
      SSOAction.CONFIG_CHANGE,
      SSOAction.USER_IMPERSONATE,
      SSOAction.ROLE_ASSIGN,
      SSOAction.ROLE_REVOKE,
    ].includes(this.action);
  }

  /**
   * Lấy mô tả hành động - Get action description
   */
  get actionDescription(): string {
    const descriptions = {
      [SSOAction.LOGIN]: 'User logged in',
      [SSOAction.LOGOUT]: 'User logged out',
      [SSOAction.GLOBAL_LOGOUT]: 'User logged out from all devices',
      [SSOAction.TOKEN_REFRESH]: 'Token refreshed',
      [SSOAction.TOKEN_VERIFY]: 'Token verified',
      [SSOAction.SESSION_CREATE]: 'Session created',
      [SSOAction.SESSION_EXTEND]: 'Session extended',
      [SSOAction.SESSION_TERMINATE]: 'Session terminated',
      [SSOAction.SESSION_CLEANUP]: 'Session cleaned up',
      [SSOAction.PERMISSION_CHECK]: 'Permission checked',
      [SSOAction.PERMISSION_DENIED]: 'Permission denied',
      [SSOAction.ROLE_ASSIGN]: 'Role assigned',
      [SSOAction.ROLE_REVOKE]: 'Role revoked',
      [SSOAction.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected',
      [SSOAction.DEVICE_VERIFICATION]: 'Device verified',
      [SSOAction.IP_CHANGE]: 'IP address changed',
      [SSOAction.MULTIPLE_SESSIONS]: 'Multiple sessions detected',
      [SSOAction.APP_ACCESS]: 'Application accessed',
      [SSOAction.APP_SWITCH]: 'Application switched',
      [SSOAction.CROSS_DOMAIN_REQUEST]: 'Cross-domain request',
      [SSOAction.ADMIN_ACTION]: 'Administrative action',
      [SSOAction.CONFIG_CHANGE]: 'Configuration changed',
      [SSOAction.USER_IMPERSONATE]: 'User impersonated',
    };

    return descriptions[this.action] || 'Unknown action';
  }

  /**
   * Lấy mức độ nghiêm trọng - Get severity level
   */
  get severity(): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (!this.success) {
      if (this.isSecurityAction) return 'CRITICAL';
      if (this.isAuthAction) return 'HIGH';
      return 'MEDIUM';
    }

    if (this.isSecurityAction) return 'MEDIUM';
    if (this.isAdminAction) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Tạo log entry mới - Create new log entry
   */
  static create(
    action: SSOAction,
    success: boolean,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): SSOAuditLog {
    const log = new SSOAuditLog();
    log.action = action;
    log.success = success;
    log.userId = options.userId;
    log.sessionId = options.sessionId;
    log.application = options.application;
    log.resource = options.resource;
    log.ipAddress = options.ipAddress;
    log.userAgent = options.userAgent;
    log.deviceId = options.deviceId;
    log.errorMessage = options.errorMessage;
    log.metadata = options.metadata;
    
    return log;
  }

  /**
   * Tạo log thành công - Create success log
   */
  static success(
    action: SSOAction,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      metadata?: Record<string, any>;
    } = {},
  ): SSOAuditLog {
    return SSOAuditLog.create(action, true, options);
  }

  /**
   * Tạo log thất bại - Create failure log
   */
  static failure(
    action: SSOAction,
    errorMessage: string,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      metadata?: Record<string, any>;
    } = {},
  ): SSOAuditLog {
    return SSOAuditLog.create(action, false, {
      ...options,
      errorMessage,
    });
  }

  /**
   * Lấy thống kê audit logs - Get audit log statistics
   */
  static async getStatistics(
    repository: any,
    startDate?: Date,
    endDate?: Date,
    application?: string,
  ): Promise<any> {
    const query = repository
      .createQueryBuilder('log')
      .select('log.action', 'action')
      .addSelect('log.success', 'success')
      .addSelect('COUNT(*)', 'count')
      .groupBy('log.action, log.success');

    if (startDate) {
      query.andWhere('log.created_at >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('log.created_at <= :endDate', { endDate });
    }

    if (application) {
      query.andWhere('log.application = :application', { application });
    }

    return query.getRawMany();
  }

  /**
   * Lấy logs bảo mật - Get security logs
   */
  static async getSecurityLogs(
    repository: any,
    limit: number = 100,
    offset: number = 0,
  ): Promise<SSOAuditLog[]> {
    return repository.find({
      where: {
        action: In([
          SSOAction.SUSPICIOUS_ACTIVITY,
          SSOAction.PERMISSION_DENIED,
          SSOAction.IP_CHANGE,
          SSOAction.MULTIPLE_SESSIONS,
        ]),
      },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
      relations: ['user'],
    });
  }

  /**
   * Cleanup old logs - Cleanup old logs
   */
  static async cleanupOldLogs(
    repository: any,
    retentionDays: number = 90,
  ): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await repository
      .createQueryBuilder()
      .delete()
      .from(SSOAuditLog)
      .where('created_at < :cutoffDate', { cutoffDate })
      .execute();

    return result.affected || 0;
  }
}
