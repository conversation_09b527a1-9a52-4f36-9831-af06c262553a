import { SetMetadata, applyDecorators, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiForbiddenResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RBACGuard, PermissionGuard, RoleGuard, RoleLevelGuard } from '../guards/rbac.guard';

// Metadata keys
export const PERMISSION_KEY = 'permission';
export const ROLE_KEY = 'role';
export const ROLE_LEVEL_KEY = 'roleLevel';

/**
 * Decorator yêu cầu quyền cụ thể - Decorator requiring specific permission
 * @param permission Mã quyền hạn - Permission code
 */
export const RequirePermission = (permission: string) => {
  return applyDecorators(
    SetMetadata(PERMISSION_KEY, permission),
    UseGuards(JwtAuthGuard, PermissionGuard),
    ApiBearerAuth(),
    ApiOperation({ 
      summary: `Requires permission: ${permission}`,
      description: `Endpoint này yêu cầu quyền ${permission} - This endpoint requires ${permission} permission`
    }),
    ApiResponse({ 
      status: 200, 
      description: 'Thành công - Success' 
    }),
    ApiForbiddenResponse({ 
      description: 'Không có quyền truy cập - Access denied',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 403 },
          message: { type: 'string', example: 'User lacks required permission' },
          error: { type: 'string', example: 'Forbidden' }
        }
      }
    }),
  );
};

/**
 * Decorator yêu cầu vai trò cụ thể - Decorator requiring specific role
 * @param role Tên vai trò - Role name
 */
export const RequireRole = (role: string) => {
  return applyDecorators(
    SetMetadata(ROLE_KEY, role),
    UseGuards(JwtAuthGuard, RoleGuard),
    ApiBearerAuth(),
    ApiOperation({ 
      summary: `Requires role: ${role}`,
      description: `Endpoint này yêu cầu vai trò ${role} - This endpoint requires ${role} role`
    }),
    ApiResponse({ 
      status: 200, 
      description: 'Thành công - Success' 
    }),
    ApiForbiddenResponse({ 
      description: 'Không có vai trò phù hợp - Insufficient role',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 403 },
          message: { type: 'string', example: `Required role: ${role}` },
          error: { type: 'string', example: 'Forbidden' }
        }
      }
    }),
  );
};

/**
 * Decorator yêu cầu cấp độ vai trò tối thiểu - Decorator requiring minimum role level
 * @param level Cấp độ tối thiểu (số càng nhỏ quyền càng cao) - Minimum level (lower number = higher authority)
 */
export const RequireRoleLevel = (level: number) => {
  return applyDecorators(
    SetMetadata(ROLE_LEVEL_KEY, level),
    UseGuards(JwtAuthGuard, RoleLevelGuard),
    ApiBearerAuth(),
    ApiOperation({ 
      summary: `Requires role level: ${level} or higher`,
      description: `Endpoint này yêu cầu cấp độ vai trò ${level} trở lên - This endpoint requires role level ${level} or higher`
    }),
    ApiResponse({ 
      status: 200, 
      description: 'Thành công - Success' 
    }),
    ApiForbiddenResponse({ 
      description: 'Cấp độ vai trò không đủ - Insufficient role level',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 403 },
          message: { type: 'string', example: `Required role level: ${level} or higher` },
          error: { type: 'string', example: 'Forbidden' }
        }
      }
    }),
  );
};

/**
 * Decorator kết hợp RBAC - Combined RBAC decorator
 * @param options Tùy chọn RBAC - RBAC options
 */
export interface RBACOptions {
  permission?: string;
  role?: string;
  roleLevel?: number;
  description?: string;
}

export const RBAC = (options: RBACOptions) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  if (options.permission) {
    decorators.push(SetMetadata(PERMISSION_KEY, options.permission));
  }

  if (options.role) {
    decorators.push(SetMetadata(ROLE_KEY, options.role));
  }

  if (options.roleLevel !== undefined) {
    decorators.push(SetMetadata(ROLE_LEVEL_KEY, options.roleLevel));
  }

  // Tạo summary cho API documentation
  let summary = 'Protected endpoint';
  const requirements = [];

  if (options.permission) {
    requirements.push(`permission: ${options.permission}`);
  }
  if (options.role) {
    requirements.push(`role: ${options.role}`);
  }
  if (options.roleLevel !== undefined) {
    requirements.push(`role level: ${options.roleLevel}+`);
  }

  if (requirements.length > 0) {
    summary = `Requires ${requirements.join(', ')}`;
  }

  // Add Swagger decorators with proper typing
  decorators.push(
    ApiOperation({
      summary,
      description: options.description || `Endpoint được bảo vệ bởi RBAC - RBAC protected endpoint`
    }) as MethodDecorator,
    ApiResponse({
      status: 200,
      description: 'Thành công - Success'
    }) as MethodDecorator,
    ApiForbiddenResponse({
      description: 'Không có quyền truy cập - Access denied',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 403 },
          message: { type: 'string', example: 'Access denied' },
          error: { type: 'string', example: 'Forbidden' }
        }
      }
    }) as MethodDecorator,
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};

/**
 * Decorator cho Admin - Admin decorator
 */
export const AdminOnly = () => {
  return RBAC({
    roleLevel: 1,
    description: 'Chỉ dành cho Admin - Admin only'
  });
};

/**
 * Decorator cho Manager - Manager decorator
 */
export const ManagerOnly = () => {
  return RBAC({
    roleLevel: 2,
    description: 'Chỉ dành cho Manager trở lên - Manager and above only'
  });
};

/**
 * Decorator cho Master Account - Master Account decorator
 */
export const MasterOnly = () => {
  return RBAC({
    role: 'MASTER_ACCOUNT',
    description: 'Chỉ dành cho Master Account - Master Account only'
  });
};

/**
 * Decorator cho quyền đọc - Read permission decorator
 */
export const CanRead = (module: string) => {
  return RequirePermission(`${module}_READ`);
};

/**
 * Decorator cho quyền tạo - Create permission decorator
 */
export const CanCreate = (module: string) => {
  return RequirePermission(`${module}_CREATE`);
};

/**
 * Decorator cho quyền cập nhật - Update permission decorator
 */
export const CanUpdate = (module: string) => {
  return RequirePermission(`${module}_UPDATE`);
};

/**
 * Decorator cho quyền xóa - Delete permission decorator
 */
export const CanDelete = (module: string) => {
  return RequirePermission(`${module}_DELETE`);
};

/**
 * Decorator cho quyền quản lý - Manage permission decorator
 */
export const CanManage = (module: string) => {
  return RequirePermission(`${module}_MANAGE`);
};

/**
 * Decorator cho quyền xuất dữ liệu - Export permission decorator
 */
export const CanExport = (module: string) => {
  return RequirePermission(`${module}_EXPORT`);
};

/**
 * Decorator cho quyền phê duyệt - Approve permission decorator
 */
export const CanApprove = (module: string) => {
  return RequirePermission(`${module}_APPROVE`);
};

/**
 * Decorator cho quyền gán - Assign permission decorator
 */
export const CanAssign = (module: string) => {
  return RequirePermission(`${module}_ASSIGN`);
};

/**
 * Decorator kết hợp cho User Management - Combined User Management decorator
 */
export const UserManagement = {
  Read: () => CanRead('USER_MANAGEMENT'),
  Create: () => CanCreate('USER_MANAGEMENT'),
  Update: () => CanUpdate('USER_MANAGEMENT'),
  Delete: () => CanDelete('USER_MANAGEMENT'),
  Manage: () => CanManage('USER_MANAGEMENT'),
  Export: () => CanExport('USER_MANAGEMENT'),
};

/**
 * Decorator kết hợp cho Role Management - Combined Role Management decorator
 */
export const RoleManagement = {
  Read: () => CanRead('ROLE_MANAGEMENT'),
  Create: () => CanCreate('ROLE_MANAGEMENT'),
  Update: () => CanUpdate('ROLE_MANAGEMENT'),
  Delete: () => CanDelete('ROLE_MANAGEMENT'),
  Manage: () => CanManage('ROLE_MANAGEMENT'),
  Assign: () => CanAssign('ROLE_MANAGEMENT'),
};

/**
 * Decorator kết hợp cho Content Management - Combined Content Management decorator
 */
export const ContentManagement = {
  Read: () => CanRead('CONTENT_MANAGEMENT'),
  Create: () => CanCreate('CONTENT_MANAGEMENT'),
  Update: () => CanUpdate('CONTENT_MANAGEMENT'),
  Delete: () => CanDelete('CONTENT_MANAGEMENT'),
  Manage: () => CanManage('CONTENT_MANAGEMENT'),
  Approve: () => CanApprove('CONTENT_MANAGEMENT'),
  Export: () => CanExport('CONTENT_MANAGEMENT'),
};

/**
 * Decorator cho endpoint công khai (không cần xác thực) - Public endpoint decorator
 */
export const Public = () => {
  return applyDecorators(
    ApiOperation({ 
      summary: 'Public endpoint',
      description: 'Endpoint công khai, không cần xác thực - Public endpoint, no authentication required'
    }),
    ApiResponse({ 
      status: 200, 
      description: 'Thành công - Success' 
    }),
  );
};

/**
 * Decorator cho endpoint chỉ cần xác thực (không cần quyền cụ thể) - Authenticated only decorator
 */
export const AuthenticatedOnly = () => {
  return applyDecorators(
    UseGuards(JwtAuthGuard),
    ApiBearerAuth(),
    ApiOperation({ 
      summary: 'Authenticated users only',
      description: 'Chỉ cần xác thực, không cần quyền cụ thể - Authentication required, no specific permissions needed'
    }),
    ApiResponse({ 
      status: 200, 
      description: 'Thành công - Success' 
    }),
    ApiResponse({ 
      status: 401, 
      description: 'Chưa xác thực - Unauthorized' 
    }),
  );
};
