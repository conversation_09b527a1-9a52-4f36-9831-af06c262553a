{"name": "delify-platform", "version": "1.0.0", "description": "Comprehensive business platform for startups and small businesses", "author": "Delify Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --config test/jest.config.js", "test:watch": "jest --config test/jest.config.js --watch", "test:cov": "jest --config test/jest.config.js --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config test/jest.config.js --runInBand", "test:e2e": "jest --config test/jest.config.js --testMatch='**/test/**/*.e2e-spec.ts'", "test:unit": "jest --config test/jest.config.js --testMatch='**/src/**/*.spec.ts'", "test:ai": "jest --config test/jest.config.js --testMatch='**/src/modules/ai/**/*.spec.ts'", "test:ai:e2e": "jest --config test/jest.config.js --testMatch='**/test/*ai*.e2e-spec.ts'", "test:ollama": "jest --config test/jest.config.js --testMatch='**/*ollama*.spec.ts'", "test:providers": "jest --config test/jest.config.js --testMatch='**/providers/**/*.spec.ts'", "test:integration": "jest --config test/jest.config.js --testMatch='**/*.integration.spec.ts'", "test:ci": "jest --config test/jest.config.js --coverage --watchAll=false --passWithNoTests", "test:smoke": "jest --config test/jest.config.js --testNamePattern='smoke' --passWithNoTests", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/config/database.config.ts", "migration:run": "npm run typeorm -- migration:run -d src/config/database.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/config/database.config.ts", "db:test": "node scripts/test-db-connection.js", "db:migrate": "node scripts/run-migrations.js"}, "dependencies": {"@nestjs/bull": "^10.0.0", "@nestjs/cache-manager": "^2.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/swagger": "^7.1.0", "@nestjs/throttler": "^5.2.0", "@nestjs/typeorm": "^10.0.0", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "bull": "^4.11.0", "cache-manager": "^5.2.0", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cron": "^2.4.0", "facebook-nodejs-business-sdk": "^18.0.0", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.0", "openai": "^4.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.0", "qrcode": "^1.5.4", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "sqlite3": "^5.1.7", "typeorm": "^0.3.17", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/cron": "^2.0.1", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.8", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}