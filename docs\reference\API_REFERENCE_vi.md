# 📚 Hướng Dẫn Tham Khảo API

## 📋 Tổng Quan

Tài liệu tham khảo API hoàn chỉnh cho Delify Platform với **các endpoints chi tiết**, **ví dụ request/response**, và **yêu cầu xác thực**.

## 🔗 Thông Tin Cơ Bản

### URL Cơ Sở API
```
Development: http://localhost:3000/api/v1
Staging:     https://staging-api.delify.com/api/v1
Production:  https://api.delify.com/api/v1
```

### Tài Liệu API
```
Swagger UI: {BASE_URL}/docs
OpenAPI JSON: {BASE_URL}/docs-json
```

### Xác Thực
```http
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

## 🔐 Endpoints Xác Thực

### POST /auth/register
Đăng ký tài khoản người dùng mới.

**<PERSON><PERSON><PERSON> cầu:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "accountType": "business"
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "accountType": "business",
      "isActive": true,
      "emailVerified": false,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900
    }
  }
}
```

### POST /auth/login
Xác thực người dùng và nhận tokens.

**Yêu cầu:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********"
  }
}
```

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "accountType": "business",
      "lastLoginAt": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900
    },
    "session": {
      "id": "session-uuid",
      "deviceInfo": {
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "***********"
      },
      "lastActivity": "2024-01-01T00:00:00Z"
    }
  }
}
```

### POST /auth/refresh
Làm mới access token bằng refresh token.

**Yêu cầu:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 900
  }
}
```

### POST /auth/logout
Đăng xuất người dùng và vô hiệu hóa tokens.

**Headers:** `Authorization: Bearer {token}`

**Phản hồi (200):**
```json
{
  "success": true,
  "message": "Đăng xuất thành công"
}
```

## 👥 Endpoints Người Dùng

### GET /users/profile
Lấy thông tin hồ sơ người dùng hiện tại.

**Headers:** `Authorization: Bearer {token}`

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "accountType": "business",
    "isActive": true,
    "emailVerified": true,
    "lastLoginAt": "2024-01-01T00:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "organizationMemberships": [
      {
        "id": "membership-uuid",
        "role": "owner",
        "organization": {
          "id": "org-uuid",
          "name": "Acme Corp",
          "type": "company"
        }
      }
    ]
  }
}
```

### PUT /users/profile
Cập nhật thông tin hồ sơ người dùng hiện tại.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "firstName": "John",
  "lastName": "Smith"
}
```

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Smith",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### GET /users
Lấy tất cả người dùng (chỉ Admin).

**Headers:** `Authorization: Bearer {token}`

**Tham số Query:**
- `page` (number): Số trang (mặc định: 1)
- `limit` (number): Số mục mỗi trang (mặc định: 20, tối đa: 100)
- `search` (string): Từ khóa tìm kiếm cho tên hoặc email
- `accountType` (string): Lọc theo loại tài khoản
- `sortBy` (string): Trường sắp xếp (mặc định: createdAt)
- `sortOrder` (string): Thứ tự sắp xếp ASC/DESC (mặc định: DESC)

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "uuid",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "accountType": "business",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "total": 150,
      "page": 1,
      "limit": 20,
      "totalPages": 8,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

## 🏢 Endpoints Tổ Chức

### POST /organizations
Tạo tổ chức mới.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "name": "Acme Corporation",
  "description": "Công ty công nghệ hàng đầu",
  "type": "company",
  "settings": {
    "timezone": "UTC",
    "language": "vi",
    "currency": "VND"
  }
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "name": "Acme Corporation",
    "description": "Công ty công nghệ hàng đầu",
    "type": "company",
    "settings": {
      "timezone": "UTC",
      "language": "vi",
      "currency": "VND"
    },
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### GET /organizations
Lấy các tổ chức của người dùng.

**Headers:** `Authorization: Bearer {token}`

**Phản hồi (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "org-uuid",
      "name": "Acme Corporation",
      "type": "company",
      "memberRole": "owner",
      "memberCount": 15,
      "teamCount": 3,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### GET /organizations/:id
Lấy chi tiết tổ chức.

**Headers:** `Authorization: Bearer {token}`

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "id": "org-uuid",
    "name": "Acme Corporation",
    "description": "Công ty công nghệ hàng đầu",
    "type": "company",
    "settings": {
      "timezone": "UTC",
      "language": "vi",
      "currency": "VND"
    },
    "members": [
      {
        "id": "member-uuid",
        "role": "owner",
        "user": {
          "id": "user-uuid",
          "firstName": "John",
          "lastName": "Doe",
          "email": "<EMAIL>"
        },
        "joinedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "teams": [
      {
        "id": "team-uuid",
        "name": "Nhóm Phát Triển",
        "memberCount": 5
      }
    ]
  }
}
```

### POST /organizations/:id/invitations
Gửi lời mời tham gia tổ chức.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "email": "<EMAIL>",
  "role": "member",
  "message": "Chào mừng bạn đến với nhóm của chúng tôi!",
  "expiresIn": "7d"
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "id": "invitation-uuid",
    "email": "<EMAIL>",
    "role": "member",
    "status": "pending",
    "token": "invitation-token",
    "expiresAt": "2024-01-08T00:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

## 🤖 Endpoints AI

### POST /ai/smart/generate
Tạo văn bản AI thông minh với lựa chọn nhà cung cấp tự động.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "messages": [
    {
      "role": "system",
      "content": "Bạn là một trợ lý hữu ích."
    },
    {
      "role": "user",
      "content": "Giải thích trí tuệ nhân tạo bằng thuật ngữ đơn giản"
    }
  ],
  "task": "text",
  "options": {
    "temperature": 0.7,
    "maxTokens": 500,
    "privacyLevel": "public"
  }
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "content": "Trí tuệ nhân tạo (AI) giống như việc cho máy tính khả năng suy nghĩ và học hỏi...",
    "usage": {
      "promptTokens": 25,
      "completionTokens": 150,
      "totalTokens": 175
    },
    "model": "gpt-3.5-turbo",
    "providerUsed": {
      "provider": "openai",
      "model": "gpt-3.5-turbo",
      "reason": "Hiệu suất tốt nhất cho tác vụ tạo văn bản"
    },
    "responseTime": 1200,
    "cost": 0.0003
  }
}
```

### POST /ai/compare/providers
So sánh phản hồi từ nhiều nhà cung cấp AI.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Viết một câu chuyện sáng tạo về AI"
    }
  ],
  "providers": [
    {
      "provider": "openai",
      "model": "gpt-4-turbo"
    },
    {
      "provider": "grok",
      "model": "grok-1.5"
    },
    {
      "provider": "ollama",
      "model": "llama2:7b"
    }
  ],
  "options": {
    "temperature": 0.8,
    "maxTokens": 300
  }
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "provider": "openai",
        "model": "gpt-4-turbo",
        "response": {
          "content": "Trong một thế giới nơi trí tuệ nhân tạo...",
          "usage": {
            "totalTokens": 250
          }
        },
        "responseTime": 1500,
        "cost": 0.005,
        "success": true
      },
      {
        "provider": "grok",
        "model": "grok-1.5",
        "response": {
          "content": "Ngày xửa ngày xưa, trong vương quốc số...",
          "usage": {
            "totalTokens": 280
          }
        },
        "responseTime": 800,
        "cost": 0.002,
        "success": true
      }
    ],
    "comparison": {
      "fastest": {
        "provider": "grok",
        "responseTime": 800
      },
      "mostTokens": {
        "provider": "grok",
        "totalTokens": 280
      },
      "cheapest": {
        "provider": "grok",
        "cost": 0.002
      }
    }
  }
}
```

### GET /ai/ollama/models
Lấy danh sách các models OLLAMA có sẵn.

**Headers:** `Authorization: Bearer {token}`

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "name": "llama2:7b",
        "size": "3.8GB",
        "digest": "sha256:...",
        "modified_at": "2024-01-01T00:00:00Z"
      },
      {
        "name": "codellama:7b",
        "size": "3.8GB",
        "digest": "sha256:...",
        "modified_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### POST /ai/ollama/models/pull
Tải model OLLAMA mới.

**Headers:** `Authorization: Bearer {token}`

**Yêu cầu:**
```json
{
  "modelName": "mistral:7b"
}
```

**Phản hồi (201):**
```json
{
  "success": true,
  "data": {
    "status": "pulling",
    "progress": 0,
    "message": "Đang tải model mistral:7b..."
  }
}
```

## 📊 Endpoints Thống Kê

### GET /analytics/usage
Lấy thống kê sử dụng AI.

**Headers:** `Authorization: Bearer {token}`

**Tham số Query:**
- `period` (string): Khoảng thời gian (day, week, month, year)
- `provider` (string): Lọc theo nhà cung cấp AI
- `organizationId` (string): ID tổ chức (tùy chọn)

**Phản hồi (200):**
```json
{
  "success": true,
  "data": {
    "totalRequests": 1250,
    "totalTokens": 125000,
    "totalCost": 12.50,
    "averageResponseTime": 1200,
    "providerBreakdown": [
      {
        "provider": "openai",
        "requests": 500,
        "tokens": 50000,
        "cost": 7.50,
        "avgResponseTime": 1000
      },
      {
        "provider": "grok",
        "requests": 400,
        "tokens": 40000,
        "cost": 3.00,
        "avgResponseTime": 800
      },
      {
        "provider": "ollama",
        "requests": 350,
        "tokens": 35000,
        "cost": 0.00,
        "avgResponseTime": 2000
      }
    ],
    "dailyUsage": [
      {
        "date": "2024-01-01",
        "requests": 50,
        "tokens": 5000,
        "cost": 0.75
      }
    ]
  }
}
```

## 🔧 Endpoints Hệ Thống

### GET /health
Kiểm tra tình trạng hệ thống.

**Phản hồi (200):**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00Z",
  "uptime": 86400,
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "ai_providers": {
      "openai": "healthy",
      "grok": "healthy",
      "gemini": "healthy",
      "ollama": "healthy"
    }
  }
}
```

### GET /version
Lấy thông tin phiên bản API.

**Phản hồi (200):**
```json
{
  "version": "1.0.0",
  "buildDate": "2024-01-01T00:00:00Z",
  "gitCommit": "abc123def456",
  "environment": "production",
  "features": [
    "multi_ai_providers",
    "rbac_system",
    "organization_management",
    "real_time_analytics"
  ]
}
```

## 🚨 Mã Lỗi

### Mã Lỗi HTTP Thường Gặp

| Mã | Tên | Mô Tả |
|-----|-----|-------|
| 200 | OK | Yêu cầu thành công |
| 201 | Created | Tài nguyên được tạo thành công |
| 400 | Bad Request | Yêu cầu không hợp lệ |
| 401 | Unauthorized | Chưa xác thực |
| 403 | Forbidden | Không có quyền truy cập |
| 404 | Not Found | Tài nguyên không tồn tại |
| 409 | Conflict | Xung đột dữ liệu |
| 422 | Unprocessable Entity | Dữ liệu không hợp lệ |
| 429 | Too Many Requests | Vượt quá giới hạn tốc độ |
| 500 | Internal Server Error | Lỗi máy chủ nội bộ |

### Định Dạng Phản Hồi Lỗi

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dữ liệu đầu vào không hợp lệ",
    "details": [
      {
        "field": "email",
        "message": "Email không hợp lệ"
      }
    ],
    "timestamp": "2024-01-01T00:00:00Z",
    "path": "/api/v1/auth/register"
  }
}
```

## 🎯 Thực Hành Tốt Nhất

### 1. Xác Thực
- **Luôn bao gồm JWT token** trong header Authorization
- **Làm mới token** trước khi hết hạn
- **Xử lý lỗi 401** bằng cách chuyển hướng đến trang đăng nhập

### 2. Xử Lý Lỗi
- **Kiểm tra trường success** trong phản hồi
- **Hiển thị thông báo lỗi** thân thiện với người dùng
- **Ghi log lỗi** để debug

### 3. Hiệu Suất
- **Sử dụng pagination** cho danh sách lớn
- **Cache dữ liệu** ít thay đổi
- **Giới hạn số lượng yêu cầu** đồng thời

### 4. Bảo Mật
- **Không lưu trữ token** trong localStorage
- **Sử dụng HTTPS** cho tất cả yêu cầu
- **Xác thực đầu vào** ở phía client

**API này cung cấp tất cả chức năng cần thiết để xây dựng ứng dụng cấp doanh nghiệp với tích hợp AI đa nhà cung cấp.** 🚀✨
