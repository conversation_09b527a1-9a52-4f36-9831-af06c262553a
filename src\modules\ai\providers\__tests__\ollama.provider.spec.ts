import { Test, TestingModule } from '@nestjs/testing';
import { OllamaProvider } from '../ollama.provider';
import { AIProviderConfig } from '../base-ai.provider';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OllamaProvider', () => {
  let provider: OllamaProvider;
  let mockAxiosInstance: jest.Mocked<any>;

  const testConfig: AIProviderConfig = {
    apiKey: 'not-required',
    baseURL: 'http://localhost:11434',
    model: 'llama2:7b',
    temperature: 0.7,
    maxTokens: 1000,
  };

  beforeEach(async () => {
    // Create mock axios instance
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    provider = new OllamaProvider(testConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generateText', () => {
    it('should generate text successfully', async () => {
      const mockResponse = {
        data: {
          message: {
            content: 'Hello! How can I help you today?',
          },
          done: true,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const messages = [
        { role: 'user' as const, content: 'Hello' },
      ];

      const result = await provider.generateText(messages);

      expect(result).toEqual({
        content: 'Hello! How can I help you today?',
        usage: {
          promptTokens: expect.any(Number),
          completionTokens: expect.any(Number),
          totalTokens: expect.any(Number),
        },
        model: 'llama2:7b',
        finishReason: 'stop',
        responseTime: expect.any(Number),
      });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/chat', {
        model: 'llama2:7b',
        messages: [{ role: 'user', content: 'Hello' }],
        stream: false,
        options: {
          temperature: 0.7,
          num_predict: 1000,
          top_p: 1,
          num_ctx: 4096,
        },
      });
    });

    it('should handle API errors gracefully', async () => {
      const mockError = {
        code: 'ECONNREFUSED',
        message: 'Connection refused',
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);

      const messages = [
        { role: 'user' as const, content: 'Hello' },
      ];

      await expect(provider.generateText(messages)).rejects.toThrow(
        'OLLAMA: Server not running. Please start OLLAMA server first.'
      );
    });
  });

  describe('listModels', () => {
    it('should list available models', async () => {
      const mockModels = [
        {
          name: 'llama2:7b',
          modified_at: '2024-01-01T00:00:00Z',
          size: **********,
          digest: 'sha256:test',
          details: {
            format: 'gguf',
            family: 'llama',
            families: ['llama'],
            parameter_size: '7B',
            quantization_level: 'Q4_0',
          },
        },
      ];

      mockAxiosInstance.get.mockResolvedValue({
        data: { models: mockModels },
      });

      const result = await provider.listModels();

      expect(result).toEqual(mockModels);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/tags');
    });
  });

  describe('pullModel', () => {
    it('should pull model successfully', async () => {
      mockAxiosInstance.post.mockResolvedValue({
        data: { status: 'success' },
      });

      const result = await provider.pullModel('mistral:7b');

      expect(result).toEqual({
        success: true,
        status: 'success',
      });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/pull', {
        name: 'mistral:7b',
        stream: false,
      });
    });

    it('should handle pull errors', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Model not found'));

      const result = await provider.pullModel('invalid-model');

      expect(result).toEqual({
        success: false,
        status: 'Model not found',
      });
    });
  });

  describe('deleteModel', () => {
    it('should delete model successfully', async () => {
      mockAxiosInstance.delete.mockResolvedValue({});

      const result = await provider.deleteModel('llama2:7b');

      expect(result).toEqual({
        success: true,
        message: 'Model llama2:7b deleted successfully',
      });

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/api/delete', {
        data: { name: 'llama2:7b' },
      });
    });
  });

  describe('checkServerHealth', () => {
    it('should return healthy status when server is running', async () => {
      mockAxiosInstance.get
        .mockResolvedValueOnce({ data: { version: '0.1.0' } })
        .mockResolvedValueOnce({ data: { models: [{}, {}] } });

      const result = await provider.checkServerHealth();

      expect(result).toEqual({
        isHealthy: true,
        version: '0.1.0',
        models: 2,
      });
    });

    it('should return unhealthy status when server is down', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection refused'));

      const result = await provider.checkServerHealth();

      expect(result).toEqual({
        isHealthy: false,
        error: 'Connection refused',
      });
    });
  });

  describe('generateCode', () => {
    it('should generate code successfully', async () => {
      const mockResponse = {
        data: {
          message: {
            content: 'def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)',
          },
          done: true,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await provider.generateCode('Create fibonacci function', 'python');

      expect(result.content).toContain('fibonacci');
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/chat', expect.objectContaining({
        messages: expect.arrayContaining([
          expect.objectContaining({
            content: expect.stringContaining('fibonacci'),
          }),
        ]),
      }));
    });
  });

  describe('validateConfig', () => {
    it('should validate configuration successfully', async () => {
      mockAxiosInstance.get.mockResolvedValue({ data: { models: [] } });

      const result = await provider.validateConfig();

      expect(result).toBe(true);
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/tags');
    });

    it('should return false for invalid configuration', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Connection failed'));

      const result = await provider.validateConfig();

      expect(result).toBe(false);
    });
  });
});
