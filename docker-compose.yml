version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: delify-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: delify_db
      POSTGRES_USER: delify_user
      POSTGRES_PASSWORD: delify_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - delify-network

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    container_name: delify-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - delify-network

  # NestJS Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: delify-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: delify_db
      DATABASE_USER: delify_user
      DATABASE_PASSWORD: delify_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - delify-network
    command: npm run start:dev

  # Production build
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: delify-app-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      NODE_ENV: production
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: delify_db
      DATABASE_USER: delify_user
      DATABASE_PASSWORD: delify_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
    depends_on:
      - postgres
      - redis
    networks:
      - delify-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  delify-network:
    driver: bridge
