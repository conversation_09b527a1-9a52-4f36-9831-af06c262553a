# Authentication Flow Diagrams

This document contains detailed diagrams illustrating the authentication flows in the system.

## 1. Authentication System Overview

```mermaid
graph TB
    A[Client Application] --> B[Auth API]
    B --> C[Database]
    B --> D[Email Service]
    B --> E[Verification Service]
    B --> F[Device Service]

    C --> G[(Users Table)]
    C --> H[(User Devices Table)]

    E --> I[TOTP Generator]
    E --> J[Fixed Code Validator]
    E --> K[Email Code Generator]

    D --> L[Email Provider]

    subgraph "Security Methods"
        M[DISABLED]
        N[EMAIL_VERIFICATION]
        O[TWO_FACTOR_AUTH]
        P[FIXED_CODE]
    end
```

## 2. Registration Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Device as Device Service

    C->>API: POST /auth/register
    Note over C,API: { email, username, password, accountType }

    API->>DB: Check email/username exists
    alt Email/Username already exists
        API->>C: 409 Conflict
    else Success
        API->>DB: Create new user
        Note over DB: INSERT INTO users

        API->>Device: Create device session
        Note over Device: Store device information

        API->>API: Create access & refresh tokens
        Note over API: JWT with 15m & 7d expiry

        API->>DB: Save refresh token
        Note over DB: UPDATE users SET refreshToken

        API->>Email: Send welcome email (optional)

        API->>C: 201 Created
        Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
    end
```

## 3. Basic Login Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service

    C->>API: POST /auth/login
    Note over C,API: { emailOrUsername, password }

    API->>DB: Find user
    Note over DB: SELECT * FROM users WHERE email/username

    alt User does not exist or inactive
        API->>C: 401 Unauthorized
    else Wrong password
        API->>C: 401 Unauthorized
    else Security Method = DISABLED
        API->>API: Create tokens immediately
        API->>DB: Save refresh token & device session
        API->>C: 200 OK (Complete Login)
        Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
    else Security Method Enabled
        API->>VS: Create verification session
        Note over VS: Store session in memory/Redis

        alt EMAIL_VERIFICATION
            API->>API: Create verification code
            API->>DB: Save verification code
            API->>Email: Send email with code
        end

        API->>C: 200 OK (Partial Login)
        Note over API,C: { requiresVerification: true, sessionId, message }
    end
```

## 4. Login Verification Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    C->>API: POST /auth/verify-login
    Note over C,API: { sessionId, verificationCode }

    API->>VS: Check verification session
    alt Session does not exist or expired
        API->>C: 401 Unauthorized
    else Session valid
        API->>DB: Get user information
        API->>API: Verify code by security method

        alt EMAIL_VERIFICATION
            API->>DB: Check verification code
            Note over DB: user.isVerificationCodeValid()
        else TWO_FACTOR_AUTH
            API->>VS: Verify TOTP code
            Note over VS: speakeasy.verify()
        else FIXED_CODE
            API->>VS: Verify fixed code
            Note over VS: bcrypt.compare()
        end

        alt Code invalid
            API->>C: 401 Unauthorized
        else Code valid
            API->>API: Create access & refresh tokens
            API->>DB: Save refresh token & device session

            alt EMAIL_VERIFICATION
                API->>DB: Clear verification code
            end

            API->>VS: Remove verification session
            API->>C: 200 OK (Complete Login)
            Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
        end
    end
```

## 5. Refresh Token Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    C->>API: POST /auth/refresh
    Note over C,API: { refreshToken }

    API->>API: Verify refresh token JWT
    alt Token invalid or expired
        API->>C: 401 Unauthorized
    else Token valid
        API->>DB: Find user by refresh token
        Note over DB: SELECT * FROM users WHERE refreshToken = ?

        alt User does not exist
            API->>C: 401 Unauthorized
        else Token expired in DB
            API->>C: 401 Unauthorized
        else Success
            API->>API: Create new tokens
            API->>DB: Update new refresh token
            Note over DB: UPDATE users SET refreshToken = new_token

            API->>C: 200 OK
            Note over API,C: { accessToken, refreshToken, expiresIn, tokenType }
        end
    end
```

## 6. Enable Security Methods Flow

### 6.1 Enable Email Verification

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service

    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "email_verification" }

    API->>DB: Update user security method
    Note over DB: UPDATE users SET securityMethod = 'email_verification'

    API->>Email: Send notification email

    API->>C: 200 OK
    Note over API,C: { securityMethod: "email_verification", twoFactorEnabled: false, hasFixedCode: false }
```

### 6.2 Enable Two-Factor Authentication

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    Note over C,API: Step 1: Create 2FA Secret
    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "two_factor_auth" }

    API->>VS: Create TOTP secret
    API->>VS: Create QR code
    API->>DB: Save secret temporarily
    Note over DB: UPDATE users SET twoFactorSecret = secret

    API->>C: 200 OK
    Note over API,C: { qrCodeUrl, manualEntryKey, securityMethod: "disabled" }

    Note over C: User scans QR code into authenticator app

    Note over C,API: Step 2: Verify and Enable
    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "two_factor_auth", verificationCode: "123456" }

    API->>VS: Verify TOTP code
    alt Code invalid
        API->>C: 400 Bad Request
    else Code valid
        API->>DB: Enable 2FA
        Note over DB: UPDATE users SET securityMethod = 'two_factor_auth', twoFactorEnabled = true

        API->>Email: Send notification email

        API->>C: 200 OK
        Note over API,C: { securityMethod: "two_factor_auth", twoFactorEnabled: true }
    end
```

### 6.3 Enable Fixed Code

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "fixed_code", fixedCode: "789123" }

    API->>VS: Validate fixed code format
    Note over VS: Check length, pattern, weak codes

    alt Code invalid
        API->>C: 400 Bad Request
        Note over API,C: "Fixed code is too weak"
    else Code valid
        API->>VS: Hash fixed code
        Note over VS: SHA-256 hashing

        API->>DB: Update user
        Note over DB: UPDATE users SET securityMethod = 'fixed_code', fixedCode = hashed_code

        API->>Email: Send notification email

        API->>C: 200 OK
        Note over API,C: { securityMethod: "fixed_code", hasFixedCode: true }
    end
```

## 7. Token Revocation Flow (Logout)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    Note over C,API: Logout from current device
    C->>API: POST /auth/revoke
    Note over C,API: { refreshToken }

    API->>API: Verify refresh token
    API->>DB: Find user by refresh token
    API->>DB: Clear refresh token
    Note over DB: UPDATE users SET refreshToken = null

    API->>C: 200 OK
    Note over API,C: { message: "Token revoked successfully" }

    Note over C,API: Logout from all devices
    C->>API: POST /auth/revoke-all
    Note over C,API: Authorization: Bearer access_token

    API->>DB: Clear all refresh tokens for user
    API->>DB: Deactivate all device sessions
    Note over DB: UPDATE user_devices SET isActive = false

    API->>C: 200 OK
    Note over API,C: { message: "All tokens revoked successfully" }
```

## 8. Error Handling Flow

```mermaid
flowchart TD
    A[API Request] --> B{Validate Request}
    B -->|Invalid| C[400 Bad Request]
    B -->|Valid| D{Authenticate User}

    D -->|Invalid Credentials| E[401 Unauthorized]
    D -->|Account Inactive| F[403 Forbidden]
    D -->|Too Many Attempts| G[429 Rate Limited]
    D -->|Valid| H{Check Security Method}

    H -->|DISABLED| I[Complete Login]
    H -->|Enabled| J{Verification Required}

    J -->|Code Invalid| K[401 Unauthorized]
    J -->|Session Expired| L[401 Unauthorized]
    J -->|Code Valid| I

    I --> M[200 Success]

    C --> N[Error Response]
    E --> N
    F --> N
    G --> N
    K --> N
    L --> N
    M --> O[Success Response]
```

## 9. Database State Transitions

```mermaid
stateDiagram-v2
    [*] --> UserCreated: Register
    UserCreated --> SecurityDisabled: Default State

    SecurityDisabled --> EmailVerification: Enable Email
    SecurityDisabled --> TwoFactorSetup: Enable 2FA (Step 1)
    SecurityDisabled --> FixedCode: Enable Fixed Code

    TwoFactorSetup --> TwoFactorAuth: Verify Code (Step 2)
    TwoFactorSetup --> SecurityDisabled: Cancel Setup

    EmailVerification --> SecurityDisabled: Disable
    EmailVerification --> TwoFactorSetup: Switch to 2FA
    EmailVerification --> FixedCode: Switch to Fixed Code

    TwoFactorAuth --> SecurityDisabled: Disable
    TwoFactorAuth --> EmailVerification: Switch to Email
    TwoFactorAuth --> FixedCode: Switch to Fixed Code

    FixedCode --> SecurityDisabled: Disable
    FixedCode --> EmailVerification: Switch to Email
    FixedCode --> TwoFactorSetup: Switch to 2FA

    FixedCode --> FixedCode: Change Fixed Code
```

---

**These diagrams illustrate detailed authentication flows in the system. For more details about implementation, refer to [Authentication Flow Guide](./AUTHENTICATION_FLOW_GUIDE.md).**
