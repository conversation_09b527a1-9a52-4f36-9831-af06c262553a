import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission, PermissionCategory, PermissionAction } from '../entities/permission.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    private logger: LoggerService,
  ) {}

  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { isActive: true },
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  async getPermissionById(id: string): Promise<Permission> {
    const permission = await this.permissionRepository.findOne({
      where: { id },
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  async getPermissionByName(name: string): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { name, isActive: true },
    });
  }

  async getPermissionsByCategory(): Promise<Record<PermissionCategory, Permission[]>> {
    const permissions = await this.getAllPermissions();

    const grouped = permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<PermissionCategory, Permission[]>);

    return grouped;
  }

  async getPermissionsByIds(ids: string[]): Promise<Permission[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    return this.permissionRepository
      .createQueryBuilder('permission')
      .where('permission.id IN (:...ids)', { ids })
      .andWhere('permission.isActive = :isActive', { isActive: true })
      .getMany();
  }

  async createPermission(createPermissionDto: {
    name: string;
    displayName: string;
    description?: string;
    category: PermissionCategory;
    action: PermissionAction;
    resource?: string;
    metadata?: any;
  }): Promise<Permission> {
    // Check if permission already exists
    const existing = await this.getPermissionByName(createPermissionDto.name);
    if (existing) {
      throw new BadRequestException('Permission with this name already exists');
    }

    const permission = this.permissionRepository.create({
      ...createPermissionDto,
      isSystemPermission: false,
    });

    const savedPermission = await this.permissionRepository.save(permission);

    this.logger.logWithContext(`Permission created: ${savedPermission.name}`, 'PermissionsService');

    return savedPermission;
  }

  async updatePermission(id: string, updatePermissionDto: {
    displayName?: string;
    description?: string;
    isActive?: boolean;
    metadata?: any;
  }): Promise<Permission> {
    const permission = await this.getPermissionById(id);

    if (permission.isSystemPermission) {
      throw new BadRequestException('Cannot modify system permissions');
    }

    Object.assign(permission, updatePermissionDto);

    const savedPermission = await this.permissionRepository.save(permission);

    this.logger.logWithContext(`Permission updated: ${savedPermission.name}`, 'PermissionsService');

    return savedPermission;
  }

  async deletePermission(id: string): Promise<void> {
    const permission = await this.getPermissionById(id);

    if (permission.isSystemPermission) {
      throw new BadRequestException('Cannot delete system permissions');
    }

    await this.permissionRepository.delete(id);

    this.logger.logWithContext(`Permission deleted: ${permission.name}`, 'PermissionsService');
  }

  async validatePermissions(permissionNames: string[]): Promise<{ valid: string[]; invalid: string[] }> {
    const valid: string[] = [];
    const invalid: string[] = [];

    for (const name of permissionNames) {
      const permission = await this.getPermissionByName(name);
      if (permission) {
        valid.push(name);
      } else {
        invalid.push(name);
      }
    }

    return { valid, invalid };
  }

  async getPermissionsByRole(roleId: string): Promise<Permission[]> {
    return this.permissionRepository
      .createQueryBuilder('permission')
      .innerJoin('permission.roles', 'role')
      .where('role.id = :roleId', { roleId })
      .andWhere('permission.isActive = true')
      .orderBy('permission.category', 'ASC')
      .addOrderBy('permission.name', 'ASC')
      .getMany();
  }

  async checkUserPermission(userId: string, organizationId: string, permissionName: string): Promise<boolean> {
    const result = await this.permissionRepository
      .createQueryBuilder('permission')
      .innerJoin('permission.roles', 'role')
      .innerJoin('role.organizationMembers', 'member')
      .where('member.userId = :userId', { userId })
      .andWhere('member.organizationId = :organizationId', { organizationId })
      .andWhere('member.status = :status', { status: 'active' })
      .andWhere('permission.name = :permissionName', { permissionName })
      .andWhere('permission.isActive = true')
      .getOne();

    return !!result;
  }

  async getUserPermissions(userId: string, organizationId: string): Promise<Permission[]> {
    return this.permissionRepository
      .createQueryBuilder('permission')
      .innerJoin('permission.roles', 'role')
      .innerJoin('role.organizationMembers', 'member')
      .where('member.userId = :userId', { userId })
      .andWhere('member.organizationId = :organizationId', { organizationId })
      .andWhere('member.status = :status', { status: 'active' })
      .andWhere('permission.isActive = true')
      .orderBy('permission.category', 'ASC')
      .addOrderBy('permission.name', 'ASC')
      .getMany();
  }

  async getPermissionStats(): Promise<any> {
    const total = await this.permissionRepository.count();
    const active = await this.permissionRepository.count({ where: { isActive: true } });
    const system = await this.permissionRepository.count({ where: { isSystemPermission: true } });
    const custom = await this.permissionRepository.count({ where: { isSystemPermission: false } });

    const byCategory = await this.permissionRepository
      .createQueryBuilder('permission')
      .select('permission.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .where('permission.isActive = true')
      .groupBy('permission.category')
      .getRawMany();

    const categoryStats = byCategory.reduce((acc, item) => {
      acc[item.category] = parseInt(item.count);
      return acc;
    }, {});

    return {
      total,
      active,
      inactive: total - active,
      system,
      custom,
      byCategory: categoryStats,
    };
  }

  async searchPermissions(query: {
    search?: string;
    category?: PermissionCategory;
    action?: PermissionAction;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{ permissions: Permission[]; total: number }> {
    const queryBuilder = this.permissionRepository.createQueryBuilder('permission');

    if (query.search) {
      queryBuilder.andWhere(
        '(permission.name ILIKE :search OR permission.displayName ILIKE :search OR permission.description ILIKE :search)',
        { search: `%${query.search}%` }
      );
    }

    if (query.category) {
      queryBuilder.andWhere('permission.category = :category', { category: query.category });
    }

    if (query.action) {
      queryBuilder.andWhere('permission.action = :action', { action: query.action });
    }

    if (query.isActive !== undefined) {
      queryBuilder.andWhere('permission.isActive = :isActive', { isActive: query.isActive });
    }

    queryBuilder.orderBy('permission.category', 'ASC').addOrderBy('permission.name', 'ASC');

    const total = await queryBuilder.getCount();

    if (query.limit) {
      queryBuilder.limit(query.limit);
    }

    if (query.offset) {
      queryBuilder.offset(query.offset);
    }

    const permissions = await queryBuilder.getMany();

    return { permissions, total };
  }

  generatePermissionName(category: PermissionCategory, resource: string, action: PermissionAction): string {
    return `${category}:${resource}:${action}`;
  }

  parsePermissionName(permissionName: string): { category: string; resource: string; action: string } {
    const parts = permissionName.split(':');
    return {
      category: parts[0] || '',
      resource: parts[1] || '',
      action: parts[2] || '',
    };
  }
}
