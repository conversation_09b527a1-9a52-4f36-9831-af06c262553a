# Production Environment Configuration

# Environment
NODE_ENV=production

# Database Configuration - Remote PostgreSQL
DATABASE_HOST=***************
DATABASE_PORT=5432
DATABASE_NAME=delify_db
DATABASE_USER=delify_user
DATABASE_PASSWORD=delify_password

# Redis Configuration (if available)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-for-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-for-production
JWT_REFRESH_EXPIRES_IN=30d

# API Configuration
API_PREFIX=api/v1
PORT=3000

# CORS Configuration
CORS_ORIGIN=https://delify.vn,https://app.delify.vn
CORS_CREDENTIALS=true

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Email Configuration (if using email features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# AI Configuration (if using AI features)
OPENAI_API_KEY=your-openai-api-key
OLLAMA_BASE_URL=http://localhost:11434

# Social Media APIs (if using marketing features)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
