# 🏗️ System Architecture & Design

## 📋 Tổng quan

Documentation về **system architecture**, **design patterns**, và **core system components** của Delify Platform.

## 📚 Architecture Guides

### 🎯 **Core Architecture**
- **[System Architecture](SYSTEM_ARCHITECTURE.md)** - Complete system design và component overview
- **[Database Schema](DATABASE_SCHEMA.md)** - Complete database design và relationships

### 🔐 **Security & Authentication**
- **[Authentication Flow](AUTHENTICATION_FLOW.md)** - RBAC system với JWT và session management
- **[Organization System](ORGANIZATION_SYSTEM.md)** - Team management với roles và permissions

### 🤖 **AI Integration**
- **[AI System Guide](AI_SYSTEM_GUIDE.md)** - Multi-provider AI integration (OpenAI, Grok, Gemini, OLLAMA)

## 🎯 Key Concepts

### System Design Principles
1. **Modular Architecture** - Clear separation of concerns
2. **Scalable Design** - Horizontal và vertical scaling ready
3. **Security First** - Authentication và authorization throughout
4. **Performance Optimized** - Efficient queries và caching
5. **Maintainable Code** - Clean architecture patterns

### Technology Stack
- **Backend**: NestJS, TypeScript, PostgreSQL
- **Authentication**: JWT, Passport, RBAC
- **AI Integration**: OpenAI, Grok, Gemini, OLLAMA
- **Database**: TypeORM, PostgreSQL
- **Testing**: Jest, Supertest
- **Documentation**: Swagger/OpenAPI

**These guides provide deep understanding của system architecture và design decisions trong Delify Platform.** 🏗️✨
