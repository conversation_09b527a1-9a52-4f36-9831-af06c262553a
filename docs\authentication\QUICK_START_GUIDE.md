# Quick Start Guide - Authentication System

## Quick Overview

The authentication system supports 4 security methods and uses refresh tokens for enhanced security:

- **DISABLED**: Direct login (email/password only)
- **EMAIL_VERIFICATION**: Email verification code (6 digits, 5 minutes)
- **TWO_FACTOR_AUTH**: TOTP with authenticator app (Google Authenticator, Authy)
- **FIXED_CODE**: User-defined fixed code (6-8 digits)

## Token System

- **Access Token**: 15 minutes, used for API calls
- **Refresh Token**: 7 days, used to refresh access token

## Core API Endpoints

### Authentication
```
POST /auth/register          # Register
POST /auth/login             # Login (may be partial)
POST /auth/verify-login      # Verify login
POST /auth/refresh           # Refresh token
POST /auth/revoke            # Logout (revoke token)
POST /auth/revoke-all        # Logout from all devices
```

### Security Methods
```
GET  /auth/security-method   # View current method
POST /auth/security-method   # Change method
POST /auth/change-fixed-code # Change fixed code
```

## Basic Login Flow

### 1. Security Method = DISABLED
```javascript
const response = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const { user, accessToken, refreshToken } = await response.json();
// Store tokens and use immediately
```

### 2. Security Method Enabled (EMAIL/2FA/FIXED_CODE)
```javascript
// Step 1: Login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.requiresVerification) {
  // Step 2: Verify
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: '123456' // User input
    })
  });

  const { user, accessToken, refreshToken } = await verifyResponse.json();
  // Store tokens and use
}
```

## Token Management

### Automatic Token Refresh
```javascript
// Axios interceptor
axios.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401 && !error.config._retry) {
      error.config._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken })
        });

        const tokens = await response.json();
        localStorage.setItem('accessToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);

        error.config.headers.Authorization = `Bearer ${tokens.accessToken}`;
        return axios(error.config);
      } catch (refreshError) {
        // Redirect to login
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);
```

### Manual Token Refresh
```javascript
async function refreshTokens() {
  const refreshToken = localStorage.getItem('refreshToken');

  const response = await fetch('/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });

  if (response.ok) {
    const tokens = await response.json();
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
    return tokens;
  } else {
    // Redirect to login
    window.location.href = '/login';
  }
}
```

## Setup Security Methods

### Enable Email Verification
```javascript
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'email_verification'
  })
});
```

### Enable Two-Factor Authentication
```javascript
// Step 1: Create QR code
const setupResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth'
  })
});

const { qrCodeUrl, manualEntryKey } = await setupResponse.json();
// Display QR code for user to scan

// Step 2: Verify and enable
const enableResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth',
    verificationCode: '123456' // Code from authenticator app
  })
});
```

### Enable Fixed Code
```javascript
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'fixed_code',
    fixedCode: '789123' // 6-8 digits
  })
});
```

## Error Handling

### Common Error Responses
```javascript
// 401 - Invalid credentials
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}

// 401 - Invalid verification code
{
  "statusCode": 401,
  "message": "Invalid verification code",
  "error": "Unauthorized"
}

// 400 - Weak fixed code
{
  "statusCode": 400,
  "message": "Fixed code is too weak. Avoid sequential numbers, repeated digits, or common patterns.",
  "error": "Bad Request"
}

// 429 - Rate limited
{
  "statusCode": 429,
  "message": "Too many requests. Please try again later.",
  "error": "Too Many Requests",
  "retryAfter": 60
}
```

### Error Handling Example
```javascript
try {
  const response = await authAPI.login(credentials);
  handleSuccessfulLogin(response);
} catch (error) {
  switch (error.status) {
    case 401:
      showError('Invalid email/password');
      break;
    case 403:
      showError('Account has been locked');
      break;
    case 429:
      showError(`Too many attempts. Please wait ${error.retryAfter} seconds`);
      break;
    default:
      showError('An error occurred. Please try again');
  }
}
```

## Database Migration

```bash
# Run migrations to create refresh token fields
npm run migration:run
```

## Security Best Practices

1. **Always use HTTPS** in production
2. **Store access token** in localStorage/memory
3. **Store refresh token** in httpOnly cookie (recommended) or localStorage
4. **Implement rate limiting** for login attempts
5. **Monitor authentication events** to detect suspicious activities
6. **Regular cleanup** of expired tokens in database
7. **Validate input** on both client and server side

## Testing

```bash
# Test authentication service
npm test -- auth.service.spec.ts

# Test entire system
npm test
```

## Troubleshooting

### Tokens expiring continuously
- Check system time synchronization (important for 2FA)
- Verify JWT secret configuration
- Check token expiration settings

### Email verification not working
- Check email service configuration
- Verify SMTP settings
- Check spam folder

### 2FA code incorrect
- Ensure device time synchronization
- Verify QR code scan is correct
- Check manual entry key

---

**For more details, refer to [Authentication Flow Guide](./AUTHENTICATION_FLOW_GUIDE.md) and [Authentication Diagrams](./AUTHENTICATION_DIAGRAMS.md).**
