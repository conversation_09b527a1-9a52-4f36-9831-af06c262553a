import { Injectable, Logger, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, In } from 'typeorm';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { RoleHierarchy, InheritanceType } from '../entities/role-hierarchy.entity';
import { RBACService } from './rbac.service';
import { CreateRoleDto, UpdateRoleDto, AssignPermissionsDto } from '../dto/role.dto';

/**
 * Role Service - Dịch vụ quản lý vai trò
 * Role Service - Role management service
 */
@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);

  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(RoleHierarchy)
    private readonly roleHierarchyRepository: Repository<RoleHierarchy>,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Tạo vai trò mới - Create new role
   */
  async createRole(createRoleDto: CreateRoleDto, createdBy: string): Promise<Role> {
    // Kiểm tra quyền tạo role
    const canCreate = await this.rbacService.checkPermission(createdBy, 'ROLE_MANAGEMENT_CREATE');
    if (!canCreate.allowed) {
      throw new ForbiddenException('You do not have permission to create roles');
    }

    // Kiểm tra tên role đã tồn tại
    const existingRole = await this.roleRepository.findOne({
      where: { name: createRoleDto.name },
    });

    if (existingRole) {
      throw new ConflictException('Role name already exists');
    }

    // Kiểm tra parent role nếu có
    let parentRole: Role | null = null;
    if (createRoleDto.parentRoleId) {
      parentRole = await this.roleRepository.findOne({
        where: { id: createRoleDto.parentRoleId },
      });

      if (!parentRole) {
        throw new NotFoundException('Parent role not found');
      }

      // Kiểm tra level hợp lệ (level phải lớn hơn parent)
      if (createRoleDto.level <= parentRole.level) {
        throw new ForbiddenException('Role level must be greater than parent role level');
      }
    }

    // Kiểm tra quyền tạo role ở level này
    const creatorPermissions = await this.rbacService.getUserPermissions(createdBy);
    if (createRoleDto.level <= creatorPermissions.roleLevel && !creatorPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot create role at or above your own level');
    }

    // Tạo role mới
    const role = this.roleRepository.create({
      ...createRoleDto,
      createdBy,
    });

    const savedRole = await this.roleRepository.save(role);

    // Tạo hierarchy nếu có parent
    if (parentRole) {
      await this.createRoleHierarchy(parentRole.id, savedRole.id, InheritanceType.FULL);
    }

    this.logger.log(`Role ${savedRole.name} created by user ${createdBy}`);
    return savedRole;
  }

  /**
   * Cập nhật vai trò - Update role
   */
  async updateRole(id: string, updateRoleDto: UpdateRoleDto, updatedBy: string): Promise<Role> {
    // Kiểm tra quyền cập nhật
    const canUpdate = await this.rbacService.checkPermission(updatedBy, 'ROLE_MANAGEMENT_UPDATE');
    if (!canUpdate.allowed) {
      throw new ForbiddenException('You do not have permission to update roles');
    }

    const role = await this.roleRepository.findOne({ where: { id } });
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Không thể cập nhật role MASTER_ACCOUNT
    if (role.name === 'MASTER_ACCOUNT') {
      throw new ForbiddenException('Cannot update MASTER_ACCOUNT role');
    }

    // Kiểm tra quyền cập nhật role này
    const updaterPermissions = await this.rbacService.getUserPermissions(updatedBy);
    if (role.level < updaterPermissions.roleLevel && !updaterPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot update role higher than your own level');
    }

    // Kiểm tra tên role nếu thay đổi
    if (updateRoleDto.name && updateRoleDto.name !== role.name) {
      const existingRole = await this.roleRepository.findOne({
        where: { name: updateRoleDto.name },
      });

      if (existingRole) {
        throw new ConflictException('Role name already exists');
      }
    }

    // Cập nhật role
    Object.assign(role, updateRoleDto);
    const updatedRole = await this.roleRepository.save(role);

    this.logger.log(`Role ${updatedRole.name} updated by user ${updatedBy}`);
    return updatedRole;
  }

  /**
   * Xóa vai trò - Delete role
   */
  async deleteRole(id: string, deletedBy: string): Promise<void> {
    // Kiểm tra quyền xóa
    const canDelete = await this.rbacService.checkPermission(deletedBy, 'ROLE_MANAGEMENT_DELETE');
    if (!canDelete.allowed) {
      throw new ForbiddenException('You do not have permission to delete roles');
    }

    const role = await this.roleRepository.findOne({ where: { id } });
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Không thể xóa role hệ thống
    if (role.isSystemRole) {
      throw new ForbiddenException('Cannot delete system role');
    }

    // Kiểm tra quyền xóa role này
    const deleterPermissions = await this.rbacService.getUserPermissions(deletedBy);
    if (role.level < deleterPermissions.roleLevel && !deleterPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot delete role higher than your own level');
    }

    // Kiểm tra role có đang được sử dụng không
    const userRoleCount = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoin('role.userRoles', 'userRole')
      .where('role.id = :id', { id })
      .andWhere('userRole.isActive = :isActive', { isActive: true })
      .getCount();

    if (userRoleCount > 0) {
      throw new ForbiddenException('Cannot delete role that is currently assigned to users');
    }

    await this.roleRepository.remove(role);
    this.logger.log(`Role ${role.name} deleted by user ${deletedBy}`);
  }

  /**
   * Lấy danh sách vai trò - Get roles list
   */
  async getRoles(options?: FindManyOptions<Role>): Promise<Role[]> {
    return this.roleRepository.find({
      ...options,
      relations: ['parentRole', 'childRoles', 'creator'],
      order: { level: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Lấy vai trò theo ID - Get role by ID
   */
  async getRoleById(id: string): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['parentRole', 'childRoles', 'creator', 'rolePermissions', 'rolePermissions.permission'],
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  /**
   * Gán quyền cho vai trò - Assign permissions to role
   */
  async assignPermissionsToRole(
    roleId: string,
    assignPermissionsDto: AssignPermissionsDto,
    assignedBy: string,
  ): Promise<void> {
    // Kiểm tra quyền gán permission
    const canAssign = await this.rbacService.checkPermission(assignedBy, 'ROLE_MANAGEMENT_ASSIGN');
    if (!canAssign.allowed) {
      throw new ForbiddenException('You do not have permission to assign permissions');
    }

    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Kiểm tra quyền gán permission cho role này
    const assignerPermissions = await this.rbacService.getUserPermissions(assignedBy);
    if (role.level < assignerPermissions.roleLevel && !assignerPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot assign permissions to role higher than your own level');
    }

    // Kiểm tra permissions tồn tại
    const permissions = await this.permissionRepository.findByIds(assignPermissionsDto.permissionIds);
    if (permissions.length !== assignPermissionsDto.permissionIds.length) {
      throw new NotFoundException('Some permissions not found');
    }

    // Xóa permissions cũ nếu replace = true
    if (assignPermissionsDto.replace) {
      await this.rolePermissionRepository.delete({ roleId });
    }

    // Thêm permissions mới
    for (const permission of permissions) {
      const existingRolePermission = await this.rolePermissionRepository.findOne({
        where: { roleId, permissionId: permission.id },
      });

      if (!existingRolePermission) {
        const rolePermission = this.rolePermissionRepository.create({
          roleId,
          permissionId: permission.id,
          grantedBy: assignedBy,
        });

        await this.rolePermissionRepository.save(rolePermission);
      }
    }

    // Invalidate cache
    await this.rbacService.invalidateAllCaches();

    this.logger.log(`Permissions assigned to role ${role.name} by user ${assignedBy}`);
  }

  /**
   * Thu hồi quyền từ vai trò - Revoke permissions from role
   */
  async revokePermissionsFromRole(
    roleId: string,
    permissionIds: string[],
    revokedBy: string,
  ): Promise<void> {
    // Kiểm tra quyền thu hồi permission
    const canRevoke = await this.rbacService.checkPermission(revokedBy, 'ROLE_MANAGEMENT_ASSIGN');
    if (!canRevoke.allowed) {
      throw new ForbiddenException('You do not have permission to revoke permissions');
    }

    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Không thể thu hồi permission từ MASTER_ACCOUNT
    if (role.name === 'MASTER_ACCOUNT') {
      throw new ForbiddenException('Cannot revoke permissions from MASTER_ACCOUNT role');
    }

    // Kiểm tra quyền thu hồi permission từ role này
    const revokerPermissions = await this.rbacService.getUserPermissions(revokedBy);
    if (role.level < revokerPermissions.roleLevel && !revokerPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot revoke permissions from role higher than your own level');
    }

    // Thu hồi permissions
    await this.rolePermissionRepository.delete({
      roleId,
      permissionId: In(permissionIds),
    });

    // Invalidate cache
    await this.rbacService.invalidateAllCaches();

    this.logger.log(`Permissions revoked from role ${role.name} by user ${revokedBy}`);
  }

  /**
   * Tạo phân cấp vai trò - Create role hierarchy
   */
  private async createRoleHierarchy(
    parentRoleId: string,
    childRoleId: string,
    inheritanceType: InheritanceType = InheritanceType.FULL,
  ): Promise<RoleHierarchy> {
    const roleHierarchy = this.roleHierarchyRepository.create({
      parentRoleId,
      childRoleId,
      inheritanceType,
    });

    return this.roleHierarchyRepository.save(roleHierarchy);
  }

  /**
   * Lấy quyền của vai trò - Get role permissions
   */
  async getRolePermissions(roleId: string): Promise<Permission[]> {
    const rolePermissions = await this.rolePermissionRepository.find({
      where: { roleId },
      relations: ['permission'],
    });

    return rolePermissions.map(rp => rp.permission);
  }

  /**
   * Lấy phân cấp vai trò - Get role hierarchy
   */
  async getRoleHierarchy(): Promise<RoleHierarchy[]> {
    return this.roleHierarchyRepository.find({
      relations: ['parentRole', 'childRole'],
      order: { parentRole: { level: 'ASC' } },
    });
  }
}
