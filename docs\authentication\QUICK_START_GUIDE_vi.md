# Hướng Dẫn Nhanh - <PERSON><PERSON> Thống Xác Thực

## Tổng Quan <PERSON>hanh

Hệ thống xác thực hỗ trợ 4 phương thức bảo mật và sử dụng refresh token để tăng cường bảo mật:

- **DISABLED**: <PERSON><PERSON><PERSON> nhập trực tiếp (chỉ email/password)
- **EMAIL_VERIFICATION**: Mã xác minh qua email (6 chữ số, 5 phút)
- **TWO_FACTOR_AUTH**: TOTP với authenticator app (Google Authenticator, Authy)
- **FIXED_CODE**: Mã cố định do user đặt (6-8 chữ số)

## Token System

- **Access Token**: 15 phút, dùng cho API calls
- **Refresh Token**: 7 ngày, dùng để làm mới access token

## API Endpoints Chính

### Authentication
```
POST /auth/register          # Đăng ký
POST /auth/login             # Đăng nhập (c<PERSON> thể partial)
POST /auth/verify-login      # X<PERSON>c minh đăng nhập
POST /auth/refresh           # Làm mới token
POST /auth/revoke            # Logout (revoke token)
POST /auth/revoke-all        # Logout tất cả devices
```

### Security Methods
```
GET  /auth/security-method   # Xem phương thức hiện tại
POST /auth/security-method   # Thay đổi phương thức
POST /auth/change-fixed-code # Đổi fixed code
```

## Luồng Đăng Nhập Cơ Bản

### 1. Security Method = DISABLED
```javascript
const response = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const { user, accessToken, refreshToken } = await response.json();
// Lưu tokens và sử dụng ngay
```

### 2. Security Method Enabled (EMAIL/2FA/FIXED_CODE)
```javascript
// Bước 1: Login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.requiresVerification) {
  // Bước 2: Verify
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: '123456' // User input
    })
  });
  
  const { user, accessToken, refreshToken } = await verifyResponse.json();
  // Lưu tokens và sử dụng
}
```

## Quản Lý Token

### Automatic Token Refresh
```javascript
// Axios interceptor
axios.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401 && !error.config._retry) {
      error.config._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken })
        });
        
        const tokens = await response.json();
        localStorage.setItem('accessToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);
        
        error.config.headers.Authorization = `Bearer ${tokens.accessToken}`;
        return axios(error.config);
      } catch (refreshError) {
        // Redirect to login
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);
```

### Manual Token Refresh
```javascript
async function refreshTokens() {
  const refreshToken = localStorage.getItem('refreshToken');
  
  const response = await fetch('/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });
  
  if (response.ok) {
    const tokens = await response.json();
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
    return tokens;
  } else {
    // Redirect to login
    window.location.href = '/login';
  }
}
```

## Setup Security Methods

### Enable Email Verification
```javascript
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'email_verification'
  })
});
```

### Enable Two-Factor Authentication
```javascript
// Bước 1: Tạo QR code
const setupResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth'
  })
});

const { qrCodeUrl, manualEntryKey } = await setupResponse.json();
// Hiển thị QR code cho user scan

// Bước 2: Verify và enable
const enableResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth',
    verificationCode: '123456' // Code từ authenticator app
  })
});
```

### Enable Fixed Code
```javascript
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'fixed_code',
    fixedCode: '789123' // 6-8 chữ số
  })
});
```

## Error Handling

### Common Error Responses
```javascript
// 401 - Invalid credentials
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}

// 401 - Invalid verification code
{
  "statusCode": 401,
  "message": "Invalid verification code",
  "error": "Unauthorized"
}

// 400 - Weak fixed code
{
  "statusCode": 400,
  "message": "Fixed code is too weak. Avoid sequential numbers, repeated digits, or common patterns.",
  "error": "Bad Request"
}

// 429 - Rate limited
{
  "statusCode": 429,
  "message": "Too many requests. Please try again later.",
  "error": "Too Many Requests",
  "retryAfter": 60
}
```

### Error Handling Example
```javascript
try {
  const response = await authAPI.login(credentials);
  handleSuccessfulLogin(response);
} catch (error) {
  switch (error.status) {
    case 401:
      showError('Email/password không đúng');
      break;
    case 403:
      showError('Tài khoản đã bị khóa');
      break;
    case 429:
      showError(`Quá nhiều lần thử. Vui lòng đợi ${error.retryAfter} giây`);
      break;
    default:
      showError('Có lỗi xảy ra. Vui lòng thử lại');
  }
}
```

## Database Migration

```bash
# Chạy migrations để tạo refresh token fields
npm run migration:run
```

## Security Best Practices

1. **Luôn sử dụng HTTPS** trong production
2. **Lưu access token** trong localStorage/memory
3. **Lưu refresh token** trong httpOnly cookie (khuyến nghị) hoặc localStorage
4. **Implement rate limiting** cho login attempts
5. **Monitor authentication events** để phát hiện hoạt động đáng ngờ
6. **Regular cleanup** expired tokens trong database
7. **Validate input** ở cả client và server side

## Testing

```bash
# Test authentication service
npm test -- auth.service.spec.ts

# Test toàn bộ hệ thống
npm test
```

## Troubleshooting

### Token hết hạn liên tục
- Kiểm tra system time đồng bộ (quan trọng cho 2FA)
- Verify JWT secret configuration
- Check token expiration settings

### Email verification không hoạt động
- Kiểm tra email service configuration
- Verify SMTP settings
- Check spam folder

### 2FA code không đúng
- Đảm bảo device time đồng bộ
- Verify QR code scan đúng
- Check manual entry key

---

**Để biết thêm chi tiết, tham khảo [Authentication Flow Guide](./AUTHENTICATION_FLOW_GUIDE.md) và [Authentication Diagrams](./AUTHENTICATION_DIAGRAMS.md).**
