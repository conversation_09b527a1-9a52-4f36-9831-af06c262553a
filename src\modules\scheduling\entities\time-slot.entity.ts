import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum SlotType {
  AVAILABLE = 'available',
  BLOCKED = 'blocked',
  RECURRING = 'recurring',
}

export enum RecurrenceType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

@Entity('time_slots')
@Index(['userId', 'isAvailable'])
@Index(['startTime', 'endTime'])
export class TimeSlot {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  startTime: Date;

  @Column()
  endTime: Date;

  @Column({
    type: 'enum',
    enum: SlotType,
    default: SlotType.AVAILABLE,
  })
  slotType: SlotType;

  @Column({ default: true })
  isAvailable: boolean;

  @Column({ nullable: true })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: false })
  isRecurring: boolean;

  @Column({
    type: 'enum',
    enum: RecurrenceType,
    nullable: true,
  })
  recurrenceType: RecurrenceType;

  @Column({ type: 'jsonb', nullable: true })
  recurrenceSettings: {
    interval?: number; // every N days/weeks/months
    daysOfWeek?: number[]; // 0-6 for weekly recurrence
    endDate?: string;
    maxOccurrences?: number;
  };

  @Column({ nullable: true })
  parentSlotId: string; // for recurring slots

  @Column({ type: 'jsonb', nullable: true })
  allowedServices: string[]; // service type IDs that can use this slot

  @Column({ default: 1 })
  maxBookings: number; // how many appointments can be booked in this slot

  @Column({ default: 0 })
  currentBookings: number;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    bufferBefore?: number; // minutes
    bufferAfter?: number; // minutes
    autoConfirm?: boolean;
    requireApproval?: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isFullyBooked(): boolean {
    return this.currentBookings >= this.maxBookings;
  }

  get canAcceptBooking(): boolean {
    return this.isAvailable && !this.isFullyBooked && this.startTime > new Date();
  }

  get duration(): number {
    return Math.floor((this.endTime.getTime() - this.startTime.getTime()) / 60000); // in minutes
  }

  get isInPast(): boolean {
    return this.endTime < new Date();
  }
}
