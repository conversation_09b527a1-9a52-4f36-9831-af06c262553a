import { Injectable } from '@nestjs/common';
import { createHash } from 'crypto';

/**
 * Interface cho thông tin thiết bị
 * Interface for device information
 */
export interface DeviceInfo {
  userAgent: string;
  ipAddress: string;
  acceptLanguage?: string;
  acceptEncoding?: string;
  acceptCharset?: string;
  connection?: string;
  dnt?: string; // Do Not Track
  upgradeInsecureRequests?: string;
  secFetchSite?: string;
  secFetchMode?: string;
  secFetchUser?: string;
  secFetchDest?: string;
  viewport?: {
    width: number;
    height: number;
  };
  timezone?: string;
  platform?: string;
  cookieEnabled?: boolean;
  javaEnabled?: boolean;
  plugins?: string[];
  mimeTypes?: string[];
  webgl?: string;
  canvas?: string;
  audioContext?: string;
}

/**
 * Interface cho kết quả phân tích thiết bị
 * Interface for device analysis result
 */
export interface DeviceAnalysis {
  deviceId: string;
  fingerprint: string;
  deviceType: 'DESKTOP' | 'MOBILE' | 'TABLET' | 'BOT' | 'UNKNOWN';
  browser: {
    name: string;
    version: string;
    engine: string;
  };
  os: {
    name: string;
    version: string;
  };
  isSuspicious: boolean;
  suspiciousReasons: string[];
  trustScore: number; // 0-100
  isBot: boolean;
}

/**
 * DeviceFingerprintService - Dịch vụ tạo dấu vân tay thiết bị
 * DeviceFingerprintService - Device fingerprinting service
 */
@Injectable()
export class DeviceFingerprintService {
  
  /**
   * Tạo device fingerprint từ thông tin thiết bị
   * Generate device fingerprint from device information
   */
  generateFingerprint(deviceInfo: DeviceInfo): string {
    const fingerprintData = {
      userAgent: this.normalizeUserAgent(deviceInfo.userAgent),
      acceptLanguage: deviceInfo.acceptLanguage || '',
      acceptEncoding: deviceInfo.acceptEncoding || '',
      timezone: deviceInfo.timezone || '',
      platform: deviceInfo.platform || '',
      viewport: deviceInfo.viewport || { width: 0, height: 0 },
      cookieEnabled: deviceInfo.cookieEnabled || false,
      javaEnabled: deviceInfo.javaEnabled || false,
      plugins: (deviceInfo.plugins || []).sort(),
      mimeTypes: (deviceInfo.mimeTypes || []).sort(),
      webgl: deviceInfo.webgl || '',
      canvas: deviceInfo.canvas || '',
      audioContext: deviceInfo.audioContext || '',
    };

    const fingerprintString = JSON.stringify(fingerprintData);
    return createHash('sha256').update(fingerprintString).digest('hex');
  }

  /**
   * Tạo device ID đơn giản từ User Agent và IP
   * Generate simple device ID from User Agent and IP
   */
  generateDeviceId(userAgent: string, ipAddress: string): string {
    const deviceData = {
      userAgent: this.normalizeUserAgent(userAgent),
      ipAddress: this.hashIP(ipAddress),
    };

    const deviceString = JSON.stringify(deviceData);
    return createHash('md5').update(deviceString).digest('hex');
  }

  /**
   * Phân tích thông tin thiết bị
   * Analyze device information
   */
  analyzeDevice(deviceInfo: DeviceInfo): DeviceAnalysis {
    const fingerprint = this.generateFingerprint(deviceInfo);
    const deviceId = this.generateDeviceId(deviceInfo.userAgent, deviceInfo.ipAddress);
    const browser = this.parseBrowser(deviceInfo.userAgent);
    const os = this.parseOS(deviceInfo.userAgent);
    const deviceType = this.detectDeviceType(deviceInfo.userAgent);
    const isBot = this.detectBot(deviceInfo.userAgent);
    
    const suspiciousReasons: string[] = [];
    let trustScore = 100;

    // Check for suspicious patterns
    if (isBot) {
      suspiciousReasons.push('Bot detected');
      trustScore -= 50;
    }

    if (this.hasInconsistentHeaders(deviceInfo)) {
      suspiciousReasons.push('Inconsistent headers');
      trustScore -= 20;
    }

    if (this.hasUnusualUserAgent(deviceInfo.userAgent)) {
      suspiciousReasons.push('Unusual user agent');
      trustScore -= 15;
    }

    if (this.hasSuspiciousFeatures(deviceInfo)) {
      suspiciousReasons.push('Suspicious browser features');
      trustScore -= 10;
    }

    return {
      deviceId,
      fingerprint,
      deviceType,
      browser,
      os,
      isSuspicious: suspiciousReasons.length > 0,
      suspiciousReasons,
      trustScore: Math.max(0, trustScore),
      isBot,
    };
  }

  /**
   * So sánh hai fingerprint
   * Compare two fingerprints
   */
  compareFingerprints(fingerprint1: string, fingerprint2: string): {
    match: boolean;
    similarity: number;
  } {
    if (fingerprint1 === fingerprint2) {
      return { match: true, similarity: 100 };
    }

    // Calculate similarity based on common characters
    const similarity = this.calculateSimilarity(fingerprint1, fingerprint2);
    
    return {
      match: similarity > 90, // Consider match if > 90% similar
      similarity,
    };
  }

  /**
   * Kiểm tra thiết bị có đáng ngờ không
   * Check if device is suspicious
   */
  isSuspiciousDevice(
    currentFingerprint: string,
    storedFingerprint: string,
    currentIP: string,
    storedIP: string,
  ): boolean {
    const fingerprintMatch = this.compareFingerprints(currentFingerprint, storedFingerprint);
    const ipChanged = currentIP !== storedIP;

    // Suspicious if fingerprint doesn't match and IP changed
    return !fingerprintMatch.match && ipChanged;
  }

  /**
   * Normalize User Agent để tăng tính nhất quán
   * Normalize User Agent for consistency
   */
  private normalizeUserAgent(userAgent: string): string {
    return userAgent
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim()
      .toLowerCase();
  }

  /**
   * Hash IP address để bảo vệ privacy
   * Hash IP address for privacy protection
   */
  private hashIP(ipAddress: string): string {
    return createHash('sha256').update(ipAddress).digest('hex').substring(0, 16);
  }

  /**
   * Parse thông tin browser từ User Agent
   * Parse browser information from User Agent
   */
  private parseBrowser(userAgent: string): { name: string; version: string; engine: string } {
    const ua = userAgent.toLowerCase();

    // Chrome
    if (ua.includes('chrome') && !ua.includes('edg')) {
      const version = this.extractVersion(ua, 'chrome/');
      return { name: 'Chrome', version, engine: 'Blink' };
    }

    // Edge
    if (ua.includes('edg')) {
      const version = this.extractVersion(ua, 'edg/');
      return { name: 'Edge', version, engine: 'Blink' };
    }

    // Firefox
    if (ua.includes('firefox')) {
      const version = this.extractVersion(ua, 'firefox/');
      return { name: 'Firefox', version, engine: 'Gecko' };
    }

    // Safari
    if (ua.includes('safari') && !ua.includes('chrome')) {
      const version = this.extractVersion(ua, 'version/');
      return { name: 'Safari', version, engine: 'WebKit' };
    }

    return { name: 'Unknown', version: 'Unknown', engine: 'Unknown' };
  }

  /**
   * Parse thông tin OS từ User Agent
   * Parse OS information from User Agent
   */
  private parseOS(userAgent: string): { name: string; version: string } {
    const ua = userAgent.toLowerCase();

    if (ua.includes('windows nt')) {
      const version = this.extractVersion(ua, 'windows nt ');
      return { name: 'Windows', version };
    }

    if (ua.includes('mac os x')) {
      const version = this.extractVersion(ua, 'mac os x ');
      return { name: 'macOS', version };
    }

    if (ua.includes('linux')) {
      return { name: 'Linux', version: 'Unknown' };
    }

    if (ua.includes('android')) {
      const version = this.extractVersion(ua, 'android ');
      return { name: 'Android', version };
    }

    if (ua.includes('iphone') || ua.includes('ipad')) {
      const version = this.extractVersion(ua, 'os ');
      return { name: 'iOS', version };
    }

    return { name: 'Unknown', version: 'Unknown' };
  }

  /**
   * Detect device type từ User Agent
   * Detect device type from User Agent
   */
  private detectDeviceType(userAgent: string): 'DESKTOP' | 'MOBILE' | 'TABLET' | 'BOT' | 'UNKNOWN' {
    const ua = userAgent.toLowerCase();

    if (this.detectBot(userAgent)) {
      return 'BOT';
    }

    if (ua.includes('mobile') || ua.includes('iphone') || ua.includes('android')) {
      return 'MOBILE';
    }

    if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'TABLET';
    }

    if (ua.includes('windows') || ua.includes('macintosh') || ua.includes('linux')) {
      return 'DESKTOP';
    }

    return 'UNKNOWN';
  }

  /**
   * Detect bot từ User Agent
   * Detect bot from User Agent
   */
  private detectBot(userAgent: string): boolean {
    const ua = userAgent.toLowerCase();
    const botPatterns = [
      'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python',
      'java', 'go-http-client', 'okhttp', 'apache-httpclient', 'postman',
      'insomnia', 'facebookexternalhit', 'twitterbot', 'linkedinbot',
      'googlebot', 'bingbot', 'slackbot', 'whatsapp', 'telegram',
    ];

    return botPatterns.some(pattern => ua.includes(pattern));
  }

  /**
   * Extract version từ User Agent string
   * Extract version from User Agent string
   */
  private extractVersion(userAgent: string, pattern: string): string {
    const index = userAgent.indexOf(pattern);
    if (index === -1) return 'Unknown';

    const versionStart = index + pattern.length;
    const versionEnd = userAgent.indexOf(' ', versionStart);
    const version = userAgent.substring(versionStart, versionEnd === -1 ? undefined : versionEnd);

    return version.split('.').slice(0, 2).join('.'); // Return major.minor version
  }

  /**
   * Kiểm tra headers có bất thường không
   * Check for inconsistent headers
   */
  private hasInconsistentHeaders(deviceInfo: DeviceInfo): boolean {
    // Check if mobile user agent but no mobile-specific headers
    if (deviceInfo.userAgent.toLowerCase().includes('mobile')) {
      return !deviceInfo.userAgent.toLowerCase().includes('mobile');
    }

    return false;
  }

  /**
   * Kiểm tra User Agent có bất thường không
   * Check for unusual user agent
   */
  private hasUnusualUserAgent(userAgent: string): boolean {
    // Very short or very long user agents are suspicious
    if (userAgent.length < 20 || userAgent.length > 500) {
      return true;
    }

    // Missing common browser identifiers
    const commonBrowsers = ['chrome', 'firefox', 'safari', 'edge', 'opera'];
    const hasCommonBrowser = commonBrowsers.some(browser => 
      userAgent.toLowerCase().includes(browser)
    );

    return !hasCommonBrowser;
  }

  /**
   * Kiểm tra features có đáng ngờ không
   * Check for suspicious browser features
   */
  private hasSuspiciousFeatures(deviceInfo: DeviceInfo): boolean {
    // No JavaScript but has canvas/WebGL data (suspicious)
    if (!deviceInfo.javaEnabled && (deviceInfo.canvas || deviceInfo.webgl)) {
      return true;
    }

    // Unusual plugin combinations
    if (deviceInfo.plugins && deviceInfo.plugins.length > 50) {
      return true;
    }

    return false;
  }

  /**
   * Tính toán độ tương đồng giữa hai string
   * Calculate similarity between two strings
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 100;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return ((longer.length - editDistance) / longer.length) * 100;
  }

  /**
   * Tính Levenshtein distance
   * Calculate Levenshtein distance
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}
