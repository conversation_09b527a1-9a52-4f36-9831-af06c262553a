import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan } from 'typeorm';
import { User, UserRole, UserStatus } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UtilsService } from '../../common/services/utils.service';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private utilsService: UtilsService,
    private logger: LoggerService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const { email, username, password, ...userData } = createUserDto;

    // Check if user already exists
    const existingUser = await this.usersRepository.findOne({
      where: [{ email }, { username }],
    });

    if (existingUser) {
      throw new ConflictException('User with this email or username already exists');
    }

    // Hash password
    const hashedPassword = await this.utilsService.hashPassword(password);

    // Create user
    const user = this.usersRepository.create({
      ...userData,
      email,
      username,
      password: hashedPassword,
      emailVerificationToken: this.utilsService.generateRandomString(),
    });

    const savedUser = await this.usersRepository.save(user);
    this.logger.logWithContext(`User created: ${savedUser.email}`, 'UsersService');

    return savedUser;
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ users: User[]; total: number }> {
    const [users, total] = await this.usersRepository.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return { users, total };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { username } });
  }

  async findByEmailOrUsername(emailOrUsername: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: [
        { email: emailOrUsername },
        { username: emailOrUsername },
      ],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // If password is being updated, hash it
    if (updateUserDto.password) {
      updateUserDto.password = await this.utilsService.hashPassword(updateUserDto.password);
    }

    // If email or username is being updated, check for conflicts
    if (updateUserDto.email || updateUserDto.username) {
      const existingUser = await this.usersRepository.findOne({
        where: [
          { email: updateUserDto.email },
          { username: updateUserDto.username },
        ],
      });

      if (existingUser && existingUser.id !== id) {
        throw new ConflictException('User with this email or username already exists');
      }
    }

    Object.assign(user, updateUserDto);
    const updatedUser = await this.usersRepository.save(user);

    this.logger.logWithContext(`User updated: ${updatedUser.email}`, 'UsersService');
    return updatedUser;
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.usersRepository.remove(user);
    this.logger.logWithContext(`User deleted: ${user.email}`, 'UsersService');
  }

  async updateLastLogin(id: string, ip: string): Promise<void> {
    await this.usersRepository.update(id, {
      lastLoginAt: new Date(),
      lastLoginIp: ip,
    });
  }

  async verifyEmail(token: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw new NotFoundException('Invalid verification token');
    }

    user.emailVerified = true;
    user.emailVerificationToken = null;

    return this.usersRepository.save(user);
  }

  async setPasswordResetToken(email: string): Promise<string> {
    const user = await this.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const resetToken = this.utilsService.generateRandomString();
    const resetExpires = this.utilsService.addTimeToDate(new Date(), 1, 'hour');

    user.passwordResetToken = resetToken;
    user.passwordResetExpires = resetExpires;

    await this.usersRepository.save(user);
    return resetToken;
  }

  async resetPassword(token: string, newPassword: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { passwordResetToken: token },
    });

    if (!user || this.utilsService.isDateInPast(user.passwordResetExpires)) {
      throw new NotFoundException('Invalid or expired reset token');
    }

    user.password = await this.utilsService.hashPassword(newPassword);
    user.passwordResetToken = null;
    user.passwordResetExpires = null;

    return this.usersRepository.save(user);
  }

  async updateSocialAccounts(id: string, socialAccounts: any): Promise<User> {
    const user = await this.findOne(id);
    user.socialAccounts = { ...user.socialAccounts, ...socialAccounts };
    return this.usersRepository.save(user);
  }

  async changeUserStatus(id: string, status: UserStatus): Promise<User> {
    const user = await this.findOne(id);
    user.status = status;
    return this.usersRepository.save(user);
  }

  async changeUserRole(id: string, role: UserRole): Promise<User> {
    const user = await this.findOne(id);
    user.role = role;
    return this.usersRepository.save(user);
  }

  /**
   * Set verification code for user
   */
  async setVerificationCode(id: string, code: string): Promise<void> {
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    await this.usersRepository.update(id, {
      verificationCode: code,
      verificationCodeExpires: expiresAt,
    });
  }

  /**
   * Clear verification code for user
   */
  async clearVerificationCode(id: string): Promise<void> {
    await this.usersRepository.update(id, {
      verificationCode: null,
      verificationCodeExpires: null,
    });
  }

  /**
   * Set fixed code for user
   */
  async setFixedCode(id: string, hashedCode: string): Promise<void> {
    await this.usersRepository.update(id, {
      fixedCode: hashedCode,
    });
  }

  /**
   * Clear fixed code for user
   */
  async clearFixedCode(id: string): Promise<void> {
    await this.usersRepository.update(id, {
      fixedCode: null,
    });
  }

  /**
   * Set refresh token for user
   */
  async setRefreshToken(id: string, refreshToken: string, expiresAt: Date): Promise<void> {
    await this.usersRepository.update(id, {
      refreshToken,
      refreshTokenExpires: expiresAt,
    });
  }

  /**
   * Clear refresh token for user
   */
  async clearRefreshToken(id: string): Promise<void> {
    await this.usersRepository.update(id, {
      refreshToken: null,
      refreshTokenExpires: null,
    });
  }

  /**
   * Find user by refresh token
   */
  async findByRefreshToken(refreshToken: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: {
        refreshToken,
        refreshTokenExpires: MoreThan(new Date())
      },
    });
  }
}
