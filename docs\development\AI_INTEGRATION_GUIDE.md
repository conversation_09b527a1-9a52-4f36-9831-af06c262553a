# 🤖 AI Integration Guide

## 📋 Tổng quan

Comprehensive guide để **integrate AI providers** vào Delify Platform, bao gồm setup, configuration, và implementation details cho multi-provider AI system.

## 🎯 AI Provider Architecture

### Multi-Provider Strategy
```
┌─────────────────────────────────────────────────────────────┐
│                AI Provider Integration                      │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Client    │ │     AI      │ │  Provider   │          │
│  │  Request    │ │ Controller  │ │  Factory    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Smart       │ │     AI      │ │  Provider   │          │
│  │ Selection   │ │  Service    │ │ Selection   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │    Grok     │ │   Gemini    │          │
│  │  Provider   │ │  Provider   │ │  Provider   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐                                          │
│  │   OLLAMA    │                                          │
│  │  Provider   │                                          │
│  │ (Local AI)  │                                          │
│  └─────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Provider Implementation

### Base Provider Interface
```typescript
// src/modules/ai/interfaces/ai-provider.interface.ts
export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason?: string;
}

export interface AIProviderConfig {
  provider: AIProvider;
  model: string;
  apiKey?: string;
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

export abstract class BaseAIProvider {
  protected config: AIProviderConfig;
  
  constructor(config: AIProviderConfig) {
    this.config = config;
  }
  
  abstract generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown>;
  
  abstract analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract validateConfig(): Promise<boolean>;
  
  abstract getModels(): Promise<string[]>;
}
```

### OpenAI Provider Implementation
```typescript
// src/modules/ai/providers/openai.provider.ts
import OpenAI from 'openai';
import { BaseAIProvider } from '../interfaces/ai-provider.interface';

@Injectable()
export class OpenAIProvider extends BaseAIProvider {
  private client: OpenAI;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: options?.model || this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature: options?.temperature || this.config.temperature || 0.7,
        max_tokens: options?.maxTokens || this.config.maxTokens || 1000,
        top_p: options?.topP || this.config.topP || 1,
        frequency_penalty: options?.frequencyPenalty || this.config.frequencyPenalty || 0,
        presence_penalty: options?.presencePenalty || this.config.presencePenalty || 0,
      });

      const choice = response.choices[0];
      
      return {
        content: choice.message.content || '',
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
        },
        model: response.model,
        finishReason: choice.finish_reason || undefined,
      };
    } catch (error) {
      throw new Error(`OpenAI API Error: ${error.message}`);
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    try {
      const stream = await this.client.chat.completions.create({
        model: options?.model || this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature: options?.temperature || this.config.temperature || 0.7,
        max_tokens: options?.maxTokens || this.config.maxTokens || 1000,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          yield content;
        }
      }
    } catch (error) {
      throw new Error(`OpenAI Streaming Error: ${error.message}`);
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: imageUrl } },
            ],
          },
        ],
        max_tokens: options?.maxTokens || 300,
      });

      const choice = response.choices[0];
      
      return {
        content: choice.message.content || '',
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
        },
        model: response.model,
        finishReason: choice.finish_reason || undefined,
      };
    } catch (error) {
      throw new Error(`OpenAI Vision Error: ${error.message}`);
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id);
    } catch (error) {
      throw new Error(`Failed to fetch OpenAI models: ${error.message}`);
    }
  }
}
```

### OLLAMA Provider Implementation
```typescript
// src/modules/ai/providers/ollama.provider.ts
import axios, { AxiosInstance } from 'axios';
import { BaseAIProvider } from '../interfaces/ai-provider.interface';

@Injectable()
export class OllamaProvider extends BaseAIProvider {
  private client: AxiosInstance;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = axios.create({
      baseURL: config.baseURL || 'http://localhost:11434',
      timeout: 60000, // 60 seconds timeout
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    try {
      const response = await this.client.post('/api/chat', {
        model: options?.model || this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        stream: false,
        options: {
          temperature: options?.temperature || this.config.temperature || 0.7,
          num_predict: options?.maxTokens || this.config.maxTokens || 1000,
          top_p: options?.topP || this.config.topP || 0.9,
        },
      });

      const data = response.data;
      
      return {
        content: data.message.content,
        usage: {
          promptTokens: data.prompt_eval_count || 0,
          completionTokens: data.eval_count || 0,
          totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0),
        },
        model: options?.model || this.config.model,
        finishReason: data.done ? 'stop' : undefined,
      };
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('OLLAMA: Server not running. Please start OLLAMA server.');
      }
      throw new Error(`OLLAMA Error: ${error.message}`);
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    try {
      const response = await this.client.post('/api/chat', {
        model: options?.model || this.config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        stream: true,
        options: {
          temperature: options?.temperature || this.config.temperature || 0.7,
          num_predict: options?.maxTokens || this.config.maxTokens || 1000,
        },
      }, {
        responseType: 'stream',
      });

      let buffer = '';
      
      for await (const chunk of response.data) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.message?.content) {
                yield data.message.content;
              }
            } catch (parseError) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } catch (error) {
      throw new Error(`OLLAMA Streaming Error: ${error.message}`);
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    // OLLAMA supports vision with LLaVA models
    try {
      const response = await this.client.post('/api/generate', {
        model: 'llava:latest', // Use LLaVA model for vision
        prompt: prompt,
        images: [imageUrl],
        stream: false,
        options: {
          temperature: options?.temperature || 0.7,
          num_predict: options?.maxTokens || 300,
        },
      });

      const data = response.data;
      
      return {
        content: data.response,
        usage: {
          promptTokens: data.prompt_eval_count || 0,
          completionTokens: data.eval_count || 0,
          totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0),
        },
        model: 'llava:latest',
        finishReason: data.done ? 'stop' : undefined,
      };
    } catch (error) {
      throw new Error(`OLLAMA Vision Error: ${error.message}`);
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.get('/api/tags');
      return true;
    } catch (error) {
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/api/tags');
      return response.data.models.map(model => model.name);
    } catch (error) {
      throw new Error(`Failed to fetch OLLAMA models: ${error.message}`);
    }
  }

  // OLLAMA specific methods
  async pullModel(modelName: string): Promise<void> {
    try {
      await this.client.post('/api/pull', {
        name: modelName,
      });
    } catch (error) {
      throw new Error(`Failed to pull model ${modelName}: ${error.message}`);
    }
  }

  async deleteModel(modelName: string): Promise<void> {
    try {
      await this.client.delete('/api/delete', {
        data: { name: modelName },
      });
    } catch (error) {
      throw new Error(`Failed to delete model ${modelName}: ${error.message}`);
    }
  }

  async getModelInfo(modelName: string): Promise<any> {
    try {
      const response = await this.client.post('/api/show', {
        name: modelName,
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get model info for ${modelName}: ${error.message}`);
    }
  }
}
```

## 🏭 Provider Factory

### AI Provider Factory
```typescript
// src/modules/ai/factories/ai-provider.factory.ts
@Injectable()
export class AIProviderFactory {
  constructor(private configService: ConfigService) {}

  createProvider(config: ProviderConfig): BaseAIProvider {
    switch (config.provider) {
      case AIProvider.OPENAI:
        return new OpenAIProvider({
          ...config,
          apiKey: config.apiKey || this.configService.get('OPENAI_API_KEY'),
          baseURL: config.baseURL || this.configService.get('OPENAI_BASE_URL'),
        });
        
      case AIProvider.GROK:
        return new GrokProvider({
          ...config,
          apiKey: config.apiKey || this.configService.get('GROK_API_KEY'),
          baseURL: config.baseURL || this.configService.get('GROK_BASE_URL'),
        });
        
      case AIProvider.GEMINI:
        return new GeminiProvider({
          ...config,
          apiKey: config.apiKey || this.configService.get('GEMINI_API_KEY'),
          baseURL: config.baseURL || this.configService.get('GEMINI_BASE_URL'),
        });
        
      case AIProvider.OLLAMA:
        return new OllamaProvider({
          ...config,
          baseURL: config.baseURL || this.configService.get('OLLAMA_BASE_URL') || 'http://localhost:11434',
        });
        
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  createProviderFromEnv(provider: AIProvider, model?: AIModelName): BaseAIProvider {
    const config = this.getProviderConfigs()[provider];
    return this.createProvider({
      provider,
      model: model || this.getDefaultModel(provider),
      ...config,
    });
  }

  private getProviderConfigs(): Record<AIProvider, Partial<AIProviderConfig>> {
    return {
      [AIProvider.OPENAI]: {
        apiKey: this.configService.get('OPENAI_API_KEY'),
        baseURL: this.configService.get('OPENAI_BASE_URL'),
      },
      [AIProvider.GROK]: {
        apiKey: this.configService.get('GROK_API_KEY'),
        baseURL: this.configService.get('GROK_BASE_URL'),
      },
      [AIProvider.GEMINI]: {
        apiKey: this.configService.get('GEMINI_API_KEY'),
        baseURL: this.configService.get('GEMINI_BASE_URL'),
      },
      [AIProvider.OLLAMA]: {
        baseURL: this.configService.get('OLLAMA_BASE_URL') || 'http://localhost:11434',
      },
    };
  }

  private getDefaultModel(provider: AIProvider): string {
    const defaultModels = {
      [AIProvider.OPENAI]: 'gpt-3.5-turbo',
      [AIProvider.GROK]: 'grok-1.5',
      [AIProvider.GEMINI]: 'gemini-pro',
      [AIProvider.OLLAMA]: 'llama2:7b',
    };
    
    return defaultModels[provider];
  }
}
```

## 🧠 Smart Provider Selection

### Provider Selection Service
```typescript
// src/modules/ai/services/provider-selection.service.ts
@Injectable()
export class ProviderSelectionService {
  private readonly providerPreferences = {
    text: ['grok', 'ollama', 'openai', 'gemini'],
    code: ['ollama', 'openai', 'grok', 'gemini'],
    image: ['openai', 'gemini', 'grok'],
    creative: ['grok', 'ollama', 'openai', 'gemini'],
    embedding: ['openai', 'gemini', 'ollama'],
    privacy: ['ollama'],
  };

  constructor(
    private providerFactory: AIProviderFactory,
    private configService: ConfigService,
  ) {}

  async selectBestProvider(
    task: string,
    options: {
      privacyLevel?: 'public' | 'internal' | 'confidential';
      costOptimized?: boolean;
      performanceOptimized?: boolean;
    } = {}
  ): Promise<ProviderRecommendation> {
    // Handle privacy requirements
    if (options.privacyLevel === 'confidential') {
      return this.selectProvider(['ollama'], task);
    }

    // Get preferred providers for task
    const preferredProviders = this.providerPreferences[task] || this.providerPreferences.text;
    
    // Filter available providers
    const availableProviders = await this.getAvailableProviders(preferredProviders);
    
    if (availableProviders.length === 0) {
      throw new Error('No AI providers available');
    }

    // Apply selection strategy
    if (options.costOptimized) {
      return this.selectCheapestProvider(availableProviders, task);
    }
    
    if (options.performanceOptimized) {
      return this.selectFastestProvider(availableProviders, task);
    }

    // Default: use first available preferred provider
    return this.selectProvider(availableProviders, task);
  }

  private async getAvailableProviders(preferredProviders: string[]): Promise<string[]> {
    const available = [];
    
    for (const provider of preferredProviders) {
      try {
        const providerInstance = this.providerFactory.createProviderFromEnv(provider as AIProvider);
        const isValid = await providerInstance.validateConfig();
        
        if (isValid) {
          available.push(provider);
        }
      } catch (error) {
        // Provider not available, skip
      }
    }
    
    return available;
  }

  private async selectProvider(providers: string[], task: string): Promise<ProviderRecommendation> {
    const selectedProvider = providers[0];
    const model = this.getRecommendedModel(selectedProvider, task);
    
    return {
      provider: selectedProvider as AIProvider,
      model,
      reason: `Best available provider for ${task} tasks`,
      confidence: 0.9,
    };
  }

  private async selectCheapestProvider(providers: string[], task: string): Promise<ProviderRecommendation> {
    const costs = providers.map(provider => ({
      provider,
      cost: this.getProviderCost(provider),
    }));
    
    costs.sort((a, b) => a.cost - b.cost);
    const cheapest = costs[0];
    
    return {
      provider: cheapest.provider as AIProvider,
      model: this.getRecommendedModel(cheapest.provider, task),
      reason: `Most cost-effective option (${cheapest.cost}/1K tokens)`,
      confidence: 0.8,
    };
  }

  private async selectFastestProvider(providers: string[], task: string): Promise<ProviderRecommendation> {
    // Get performance metrics from cache or database
    const performance = await this.getProviderPerformanceMetrics();
    
    const fastest = providers.reduce((best, current) => {
      const currentSpeed = performance[current]?.avgResponseTime || Infinity;
      const bestSpeed = performance[best]?.avgResponseTime || Infinity;
      return currentSpeed < bestSpeed ? current : best;
    });
    
    return {
      provider: fastest as AIProvider,
      model: this.getRecommendedModel(fastest, task),
      reason: `Fastest response time (${performance[fastest]?.avgResponseTime}ms avg)`,
      confidence: 0.85,
    };
  }

  private getProviderCost(provider: string): number {
    const costs = {
      openai: 0.002,
      grok: 0.002,
      gemini: 0.0005,
      ollama: 0, // Local processing
    };
    
    return costs[provider] || 0.002;
  }

  private getRecommendedModel(provider: string, task: string): string {
    const recommendations = {
      openai: {
        text: 'gpt-3.5-turbo',
        code: 'gpt-4-turbo',
        image: 'gpt-4-vision-preview',
        creative: 'gpt-4-turbo',
      },
      grok: {
        text: 'grok-1.5',
        code: 'grok-1.5',
        creative: 'grok-1.5',
      },
      gemini: {
        text: 'gemini-pro',
        code: 'gemini-pro',
        image: 'gemini-pro-vision',
      },
      ollama: {
        text: 'llama2:7b',
        code: 'codellama:7b',
        creative: 'llama2:13b',
      },
    };
    
    return recommendations[provider]?.[task] || recommendations[provider]?.text || 'default';
  }

  private async getProviderPerformanceMetrics(): Promise<Record<string, any>> {
    // This would typically come from a database or cache
    // For now, return mock data
    return {
      openai: { avgResponseTime: 1200, successRate: 0.99 },
      grok: { avgResponseTime: 800, successRate: 0.98 },
      gemini: { avgResponseTime: 1000, successRate: 0.97 },
      ollama: { avgResponseTime: 2000, successRate: 0.95 },
    };
  }
}

interface ProviderRecommendation {
  provider: AIProvider;
  model: string;
  reason: string;
  confidence: number;
}
```

## 🎯 Integration Best Practices

### 1. Error Handling
- **Graceful degradation** when providers fail
- **Retry logic** with exponential backoff
- **Fallback providers** for high availability
- **Clear error messages** for debugging

### 2. Performance Optimization
- **Connection pooling** for HTTP clients
- **Response caching** for repeated requests
- **Streaming responses** for real-time applications
- **Load balancing** across providers

### 3. Security Considerations
- **API key management** through environment variables
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **Audit logging** for compliance

### 4. Monitoring & Observability
- **Request/response logging**
- **Performance metrics** tracking
- **Cost monitoring** per provider
- **Health checks** for provider availability

**This integration guide provides complete implementation details cho AI multi-provider system trong Delify Platform.** 🤖✨
