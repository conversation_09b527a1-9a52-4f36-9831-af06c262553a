import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';
import OpenAI from 'openai';

@Injectable()
export class OpenAiService {
  private openai: OpenAI;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (apiKey) {
      this.openai = new OpenAI({
        apiKey,
      });
    } else {
      this.logger.logWithContext('OpenAI API key not configured', 'OpenAiService', 'warn');
    }
  }

  async generateCompletion(prompt: string, options: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  } = {}): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI not configured');
    }

    const {
      model = this.configService.get('OPENAI_MODEL', 'gpt-4'),
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt,
    } = options;

    try {
      const messages: any[] = [];
      
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }
      
      messages.push({ role: 'user', content: prompt });

      const response = await this.openai.chat.completions.create({
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      });

      return response.choices[0]?.message?.content || '';
    } catch (error) {
      this.logger.logError(error, 'OpenAiService');
      throw error;
    }
  }

  async analyzeComment(comment: string, platform: string): Promise<{
    containsAddress: boolean;
    containsPhone: boolean;
    containsEmail: boolean;
    isConsultationRequest: boolean;
    isBusinessInquiry: boolean;
    sentiment: 'positive' | 'negative' | 'neutral';
    confidence: number;
    extractedInfo: {
      addresses: string[];
      phones: string[];
      emails: string[];
    };
  }> {
    const systemPrompt = `You are an AI assistant that analyzes social media comments to identify potential business leads and extract contact information. 

    Analyze the comment and determine:
    1. Does it contain an address?
    2. Does it contain a phone number?
    3. Does it contain an email address?
    4. Is it a consultation request?
    5. Is it a business inquiry?
    6. What is the sentiment (positive, negative, neutral)?
    7. Extract any contact information found

    Respond in JSON format with the specified structure.`;

    const prompt = `Platform: ${platform}
Comment: "${comment}"

Please analyze this comment and provide a detailed assessment.`;

    try {
      const response = await this.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.3,
        maxTokens: 500,
      });

      return JSON.parse(response);
    } catch (error) {
      this.logger.logError(error, 'OpenAiService - analyzeComment');
      return {
        containsAddress: false,
        containsPhone: false,
        containsEmail: false,
        isConsultationRequest: false,
        isBusinessInquiry: false,
        sentiment: 'neutral',
        confidence: 0,
        extractedInfo: {
          addresses: [],
          phones: [],
          emails: [],
        },
      };
    }
  }

  async analyzeEmail(subject: string, content: string, sender: string): Promise<{
    category: string;
    priority: 'high' | 'medium' | 'low';
    sentiment: 'positive' | 'negative' | 'neutral';
    isSpam: boolean;
    requiresResponse: boolean;
    suggestedActions: string[];
    summary: string;
    extractedEntities: {
      people: string[];
      organizations: string[];
      dates: string[];
      amounts: string[];
    };
  }> {
    const systemPrompt = `You are an AI assistant that analyzes business emails to help with email management and prioritization.

    Analyze the email and provide:
    1. Category (sales, support, inquiry, complaint, etc.)
    2. Priority level
    3. Sentiment analysis
    4. Spam detection
    5. Whether it requires a response
    6. Suggested actions
    7. Brief summary
    8. Extract entities (people, organizations, dates, amounts)

    Respond in JSON format with the specified structure.`;

    const prompt = `From: ${sender}
Subject: ${subject}
Content: ${content}

Please analyze this email comprehensively.`;

    try {
      const response = await this.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.3,
        maxTokens: 800,
      });

      return JSON.parse(response);
    } catch (error) {
      this.logger.logError(error, 'OpenAiService - analyzeEmail');
      return {
        category: 'general',
        priority: 'medium',
        sentiment: 'neutral',
        isSpam: false,
        requiresResponse: false,
        suggestedActions: [],
        summary: 'Unable to analyze email',
        extractedEntities: {
          people: [],
          organizations: [],
          dates: [],
          amounts: [],
        },
      };
    }
  }

  async generateChatbotResponse(message: string, context: any = {}): Promise<string> {
    const systemPrompt = `You are a helpful AI assistant for a business platform. You help users with:
    - Marketing automation
    - Social media management
    - Email campaigns
    - Document management
    - AI-powered features
    - General business questions

    Be professional, helpful, and concise. If you don't know something, admit it and suggest alternatives.
    
    Company context: ${JSON.stringify(context.companyInfo || {})}
    User context: ${JSON.stringify(context.userInfo || {})}`;

    return this.generateCompletion(message, {
      systemPrompt,
      temperature: 0.8,
      maxTokens: 500,
    });
  }
}
