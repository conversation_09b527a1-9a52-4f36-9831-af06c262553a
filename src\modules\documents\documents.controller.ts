import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, UploadedFile, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { DocumentsService } from './documents.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Documents')
@Controller('documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Get()
  async getDocuments(@CurrentUser() user: User) {
    return this.documentsService.getDocuments(user.id);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  async uploadDocument(
    @CurrentUser() user: User,
    @UploadedFile() file: Express.Multer.File,
    @Body() data: any
  ) {
    return this.documentsService.uploadDocument(user.id, file, data);
  }

  @Post(':id/sign')
  async signDocument(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.documentsService.signDocument(user.id, id, data);
  }

  @Get(':id/signatures')
  async getDocumentSignatures(@CurrentUser() user: User, @Param('id') id: string) {
    return this.documentsService.getDocumentSignatures(user.id, id);
  }

  @Post(':id/workflow')
  async createWorkflow(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.documentsService.createWorkflow(user.id, id, data);
  }

  @Get('workflows')
  async getWorkflows(@CurrentUser() user: User) {
    return this.documentsService.getWorkflows(user.id);
  }
}
