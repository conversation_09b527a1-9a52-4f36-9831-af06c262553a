# 📝 Code Standards & Best Practices

## 📋 Tổng quan

Comprehensive coding standards cho Delify Platform để đảm bảo **code quality**, **consistency**, và **maintainability** across the entire development team.

## 🎯 General Principles

### Code Quality Pillars
1. **Readability** - Code should be self-documenting
2. **Consistency** - Follow established patterns
3. **Maintainability** - Easy to modify and extend
4. **Performance** - Efficient and scalable
5. **Security** - Secure by design

## 📁 Project Structure

### Directory Organization
```
src/
├── common/                 # Shared utilities and components
│   ├── decorators/        # Custom decorators
│   ├── filters/           # Exception filters
│   ├── guards/            # Authentication guards
│   ├── interceptors/      # Request/response interceptors
│   ├── interfaces/        # Shared interfaces
│   ├── services/          # Common services
│   └── utils/             # Utility functions
├── config/                # Configuration files
├── database/              # Database related files
│   ├── migrations/        # Database migrations
│   └── seeds/             # Database seeds
├── modules/               # Feature modules
│   ├── auth/              # Authentication module
│   ├── users/             # Users module
│   ├── organizations/     # Organizations module
│   └── ai/                # AI services module
└── main.ts                # Application entry point
```

### Module Structure
```
module-name/
├── controllers/           # HTTP controllers
├── services/              # Business logic services
├── entities/              # Database entities
├── dto/                   # Data Transfer Objects
├── enums/                 # Enumerations
├── interfaces/            # Module-specific interfaces
├── guards/                # Module-specific guards
├── decorators/            # Module-specific decorators
├── __tests__/             # Test files
└── module-name.module.ts  # Module definition
```

## 🔤 Naming Conventions

### Files and Directories
```typescript
// Files - kebab-case with descriptive suffixes
user.entity.ts
create-user.dto.ts
users.controller.ts
users.service.ts
jwt-auth.guard.ts
current-user.decorator.ts

// Directories - kebab-case
auth/
organization-members/
ai-providers/

// Test files - same name with .spec.ts or .e2e-spec.ts
users.service.spec.ts
auth.controller.e2e-spec.ts
```

### Classes and Interfaces
```typescript
// Classes - PascalCase with descriptive suffixes
export class UsersService {}
export class CreateUserDto {}
export class User {}
export class JwtAuthGuard {}

// Interfaces - PascalCase with 'I' prefix (optional)
export interface IUserRepository {}
export interface ApiResponse<T> {}

// Enums - PascalCase
export enum AccountType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
}

// Constants - SCREAMING_SNAKE_CASE
export const MAX_FILE_SIZE = ********;
export const DEFAULT_PAGE_SIZE = 20;
```

### Variables and Functions
```typescript
// Variables - camelCase
const userName = 'john.doe';
const isAuthenticated = true;
const userPreferences = {};

// Functions - camelCase with descriptive verbs
function getUserById(id: string) {}
function validateEmailFormat(email: string) {}
function calculateTotalCost(items: Item[]) {}

// Boolean variables - use is/has/can/should prefixes
const isActive = true;
const hasPermission = false;
const canEdit = true;
const shouldValidate = false;
```

## 🏗️ TypeScript Standards

### Type Definitions
```typescript
// Use explicit types for function parameters and return values
function createUser(userData: CreateUserDto): Promise<User> {
  // Implementation
}

// Use interfaces for object shapes
interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
  };
}

// Use union types for limited options
type UserRole = 'admin' | 'user' | 'viewer';
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// Use generics for reusable types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// Use utility types when appropriate
type PartialUser = Partial<User>;
type UserEmail = Pick<User, 'email'>;
type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
```

### Strict TypeScript Configuration
```json
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 🎨 Code Formatting

### Prettier Configuration
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### ESLint Configuration
```json
// .eslintrc.js
module.exports = {
  extends: [
    '@nestjs',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'error',
    '@typescript-eslint/explicit-module-boundary-types': 'error',
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
  },
};
```

## 🏛️ Architecture Patterns

### Service Layer Pattern
```typescript
// Good: Single responsibility, clear dependencies
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly emailService: EmailService,
    private readonly loggerService: LoggerService,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    try {
      // Validate business rules
      await this.validateUserCreation(createUserDto);
      
      // Create user
      const user = await this.userRepository.save(
        this.userRepository.create(createUserDto)
      );
      
      // Send welcome email
      await this.emailService.sendWelcomeEmail(user.email);
      
      // Log activity
      this.loggerService.logInfo(`User created: ${user.id}`);
      
      return user;
    } catch (error) {
      this.loggerService.logError(error, 'Failed to create user');
      throw error;
    }
  }

  private async validateUserCreation(createUserDto: CreateUserDto): Promise<void> {
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });
    
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }
  }
}
```

### Repository Pattern
```typescript
// Good: Abstracted data access
@Injectable()
export class UsersRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  async findActiveUsers(organizationId: string): Promise<User[]> {
    return this.repository
      .createQueryBuilder('user')
      .innerJoin('user.organizationMemberships', 'membership')
      .where('membership.organizationId = :organizationId', { organizationId })
      .andWhere('membership.isActive = :isActive', { isActive: true })
      .getMany();
  }

  async getUserStats(): Promise<UserStats> {
    const [total, active, verified] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { isActive: true } }),
      this.repository.count({ where: { emailVerified: true } }),
    ]);

    return { total, active, verified };
  }
}
```

### DTO Pattern
```typescript
// Good: Proper validation and transformation
export class CreateUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @Transform(({ value }) => value.toLowerCase().trim())
  email: string;

  @ApiProperty({ example: 'SecurePassword123!' })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/,
    { message: 'Password must contain uppercase, lowercase, number, and special character' }
  )
  password: string;

  @ApiProperty({ example: 'John' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  lastName: string;
}
```

## 🔒 Security Best Practices

### Input Validation
```typescript
// Always validate and sanitize inputs
@Post()
async createUser(@Body() createUserDto: CreateUserDto): Promise<User> {
  // DTO validation happens automatically
  return this.usersService.create(createUserDto);
}

// Custom validation for complex rules
@ValidatorConstraint({ name: 'isStrongPassword', async: false })
export class IsStrongPasswordConstraint implements ValidatorConstraintInterface {
  validate(password: string): boolean {
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(password);
  }

  defaultMessage(): string {
    return 'Password must contain uppercase, lowercase, number, and special character';
  }
}
```

### Authentication & Authorization
```typescript
// Use guards consistently
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  @Get('profile')
  async getProfile(@CurrentUser() user: User): Promise<User> {
    return this.usersService.findById(user.id);
  }

  @Put(':id')
  @UseGuards(ResourceOwnerGuard)
  async updateUser(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(id, updateDto);
  }
}
```

### Error Handling
```typescript
// Consistent error handling
@Injectable()
export class UsersService {
  async findById(id: string): Promise<User> {
    try {
      const user = await this.userRepository.findOne({ where: { id } });
      
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }
      
      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to find user ${id}`, error.stack);
      throw new InternalServerErrorException('Failed to retrieve user');
    }
  }
}
```

## 📊 Performance Best Practices

### Database Queries
```typescript
// Good: Efficient queries with proper indexing
async findUsersWithPagination(query: QueryUsersDto): Promise<PaginatedResult<User>> {
  const queryBuilder = this.userRepository
    .createQueryBuilder('user')
    .leftJoinAndSelect('user.organizationMemberships', 'membership')
    .where('user.isActive = :isActive', { isActive: true });

  // Apply filters efficiently
  if (query.search) {
    queryBuilder.andWhere(
      '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
      { search: `%${query.search}%` }
    );
  }

  // Get total count and data in parallel
  const [users, total] = await Promise.all([
    queryBuilder
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .getMany(),
    queryBuilder.getCount(),
  ]);

  return {
    data: users,
    meta: {
      total,
      page: query.page,
      limit: query.limit,
      totalPages: Math.ceil(total / query.limit),
    },
  };
}
```

### Caching Strategy
```typescript
// Good: Strategic caching for expensive operations
@Injectable()
export class UsersService {
  @Cacheable('user-profile', 300) // 5 minutes
  async getUserProfile(userId: string): Promise<UserProfile> {
    return this.userRepository.findOne({
      where: { id: userId },
      relations: ['organizationMemberships', 'preferences'],
    });
  }

  @CacheEvict('user-profile')
  async updateUserProfile(userId: string, updateData: UpdateUserDto): Promise<User> {
    // Update logic
  }
}
```

## 🧪 Testing Standards

### Test Structure
```typescript
// Good: Clear, focused tests
describe('UsersService', () => {
  let service: UsersService;
  let repository: jest.Mocked<Repository<User>>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get(getRepositoryToken(User));
  });

  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      const expectedUser = { id: 'user-id', ...createUserDto };
      repository.findOne.mockResolvedValue(null);
      repository.create.mockReturnValue(expectedUser as any);
      repository.save.mockResolvedValue(expectedUser as any);

      // Act
      const result = await service.createUser(createUserDto);

      // Assert
      expect(result).toEqual(expectedUser);
      expect(repository.save).toHaveBeenCalledWith(expectedUser);
    });

    it('should throw ConflictException when email exists', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      repository.findOne.mockResolvedValue({ id: 'existing-id' } as User);

      // Act & Assert
      await expect(service.createUser(createUserDto)).rejects.toThrow(
        ConflictException
      );
    });
  });
});
```

## 📝 Documentation Standards

### Code Comments
```typescript
/**
 * Service responsible for user management operations.
 * Handles user creation, authentication, and profile management.
 */
@Injectable()
export class UsersService {
  /**
   * Creates a new user with the provided data.
   * 
   * @param createUserDto - User creation data
   * @returns Promise resolving to the created user
   * @throws ConflictException when email already exists
   * @throws BadRequestException when validation fails
   */
  async createUser(createUserDto: CreateUserDto): Promise<User> {
    // Implementation
  }

  /**
   * Validates user creation business rules.
   * Checks for email uniqueness and other constraints.
   * 
   * @private
   * @param createUserDto - User data to validate
   * @throws ConflictException when validation fails
   */
  private async validateUserCreation(createUserDto: CreateUserDto): Promise<void> {
    // Implementation
  }
}
```

### API Documentation
```typescript
// Complete Swagger documentation
@ApiTags('Users')
@Controller('users')
@ApiBearerAuth()
export class UsersController {
  @Post()
  @ApiOperation({ 
    summary: 'Create new user',
    description: 'Creates a new user account with the provided information'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'User created successfully',
    type: User 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: {
          email: ['Please provide a valid email address']
        }
      }
    }
  })
  @ApiResponse({ 
    status: 409, 
    description: 'Email already exists' 
  })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.createUser(createUserDto);
  }
}
```

## 🔧 Development Tools

### Pre-commit Hooks
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,js}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

### Commit Message Convention
```bash
# Format: type(scope): description

feat(auth): add JWT refresh token functionality
fix(users): resolve email validation issue
docs(api): update authentication endpoints documentation
test(ai): add unit tests for OLLAMA provider
refactor(db): optimize user queries for better performance
chore(deps): update dependencies to latest versions

# Types: feat, fix, docs, style, refactor, test, chore
# Scope: module or component being changed
# Description: clear, concise description of the change
```

## 🎯 Code Review Guidelines

### Review Checklist
- [ ] **Functionality** - Does the code work as intended?
- [ ] **Code Quality** - Is the code clean and readable?
- [ ] **Performance** - Are there any performance issues?
- [ ] **Security** - Are there any security vulnerabilities?
- [ ] **Tests** - Are there adequate tests?
- [ ] **Documentation** - Is the code properly documented?
- [ ] **Standards** - Does it follow coding standards?

### Review Process
1. **Self Review** - Review your own code before submitting
2. **Automated Checks** - Ensure all CI checks pass
3. **Peer Review** - At least one team member review
4. **Address Feedback** - Respond to all review comments
5. **Final Approval** - Get approval before merging

## 🎯 Best Practices Summary

### Do's ✅
- Use TypeScript strictly with proper typing
- Follow consistent naming conventions
- Write comprehensive tests
- Document complex business logic
- Use proper error handling
- Validate all inputs
- Follow security best practices
- Keep functions small and focused
- Use dependency injection properly
- Write meaningful commit messages

### Don'ts ❌
- Don't use `any` type unless absolutely necessary
- Don't ignore TypeScript errors
- Don't commit code without tests
- Don't hardcode configuration values
- Don't expose sensitive information
- Don't write overly complex functions
- Don't skip code reviews
- Don't ignore linting errors
- Don't commit commented-out code
- Don't use console.log in production code

**Following these standards ensures high-quality, maintainable code trong Delify Platform với consistent development experience across the team.** 📝✨
