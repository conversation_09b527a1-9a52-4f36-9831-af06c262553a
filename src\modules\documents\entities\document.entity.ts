import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum DocumentStatus {
  DRAFT = 'draft',
  PENDING_SIGNATURE = 'pending_signature',
  SIGNED = 'signed',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

export enum DocumentType {
  CONTRACT = 'contract',
  AGREEMENT = 'agreement',
  INVOICE = 'invoice',
  PROPOSAL = 'proposal',
  REPORT = 'report',
  OTHER = 'other',
}

@Entity('documents')
@Index(['userId', 'status'])
@Index(['documentType', 'status'])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.OTHER,
  })
  documentType: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  status: DocumentStatus;

  @Column()
  fileName: string;

  @Column()
  filePath: string;

  @Column()
  fileSize: number;

  @Column()
  mimeType: string;

  @Column({ nullable: true })
  fileHash: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    pages?: number;
    version?: string;
    author?: string;
    createdWith?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  signers: Array<{
    email: string;
    name: string;
    role: string;
    order: number;
    required: boolean;
    signed: boolean;
    signedAt?: string;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    requireAllSignatures?: boolean;
    expiresAt?: string;
    reminderFrequency?: number; // days
    allowComments?: boolean;
    downloadAfterSigning?: boolean;
  };

  @Column({ nullable: true })
  expiresAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ default: 0 })
  viewCount: number;

  @Column({ nullable: true })
  lastViewedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isPendingSignature(): boolean {
    return this.status === DocumentStatus.PENDING_SIGNATURE;
  }

  get isSigned(): boolean {
    return this.status === DocumentStatus.SIGNED;
  }

  get isExpired(): boolean {
    return this.status === DocumentStatus.EXPIRED || 
           (this.expiresAt && new Date() > this.expiresAt);
  }

  get allSignersCompleted(): boolean {
    return this.signers?.every(signer => !signer.required || signer.signed) || false;
  }
}
