import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EmailCampaign, CampaignStatus, CampaignType } from '../entities/email-campaign.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class EmailCampaignService {
  constructor(
    @InjectRepository(EmailCampaign)
    private emailCampaignRepository: Repository<EmailCampaign>,
    @InjectQueue('email')
    private emailQueue: Queue,
    private logger: LoggerService,
  ) {}

  async createCampaign(userId: string, data: {
    name: string;
    description?: string;
    campaignType?: CampaignType;
    subject: string;
    htmlContent: string;
    textContent?: string;
    recipients: Array<{
      email: string;
      name?: string;
      variables?: Record<string, any>;
    }>;
    segmentation?: any;
    scheduledAt?: Date;
    settings?: any;
  }): Promise<EmailCampaign> {
    const campaign = this.emailCampaignRepository.create({
      userId,
      totalRecipients: data.recipients.length,
      ...data,
    });

    const savedCampaign = await this.emailCampaignRepository.save(campaign);

    this.logger.logWithContext(`Email campaign created: ${savedCampaign.id}`, 'EmailCampaignService');

    return savedCampaign;
  }

  async getCampaigns(userId: string, status?: CampaignStatus): Promise<EmailCampaign[]> {
    const where: any = { userId };
    if (status) {
      where.status = status;
    }

    return this.emailCampaignRepository.find({
      where,
      order: { createdAt: 'DESC' },
    });
  }

  async getCampaign(userId: string, campaignId: string): Promise<EmailCampaign> {
    return this.emailCampaignRepository.findOne({
      where: { id: campaignId, userId },
    });
  }

  async updateCampaign(userId: string, campaignId: string, data: Partial<EmailCampaign>): Promise<EmailCampaign> {
    await this.emailCampaignRepository.update(
      { id: campaignId, userId },
      data
    );

    const updatedCampaign = await this.getCampaign(userId, campaignId);

    this.logger.logWithContext(`Email campaign updated: ${campaignId}`, 'EmailCampaignService');

    return updatedCampaign;
  }

  async deleteCampaign(userId: string, campaignId: string): Promise<void> {
    const campaign = await this.getCampaign(userId, campaignId);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status === CampaignStatus.SENDING) {
      throw new Error('Cannot delete campaign that is currently sending');
    }

    await this.emailCampaignRepository.delete({
      id: campaignId,
      userId,
    });

    this.logger.logWithContext(`Email campaign deleted: ${campaignId}`, 'EmailCampaignService');
  }

  async sendCampaign(userId: string, campaignId: string): Promise<void> {
    const campaign = await this.getCampaign(userId, campaignId);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (!campaign.canBeSent) {
      throw new Error('Campaign cannot be sent in its current status');
    }

    if (campaign.scheduledAt && campaign.scheduledAt > new Date()) {
      // Schedule for later
      const delay = campaign.scheduledAt.getTime() - Date.now();
      await this.emailQueue.add(
        'send-campaign',
        { campaignId },
        { delay }
      );

      campaign.status = CampaignStatus.SCHEDULED;
    } else {
      // Send immediately
      await this.emailQueue.add('send-campaign', { campaignId });
      campaign.status = CampaignStatus.SENDING;
    }

    await this.emailCampaignRepository.save(campaign);

    this.logger.logWithContext(`Email campaign queued for sending: ${campaignId}`, 'EmailCampaignService');
  }

  async pauseCampaign(userId: string, campaignId: string): Promise<void> {
    const campaign = await this.getCampaign(userId, campaignId);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status !== CampaignStatus.SENDING) {
      throw new Error('Only sending campaigns can be paused');
    }

    campaign.status = CampaignStatus.PAUSED;
    await this.emailCampaignRepository.save(campaign);

    this.logger.logWithContext(`Email campaign paused: ${campaignId}`, 'EmailCampaignService');
  }

  async resumeCampaign(userId: string, campaignId: string): Promise<void> {
    const campaign = await this.getCampaign(userId, campaignId);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status !== CampaignStatus.PAUSED) {
      throw new Error('Only paused campaigns can be resumed');
    }

    await this.emailQueue.add('send-campaign', { campaignId });
    campaign.status = CampaignStatus.SENDING;
    await this.emailCampaignRepository.save(campaign);

    this.logger.logWithContext(`Email campaign resumed: ${campaignId}`, 'EmailCampaignService');
  }

  async cancelCampaign(userId: string, campaignId: string): Promise<void> {
    const campaign = await this.getCampaign(userId, campaignId);

    if (!campaign) {
      throw new Error('Campaign not found');
    }

    if (campaign.status === CampaignStatus.SENT) {
      throw new Error('Cannot cancel campaign that has already been sent');
    }

    campaign.status = CampaignStatus.CANCELLED;
    await this.emailCampaignRepository.save(campaign);

    this.logger.logWithContext(`Email campaign cancelled: ${campaignId}`, 'EmailCampaignService');
  }

  async duplicateCampaign(userId: string, campaignId: string, newName: string): Promise<EmailCampaign> {
    const originalCampaign = await this.getCampaign(userId, campaignId);

    if (!originalCampaign) {
      throw new Error('Campaign not found');
    }

    const duplicatedCampaign = this.emailCampaignRepository.create({
      ...originalCampaign,
      id: undefined, // Let TypeORM generate new ID
      name: newName,
      status: CampaignStatus.DRAFT,
      sentCount: 0,
      failedCount: 0,
      sentAt: null,
      analytics: null,
      errorMessage: null,
      createdAt: undefined,
      updatedAt: undefined,
    });

    const savedCampaign = await this.emailCampaignRepository.save(duplicatedCampaign);

    this.logger.logWithContext(`Email campaign duplicated: ${campaignId} -> ${savedCampaign.id}`, 'EmailCampaignService');

    return savedCampaign;
  }

  async getCampaignStats(userId: string): Promise<{
    total: number;
    draft: number;
    scheduled: number;
    sending: number;
    sent: number;
    cancelled: number;
    totalEmailsSent: number;
    averageOpenRate: number;
    averageClickRate: number;
  }> {
    const campaigns = await this.getCampaigns(userId);

    const stats = {
      total: campaigns.length,
      draft: campaigns.filter(c => c.status === CampaignStatus.DRAFT).length,
      scheduled: campaigns.filter(c => c.status === CampaignStatus.SCHEDULED).length,
      sending: campaigns.filter(c => c.status === CampaignStatus.SENDING).length,
      sent: campaigns.filter(c => c.status === CampaignStatus.SENT).length,
      cancelled: campaigns.filter(c => c.status === CampaignStatus.CANCELLED).length,
      totalEmailsSent: campaigns.reduce((sum, c) => sum + (c.sentCount || 0), 0),
      averageOpenRate: 0,
      averageClickRate: 0,
    };

    // Calculate average rates
    const sentCampaigns = campaigns.filter(c => c.analytics);
    if (sentCampaigns.length > 0) {
      stats.averageOpenRate = Math.round(
        sentCampaigns.reduce((sum, c) => sum + (c.analytics?.openRate || 0), 0) / sentCampaigns.length
      );
      stats.averageClickRate = Math.round(
        sentCampaigns.reduce((sum, c) => sum + (c.analytics?.clickRate || 0), 0) / sentCampaigns.length
      );
    }

    return stats;
  }

  async getRecentCampaigns(userId: string, limit: number = 5): Promise<EmailCampaign[]> {
    return this.emailCampaignRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async updateCampaignAnalytics(campaignId: string): Promise<void> {
    await this.emailQueue.add('update-campaign-analytics', { campaignId });
  }
}
