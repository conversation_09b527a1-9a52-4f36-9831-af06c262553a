# TypeORM Query Operators Guide

## Overview

This guide provides information about TypeORM query operators that need to be properly imported when used in entity files or services. These operators are commonly used in `where` clauses for database queries.

## Common TypeORM Operators

### Import Statement

All TypeORM operators must be imported from the `typeorm` package:

```typescript
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  // ... other decorators
  In,
  Not,
  Like,
  ILike,
  Between,
  MoreThan,
  MoreThanOrEqual,
  LessThan,
  LessThanOrEqual,
  IsNull,
  IsNotNull,
  Raw,
} from 'typeorm';
```

### Operator Reference

#### `In(values: any[])`
Checks if column value is in the given array.

```typescript
// Usage in entity static methods
static async getSecurityLogs(repository: any): Promise<SSOAuditLog[]> {
  return repository.find({
    where: {
      action: In([
        SSOAction.SUSPICIOUS_ACTIVITY,
        SSOAction.PERMISSION_DENIED,
        SSOAction.IP_CHANGE,
      ]),
    },
  });
}

// Usage in services
const users = await this.userRepository.find({
  where: {
    status: In(['ACTIVE', 'PENDING']),
  },
});
```

#### `Not(value: any)`
Negates the given condition.

```typescript
const activeUsers = await this.userRepository.find({
  where: {
    status: Not('DELETED'),
  },
});
```

#### `Like(pattern: string)`
SQL LIKE operator (case-sensitive).

```typescript
const users = await this.userRepository.find({
  where: {
    email: Like('%@company.com'),
  },
});
```

#### `ILike(pattern: string)`
SQL ILIKE operator (case-insensitive).

```typescript
const users = await this.userRepository.find({
  where: {
    firstName: ILike('%john%'),
  },
});
```

#### `Between(from: any, to: any)`
SQL BETWEEN operator.

```typescript
const recentLogs = await this.auditRepository.find({
  where: {
    createdAt: Between(startDate, endDate),
  },
});
```

#### `MoreThan(value: any)` / `MoreThanOrEqual(value: any)`
Greater than / Greater than or equal operators.

```typescript
const recentSessions = await this.sessionRepository.find({
  where: {
    lastActivityAt: MoreThan(new Date(Date.now() - 24 * 60 * 60 * 1000)),
  },
});
```

#### `LessThan(value: any)` / `LessThanOrEqual(value: any)`
Less than / Less than or equal operators.

```typescript
const expiredSessions = await this.sessionRepository.find({
  where: {
    expiresAt: LessThan(new Date()),
  },
});
```

#### `IsNull()` / `IsNotNull()`
NULL checks.

```typescript
const usersWithoutPhone = await this.userRepository.find({
  where: {
    phoneNumber: IsNull(),
  },
});

const usersWithPhone = await this.userRepository.find({
  where: {
    phoneNumber: IsNotNull(),
  },
});
```

#### `Raw(sql: string, parameters?: any[])`
Raw SQL condition.

```typescript
const users = await this.userRepository.find({
  where: {
    name: Raw("LOWER(name) = LOWER(:name)", { name: 'John' }),
  },
});
```

## Best Practices

### 1. Always Import Required Operators

```typescript
// ❌ Bad - Missing import
export class SSOAuditLog {
  static async getSecurityLogs(repository: any) {
    return repository.find({
      where: {
        action: In([...]), // TS2304: Cannot find name 'In'
      },
    });
  }
}

// ✅ Good - Proper import
import { Entity, Column, In } from 'typeorm';

export class SSOAuditLog {
  static async getSecurityLogs(repository: any) {
    return repository.find({
      where: {
        action: In([...]),
      },
    });
  }
}
```

### 2. Use Type-Safe Operators

```typescript
// ✅ Good - Type-safe with enums
where: {
  action: In([
    SSOAction.SUSPICIOUS_ACTIVITY,
    SSOAction.PERMISSION_DENIED,
  ]),
}

// ❌ Avoid - String literals (prone to typos)
where: {
  action: In(['SUSPICIOUS_ACTIVITY', 'PERMISSION_DENIED']),
}
```

### 3. Combine Operators for Complex Queries

```typescript
const complexQuery = await this.repository.find({
  where: {
    status: In(['ACTIVE', 'PENDING']),
    createdAt: Between(startDate, endDate),
    email: Not(IsNull()),
    role: Not(In(['GUEST', 'TEMP'])),
  },
});
```

### 4. Use Query Builder for Advanced Cases

```typescript
const results = await this.repository
  .createQueryBuilder('user')
  .where('user.status IN (:...statuses)', { statuses: ['ACTIVE', 'PENDING'] })
  .andWhere('user.createdAt BETWEEN :start AND :end', { start: startDate, end: endDate })
  .getMany();
```

## Common Errors and Solutions

### Error: TS2304 - Cannot find name 'In'

**Problem**: TypeORM operator not imported.

**Solution**: Add the operator to your imports.

```typescript
// Add missing operator to imports
import {
  Entity,
  Column,
  In, // ← Add this
} from 'typeorm';
```

### Error: TS2345 - Argument type not assignable

**Problem**: Incorrect parameter type for operator.

**Solution**: Ensure parameter types match the operator requirements.

```typescript
// ❌ Wrong - In expects array
where: { status: In('ACTIVE') }

// ✅ Correct - In with array
where: { status: In(['ACTIVE']) }
```

## Project-Specific Usage

### SSO Module

The SSO module commonly uses these operators:

- `In()` - For filtering multiple enum values (actions, statuses)
- `Between()` - For date range queries in audit logs
- `MoreThan()` / `LessThan()` - For session expiration checks
- `IsNull()` / `IsNotNull()` - For optional field filtering

### RBAC Module

The RBAC module commonly uses:

- `In()` - For permission and role filtering
- `Not()` - For excluding certain roles/permissions
- `Like()` / `ILike()` - For searching roles/permissions by name

### Cache Integration

When using operators with cached queries, ensure cache keys are consistent:

```typescript
@Cacheable('security-logs-{0}', 300)
static async getSecurityLogs(actions: SSOAction[]): Promise<SSOAuditLog[]> {
  return this.repository.find({
    where: {
      action: In(actions),
    },
  });
}
```

## Testing Operators

Always test queries with operators:

```typescript
describe('SSOAuditLog', () => {
  it('should filter security logs correctly', async () => {
    const logs = await SSOAuditLog.getSecurityLogs(repository);
    
    expect(logs).toBeDefined();
    expect(logs.every(log => 
      [SSOAction.SUSPICIOUS_ACTIVITY, SSOAction.PERMISSION_DENIED].includes(log.action)
    )).toBe(true);
  });
});
```

## Migration Considerations

When adding new operators to existing entities:

1. **Update imports** in the entity file
2. **Test queries** thoroughly
3. **Update related services** that use the entity
4. **Check cache invalidation** if using cached queries
5. **Update documentation** and examples

---

For more information about TypeORM operators, see the [official TypeORM documentation](https://typeorm.io/find-options).
