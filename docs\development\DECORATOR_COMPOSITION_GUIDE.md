# Decorator Composition Guide

## Overview

This guide explains how to properly compose decorators in NestJS, particularly when dealing with TypeScript strict mode and decorator type compatibility issues.

## Common TypeScript Decorator Errors

### TS2345: Decorator Type Mismatch

**Error Message:**
```
Argument of type 'MethodDecorator' is not assignable to parameter of type 'MethodDecorator & ClassDecorator'.
Type 'MethodDecorator' is not assignable to type 'ClassDecorator'.
Target signature provides too few arguments. Expected 3 or more, but got 1.
```

**Root Cause:**
This error occurs when using `applyDecorators()` with decorators that are only `MethodDecorator` types, but TypeScript expects decorators that can work as both `MethodDecorator & ClassDecorator`.

## Solution: Proper Type Casting

### Before (Causing TS2345 Error)

```typescript
export const RBAC = (options: RBACOptions) => {
  const decorators = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  decorators.push(
    ApiOperation({ 
      summary: 'Protected endpoint',
      description: 'RBAC protected endpoint'
    }), // ❌ TS2345 Error: MethodDecorator not assignable to MethodDecorator & ClassDecorator
    ApiResponse({ 
      status: 200, 
      description: 'Success' 
    }), // ❌ Same error
  );

  return applyDecorators(...decorators);
};
```

### After (Error Resolved)

```typescript
export const RBAC = (options: RBACOptions) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  decorators.push(
    ApiOperation({ 
      summary: 'Protected endpoint',
      description: 'RBAC protected endpoint'
    }) as MethodDecorator, // ✅ Explicit type casting
    ApiResponse({ 
      status: 200, 
      description: 'Success' 
    }) as MethodDecorator, // ✅ Explicit type casting
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Key Changes Explained

### 1. Explicit Array Typing

```typescript
// Before
const decorators = [];

// After
const decorators: Array<MethodDecorator | ClassDecorator> = [];
```

**Why:** Explicitly typing the array helps TypeScript understand what types of decorators are allowed.

### 2. Type Casting Swagger Decorators

```typescript
// Before
ApiOperation({ summary: 'Test' })

// After
ApiOperation({ summary: 'Test' }) as MethodDecorator
```

**Why:** Swagger decorators (`ApiOperation`, `ApiResponse`, `ApiForbiddenResponse`) are `MethodDecorator` types. Casting them explicitly tells TypeScript to treat them as such.

### 3. Final Array Casting

```typescript
// Before
return applyDecorators(...decorators);

// After
return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
```

**Why:** `applyDecorators` expects decorators that can work as both method and class decorators. The final cast ensures compatibility.

## Decorator Types in NestJS

### MethodDecorator
Applied to class methods:
```typescript
class Controller {
  @MethodDecorator()
  method() {}
}
```

### ClassDecorator
Applied to classes:
```typescript
@ClassDecorator()
class Controller {}
```

### MethodDecorator & ClassDecorator
Can be applied to both:
```typescript
@BothDecorator()
class Controller {
  @BothDecorator()
  method() {}
}
```

## Common NestJS Decorators by Type

### MethodDecorator Only
- `@ApiOperation()`
- `@ApiResponse()`
- `@ApiForbiddenResponse()`
- `@Get()`, `@Post()`, `@Put()`, `@Delete()`
- `@Body()`, `@Param()`, `@Query()`

### ClassDecorator Only
- `@Controller()`
- `@Injectable()`
- `@Module()`

### Both (MethodDecorator & ClassDecorator)
- `@UseGuards()`
- `@ApiBearerAuth()`
- `@SetMetadata()`
- Custom decorators using `applyDecorators()`

## Best Practices

### 1. Use Explicit Typing

```typescript
// ✅ Good
const decorators: Array<MethodDecorator | ClassDecorator> = [];

// ❌ Avoid
const decorators = [];
```

### 2. Cast Swagger Decorators

```typescript
// ✅ Good
decorators.push(
  ApiOperation({ summary: 'Test' }) as MethodDecorator,
  ApiResponse({ status: 200 }) as MethodDecorator,
);

// ❌ Avoid
decorators.push(
  ApiOperation({ summary: 'Test' }),
  ApiResponse({ status: 200 }),
);
```

### 3. Group Similar Decorators

```typescript
// ✅ Good - Group by type
const guardDecorators = [
  UseGuards(JwtAuthGuard, RBACGuard),
  ApiBearerAuth(),
];

const swaggerDecorators = [
  ApiOperation({ summary: 'Test' }) as MethodDecorator,
  ApiResponse({ status: 200 }) as MethodDecorator,
];

return applyDecorators(
  ...guardDecorators,
  ...swaggerDecorators as Array<MethodDecorator & ClassDecorator>
);
```

### 4. Create Type-Safe Helper Functions

```typescript
// Helper function for Swagger decorators
const createSwaggerDecorators = (summary: string, description: string) => [
  ApiOperation({ summary, description }) as MethodDecorator,
  ApiResponse({ status: 200, description: 'Success' }) as MethodDecorator,
  ApiForbiddenResponse({ description: 'Access denied' }) as MethodDecorator,
];

// Usage
export const RBAC = (options: RBACOptions) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
    ...createSwaggerDecorators('Protected endpoint', options.description),
  ];

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Testing Decorator Composition

### Unit Test Example

```typescript
describe('RBAC Decorator', () => {
  it('should compose decorators without TypeScript errors', () => {
    const decorator = RBAC({
      permission: 'TEST_PERMISSION',
      description: 'Test endpoint'
    });

    expect(decorator).toBeDefined();
    expect(typeof decorator).toBe('function');
  });

  it('should apply to controller methods', () => {
    class TestController {
      @RBAC({ permission: 'TEST_PERMISSION' })
      testMethod() {
        return 'test';
      }
    }

    const controller = new TestController();
    expect(controller.testMethod()).toBe('test');
  });
});
```

## Troubleshooting

### Error: "Cannot read property of undefined"

**Cause:** Decorator not properly typed or applied.

**Solution:** Ensure all decorators are properly cast and the array is correctly typed.

### Error: "Decorator function expected"

**Cause:** Incorrect return type from decorator factory.

**Solution:** Ensure `applyDecorators()` is called with properly typed decorators.

### Error: "Metadata key already exists"

**Cause:** Duplicate metadata keys in decorator composition.

**Solution:** Check for duplicate `SetMetadata()` calls with the same key.

## Advanced Patterns

### Conditional Decorator Application

```typescript
export const ConditionalRBAC = (options: RBACOptions & { condition?: boolean }) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard),
    ApiBearerAuth(),
  ];

  if (options.condition !== false) {
    decorators.push(UseGuards(RBACGuard));
  }

  if (options.permission) {
    decorators.push(SetMetadata(PERMISSION_KEY, options.permission));
  }

  // Always add Swagger documentation
  decorators.push(
    ApiOperation({ 
      summary: options.condition ? 'Protected endpoint' : 'Authenticated endpoint' 
    }) as MethodDecorator
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

### Decorator Factory with Validation

```typescript
export const ValidatedRBAC = (options: RBACOptions) => {
  // Validate options
  if (!options.permission && !options.role && options.roleLevel === undefined) {
    throw new Error('At least one RBAC option must be provided');
  }

  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  // Add metadata based on options
  if (options.permission) {
    decorators.push(SetMetadata(PERMISSION_KEY, options.permission));
  }

  // Add Swagger documentation
  decorators.push(
    ApiOperation({ 
      summary: `Requires: ${Object.keys(options).join(', ')}`,
      description: options.description || 'RBAC protected endpoint'
    }) as MethodDecorator
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Conclusion

Proper decorator composition in NestJS requires:

1. **Explicit typing** of decorator arrays
2. **Type casting** of Swagger decorators
3. **Final casting** for `applyDecorators()`
4. **Consistent patterns** across the codebase

Following these patterns ensures TypeScript compatibility while maintaining the bilingual documentation functionality and RBAC system integrity.

---

For more information about NestJS decorators, see the [official NestJS documentation](https://docs.nestjs.com/custom-decorators).
