import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import { SecurityMethod } from '../../users/entities/user.entity';

export interface VerificationSession {
  userId: string;
  sessionId: string;
  securityMethod: SecurityMethod;
  expiresAt: Date;
  deviceInfo: {
    ip: string;
    userAgent: string;
    deviceName?: string;
  };
}

@Injectable()
export class VerificationService {
  private verificationSessions = new Map<string, VerificationSession>();

  constructor(private configService: ConfigService) {}

  /**
   * Generate a 6-digit verification code
   */
  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Generate a 2FA secret for a user
   */
  generateTwoFactorSecret(userEmail: string): { secret: string; qrCodeUrl: string; manualEntryKey: string } {
    const secret = speakeasy.generateSecret({
      name: userEmail,
      issuer: this.configService.get<string>('APP_NAME', 'Delify'),
      length: 32,
    });

    return {
      secret: secret.base32,
      qrCodeUrl: secret.otpauth_url,
      manualEntryKey: secret.base32,
    };
  }

  /**
   * Verify a TOTP code
   */
  verifyTwoFactorCode(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      token,
      window: 2, // Allow 2 time steps (60 seconds) of tolerance
      encoding: 'base32',
    });
  }

  /**
   * Generate QR code as data URL
   */
  async generateQRCode(otpauthUrl: string): Promise<string> {
    try {
      return await qrcode.toDataURL(otpauthUrl);
    } catch (error) {
      throw new BadRequestException('Failed to generate QR code');
    }
  }

  /**
   * Create a temporary verification session
   */
  createVerificationSession(
    userId: string,
    securityMethod: SecurityMethod,
    deviceInfo: { ip: string; userAgent: string; deviceName?: string }
  ): string {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    this.verificationSessions.set(sessionId, {
      userId,
      sessionId,
      securityMethod,
      expiresAt,
      deviceInfo,
    });

    // Clean up expired sessions
    this.cleanupExpiredSessions();

    return sessionId;
  }

  /**
   * Get verification session
   */
  getVerificationSession(sessionId: string): VerificationSession | null {
    const session = this.verificationSessions.get(sessionId);

    if (!session) {
      return null;
    }

    if (new Date() > session.expiresAt) {
      this.verificationSessions.delete(sessionId);
      return null;
    }

    return session;
  }

  /**
   * Remove verification session
   */
  removeVerificationSession(sessionId: string): void {
    this.verificationSessions.delete(sessionId);
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    for (const [sessionId, session] of this.verificationSessions.entries()) {
      if (now > session.expiresAt) {
        this.verificationSessions.delete(sessionId);
      }
    }
  }

  /**
   * Get time remaining for verification session
   */
  getSessionTimeRemaining(sessionId: string): number {
    const session = this.getVerificationSession(sessionId);
    if (!session) {
      return 0;
    }

    const remaining = Math.max(0, Math.floor((session.expiresAt.getTime() - Date.now()) / 1000));
    return remaining;
  }

  /**
   * Validate fixed code format
   */
  validateFixedCodeFormat(code: string): { isValid: boolean; error?: string } {
    if (!code) {
      return { isValid: false, error: 'Fixed code is required' };
    }

    // Remove any whitespace
    const cleanCode = code.trim();

    // Check length (6-8 digits)
    if (cleanCode.length < 6 || cleanCode.length > 8) {
      return { isValid: false, error: 'Fixed code must be 6-8 digits long' };
    }

    // Check if it contains only digits
    if (!/^\d+$/.test(cleanCode)) {
      return { isValid: false, error: 'Fixed code must contain only digits' };
    }

    // Check for weak patterns
    if (this.isWeakFixedCode(cleanCode)) {
      return { isValid: false, error: 'Fixed code is too weak. Avoid sequential numbers, repeated digits, or common patterns' };
    }

    return { isValid: true };
  }

  /**
   * Check if fixed code is weak (sequential, repeated, etc.)
   */
  private isWeakFixedCode(code: string): boolean {
    // Check for repeated digits (e.g., 111111, 222222)
    if (/^(\d)\1+$/.test(code)) {
      return true;
    }

    // Check for sequential ascending (e.g., 123456, 234567)
    let isAscending = true;
    for (let i = 1; i < code.length; i++) {
      if (parseInt(code[i]) !== parseInt(code[i - 1]) + 1) {
        isAscending = false;
        break;
      }
    }
    if (isAscending) {
      return true;
    }

    // Check for sequential descending (e.g., 654321, 543210)
    let isDescending = true;
    for (let i = 1; i < code.length; i++) {
      if (parseInt(code[i]) !== parseInt(code[i - 1]) - 1) {
        isDescending = false;
        break;
      }
    }
    if (isDescending) {
      return true;
    }

    // Check for common weak patterns
    const weakPatterns = [
      '000000', '111111', '222222', '333333', '444444', '555555',
      '666666', '777777', '888888', '999999', '123456', '654321',
      '000000', '111111', '123123', '456456', '789789'
    ];

    return weakPatterns.includes(code);
  }

  /**
   * Hash fixed code for storage
   */
  async hashFixedCode(code: string): Promise<string> {
    // Use a simple hash for fixed codes (in production, use bcrypt or similar)
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  /**
   * Verify fixed code against hash
   */
  async verifyFixedCode(code: string, hash: string): Promise<boolean> {
    const hashedInput = await this.hashFixedCode(code);
    return hashedInput === hash;
  }
}
