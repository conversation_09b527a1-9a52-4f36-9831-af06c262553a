// Global teardown for all tests
export default async function globalTeardown() {
  console.log('🧹 Starting global test teardown...');

  // Clean up test database connections
  await cleanupDatabase();

  // Clean up Redis connections
  await cleanupRedis();

  // Clean up any test files
  await cleanupTestFiles();

  // Clean up any running processes
  await cleanupProcesses();

  console.log('✅ Global test teardown completed');
}

async function cleanupDatabase() {
  // For SQLite in-memory, no cleanup needed
  // For other databases, close connections here
  console.log('📊 Database cleanup completed');
}

async function cleanupRedis() {
  // Redis is mocked, no cleanup needed
  console.log('🔴 Redis cleanup completed');
}

async function cleanupTestFiles() {
  const fs = require('fs').promises;
  const path = require('path');

  try {
    // Clean up test upload directory
    const testUploadsDir = path.join(__dirname, 'uploads');
    try {
      await fs.rmdir(testUploadsDir, { recursive: true });
      console.log('📁 Test uploads directory cleaned');
    } catch (error) {
      // Directory might not exist, that's okay
    }

    // Clean up any temporary test files
    const tempFiles = [
      path.join(__dirname, 'temp-test-file.txt'),
      path.join(__dirname, 'test-output.log'),
    ];

    for (const file of tempFiles) {
      try {
        await fs.unlink(file);
        console.log(`🗑️  Removed temp file: ${file}`);
      } catch (error) {
        // File might not exist, that's okay
      }
    }

  } catch (error) {
    console.log('⚠️  Error during file cleanup:', error.message);
  }
}

async function cleanupProcesses() {
  // Kill any hanging processes that might have been started during tests
  // This is especially important for integration tests

  try {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
      console.log('🗑️  Garbage collection completed');
    }

    // Clear any intervals or timeouts
    // Note: This is a simplified cleanup, in practice you'd track specific timers

    console.log('⏰ Cleared all timeouts and intervals');

  } catch (error) {
    console.log('⚠️  Error during process cleanup:', error.message);
  }
}

// Handle uncaught exceptions during teardown
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception during teardown:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection during teardown:', reason);
  process.exit(1);
});
