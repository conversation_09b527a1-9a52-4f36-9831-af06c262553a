import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Entities
import { UserSession } from './entities/user-session.entity';
import { SSOApplication } from './entities/sso-application.entity';
import { JWTBlacklist } from './entities/jwt-blacklist.entity';
import { SSOAuditLog } from './entities/sso-audit-log.entity';

// Services
import { SSOService } from './services/sso.service';
import { SSOConfigService } from './services/sso-config.service';
import { SessionManagementService } from './services/session-management.service';
import { JWTBlacklistService } from './services/jwt-blacklist.service';
import { DeviceFingerprintService } from './services/device-fingerprint.service';

// Controllers
import { SSOController } from './controllers/sso.controller';
import { SSOApplicationController } from './controllers/sso-application.controller';

// Other modules
import { AuthModule } from '../auth/auth.module';
import { RBACModule } from '../rbac/rbac.module';
import { CacheModule } from '../cache/cache.module';

/**
 * SSO Module - Module quản lý hệ thống Single Sign-On
 * SSO Module - Single Sign-On system module
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      UserSession,
      SSOApplication,
      JWTBlacklist,
      SSOAuditLog,
    ]),

    // JWT Module for token operations
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '15m'),
        },
      }),
      inject: [ConfigService],
    }),

    // Forward reference to avoid circular dependency
    forwardRef(() => AuthModule),
    forwardRef(() => RBACModule),
    
    // Cache module for session and token caching
    CacheModule,
    
    // Config module
    ConfigModule,
  ],
  
  controllers: [
    SSOController,
    SSOApplicationController,
  ],
  
  providers: [
    // Core services
    SSOService,
    SSOConfigService,
    SessionManagementService,
    JWTBlacklistService,
    DeviceFingerprintService,
  ],
  
  exports: [
    // Export services for use in other modules
    SSOService,
    SSOConfigService,
    SessionManagementService,
    JWTBlacklistService,
    DeviceFingerprintService,
    
    // Export TypeORM repositories for advanced usage
    TypeOrmModule,
  ],
})
export class SSOModule {
  /**
   * Cấu hình module cho root application
   * Configure module for root application
   */
  static forRoot() {
    return {
      module: SSOModule,
      global: true, // Make SSO available globally
    };
  }

  /**
   * Cấu hình module cho feature modules
   * Configure module for feature modules
   */
  static forFeature() {
    return {
      module: SSOModule,
    };
  }
}
