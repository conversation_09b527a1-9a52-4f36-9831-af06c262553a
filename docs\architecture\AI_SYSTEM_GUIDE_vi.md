# 🤖 Hướng Dẫn Hệ Thống AI Đa Nhà Cung Cấp

## 📋 Tổng Quan

Delify Platform tích hợp **4 nhà cung cấp AI** để cung cấp khả năng AI toàn diện với lựa chọn nhà cung cấp thông minh, tối ưu hóa chi phí, và các tùy chọn ưu tiên quyền riêng tư.

## 🎯 Kiến Trúc AI

```
┌─────────────────────────────────────────────────────────────┐
│                    Kiến Trúc Hệ Thống AI                   │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Yêu Cầu   │ │     AI      │ │  Factory    │          │
│  │  Từ Client  │ │ Controller  │ │ Nhà Cung    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Lựa Chọn    │ │     AI      │ │  Lựa Chọn   │          │
│  │ Thông Minh  │ │  Service    │ │ Nhà Cung    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │    Grok     │ │   Gemini    │          │
│  │  Provider   │ │  Provider   │ │  Provider   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐                                          │
│  │   OLLAMA    │                                          │
│  │  Provider   │                                          │
│  │ (AI Cục Bộ) │                                          │
│  │  └─────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Các Nhà Cung Cấp AI Được Hỗ Trợ

### 1. OpenAI (Cloud)
```typescript
// Các models được hỗ trợ
enum OpenAIModels {
  GPT_3_5_TURBO = 'gpt-3.5-turbo',      // Nhanh, hiệu quả chi phí
  GPT_4 = 'gpt-4',                      // Lý luận chất lượng cao
  GPT_4_TURBO = 'gpt-4-turbo',          // Mới nhất với khả năng thị giác
  GPT_4O = 'gpt-4o',                    // Phiên bản tối ưu hóa
}

// Khả năng
- ✅ Tạo văn bản
- ✅ Tạo mã nguồn  
- ✅ Phân tích hình ảnh (GPT-4 Turbo, GPT-4o)
- ✅ Gọi hàm
- ✅ Phản hồi streaming
- ✅ Kiểm duyệt nội dung
```

### 2. Grok (xAI - Cloud)
```typescript
// Các models được hỗ trợ
enum GrokModels {
  GROK_1 = 'grok-1',                    // Thế hệ đầu tiên
  GROK_1_5 = 'grok-1.5',               // Lý luận được cải thiện
  GROK_2 = 'grok-2',                   // Mới nhất với khả năng thị giác
}

// Khả năng
- ✅ Tạo văn bản
- ✅ Tạo mã nguồn
- ✅ Truy cập dữ liệu thời gian thực
- ✅ Phản hồi streaming
- ⚠️ Phân tích hình ảnh (chỉ Grok-2)
- ❌ Embeddings (chưa có sẵn)
```

### 3. Gemini (Google - Cloud)
```typescript
// Các models được hỗ trợ
enum GeminiModels {
  GEMINI_PRO = 'gemini-pro',                    // Tạo văn bản
  GEMINI_PRO_VISION = 'gemini-pro-vision',      // Văn bản + Hình ảnh
  GEMINI_ULTRA = 'gemini-ultra',                // Khả năng mạnh nhất
  GEMINI_1_5_PRO = 'gemini-1.5-pro',           // Ngữ cảnh 1M token
}

// Khả năng
- ✅ Tạo văn bản
- ✅ Tạo mã nguồn
- ✅ Phân tích hình ảnh (Pro Vision, Ultra, 1.5 Pro)
- ✅ Text embeddings
- ✅ Phản hồi streaming
- ✅ Lọc an toàn
- ✅ Ngữ cảnh dài (1M tokens cho 1.5 Pro)
```

### 4. OLLAMA (Cục Bộ)
```typescript
// Các models được hỗ trợ
enum OllamaModels {
  LLAMA_2_7B = 'llama2:7b',             // Mục đích chung
  LLAMA_2_13B = 'llama2:13b',           // Hiệu suất tốt hơn
  CODE_LLAMA_7B = 'codellama:7b',       // Chuyên về mã nguồn
  CODE_LLAMA_13B = 'codellama:13b',     // Lập trình nâng cao
  MISTRAL_7B = 'mistral:7b',            // Nhanh và hiệu quả
  PHI_3_MINI = 'phi3:mini',             // Model nhỏ gọn
  NEURAL_CHAT = 'neural-chat:7b',       // Đối thoại
  STARCODE = 'starcoder:7b',            // Mã đa ngôn ngữ
}

// Khả năng
- ✅ Tạo văn bản
- ✅ Tạo mã nguồn
- ✅ Phản hồi streaming
- ✅ Quyền riêng tư hoàn toàn (xử lý cục bộ)
- ✅ Không có chi phí API
- ✅ Khả năng offline
- ⚠️ Phân tích hình ảnh (với models LLaVA)
- ⚠️ Embeddings (với models cụ thể)
```

## 🎯 Lựa Chọn Nhà Cung Cấp Thông Minh

### Logic Lựa Chọn
```typescript
const providerPreferences = {
  text: ['grok', 'ollama', 'openai', 'gemini'],      // Grok cho văn bản chung
  code: ['ollama', 'openai', 'grok', 'gemini'],      // Ưu tiên Code Llama
  image: ['openai', 'gemini', 'grok'],               // Khả năng thị giác
  creative: ['grok', 'ollama', 'openai', 'gemini'],  // Nội dung sáng tạo
  embedding: ['openai', 'gemini', 'ollama'],         // Hỗ trợ embedding
  privacy: ['ollama'],                                // Chỉ xử lý cục bộ
};
```

### Tối Ưu Hóa Chi Phí
```typescript
// Chi phí trên 1K tokens (ước tính)
const providerCosts = {
  openai: {
    'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
  },
  grok: {
    'grok-1.5': { input: 0.002, output: 0.004 },
  },
  gemini: {
    'gemini-pro': { input: 0.0005, output: 0.0015 },
  },
  ollama: {
    'all-models': { input: 0, output: 0 }, // Xử lý cục bộ
  },
};
```

## 🔧 Chi Tiết Triển Khai

### Mẫu Provider Factory
```typescript
@Injectable()
export class AIProviderFactory {
  createProvider(config: ProviderConfig): BaseAIProvider {
    switch (config.provider) {
      case AIProvider.OPENAI:
        return new OpenAIProvider(config);
      case AIProvider.GROK:
        return new GrokProvider(config);
      case AIProvider.GEMINI:
        return new GeminiProvider(config);
      case AIProvider.OLLAMA:
        return new OllamaProvider(config);
      default:
        throw new Error(`Nhà cung cấp không được hỗ trợ: ${config.provider}`);
    }
  }
  
  createProviderFromEnv(provider: AIProvider, model?: AIModelName): BaseAIProvider {
    const config = this.getProviderConfigs()[provider];
    return this.createProvider({
      provider,
      model: model || this.getDefaultModel(provider),
      ...config,
    });
  }
}
```

### Interface Provider Cơ Bản
```typescript
export abstract class BaseAIProvider {
  protected config: AIProviderConfig;
  
  constructor(config: AIProviderConfig) {
    this.config = config;
  }
  
  abstract generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown>;
  
  abstract analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract validateConfig(): Promise<boolean>;
}
```

## 🚀 Ví Dụ Sử Dụng API

### 1. Tạo Văn Bản Thông Minh
```typescript
POST /api/v1/ai/smart/generate
{
  "messages": [
    {
      "role": "user",
      "content": "Giải thích điện toán lượng tử bằng thuật ngữ đơn giản"
    }
  ],
  "task": "text",
  "options": {
    "temperature": 0.7,
    "maxTokens": 500
  }
}

// Phản hồi
{
  "content": "Điện toán lượng tử giống như...",
  "usage": {
    "promptTokens": 10,
    "completionTokens": 150,
    "totalTokens": 160
  },
  "model": "grok-1.5",
  "providerUsed": {
    "provider": "grok",
    "model": "grok-1.5",
    "reason": "Grok được tối ưu hóa cho tạo văn bản"
  }
}
```

### 2. Sử Dụng Nhà Cung Cấp Cụ Thể
```typescript
POST /api/v1/ai/generate/text
{
  "provider": "ollama",
  "model": "llama2:7b",
  "messages": [
    {
      "role": "system",
      "content": "Bạn là một trợ lý lập trình hữu ích."
    },
    {
      "role": "user",
      "content": "Viết một hàm Python để tính fibonacci"
    }
  ],
  "options": {
    "temperature": 0.2,
    "maxTokens": 300
  }
}
```

### 3. So Sánh Nhà Cung Cấp
```typescript
POST /api/v1/ai/compare/providers
{
  "messages": [
    {
      "role": "user",
      "content": "Viết một câu chuyện sáng tạo về AI"
    }
  ],
  "providers": [
    { "provider": "openai", "model": "gpt-4-turbo" },
    { "provider": "grok", "model": "grok-1.5" },
    { "provider": "ollama", "model": "llama2:7b" }
  ],
  "options": {
    "temperature": 0.8,
    "maxTokens": 500
  }
}

// Phản hồi
{
  "results": [
    {
      "provider": "openai",
      "model": "gpt-4-turbo",
      "response": { /* Phản hồi AI */ },
      "responseTime": 1200,
      "success": true
    },
    {
      "provider": "grok",
      "model": "grok-1.5",
      "response": { /* Phản hồi AI */ },
      "responseTime": 800,
      "success": true
    },
    {
      "provider": "ollama",
      "model": "llama2:7b",
      "response": { /* Phản hồi AI */ },
      "responseTime": 2000,
      "success": true
    }
  ],
  "comparison": {
    "fastest": { "provider": "grok", "responseTime": 800 },
    "mostTokens": { "provider": "openai", "totalTokens": 520 }
  }
}
```

### 4. Quản Lý Model OLLAMA
```typescript
// Liệt kê các models có sẵn
GET /api/v1/ai/ollama/models

// Tải model mới
POST /api/v1/ai/ollama/models/pull
{
  "modelName": "mistral:7b"
}

// Xóa model
DELETE /api/v1/ai/ollama/models/llama2:7b

// Lấy đề xuất model
GET /api/v1/ai/ollama/models/recommended?task=code

// Tạo mã với OLLAMA
POST /api/v1/ai/ollama/code/generate
{
  "model": "codellama:7b",
  "prompt": "Tạo một REST API endpoint cho xác thực người dùng",
  "language": "typescript"
}
```

## 🔄 Chiến Lược Lựa Chọn Nhà Cung Cấp

### 1. Lựa Chọn Dựa Trên Tác Vụ
```typescript
async getBestProviderForTask(task: string): Promise<ProviderRecommendation> {
  switch (task) {
    case 'text':
      return this.selectProvider(['grok', 'ollama', 'openai', 'gemini']);
    case 'code':
      return this.selectProvider(['ollama', 'openai', 'grok', 'gemini']);
    case 'image':
      return this.selectProvider(['openai', 'gemini', 'grok']);
    case 'privacy':
      return this.selectProvider(['ollama']);
    default:
      return this.selectProvider(['grok', 'openai', 'gemini', 'ollama']);
  }
}
```

### 2. Lựa Chọn Dựa Trên Chi Phí
```typescript
async getCheapestProvider(task: string): Promise<ProviderRecommendation> {
  const providers = await this.getAvailableProviders();
  const costs = providers.map(p => ({
    provider: p,
    cost: this.calculateCost(p, estimatedTokens),
  }));

  return costs.sort((a, b) => a.cost - b.cost)[0];
}
```

### 3. Lựa Chọn Dựa Trên Hiệu Suất
```typescript
async getFastestProvider(task: string): Promise<ProviderRecommendation> {
  const providers = await this.getAvailableProviders();
  const performance = await this.getProviderPerformanceMetrics();

  return providers.sort((a, b) =>
    performance[a.provider].avgResponseTime - performance[b.provider].avgResponseTime
  )[0];
}
```

## 📊 Theo Dõi Sử Dụng & Phân Tích

### Ghi Log Yêu Cầu
```typescript
@Injectable()
export class AIUsageService {
  async logRequest(request: AIRequest): Promise<void> {
    await this.usageRepository.save({
      userId: request.userId,
      organizationId: request.organizationId,
      provider: request.provider,
      model: request.model,
      promptTokens: request.usage.promptTokens,
      completionTokens: request.usage.completionTokens,
      totalTokens: request.usage.totalTokens,
      responseTime: request.responseTime,
      cost: this.calculateCost(request),
      timestamp: new Date(),
    });
  }

  async getUsageStats(organizationId: string, period: string): Promise<UsageStats> {
    return this.usageRepository
      .createQueryBuilder('usage')
      .select([
        'provider',
        'COUNT(*) as requests',
        'SUM(totalTokens) as totalTokens',
        'SUM(cost) as totalCost',
        'AVG(responseTime) as avgResponseTime',
      ])
      .where('organizationId = :organizationId', { organizationId })
      .andWhere('timestamp >= :startDate', { startDate: this.getStartDate(period) })
      .groupBy('provider')
      .getRawMany();
  }
}
```

### Tính Toán Chi Phí

```typescript
calculateCost(request: AIRequest): number {
  if (request.provider === AIProvider.OLLAMA) {
    return 0; // Xử lý cục bộ
  }

  const pricing = this.getPricing(request.provider, request.model);
  const inputCost = (request.usage.promptTokens / 1000) * pricing.input;
  const outputCost = (request.usage.completionTokens / 1000) * pricing.output;

  return inputCost + outputCost;
}
```

## 🔒 Bảo Mật & Quyền Riêng Tư

### Mức Độ Quyền Riêng Tư

```typescript
enum PrivacyLevel {
  PUBLIC = 'public',           // Có thể sử dụng bất kỳ nhà cung cấp nào
  INTERNAL = 'internal',       // Ưu tiên nhà cung cấp cục bộ hoặc đáng tin cậy
  CONFIDENTIAL = 'confidential', // Chỉ xử lý cục bộ
  RESTRICTED = 'restricted',   // Không cho phép xử lý AI
}

async selectProviderByPrivacy(
  privacyLevel: PrivacyLevel,
  task: string
): Promise<ProviderRecommendation> {
  switch (privacyLevel) {
    case PrivacyLevel.CONFIDENTIAL:
    case PrivacyLevel.RESTRICTED:
      return this.selectProvider(['ollama']);
    case PrivacyLevel.INTERNAL:
      return this.selectProvider(['ollama', 'openai']);
    default:
      return this.getBestProviderForTask(task);
  }
}
```

### Quản Lý API Key

```typescript
// Cấu hình dựa trên môi trường
const providerConfigs = {
  [AIProvider.OPENAI]: {
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
  },
  [AIProvider.GROK]: {
    apiKey: process.env.GROK_API_KEY,
    baseURL: process.env.GROK_BASE_URL,
  },
  [AIProvider.GEMINI]: {
    apiKey: process.env.GEMINI_API_KEY,
    baseURL: process.env.GEMINI_BASE_URL,
  },
  [AIProvider.OLLAMA]: {
    baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    // Không cần API key cho OLLAMA
  },
};
```

## 🧪 Kiểm Thử Tính Năng AI

### Unit Tests

```typescript
describe('AIProviderFactory', () => {
  it('should create correct provider instance', () => {
    const provider = factory.createProvider({
      provider: AIProvider.OLLAMA,
      model: AIModelName.LLAMA_2_7B,
    });

    expect(provider).toBeInstanceOf(OllamaProvider);
  });
});
```

### Integration Tests

```typescript
describe('AI Controller (Integration)', () => {
  it('should generate text using smart selection', async () => {
    const response = await request(app)
      .post('/ai/smart/generate')
      .send({
        messages: [{ role: 'user', content: 'Xin chào' }],
        task: 'text',
      })
      .expect(201);

    expect(response.body).toHaveProperty('content');
    expect(response.body).toHaveProperty('providerUsed');
  });
});
```

## 🎯 Thực Hành Tốt Nhất

### 1. Lựa Chọn Nhà Cung Cấp
- **Sử dụng lựa chọn thông minh** cho hiệu suất tối ưu
- **Xem xét yêu cầu quyền riêng tư** khi chọn nhà cung cấp
- **Giám sát chi phí** và tối ưu hóa sử dụng
- **Triển khai fallbacks** cho các lỗi nhà cung cấp

### 2. Xử Lý Lỗi
- **Degradation nhẹ nhàng** khi nhà cung cấp không khả dụng
- **Logic retry** với exponential backoff
- **Thông báo lỗi rõ ràng** cho người dùng
- **Logging toàn diện** cho debugging

### 3. Tối Ưu Hóa Hiệu Suất
- **Cache phản hồi** cho các yêu cầu lặp lại
- **Sử dụng streaming** cho ứng dụng thời gian thực
- **Giám sát thời gian phản hồi** và tối ưu hóa
- **Load balance** giữa các nhà cung cấp

**Hệ thống AI này cung cấp khả năng AI cấp doanh nghiệp với tính linh hoạt, tối ưu hóa chi phí, và bảo vệ quyền riêng tư.** 🤖✨
