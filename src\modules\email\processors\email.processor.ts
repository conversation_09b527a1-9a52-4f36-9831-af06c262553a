import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailCampaign, CampaignStatus } from '../entities/email-campaign.entity';
import { EmailLog, EmailStatus } from '../entities/email-log.entity';
import { LoggerService } from '../../../common/services/logger.service';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
@Processor('email')
export class EmailProcessor {
  private transporter: nodemailer.Transporter;

  constructor(
    @InjectRepository(EmailCampaign)
    private emailCampaignRepository: Repository<EmailCampaign>,
    @InjectRepository(EmailLog)
    private emailLogRepository: Repository<EmailLog>,
    private logger: LoggerService,
    private configService: ConfigService,
  ) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get('SMTP_HOST'),
      port: this.configService.get('SMTP_PORT', 587),
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.configService.get('SMTP_USER'),
        pass: this.configService.get('SMTP_PASS'),
      },
    });
  }

  @Process('send-campaign')
  async handleSendCampaign(job: Job<{ campaignId: string }>) {
    const { campaignId } = job.data;

    try {
      const campaign = await this.emailCampaignRepository.findOne({
        where: { id: campaignId },
        relations: ['user'],
      });

      if (!campaign) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      this.logger.logWithContext(`Sending email campaign: ${campaignId}`, 'EmailProcessor');

      // Update campaign status
      campaign.status = CampaignStatus.SENDING;
      campaign.sentCount = 0;
      campaign.failedCount = 0;
      await this.emailCampaignRepository.save(campaign);

      // Send emails to all recipients
      for (const recipient of campaign.recipients) {
        try {
          await this.sendSingleEmail(campaign, recipient);
          campaign.sentCount += 1;
        } catch (error) {
          this.logger.logError(error, `Failed to send email to ${recipient.email}`);
          campaign.failedCount += 1;
        }
      }

      // Update campaign status
      campaign.status = CampaignStatus.SENT;
      campaign.sentAt = new Date();
      await this.emailCampaignRepository.save(campaign);

      this.logger.logWithContext(
        `Campaign sent successfully: ${campaignId} (${campaign.sentCount}/${campaign.totalRecipients})`,
        'EmailProcessor'
      );

    } catch (error) {
      this.logger.logError(error, 'EmailProcessor - handleSendCampaign');

      // Update campaign status to failed
      await this.emailCampaignRepository.update(campaignId, {
        status: CampaignStatus.CANCELLED,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  @Process('send-single-email')
  async handleSendSingleEmail(job: Job<{
    to: string;
    subject: string;
    htmlContent: string;
    textContent?: string;
    from?: string;
    userId: string;
  }>) {
    const { to, subject, htmlContent, textContent, from, userId } = job.data;

    try {
      this.logger.logWithContext(`Sending single email to: ${to}`, 'EmailProcessor');

      const emailLog = this.emailLogRepository.create({
        userId,
        recipientEmail: to,
        subject,
        status: EmailStatus.SENDING,
      });
      await this.emailLogRepository.save(emailLog);

      const mailOptions = {
        from: from || this.configService.get('SMTP_FROM'),
        to,
        subject,
        html: htmlContent,
        text: textContent,
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Update email log
      emailLog.status = EmailStatus.SENT;
      emailLog.messageId = result.messageId;
      emailLog.sentAt = new Date();
      emailLog.providerResponse = JSON.stringify(result);
      await this.emailLogRepository.save(emailLog);

      this.logger.logWithContext(`Email sent successfully to: ${to}`, 'EmailProcessor');

    } catch (error) {
      this.logger.logError(error, 'EmailProcessor - handleSendSingleEmail');

      // Update email log with error
      const emailLog = await this.emailLogRepository.findOne({
        where: { recipientEmail: to, status: EmailStatus.SENDING },
        order: { createdAt: 'DESC' },
      });

      if (emailLog) {
        emailLog.status = EmailStatus.FAILED;
        emailLog.errorMessage = error.message;
        await this.emailLogRepository.save(emailLog);
      }

      throw error;
    }
  }

  @Process('process-email-events')
  async handleEmailEvents(job: Job<{
    messageId: string;
    event: 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed';
    timestamp: string;
    metadata?: any;
  }>) {
    const { messageId, event, timestamp, metadata } = job.data;

    try {
      const emailLog = await this.emailLogRepository.findOne({
        where: { messageId },
      });

      if (!emailLog) {
        this.logger.logWithContext(`Email log not found for messageId: ${messageId}`, 'EmailProcessor', 'warn');
        return;
      }

      // Update email log based on event
      const eventDate = new Date(timestamp);

      switch (event) {
        case 'delivered':
          emailLog.status = EmailStatus.DELIVERED;
          emailLog.deliveredAt = eventDate;
          break;
        case 'opened':
          emailLog.status = EmailStatus.OPENED;
          emailLog.openedAt = eventDate;
          break;
        case 'clicked':
          emailLog.status = EmailStatus.CLICKED;
          emailLog.clickedAt = eventDate;
          break;
        case 'bounced':
          emailLog.status = EmailStatus.BOUNCED;
          emailLog.bouncedAt = eventDate;
          break;
        case 'complained':
          emailLog.status = EmailStatus.COMPLAINED;
          break;
        case 'unsubscribed':
          emailLog.status = EmailStatus.UNSUBSCRIBED;
          emailLog.unsubscribedAt = eventDate;
          break;
      }

      // Add event to events array
      if (!emailLog.events) {
        emailLog.events = [];
      }
      emailLog.events.push({
        type: event,
        timestamp,
        data: metadata,
      });

      // Update metadata if provided
      if (metadata) {
        emailLog.metadata = { ...emailLog.metadata, ...metadata };
      }

      await this.emailLogRepository.save(emailLog);

      this.logger.logWithContext(`Email event processed: ${event} for ${messageId}`, 'EmailProcessor');

    } catch (error) {
      this.logger.logError(error, 'EmailProcessor - handleEmailEvents');
      throw error;
    }
  }

  @Process('update-campaign-analytics')
  async handleUpdateCampaignAnalytics(job: Job<{ campaignId: string }>) {
    const { campaignId } = job.data;

    try {
      const campaign = await this.emailCampaignRepository.findOne({
        where: { id: campaignId },
      });

      if (!campaign) {
        return;
      }

      // Get email logs for this campaign
      const emailLogs = await this.emailLogRepository.find({
        where: { campaignId },
      });

      // Calculate analytics
      const analytics: any = {
        totalSent: emailLogs.filter(log => log.status !== EmailStatus.FAILED).length,
        delivered: emailLogs.filter(log => log.wasDelivered).length,
        bounced: emailLogs.filter(log => log.hasBounced).length,
        opened: emailLogs.filter(log => log.wasOpened).length,
        clicked: emailLogs.filter(log => log.wasClicked).length,
        unsubscribed: emailLogs.filter(log => log.status === EmailStatus.UNSUBSCRIBED).length,
        complained: emailLogs.filter(log => log.status === EmailStatus.COMPLAINED).length,
      };

      // Calculate rates
      analytics.openRate = analytics.delivered > 0 ?
        Math.round((analytics.opened / analytics.delivered) * 100) : 0;
      analytics.clickRate = analytics.delivered > 0 ?
        Math.round((analytics.clicked / analytics.delivered) * 100) : 0;
      analytics.bounceRate = analytics.totalSent > 0 ?
        Math.round((analytics.bounced / analytics.totalSent) * 100) : 0;

      // Update campaign analytics
      campaign.analytics = analytics;
      await this.emailCampaignRepository.save(campaign);

      this.logger.logWithContext(`Campaign analytics updated: ${campaignId}`, 'EmailProcessor');

    } catch (error) {
      this.logger.logError(error, 'EmailProcessor - handleUpdateCampaignAnalytics');
      throw error;
    }
  }

  private async sendSingleEmail(campaign: EmailCampaign, recipient: any) {
    // Replace variables in content
    let htmlContent = campaign.htmlContent;
    let textContent = campaign.textContent;
    let subject = campaign.subject;

    if (recipient.variables) {
      for (const [key, value] of Object.entries(recipient.variables)) {
        const placeholder = `{{${key}}}`;
        htmlContent = htmlContent.replace(new RegExp(placeholder, 'g'), value as string);
        if (textContent) {
          textContent = textContent.replace(new RegExp(placeholder, 'g'), value as string);
        }
        subject = subject.replace(new RegExp(placeholder, 'g'), value as string);
      }
    }

    // Create email log
    const emailLog = this.emailLogRepository.create({
      userId: campaign.userId,
      campaignId: campaign.id,
      recipientEmail: recipient.email,
      recipientName: recipient.name,
      subject,
      status: EmailStatus.SENDING,
    });
    await this.emailLogRepository.save(emailLog);

    const mailOptions = {
      from: campaign.settings?.fromEmail || this.configService.get('SMTP_FROM'),
      to: recipient.email,
      subject,
      html: htmlContent,
      text: textContent,
    };

    const result = await this.transporter.sendMail(mailOptions);

    // Update email log
    emailLog.status = EmailStatus.SENT;
    emailLog.messageId = result.messageId;
    emailLog.sentAt = new Date();
    emailLog.providerResponse = JSON.stringify(result);
    await this.emailLogRepository.save(emailLog);
  }
}
