# Cache Module Documentation

## Overview

The Cache Module provides a comprehensive Redis-based caching solution for the Delify Platform, specifically designed to support SSO (Single Sign-On) session management, token blacklisting, and general application caching needs.

## Features

- **Redis Integration**: Full Redis support with connection pooling
- **SSO Support**: Optimized for SSO session storage and token management
- **Decorators**: Easy-to-use caching decorators for methods
- **Health Monitoring**: Built-in health checks and statistics
- **Global Module**: Available throughout the application
- **TypeScript Support**: Full type safety and IntelliSense

## Installation

The cache module is already configured and ready to use. Required dependencies:

```json
{
  "@nestjs/cache-manager": "^2.1.0",
  "cache-manager": "^5.2.0",
  "cache-manager-redis-store": "^2.0.0",
  "redis": "^4.6.0"
}
```

## Configuration

### Environment Variables

```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# Cache Configuration
SSO_CACHE_TTL=900
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
```

### Module Import

The cache module is automatically imported as a global module in `app.module.ts`:

```typescript
import { CacheModule } from './modules/cache/cache.module';

@Module({
  imports: [
    CacheModule.forRoot(), // Global cache module
    // ... other modules
  ],
})
export class AppModule {}
```

## Usage

### Basic Cache Service

```typescript
import { Injectable } from '@nestjs/common';
import { CacheService } from '../cache/services/cache.service';

@Injectable()
export class UserService {
  constructor(private readonly cacheService: CacheService) {}

  async getUserProfile(userId: string) {
    // Try to get from cache first
    const cached = await this.cacheService.get(`user:profile:${userId}`);
    if (cached) {
      return cached;
    }

    // Get from database and cache
    const profile = await this.userRepository.findOne(userId);
    await this.cacheService.set(`user:profile:${userId}`, profile, { ttl: 300 });
    
    return profile;
  }
}
```

### Using Cache Decorators

```typescript
import { Injectable } from '@nestjs/common';
import { Cacheable, CacheEvict } from '../cache/decorators/cache.decorator';

@Injectable()
export class UserService {
  // Cache method result for 5 minutes
  @Cacheable('user-profile-{0}', 300)
  async getUserProfile(userId: string) {
    return this.userRepository.findOne(userId);
  }

  // Evict cache when user is updated
  @CacheEvict('user-profile-{0}')
  async updateUser(userId: string, data: UpdateUserDto) {
    return this.userRepository.update(userId, data);
  }

  // Conditional caching
  @CacheWhen(
    (userId: string) => userId !== 'admin',
    'user-profile-{0}',
    300
  )
  async getUserProfileConditional(userId: string) {
    return this.userRepository.findOne(userId);
  }
}
```

### SSO-Specific Usage

```typescript
import { Injectable } from '@nestjs/common';
import { CacheService, CACHE_KEYS } from '../cache';

@Injectable()
export class SSOService {
  constructor(private readonly cacheService: CacheService) {}

  // Store SSO session
  async storeSession(sessionId: string, sessionData: any) {
    const key = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;
    await this.cacheService.set(key, sessionData, { ttl: 28800 }); // 8 hours
  }

  // Get SSO session
  async getSession(sessionId: string) {
    const key = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;
    return this.cacheService.get(key);
  }

  // Blacklist JWT token
  async blacklistToken(tokenId: string, expiresAt: Date) {
    const key = `${CACHE_KEYS.SSO_JWT_BLACKLIST}${tokenId}`;
    const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000);
    await this.cacheService.set(key, true, { ttl });
  }

  // Check if token is blacklisted
  async isTokenBlacklisted(tokenId: string): Promise<boolean> {
    const key = `${CACHE_KEYS.SSO_JWT_BLACKLIST}${tokenId}`;
    return this.cacheService.exists(key);
  }
}
```

## API Reference

### CacheService Methods

#### `set<T>(key: string, value: T, options?: CacheOptions): Promise<void>`
Store data in cache.

```typescript
await cacheService.set('user:123', userData, { ttl: 300, prefix: 'api:' });
```

#### `get<T>(key: string, prefix?: string): Promise<T | undefined>`
Retrieve data from cache.

```typescript
const userData = await cacheService.get<User>('user:123');
```

#### `del(key: string, prefix?: string): Promise<void>`
Delete data from cache.

```typescript
await cacheService.del('user:123');
```

#### `exists(key: string, prefix?: string): Promise<boolean>`
Check if key exists in cache.

```typescript
const exists = await cacheService.exists('user:123');
```

#### `getOrSet<T>(key: string, factory: () => Promise<T>, options?: CacheOptions): Promise<T>`
Get from cache or execute factory function and cache result.

```typescript
const userData = await cacheService.getOrSet(
  'user:123',
  () => this.userRepository.findOne('123'),
  { ttl: 300 }
);
```

#### `clear(): Promise<void>`
Clear all cache.

```typescript
await cacheService.clear();
```

#### `healthCheck(): Promise<boolean>`
Check cache connection health.

```typescript
const isHealthy = await cacheService.healthCheck();
```

### Cache Decorators

#### `@Cacheable(key: string, ttl?: number)`
Cache method result.

```typescript
@Cacheable('user-{0}', 300)
async getUser(id: string) { /* ... */ }
```

#### `@CacheEvict(key: string | string[])`
Evict cache entries.

```typescript
@CacheEvict(['user-{0}', 'user-profile-{0}'])
async updateUser(id: string, data: any) { /* ... */ }
```

#### `@CacheKey(template: string, ttl?: number)`
Dynamic cache key generation.

```typescript
@CacheKey('user-{0}-org-{1}', 300)
async getUserInOrg(userId: string, orgId: string) { /* ... */ }
```

## Cache Key Patterns

The module provides predefined cache key patterns:

```typescript
export const CACHE_KEYS = {
  // SSO patterns
  SSO_SESSION: 'sso:session:',
  SSO_JWT_BLACKLIST: 'sso:jwt_blacklist:',
  SSO_DEVICE_FINGERPRINT: 'sso:device:',
  SSO_APPLICATION: 'sso:app:',
  
  // RBAC patterns
  USER_PERMISSIONS: 'rbac:user_permissions:',
  USER_ROLES: 'rbac:user_roles:',
  ROLE_PERMISSIONS: 'rbac:role_permissions:',
  
  // General patterns
  USER_PROFILE: 'user:profile:',
  AI_RESPONSE: 'ai:response:',
};
```

## Health Monitoring

### Health Check Endpoint

```http
GET /cache/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "stats": {
    "hits": 150,
    "misses": 25,
    "keys": 0,
    "memory": "N/A"
  }
}
```

### Statistics Endpoint

```http
GET /cache/stats
```

Response:
```json
{
  "hits": 150,
  "misses": 25,
  "keys": 0,
  "memory": "N/A"
}
```

## Testing

### Test Cache Operations

```http
POST /cache/test
Content-Type: application/json

{
  "key": "test-key",
  "value": {"data": "test"},
  "ttl": 60
}
```

### Manual Cache Operations

```http
# Set value
POST /cache/set
{
  "key": "my-key",
  "value": "my-value",
  "ttl": 300
}

# Get value
GET /cache/get/my-key

# Delete value
DELETE /cache/delete/my-key

# Check existence
GET /cache/exists/my-key
```

## Best Practices

### 1. Use Appropriate TTL Values

```typescript
const CACHE_TTL = {
  SHORT: 300,    // 5 minutes - frequently changing data
  MEDIUM: 900,   // 15 minutes - user sessions
  LONG: 3600,    // 1 hour - user profiles
  VERY_LONG: 86400, // 24 hours - static configuration
};
```

### 2. Use Consistent Key Patterns

```typescript
// Good
const userKey = `${CACHE_KEYS.USER_PROFILE}${userId}`;
const sessionKey = `${CACHE_KEYS.SSO_SESSION}${sessionId}`;

// Avoid
const userKey = `user_${userId}_profile`;
```

### 3. Handle Cache Failures Gracefully

```typescript
async getUserProfile(userId: string) {
  try {
    const cached = await this.cacheService.get(`user:${userId}`);
    if (cached) return cached;
  } catch (error) {
    // Log error but continue with database query
    this.logger.warn('Cache error:', error);
  }
  
  // Fallback to database
  return this.userRepository.findOne(userId);
}
```

### 4. Use Cache Eviction Strategically

```typescript
@CacheEvict(['user-profile-{0}', 'user-permissions-{0}'])
async updateUser(userId: string, data: UpdateUserDto) {
  // This will clear both user profile and permissions cache
  return this.userRepository.update(userId, data);
}
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server is running
   - Verify connection parameters in `.env`
   - Check network connectivity

2. **Cache Not Working**
   - Verify cache module is imported
   - Check Redis configuration
   - Review cache key patterns

3. **Performance Issues**
   - Monitor cache hit/miss ratio
   - Adjust TTL values
   - Consider cache warming strategies

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=debug
```

This will show cache operations in the logs:

```
[CacheService] Cache HIT: user:profile:123
[CacheService] Cache MISS: user:profile:456
[CacheService] Cache SET: user:profile:456 (TTL: 300s)
```

## Integration with SSO

The cache module is specifically optimized for SSO operations:

- **Session Storage**: Store user sessions with automatic expiration
- **Token Blacklisting**: Maintain blacklisted JWT tokens
- **Device Fingerprinting**: Cache device information for security
- **Application Configuration**: Cache SSO application settings

This ensures optimal performance for the SSO system while maintaining data consistency and security.

---

For more information about SSO implementation, see [SSO Implementation Guide](../sso/SSO_IMPLEMENTATION_GUIDE.md).
