import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/**
 * Enum định nghĩa loại thiết bị
 * Enum defining device types
 */
export enum DeviceType {
  WEB = 'WEB',
  MOBILE = 'MOBILE',
  TABLET = 'TABLET',
  DESKTOP = 'DESKTOP',
  API = 'API',
  UNKNOWN = 'UNKNOWN',
}

/**
 * UserSession Entity - Quản lý phiên đăng nhập toàn cục cho SSO
 * UserSession Entity - Manages global login sessions for SSO
 */
@Entity('user_sessions')
@Index(['userId'])
@Index(['sessionId'])
@Index(['deviceId'])
@Index(['isActive'])
@Index(['expiresAt'])
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID phiên duy nhất toàn cục - Global unique session ID
   */
  @Column({ type: 'varchar', length: 255, unique: true, name: 'session_id' })
  sessionId: string;

  /**
   * ID người dùng - User ID
   */
  @Column({ type: 'uuid', name: 'user_id' })
  userId: string;

  /**
   * ID thiết bị - Device ID
   */
  @Column({ type: 'varchar', length: 255, nullable: true, name: 'device_id' })
  deviceId?: string;

  /**
   * Dấu vân tay thiết bị - Device fingerprint
   */
  @Column({ type: 'text', nullable: true, name: 'device_fingerprint' })
  deviceFingerprint?: string;

  /**
   * Tên thiết bị - Device name
   */
  @Column({ type: 'varchar', length: 200, nullable: true, name: 'device_name' })
  deviceName?: string;

  /**
   * Loại thiết bị - Device type
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: true, 
    name: 'device_type',
    enum: DeviceType,
    default: DeviceType.UNKNOWN 
  })
  deviceType?: DeviceType;

  /**
   * Địa chỉ IP - IP address
   */
  @Column({ type: 'varchar', length: 45, nullable: true, name: 'ip_address' })
  ipAddress?: string;

  /**
   * User agent string
   */
  @Column({ type: 'text', nullable: true, name: 'user_agent' })
  userAgent?: string;

  /**
   * Vị trí địa lý - Geographic location
   */
  @Column({ type: 'varchar', length: 200, nullable: true })
  location?: string;

  /**
   * Trạng thái hoạt động - Active status
   */
  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  /**
   * Thời gian hoạt động cuối - Last activity time
   */
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', name: 'last_activity_at' })
  lastActivityAt: Date;

  /**
   * Thời gian hết hạn - Expiration time
   */
  @Column({ type: 'timestamp', name: 'expires_at' })
  expiresAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations

  /**
   * Người dùng - User
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Virtual properties

  /**
   * Kiểm tra phiên có hết hạn không - Check if session is expired
   */
  get isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Kiểm tra phiên có hợp lệ không - Check if session is valid
   */
  get isValid(): boolean {
    return this.isActive && !this.isExpired;
  }

  /**
   * Lấy số phút còn lại - Get remaining minutes
   */
  get remainingMinutes(): number {
    if (this.isExpired) return 0;
    const diffMs = this.expiresAt.getTime() - new Date().getTime();
    return Math.floor(diffMs / (1000 * 60));
  }

  /**
   * Lấy thời gian không hoạt động - Get inactive duration in minutes
   */
  get inactiveMinutes(): number {
    const diffMs = new Date().getTime() - this.lastActivityAt.getTime();
    return Math.floor(diffMs / (1000 * 60));
  }

  /**
   * Kiểm tra có phải phiên di động không - Check if mobile session
   */
  get isMobile(): boolean {
    return this.deviceType === DeviceType.MOBILE || this.deviceType === DeviceType.TABLET;
  }

  /**
   * Lấy thông tin tóm tắt thiết bị - Get device summary
   */
  get deviceSummary(): string {
    const parts = [];
    if (this.deviceName) parts.push(this.deviceName);
    if (this.deviceType && this.deviceType !== DeviceType.UNKNOWN) parts.push(this.deviceType);
    if (this.location) parts.push(this.location);
    return parts.join(' • ') || 'Unknown Device';
  }

  /**
   * Cập nhật hoạt động cuối - Update last activity
   */
  updateActivity(): void {
    this.lastActivityAt = new Date();
  }

  /**
   * Gia hạn phiên - Extend session
   */
  extend(minutes: number): void {
    const newExpiry = new Date(this.expiresAt.getTime() + minutes * 60 * 1000);
    this.expiresAt = newExpiry;
    this.updateActivity();
  }

  /**
   * Vô hiệu hóa phiên - Deactivate session
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * Kiểm tra có phải cùng thiết bị không - Check if same device
   */
  isSameDevice(deviceId: string, deviceFingerprint?: string): boolean {
    if (this.deviceId && deviceId) {
      return this.deviceId === deviceId;
    }
    
    if (this.deviceFingerprint && deviceFingerprint) {
      return this.deviceFingerprint === deviceFingerprint;
    }
    
    return false;
  }

  /**
   * Kiểm tra có đáng ngờ không - Check if suspicious
   */
  isSuspicious(currentIp: string, currentUserAgent: string): boolean {
    // Different IP and User Agent might indicate session hijacking
    return this.ipAddress !== currentIp && this.userAgent !== currentUserAgent;
  }

  /**
   * Tạo session ID mới - Generate new session ID
   */
  static generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = Math.random().toString(36).substring(2);
    return `sso_${timestamp}_${randomPart}`;
  }

  /**
   * Tạo device fingerprint - Generate device fingerprint
   */
  static generateDeviceFingerprint(userAgent: string, additionalData?: any): string {
    const crypto = require('crypto');
    const data = {
      userAgent,
      ...additionalData,
    };
    return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex');
  }
}
