import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SocialPost, PostStatus } from '../entities/social-post.entity';
import { PostSchedule, ScheduleStatus } from '../entities/post-schedule.entity';
import { FacebookService } from '../services/facebook.service';
import { TikTokService } from '../services/tiktok.service';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
@Processor('marketing')
export class MarketingProcessor {
  constructor(
    @InjectRepository(SocialPost)
    private socialPostRepository: Repository<SocialPost>,
    @InjectRepository(PostSchedule)
    private postScheduleRepository: Repository<PostSchedule>,
    private facebookService: FacebookService,
    private tiktokService: TikTokService,
    private logger: LoggerService,
  ) {}

  @Process('publish-post')
  async handlePublishPost(job: Job<{ postId: string }>) {
    const { postId } = job.data;

    try {
      const post = await this.socialPostRepository.findOne({
        where: { id: postId },
        relations: ['user'],
      });

      if (!post) {
        throw new Error(`Post not found: ${postId}`);
      }

      this.logger.logWithContext(`Publishing post: ${postId}`, 'MarketingProcessor');

      // Update post status to publishing
      post.status = PostStatus.PUBLISHED;
      await this.socialPostRepository.save(post);

      // Publish to each target account
      const results = [];
      for (const target of post.targetAccounts) {
        try {
          let result;

          if (post.platform === 'facebook') {
            if (target.pageId) {
              // Post to Facebook page
              result = await this.facebookService.postToPage(
                (target as any).accessToken,
                target.pageId,
                {
                  message: post.content,
                  // Add media if available
                }
              );
            } else if (target.groupId) {
              // Post to Facebook group
              result = await this.facebookService.postToGroup(
                (target as any).accessToken,
                target.groupId,
                {
                  message: post.content,
                }
              );
            }
          } else if (post.platform === 'tiktok') {
            // Handle TikTok posting (video upload)
            if (post.media && post.media.length > 0) {
              result = await this.tiktokService.uploadVideo(
                (target as any).accessToken,
                {
                  videoUrl: post.media[0].url,
                  title: post.content.substring(0, 100), // TikTok title limit
                  description: post.content,
                }
              );
            }
          }

          if (result) {
            results.push({
              accountId: target.accountId,
              platformPostId: result.id,
              success: true,
            });
          }
        } catch (error) {
          this.logger.logError(error, `MarketingProcessor - Failed to publish to ${target.accountId}`);
          results.push({
            accountId: target.accountId,
            success: false,
            error: error.message,
          });
        }
      }

      // Update post with results
      const successfulPosts = results.filter(r => r.success);
      if (successfulPosts.length > 0) {
        post.status = PostStatus.PUBLISHED;
        post.publishedAt = new Date();
        post.platformPostId = successfulPosts.reduce((acc, result) => {
          acc[result.accountId] = result.platformPostId;
          return acc;
        }, {});
      } else {
        post.status = PostStatus.FAILED;
        post.errorMessage = 'Failed to publish to any target account';
      }

      await this.socialPostRepository.save(post);

      this.logger.logWithContext(`Post published successfully: ${postId}`, 'MarketingProcessor');
    } catch (error) {
      this.logger.logError(error, 'MarketingProcessor - handlePublishPost');

      // Update post status to failed
      await this.socialPostRepository.update(postId, {
        status: PostStatus.FAILED,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  @Process('process-scheduled-posts')
  async handleScheduledPosts(job: Job) {
    try {
      this.logger.logWithContext('Processing scheduled posts', 'MarketingProcessor');

      // Find posts that are scheduled and due for publishing
      const duePosts = await this.socialPostRepository.find({
        where: {
          status: PostStatus.SCHEDULED,
        },
        relations: ['user'],
      });

      const now = new Date();
      const postsToPublish = duePosts.filter(post =>
        post.scheduledAt && post.scheduledAt <= now
      );

      this.logger.logWithContext(`Found ${postsToPublish.length} posts to publish`, 'MarketingProcessor');

      // Queue each post for publishing
      for (const post of postsToPublish) {
        await job.queue.add('publish-post', { postId: post.id });
      }

      // Process recurring schedules
      await this.processRecurringSchedules();

    } catch (error) {
      this.logger.logError(error, 'MarketingProcessor - handleScheduledPosts');
      throw error;
    }
  }

  @Process('analyze-comments')
  async handleAnalyzeComments(job: Job<{ postId: string; platform: string }>) {
    const { postId, platform } = job.data;

    try {
      this.logger.logWithContext(`Analyzing comments for post: ${postId}`, 'MarketingProcessor');

      const post = await this.socialPostRepository.findOne({
        where: { id: postId },
        relations: ['user'],
      });

      if (!post || !post.platformPostId) {
        return;
      }

      // Get comments from platform
      let comments = [];
      if (platform === 'facebook') {
        // Get Facebook comments
        for (const [accountId, platformPostId] of Object.entries(post.platformPostId)) {
          try {
            const accountComments = await this.facebookService.getPostComments(
              post.user.socialAccounts?.facebook?.accessToken,
              platformPostId as string
            );
            comments.push(...accountComments);
          } catch (error) {
            this.logger.logError(error, `Failed to get comments for ${accountId}`);
          }
        }
      } else if (platform === 'tiktok') {
        // Get TikTok comments
        for (const [accountId, platformPostId] of Object.entries(post.platformPostId)) {
          try {
            const result = await this.tiktokService.getVideoComments(
              post.user.socialAccounts?.tiktok?.accessToken,
              platformPostId as string
            );
            comments.push(...result.comments);
          } catch (error) {
            this.logger.logError(error, `Failed to get comments for ${accountId}`);
          }
        }
      }

      // TODO: Process comments with AI filters
      // TODO: Auto-reply to filtered comments
      // TODO: Create leads from qualified comments

      this.logger.logWithContext(`Analyzed ${comments.length} comments for post: ${postId}`, 'MarketingProcessor');

    } catch (error) {
      this.logger.logError(error, 'MarketingProcessor - handleAnalyzeComments');
      throw error;
    }
  }

  private async processRecurringSchedules() {
    const activeSchedules = await this.postScheduleRepository.find({
      where: { status: ScheduleStatus.ACTIVE },
      relations: ['user'],
    });

    const now = new Date();

    for (const schedule of activeSchedules) {
      if (schedule.shouldRun) {
        try {
          // Create new post from schedule
          const newPost = this.socialPostRepository.create({
            userId: schedule.userId,
            platform: schedule.platform,
            content: schedule.content,
            media: schedule.media,
            targetAccounts: schedule.targetAccounts,
            status: PostStatus.SCHEDULED,
            scheduledAt: now,
            settings: schedule.settings,
          });

          await this.socialPostRepository.save(newPost);

          // Update schedule for next run
          schedule.runCount += 1;
          schedule.lastRunAt = now;

          // Calculate next run time based on schedule type
          schedule.nextRunAt = this.calculateNextRunTime(schedule);

          // Check if schedule should be completed
          if (schedule.hasReachedMaxRuns ||
              (schedule.endDate && now > schedule.endDate)) {
            schedule.status = ScheduleStatus.COMPLETED;
          }

          await this.postScheduleRepository.save(schedule);

          this.logger.logWithContext(`Created post from recurring schedule: ${schedule.id}`, 'MarketingProcessor');
        } catch (error) {
          this.logger.logError(error, `Failed to process recurring schedule: ${schedule.id}`);
        }
      }
    }
  }

  private calculateNextRunTime(schedule: PostSchedule): Date {
    const now = new Date();
    const nextRun = new Date(now);

    switch (schedule.scheduleType) {
      case 'daily':
        nextRun.setDate(nextRun.getDate() + 1);
        break;
      case 'weekly':
        nextRun.setDate(nextRun.getDate() + 7);
        break;
      case 'monthly':
        nextRun.setMonth(nextRun.getMonth() + 1);
        break;
      default:
        // For custom schedules, use the next date in the list
        if (schedule.schedule.customDates) {
          const futureDates = schedule.schedule.customDates
            .map(date => new Date(date))
            .filter(date => date > now)
            .sort((a, b) => a.getTime() - b.getTime());

          if (futureDates.length > 0) {
            return futureDates[0];
          }
        }
        return null;
    }

    // Set the time from schedule
    const [hours, minutes] = schedule.schedule.time.split(':').map(Number);
    nextRun.setHours(hours, minutes, 0, 0);

    return nextRun;
  }
}
