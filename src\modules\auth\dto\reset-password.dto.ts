import { IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordDto {
  @ApiProperty({ 
    example: 'reset-token-here',
    description: 'Password reset token'
  })
  @IsString()
  token: string;

  @ApiProperty({ 
    example: 'NewSecurePassword123!',
    description: 'New password'
  })
  @IsString()
  @MinLength(8)
  newPassword: string;
}
