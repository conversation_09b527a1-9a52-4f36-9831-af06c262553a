import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { OrganizationsService } from './organizations.service';
import { OrganizationMembersService } from './services/organization-members.service';
import { InvitationService } from './services/invitation.service';
import { RolesService } from './services/roles.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { RequirePermissions } from './decorators/require-permissions.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { CurrentOrganization } from './decorators/current-organization.decorator';
import { User } from '../users/entities/user.entity';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { UpdateMemberDto } from './dto/update-member.dto';

@ApiTags('Organizations')
@Controller('organizations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class OrganizationsController {
  constructor(
    private readonly organizationsService: OrganizationsService,
    private readonly membersService: OrganizationMembersService,
    private readonly invitationService: InvitationService,
    private readonly rolesService: RolesService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new organization' })
  @ApiResponse({ status: 201, description: 'Organization created successfully' })
  async createOrganization(@CurrentUser() user: User, @Body() createOrgDto: CreateOrganizationDto) {
    return this.organizationsService.createOrganization(user.id, createOrgDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user organizations' })
  async getUserOrganizations(@CurrentUser() user: User) {
    return this.organizationsService.getUserOrganizations(user.id);
  }

  @Get(':id')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('organization:read')
  @ApiOperation({ summary: 'Get organization details' })
  async getOrganization(@Param('id') id: string) {
    return this.organizationsService.getOrganizationById(id);
  }

  @Patch(':id')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('organization:update')
  @ApiOperation({ summary: 'Update organization' })
  async updateOrganization(@Param('id') id: string, @Body() updateOrgDto: UpdateOrganizationDto) {
    return this.organizationsService.updateOrganization(id, updateOrgDto);
  }

  @Delete(':id')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('organization:delete')
  @ApiOperation({ summary: 'Delete organization' })
  async deleteOrganization(@Param('id') id: string) {
    return this.organizationsService.deleteOrganization(id);
  }

  // Members Management
  @Get(':id/members')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('users:read')
  @ApiOperation({ summary: 'Get organization members' })
  async getMembers(@Param('id') organizationId: string) {
    return this.membersService.getOrganizationMembers(organizationId);
  }

  @Post(':id/members/invite')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('users:create')
  @ApiOperation({ summary: 'Invite member to organization' })
  async inviteMember(
    @Param('id') organizationId: string,
    @CurrentUser() user: User,
    @Body() inviteDto: InviteMemberDto
  ) {
    return this.invitationService.inviteMember(organizationId, user.id, inviteDto);
  }

  @Post('invitations/:token/accept')
  @ApiOperation({ summary: 'Accept organization invitation' })
  async acceptInvitation(@Param('token') token: string, @CurrentUser() user: User) {
    return this.invitationService.acceptInvitation(token, user.id);
  }

  @Post('invitations/:token/decline')
  @ApiOperation({ summary: 'Decline organization invitation' })
  async declineInvitation(@Param('token') token: string) {
    return this.invitationService.declineInvitation(token);
  }

  @Patch(':id/members/:memberId/role')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('users:update')
  @ApiOperation({ summary: 'Update member role' })
  async updateMemberRole(
    @Param('id') organizationId: string,
    @Param('memberId') memberId: string,
    @Body() updateRoleDto: { roleId: string }
  ) {
    return this.membersService.updateMemberRole(organizationId, memberId, updateRoleDto.roleId);
  }

  @Delete(':id/members/:memberId')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('users:delete')
  @ApiOperation({ summary: 'Remove member from organization' })
  async removeMember(
    @Param('id') organizationId: string,
    @Param('memberId') memberId: string
  ) {
    return this.membersService.removeMember(organizationId, memberId);
  }

  // Roles Management
  @Get(':id/roles')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:read')
  @ApiOperation({ summary: 'Get organization roles' })
  async getRoles(@Param('id') organizationId: string) {
    return this.rolesService.getOrganizationRoles(organizationId);
  }

  @Post(':id/roles')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:manage')
  @ApiOperation({ summary: 'Create custom role' })
  async createRole(@Param('id') organizationId: string, @Body() createRoleDto: any) {
    return this.rolesService.createCustomRole(organizationId, createRoleDto);
  }

  @Patch(':id/roles/:roleId')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:manage')
  @ApiOperation({ summary: 'Update role' })
  async updateRole(
    @Param('id') organizationId: string,
    @Param('roleId') roleId: string,
    @Body() updateRoleDto: any
  ) {
    return this.rolesService.updateRole(roleId, updateRoleDto);
  }

  @Delete(':id/roles/:roleId')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:manage')
  @ApiOperation({ summary: 'Delete custom role' })
  async deleteRole(@Param('roleId') roleId: string) {
    return this.rolesService.deleteRole(roleId);
  }

  // Organization Settings
  @Get(':id/settings')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:read')
  @ApiOperation({ summary: 'Get organization settings' })
  async getSettings(@Param('id') organizationId: string) {
    return this.organizationsService.getOrganizationSettings(organizationId);
  }

  @Patch(':id/settings')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('settings:manage')
  @ApiOperation({ summary: 'Update organization settings' })
  async updateSettings(@Param('id') organizationId: string, @Body() settingsDto: any) {
    return this.organizationsService.updateOrganizationSettings(organizationId, settingsDto);
  }

  // Analytics
  @Get(':id/analytics')
  @UseGuards(PermissionsGuard)
  @RequirePermissions('analytics:read')
  @ApiOperation({ summary: 'Get organization analytics' })
  async getAnalytics(@Param('id') organizationId: string, @Query() query: any) {
    return this.organizationsService.getOrganizationAnalytics(organizationId, query);
  }
}
