import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  Request,
  Response,
  UseGuards,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response as ExpressResponse } from 'express';
import { SSOService } from '../services/sso.service';
import { SessionManagementService } from '../services/session-management.service';
import { JWTBlacklistService } from '../services/jwt-blacklist.service';
import { DeviceFingerprintService } from '../services/device-fingerprint.service';
import { SSOConfigService } from '../services/sso-config.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RBACGuard } from '../../rbac/guards/rbac.guard';
import { RequirePermission } from '../../rbac/decorators/rbac.decorators';
import { RevocationReason } from '../entities/jwt-blacklist.entity';

/**
 * SSO Controller - Controller quản lý Single Sign-On
 * SSO Controller - Single Sign-On management controller
 */
@ApiTags('SSO - Single Sign-On')
@Controller('auth/sso')
export class SSOController {
  constructor(
    private readonly ssoService: SSOService,
    private readonly sessionService: SessionManagementService,
    private readonly blacklistService: JWTBlacklistService,
    private readonly deviceFingerprintService: DeviceFingerprintService,
    private readonly ssoConfigService: SSOConfigService,
  ) {}

  /**
   * SSO Login - Đăng nhập SSO
   * SSO Login - SSO login
   */
  @Post('login')
  @ApiOperation({
    summary: 'SSO Login - Đăng nhập SSO',
    description: 'Đăng nhập với hỗ trợ Single Sign-On cross-subdomain. Tạo session toàn cục và JWT tokens.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đăng nhập thành công - Login successful',
    schema: {
      type: 'object',
      properties: {
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' },
        sessionId: { type: 'string' },
        expiresIn: { type: 'number' },
        tokenType: { type: 'string' },
        ssoEnabled: { type: 'boolean' },
        allowedApplications: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'SSO không được bật - SSO not enabled',
  })
  @HttpCode(HttpStatus.OK)
  async ssoLogin(
    @Body() loginRequest: any,
    @Request() req: any,
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const deviceInfo = this.extractDeviceInfo(req);
    
    const ssoLoginRequest = {
      userId: loginRequest.userId,
      deviceInfo,
      application: loginRequest.application,
      deviceName: loginRequest.deviceName,
      location: loginRequest.location,
      rememberDevice: loginRequest.rememberDevice,
    };

    const result = await this.ssoService.ssoLogin(ssoLoginRequest, loginRequest.rbacInfo);

    // Set SSO cookies
    this.ssoService.setSSOCookies(res, result.accessToken, result.refreshToken);

    return result;
  }

  /**
   * SSO Logout - Đăng xuất SSO
   * SSO Logout - SSO logout
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'SSO Logout - Đăng xuất SSO',
    description: 'Đăng xuất khỏi hệ thống SSO. Có thể đăng xuất toàn cục hoặc chỉ session hiện tại.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đăng xuất thành công - Logout successful',
  })
  @HttpCode(HttpStatus.OK)
  async ssoLogout(
    @Body() logoutRequest: { globalLogout?: boolean },
    @Request() req: any,
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const { globalLogout = false } = logoutRequest;
    const deviceInfo = this.extractDeviceInfo(req);
    
    const result = await this.ssoService.ssoLogout(
      req.user.sessionId,
      req.user.sub,
      globalLogout,
      deviceInfo,
    );

    // Clear SSO cookies
    this.ssoService.clearSSOCookies(res);

    return result;
  }

  /**
   * Verify Token - Xác minh token
   * Verify Token - Verify token
   */
  @Post('verify')
  @ApiOperation({
    summary: 'Verify Token - Xác minh token',
    description: 'Xác minh tính hợp lệ của JWT token cho cross-domain authentication.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token hợp lệ - Token valid',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Token không hợp lệ - Token invalid',
  })
  @HttpCode(HttpStatus.OK)
  async verifyToken(
    @Body() verifyRequest: { token: string; application?: string },
    @Request() req: any,
  ) {
    const deviceInfo = this.extractDeviceInfo(req);
    
    return this.ssoService.verifyToken(
      verifyRequest.token,
      verifyRequest.application,
      deviceInfo,
    );
  }

  /**
   * Refresh Token - Làm mới token
   * Refresh Token - Refresh token
   */
  @Post('refresh')
  @ApiOperation({
    summary: 'Refresh Token - Làm mới token',
    description: 'Làm mới access token bằng refresh token.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token được làm mới thành công - Token refreshed successfully',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Refresh token không hợp lệ - Invalid refresh token',
  })
  @HttpCode(HttpStatus.OK)
  async refreshToken(
    @Body() refreshRequest: { refreshToken: string },
    @Request() req: any,
  ) {
    const deviceInfo = this.extractDeviceInfo(req);
    
    return this.ssoService.refreshToken(refreshRequest.refreshToken, deviceInfo);
  }

  /**
   * Get Session Info - Lấy thông tin session
   * Get Session Info - Get session information
   */
  @Get('session')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get Session Info - Lấy thông tin session',
    description: 'Lấy thông tin chi tiết về session hiện tại.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin session - Session information',
  })
  async getSessionInfo(@Request() req: any) {
    if (!req.user.sessionId) {
      throw new BadRequestException('No session ID in token');
    }

    const session = await this.sessionService.getSession(req.user.sessionId);
    if (!session) {
      throw new BadRequestException('Session not found');
    }

    return {
      sessionId: session.sessionId,
      userId: session.userId,
      deviceName: session.deviceName,
      deviceType: session.deviceType,
      ipAddress: session.ipAddress,
      location: session.location,
      isActive: session.isActive,
      lastActivityAt: session.lastActivityAt,
      expiresAt: session.expiresAt,
      remainingMinutes: session.remainingMinutes,
    };
  }

  /**
   * Get User Sessions - Lấy tất cả session của user
   * Get User Sessions - Get all user sessions
   */
  @Get('sessions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get User Sessions - Lấy tất cả session của user',
    description: 'Lấy danh sách tất cả session đang hoạt động của người dùng.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sessions - Sessions list',
  })
  async getUserSessions(@Request() req: any) {
    return this.ssoService.getUserSessions(req.user.sub);
  }

  /**
   * Terminate Session - Kết thúc session
   * Terminate Session - Terminate session
   */
  @Delete('sessions/:sessionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Terminate Session - Kết thúc session',
    description: 'Kết thúc một session cụ thể.',
  })
  @ApiParam({ name: 'sessionId', description: 'ID của session cần kết thúc' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Session được kết thúc thành công - Session terminated successfully',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async terminateSession(
    @Param('sessionId') sessionId: string,
    @Request() req: any,
  ) {
    await this.ssoService.terminateUserSession(sessionId, req.user.sub, 'User terminated session');
  }

  /**
   * Terminate All Sessions - Kết thúc tất cả sessions
   * Terminate All Sessions - Terminate all sessions
   */
  @Delete('sessions/all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Terminate All Sessions - Kết thúc tất cả sessions',
    description: 'Kết thúc tất cả session của người dùng trừ session hiện tại.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tất cả sessions được kết thúc - All sessions terminated',
  })
  @HttpCode(HttpStatus.OK)
  async terminateAllSessions(@Request() req: any) {
    const sessionsTerminated = await this.sessionService.terminateAllUserSessions(
      req.user.sub,
      req.user.sessionId,
    );

    return {
      message: 'All other sessions terminated successfully',
      sessionsTerminated,
    };
  }

  /**
   * Get SSO Configuration - Lấy cấu hình SSO
   * Get SSO Configuration - Get SSO configuration
   */
  @Get('config')
  @ApiOperation({
    summary: 'Get SSO Configuration - Lấy cấu hình SSO',
    description: 'Lấy thông tin cấu hình SSO công khai.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cấu hình SSO - SSO configuration',
  })
  async getSSOConfig() {
    const config = this.ssoConfigService.getConfig();
    
    return {
      enabled: config.enabled,
      baseDomain: config.baseDomain,
      allowedApplications: config.allowedApplications,
      sessionTimeout: config.sessionTimeout,
      maxConcurrentSessions: config.maxConcurrentSessions,
      requireDeviceVerification: config.requireDeviceVerification,
    };
  }

  /**
   * Get Blacklist Statistics - Lấy thống kê blacklist
   * Get Blacklist Statistics - Get blacklist statistics
   */
  @Get('blacklist/stats')
  @UseGuards(JwtAuthGuard, RBACGuard)
  @RequirePermission('SSO_MANAGEMENT_READ')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get Blacklist Statistics - Lấy thống kê blacklist',
    description: 'Lấy thống kê về các token bị blacklist.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thống kê blacklist - Blacklist statistics',
  })
  async getBlacklistStatistics() {
    return this.blacklistService.getStatistics();
  }

  /**
   * Cleanup Expired Sessions - Dọn dẹp sessions hết hạn
   * Cleanup Expired Sessions - Cleanup expired sessions
   */
  @Post('cleanup/sessions')
  @UseGuards(JwtAuthGuard, RBACGuard)
  @RequirePermission('SSO_MANAGEMENT_MANAGE')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Cleanup Expired Sessions - Dọn dẹp sessions hết hạn',
    description: 'Dọn dẹp các session đã hết hạn khỏi hệ thống.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Sessions được dọn dẹp thành công - Sessions cleaned up successfully',
  })
  @HttpCode(HttpStatus.OK)
  async cleanupExpiredSessions() {
    const cleanedSessions = await this.sessionService.cleanupExpiredSessions();
    const cleanedTokens = await this.blacklistService.cleanupExpiredEntries();

    return {
      message: 'Cleanup completed successfully',
      cleanedSessions,
      cleanedTokens,
    };
  }

  /**
   * Extract device information từ request
   * Extract device information from request
   */
  private extractDeviceInfo(req: any) {
    return {
      userAgent: req.headers['user-agent'] || '',
      ipAddress: req.ip || req.connection.remoteAddress || '',
      acceptLanguage: req.headers['accept-language'],
      acceptEncoding: req.headers['accept-encoding'],
      acceptCharset: req.headers['accept-charset'],
      connection: req.headers['connection'],
      dnt: req.headers['dnt'],
      upgradeInsecureRequests: req.headers['upgrade-insecure-requests'],
      secFetchSite: req.headers['sec-fetch-site'],
      secFetchMode: req.headers['sec-fetch-mode'],
      secFetchUser: req.headers['sec-fetch-user'],
      secFetchDest: req.headers['sec-fetch-dest'],
    };
  }
}
