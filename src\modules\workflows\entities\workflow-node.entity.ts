import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Workflow } from './workflow.entity';

export enum NodeType {
  TRIGGER = 'trigger',
  ACTION = 'action',
  CONDITION = 'condition',
  DELAY = 'delay',
  WEBHOOK = 'webhook',
  EMAIL = 'email',
  SMS = 'sms',
  DATABASE = 'database',
  API_CALL = 'api_call',
  FILE_OPERATION = 'file_operation',
  SOCIAL_MEDIA = 'social_media',
  AI_PROCESSING = 'ai_processing',
}

export enum NodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

@Entity('workflow_nodes')
@Index(['workflowId', 'nodeType'])
export class WorkflowNode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  workflowId: string;

  @ManyToOne(() => Workflow, workflow => workflow.nodes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workflowId' })
  workflow: Workflow;

  @Column()
  nodeId: string; // Unique ID within the workflow

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: NodeType,
  })
  nodeType: NodeType;

  @Column({
    type: 'enum',
    enum: NodeStatus,
    default: NodeStatus.ACTIVE,
  })
  status: NodeStatus;

  @Column({ type: 'jsonb' })
  config: {
    // Common properties
    timeout?: number;
    retryCount?: number;
    
    // Email node
    to?: string;
    subject?: string;
    body?: string;
    template?: string;
    
    // Webhook node
    url?: string;
    method?: string;
    headers?: Record<string, string>;
    payload?: any;
    
    // Condition node
    conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    
    // Delay node
    delay?: {
      amount: number;
      unit: 'seconds' | 'minutes' | 'hours' | 'days';
    };
    
    // Social media node
    platform?: string;
    content?: string;
    media?: string[];
    
    // AI processing node
    aiModel?: string;
    prompt?: string;
    parameters?: Record<string, any>;
    
    // Database node
    operation?: 'create' | 'read' | 'update' | 'delete';
    table?: string;
    query?: string;
    data?: any;
  };

  @Column({ type: 'jsonb', nullable: true })
  position: {
    x: number;
    y: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  inputConnections: string[]; // Node IDs that connect to this node

  @Column({ type: 'jsonb', nullable: true })
  outputConnections: string[]; // Node IDs this node connects to

  @Column({ default: 0 })
  executionCount: number;

  @Column({ default: 0 })
  successCount: number;

  @Column({ default: 0 })
  failureCount: number;

  @Column({ nullable: true })
  lastExecutedAt: Date;

  @Column({ type: 'text', nullable: true })
  lastError: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === NodeStatus.ACTIVE;
  }

  get successRate(): number {
    if (this.executionCount === 0) return 0;
    return Math.round((this.successCount / this.executionCount) * 100);
  }

  get isTriggerNode(): boolean {
    return this.nodeType === NodeType.TRIGGER;
  }

  get isActionNode(): boolean {
    return [
      NodeType.ACTION,
      NodeType.EMAIL,
      NodeType.SMS,
      NodeType.WEBHOOK,
      NodeType.DATABASE,
      NodeType.API_CALL,
      NodeType.FILE_OPERATION,
      NodeType.SOCIAL_MEDIA,
      NodeType.AI_PROCESSING,
    ].includes(this.nodeType);
  }

  get isConditionNode(): boolean {
    return this.nodeType === NodeType.CONDITION;
  }
}
