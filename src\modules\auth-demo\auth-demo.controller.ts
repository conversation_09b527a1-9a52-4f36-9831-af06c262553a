import { <PERSON>, Post, Body, Get, UseGuards, Headers } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiProperty, ApiBearerAuth } from '@nestjs/swagger';
import { IsEmail, IsString, Min<PERSON>ength, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON><PERSON>al, IsEnum, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

// Demo DTOs
export enum AccountType {
  PERSONAL = 'personal',
  BUSINESS = 'business'
}

export class LoginDto {
  @ApiProperty({ 
    example: '<EMAIL>',
    description: 'Email or username'
  })
  @IsString()
  emailOrUsername: string;

  @ApiProperty({ 
    example: 'SecurePassword123!',
    description: 'User password'
  })
  @IsString()
  @MinLength(8)
  password: string;
}

export class RegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address (must be unique)'
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @Transform(({ value }) => value.toLowerCase().trim())
  email: string;

  @ApiProperty({
    example: 'username123',
    description: 'Username (3-30 characters, must be unique)',
    minLength: 3,
    maxLength: 30
  })
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(30, { message: 'Username cannot exceed 30 characters' })
  @Transform(({ value }) => value.toLowerCase().trim())
  username: string;

  @ApiProperty({
    example: 'SecurePassword123!',
    description: 'Password (minimum 8 characters, must contain uppercase, lowercase, number, and special character)',
    minLength: 8,
    maxLength: 100
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(100, { message: 'Password cannot exceed 100 characters' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    { message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character' }
  )
  password: string;

  @ApiProperty({
    example: 'John',
    description: 'First name',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1, { message: 'First name is required' })
  @MaxLength(50, { message: 'First name cannot exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1, { message: 'Last name is required' })
  @MaxLength(50, { message: 'Last name cannot exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  lastName: string;

  @ApiProperty({
    example: '+**********',
    description: 'Phone number (optional)',
    required: false
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  phone?: string;

  @ApiProperty({
    example: 'Acme Corp',
    description: 'Company name (optional)',
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Company name cannot exceed 100 characters' })
  @Transform(({ value }) => value?.trim())
  company?: string;

  @ApiProperty({
    enum: AccountType,
    example: AccountType.PERSONAL,
    description: 'Account type (personal or business)',
    enumName: 'AccountType',
    required: false
  })
  @IsOptional()
  @IsEnum(AccountType, { message: 'Account type must be either personal or business' })
  accountType?: AccountType;
}

export class ForgotPasswordDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to send reset link'
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;
}

export class UserResponse {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************' })
  id: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: 'username123' })
  username: string;

  @ApiProperty({ example: 'John' })
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  lastName: string;

  @ApiProperty({ example: '+**********', required: false })
  phone?: string;

  @ApiProperty({ example: 'Acme Corp', required: false })
  company?: string;

  @ApiProperty({ enum: AccountType, example: AccountType.PERSONAL })
  accountType: AccountType;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  createdAt: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  updatedAt: string;
}

export class AuthResponse {
  @ApiProperty({ type: UserResponse })
  user: UserResponse;

  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token'
  })
  accessToken: string;

  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT refresh token'
  })
  refreshToken: string;
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthDemoController {

  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AuthResponse
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponse> {
    return {
      user: {
        id: '123e4567-e89b-12d3-a456-************',
        email: loginDto.emailOrUsername.includes('@') ? loginDto.emailOrUsername : '<EMAIL>',
        username: loginDto.emailOrUsername.includes('@') ? 'username123' : loginDto.emailOrUsername,
        firstName: 'John',
        lastName: 'Doe',
        phone: '+**********',
        company: 'Acme Corp',
        accountType: AccountType.PERSONAL,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: new Date().toISOString()
      },
      accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.demo.access.token',
      refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.demo.refresh.token'
    };
  }

  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
    type: AuthResponse
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponse> {
    return {
      user: {
        id: '123e4567-e89b-12d3-a456-************',
        email: registerDto.email,
        username: registerDto.username,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        phone: registerDto.phone,
        company: registerDto.company,
        accountType: registerDto.accountType || AccountType.PERSONAL,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.demo.access.token',
      refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.demo.refresh.token'
    };
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Password reset email sent successfully' }
      }
    }
  })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    return { message: `Password reset email sent to ${forgotPasswordDto.email}` };
  }

  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user info' })
  @ApiResponse({
    status: 200,
    description: 'User info retrieved successfully',
    type: UserResponse
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMe(@Headers('authorization') auth?: string): Promise<UserResponse> {
    return {
      id: '123e4567-e89b-12d3-a456-************',
      email: '<EMAIL>',
      username: 'username123',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+**********',
      company: 'Acme Corp',
      accountType: AccountType.PERSONAL,
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    };
  }

  @Post('logout')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from current device' })
  @ApiResponse({
    status: 200,
    description: 'Logged out successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Logged out successfully' }
      }
    }
  })
  async logout(): Promise<{ message: string }> {
    return { message: 'Logged out successfully' };
  }
}
