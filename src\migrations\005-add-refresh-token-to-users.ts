import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRefreshTokenToUsers1700000005 implements MigrationInterface {
  name = 'AddRefreshTokenToUsers1700000005';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add refresh token column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'refreshToken',
        type: 'text',
        isNullable: true,
      })
    );

    // Add refresh token expires column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'refreshTokenExpires',
        type: 'timestamp',
        isNullable: true,
      })
    );

    // Create index for refresh token (for faster lookups)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_refresh_token ON users("refreshToken")
    `);

    // Create index for refresh token expiration (for cleanup queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_refresh_token_expires ON users("refreshTokenExpires")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_refresh_token_expires`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_refresh_token`);

    // Drop columns
    await queryRunner.dropColumn('users', 'refreshTokenExpires');
    await queryRunner.dropColumn('users', 'refreshToken');
  }
}
