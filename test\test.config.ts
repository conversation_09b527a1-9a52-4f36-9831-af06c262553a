import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';

export const testConfig = {
  // Database configuration for testing
  database: {
    type: 'sqlite' as const,
    database: ':memory:',
    entities: ['src/**/*.entity.ts'],
    synchronize: true,
    logging: false,
  },

  // JWT configuration for testing
  jwt: {
    secret: 'test-jwt-secret-key',
    signOptions: { expiresIn: '1h' },
  },

  // AI Provider test configurations
  aiProviders: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY || 'test-openai-key',
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    },
    grok: {
      apiKey: process.env.GROK_API_KEY || 'test-grok-key',
      baseURL: process.env.GROK_BASE_URL || 'https://api.x.ai/v1',
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY || 'test-gemini-key',
      baseURL: process.env.GEMINI_BASE_URL || 'https://generativelanguage.googleapis.com/v1beta',
    },
    ollama: {
      baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    },
  },
};

export const getTestModules = () => [
  ConfigModule.forRoot({
    isGlobal: true,
    envFilePath: '.env.test',
  }),
  TypeOrmModule.forRoot(testConfig.database),
  JwtModule.register(testConfig.jwt),
];

// Mock data for testing
export const mockAIResponse = {
  content: 'This is a test AI response',
  usage: {
    promptTokens: 10,
    completionTokens: 20,
    totalTokens: 30,
  },
  model: 'test-model',
  finishReason: 'stop',
  responseTime: 1000,
};

export const mockOllamaModels = [
  {
    name: 'llama2:7b',
    modified_at: '2024-01-01T00:00:00Z',
    size: 3800000000,
    digest: 'sha256:test-digest',
    details: {
      format: 'gguf',
      family: 'llama',
      families: ['llama'],
      parameter_size: '7B',
      quantization_level: 'Q4_0',
    },
  },
  {
    name: 'codellama:7b',
    modified_at: '2024-01-01T00:00:00Z',
    size: 3800000000,
    digest: 'sha256:test-digest-2',
    details: {
      format: 'gguf',
      family: 'llama',
      families: ['llama'],
      parameter_size: '7B',
      quantization_level: 'Q4_0',
    },
  },
];

export const mockMessages = [
  {
    role: 'system' as const,
    content: 'You are a helpful AI assistant.',
  },
  {
    role: 'user' as const,
    content: 'Hello, how are you?',
  },
];

export const mockGenerationOptions = {
  temperature: 0.7,
  maxTokens: 1000,
  topP: 1,
};

// Test utilities
export class TestUtils {
  static createMockUser() {
    return {
      id: 'test-user-id',
      email: '<EMAIL>',
      accountType: 'business',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  static createMockJwtToken() {
    return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
  }

  static async delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static generateRandomString(length: number = 10) {
    return Math.random().toString(36).substring(2, length + 2);
  }
}
