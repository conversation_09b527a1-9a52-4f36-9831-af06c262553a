import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SocialPlatform } from './social-post.entity';

export enum ScheduleStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum ScheduleType {
  ONCE = 'once',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  CUSTOM = 'custom',
}

@Entity('post_schedules')
@Index(['userId', 'status'])
@Index(['nextRunAt', 'status'])
export class PostSchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SocialPlatform,
  })
  platform: SocialPlatform;

  @Column({
    type: 'enum',
    enum: ScheduleType,
  })
  scheduleType: ScheduleType;

  @Column({
    type: 'enum',
    enum: ScheduleStatus,
    default: ScheduleStatus.ACTIVE,
  })
  status: ScheduleStatus;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'jsonb', nullable: true })
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }[];

  @Column({ type: 'jsonb' })
  targetAccounts: {
    accountId: string;
    accountName: string;
    pageId?: string;
    groupId?: string;
  }[];

  @Column({ type: 'jsonb' })
  schedule: {
    time: string; // HH:mm format
    timezone: string;
    daysOfWeek?: number[]; // 0-6 (Sunday-Saturday) for weekly
    dayOfMonth?: number; // 1-31 for monthly
    customDates?: string[]; // ISO dates for custom
  };

  @Column({ nullable: true })
  startDate: Date;

  @Column({ nullable: true })
  endDate: Date;

  @Column({ nullable: true })
  nextRunAt: Date;

  @Column({ nullable: true })
  lastRunAt: Date;

  @Column({ default: 0 })
  runCount: number;

  @Column({ nullable: true })
  maxRuns: number;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    autoComment?: boolean;
    commentDelay?: number;
    commentText?: string;
    enableAnalytics?: boolean;
    skipWeekends?: boolean;
    skipHolidays?: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === ScheduleStatus.ACTIVE;
  }

  get isCompleted(): boolean {
    return this.status === ScheduleStatus.COMPLETED;
  }

  get shouldRun(): boolean {
    return this.isActive && this.nextRunAt && new Date() >= this.nextRunAt;
  }

  get hasReachedMaxRuns(): boolean {
    return this.maxRuns && this.runCount >= this.maxRuns;
  }
}
