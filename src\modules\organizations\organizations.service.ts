import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization, OrganizationStatus, OrganizationPlan } from './entities/organization.entity';
import { OrganizationMember, MemberStatus } from './entities/organization-member.entity';
import { RolesService } from './services/roles.service';
import { LoggerService } from '../../common/services/logger.service';
import { UtilsService } from '../../common/services/utils.service';

@Injectable()
export class OrganizationsService {
  constructor(
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    @InjectRepository(OrganizationMember)
    private memberRepository: Repository<OrganizationMember>,
    private rolesService: RolesService,
    private logger: LoggerService,
    private utilsService: UtilsService,
  ) {}

  async createOrganization(userId: string, createOrgDto: {
    name: string;
    description?: string;
    website?: string;
    industry?: string;
    size?: string;
  }): Promise<Organization> {
    // Generate unique slug
    const slug = await this.generateUniqueSlug(createOrgDto.name);

    const organization = this.organizationRepository.create({
      ...createOrgDto,
      slug,
      ownerId: userId,
      status: OrganizationStatus.ACTIVE,
      plan: OrganizationPlan.FREE,
      memberCount: 1,
      settings: {
        allowMemberInvites: true,
        requireApprovalForJoin: false,
        defaultMemberRole: 'member',
        enableSSOLogin: false,
        enableTwoFactor: false,
        sessionTimeout: 480, // 8 hours
        features: {
          marketing: true,
          ai: true,
          documents: false,
          workflows: false,
          invoices: false,
          scheduling: false,
        },
      },
      limits: {
        maxMembers: 5,
        maxProjects: 3,
        maxStorage: 1000, // 1GB
        maxApiCalls: 1000,
        maxEmailsPerMonth: 500,
        maxWorkflows: 2,
      },
      usage: {
        currentMembers: 1,
        currentProjects: 0,
        currentStorage: 0,
        currentApiCalls: 0,
        currentEmailsSent: 0,
        currentWorkflows: 0,
      },
    });

    const savedOrganization = await this.organizationRepository.save(organization);

    // Add owner as first member with owner role
    const ownerRole = await this.rolesService.getOwnerRole();
    await this.addMemberToOrganization(savedOrganization.id, userId, ownerRole.id, MemberStatus.ACTIVE);

    this.logger.logWithContext(`Organization created: ${savedOrganization.id}`, 'OrganizationsService');

    return savedOrganization;
  }

  async getUserOrganizations(userId: string): Promise<Organization[]> {
    const members = await this.memberRepository.find({
      where: { userId, status: MemberStatus.ACTIVE },
      relations: ['organization', 'role'],
    });

    return members.map(member => member.organization);
  }

  async getOrganizationById(id: string): Promise<Organization> {
    const organization = await this.organizationRepository.findOne({
      where: { id },
      relations: ['owner', 'members', 'members.user', 'members.role'],
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async updateOrganization(id: string, updateOrgDto: Partial<Organization>): Promise<Organization> {
    const organization = await this.getOrganizationById(id);

    // Update slug if name changed
    if (updateOrgDto.name && updateOrgDto.name !== organization.name) {
      updateOrgDto.slug = await this.generateUniqueSlug(updateOrgDto.name);
    }

    await this.organizationRepository.update(id, updateOrgDto);

    this.logger.logWithContext(`Organization updated: ${id}`, 'OrganizationsService');

    return this.getOrganizationById(id);
  }

  async deleteOrganization(id: string): Promise<void> {
    const organization = await this.getOrganizationById(id);

    // Soft delete by changing status
    await this.organizationRepository.update(id, {
      status: OrganizationStatus.INACTIVE,
    });

    this.logger.logWithContext(`Organization deleted: ${id}`, 'OrganizationsService');
  }

  async getOrganizationSettings(organizationId: string): Promise<any> {
    const organization = await this.getOrganizationById(organizationId);
    return {
      settings: organization.settings,
      limits: organization.limits,
      usage: organization.usage,
      billing: organization.billing,
    };
  }

  async updateOrganizationSettings(organizationId: string, settingsDto: any): Promise<Organization> {
    const organization = await this.getOrganizationById(organizationId);

    const updatedSettings = {
      ...organization.settings,
      ...settingsDto.settings,
    };

    await this.organizationRepository.update(organizationId, {
      settings: updatedSettings,
    });

    this.logger.logWithContext(`Organization settings updated: ${organizationId}`, 'OrganizationsService');

    return this.getOrganizationById(organizationId);
  }

  async getOrganizationAnalytics(organizationId: string, query: any): Promise<any> {
    const organization = await this.getOrganizationById(organizationId);

    // Basic analytics - in real implementation, this would query various tables
    const analytics = {
      overview: {
        totalMembers: organization.memberCount,
        activeMembers: organization.usage?.currentMembers || 0,
        totalProjects: organization.usage?.currentProjects || 0,
        storageUsed: organization.usage?.currentStorage || 0,
        storageLimit: organization.limits?.maxStorage || 0,
      },
      activity: {
        lastWeek: {
          newMembers: 0,
          projectsCreated: 0,
          emailsSent: 0,
          workflowsExecuted: 0,
        },
        thisMonth: {
          apiCalls: organization.usage?.currentApiCalls || 0,
          emailsSent: organization.usage?.currentEmailsSent || 0,
          storageGrowth: 0,
        },
      },
      usage: {
        features: Object.entries(organization.settings?.features || {})
          .map(([feature, enabled]) => ({ feature, enabled, usage: 0 })),
        limits: {
          members: {
            current: organization.usage?.currentMembers || 0,
            limit: organization.limits?.maxMembers || 0,
            percentage: Math.round(((organization.usage?.currentMembers || 0) / (organization.limits?.maxMembers || 1)) * 100),
          },
          storage: {
            current: organization.usage?.currentStorage || 0,
            limit: organization.limits?.maxStorage || 0,
            percentage: organization.storageUsagePercentage,
          },
          apiCalls: {
            current: organization.usage?.currentApiCalls || 0,
            limit: organization.limits?.maxApiCalls || 0,
            percentage: Math.round(((organization.usage?.currentApiCalls || 0) / (organization.limits?.maxApiCalls || 1)) * 100),
          },
        },
      },
    };

    return analytics;
  }

  async addMemberToOrganization(
    organizationId: string,
    userId: string,
    roleId: string,
    status: MemberStatus = MemberStatus.ACTIVE
  ): Promise<OrganizationMember> {
    const member = this.memberRepository.create({
      organizationId,
      userId,
      roleId,
      status,
      acceptedAt: status === MemberStatus.ACTIVE ? new Date() : null,
    });

    const savedMember = await this.memberRepository.save(member);

    // Update organization member count
    if (status === MemberStatus.ACTIVE) {
      await this.organizationRepository.increment(
        { id: organizationId },
        'memberCount',
        1
      );
    }

    return savedMember;
  }

  async updateOrganizationUsage(organizationId: string, usage: Partial<any>): Promise<void> {
    const organization = await this.getOrganizationById(organizationId);

    const updatedUsage = {
      ...organization.usage,
      ...usage,
    };

    await this.organizationRepository.update(organizationId, {
      usage: updatedUsage,
      lastActivityAt: new Date(),
    });
  }

  async checkOrganizationLimits(organizationId: string, resource: string, amount: number = 1): Promise<boolean> {
    const organization = await this.getOrganizationById(organizationId);

    const limits = organization.limits || {};
    const usage = organization.usage || {};

    switch (resource) {
      case 'members':
        return (usage.currentMembers || 0) + amount <= (limits.maxMembers || 5);
      case 'storage':
        return (usage.currentStorage || 0) + amount <= (limits.maxStorage || 1000);
      case 'apiCalls':
        return (usage.currentApiCalls || 0) + amount <= (limits.maxApiCalls || 1000);
      case 'emails':
        return (usage.currentEmailsSent || 0) + amount <= (limits.maxEmailsPerMonth || 500);
      case 'workflows':
        return (usage.currentWorkflows || 0) + amount <= (limits.maxWorkflows || 2);
      default:
        return true;
    }
  }

  private async generateUniqueSlug(name: string): Promise<string> {
    let baseSlug = this.utilsService.slugify(name);
    let slug = baseSlug;
    let counter = 1;

    while (await this.isSlugTaken(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  private async isSlugTaken(slug: string): Promise<boolean> {
    const existing = await this.organizationRepository.findOne({
      where: { slug },
    });
    return !!existing;
  }
}
