import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { OrganizationMember } from './organization-member.entity';

export enum OrganizationStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  INACTIVE = 'inactive',
}

export enum OrganizationPlan {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
}

@Entity('organizations')
@Index(['ownerId'])
@Index(['slug'], { unique: true })
@Index(['status'])
export class Organization {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ unique: true })
  slug: string; // URL-friendly identifier

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  industry: string;

  @Column({ nullable: true })
  size: string; // e.g., "1-10", "11-50", "51-200", etc.

  @Column({ nullable: true })
  logo: string; // URL to logo image

  @Column()
  ownerId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'ownerId' })
  owner: User;

  @OneToMany(() => OrganizationMember, member => member.organization, { cascade: true })
  members: OrganizationMember[];

  @Column({
    type: 'enum',
    enum: OrganizationStatus,
    default: OrganizationStatus.ACTIVE,
  })
  status: OrganizationStatus;

  @Column({
    type: 'enum',
    enum: OrganizationPlan,
    default: OrganizationPlan.FREE,
  })
  plan: OrganizationPlan;

  @Column({ type: 'jsonb', nullable: true })
  address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  contact: {
    email?: string;
    phone?: string;
    supportEmail?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    allowMemberInvites?: boolean;
    requireApprovalForJoin?: boolean;
    defaultMemberRole?: string;
    enableSSOLogin?: boolean;
    enableTwoFactor?: boolean;
    sessionTimeout?: number; // minutes
    allowedDomains?: string[]; // Email domains allowed to join
    features?: {
      marketing?: boolean;
      ai?: boolean;
      documents?: boolean;
      workflows?: boolean;
      invoices?: boolean;
      scheduling?: boolean;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  billing: {
    customerId?: string; // Stripe customer ID
    subscriptionId?: string;
    planStartDate?: string;
    planEndDate?: string;
    trialEndDate?: string;
    paymentMethod?: string;
    billingEmail?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  limits: {
    maxMembers?: number;
    maxProjects?: number;
    maxStorage?: number; // in MB
    maxApiCalls?: number; // per month
    maxEmailsPerMonth?: number;
    maxWorkflows?: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  usage: {
    currentMembers?: number;
    currentProjects?: number;
    currentStorage?: number; // in MB
    currentApiCalls?: number; // this month
    currentEmailsSent?: number; // this month
    currentWorkflows?: number;
  };

  @Column({ default: 0 })
  memberCount: number;

  @Column({ nullable: true })
  lastActivityAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === OrganizationStatus.ACTIVE;
  }

  get isSuspended(): boolean {
    return this.status === OrganizationStatus.SUSPENDED;
  }

  get isOnTrial(): boolean {
    const trialEnd = this.billing?.trialEndDate;
    return trialEnd && new Date(trialEnd) > new Date();
  }

  get trialDaysLeft(): number {
    const trialEnd = this.billing?.trialEndDate;
    if (!trialEnd) return 0;
    const diff = new Date(trialEnd).getTime() - new Date().getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  }

  get isPaidPlan(): boolean {
    return this.plan !== OrganizationPlan.FREE;
  }

  get canInviteMembers(): boolean {
    if (!this.isActive) return false;
    const maxMembers = this.limits?.maxMembers || 5; // Default limit for free plan
    return this.memberCount < maxMembers;
  }

  get storageUsagePercentage(): number {
    const maxStorage = this.limits?.maxStorage || 1000; // 1GB default
    const currentStorage = this.usage?.currentStorage || 0;
    return Math.round((currentStorage / maxStorage) * 100);
  }

  get isStorageNearLimit(): boolean {
    return this.storageUsagePercentage > 80;
  }

  get displayName(): string {
    return this.name || `Organization ${this.slug}`;
  }

  hasFeature(feature: string): boolean {
    return this.settings?.features?.[feature] || false;
  }

  isOwner(userId: string): boolean {
    return this.ownerId === userId;
  }

  canAccessFeature(feature: string): boolean {
    if (!this.isActive) return false;
    
    // Free plan restrictions
    if (this.plan === OrganizationPlan.FREE) {
      const freeFeatures = ['marketing', 'ai'];
      return freeFeatures.includes(feature);
    }
    
    return this.hasFeature(feature);
  }
}
