import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, MoreThan } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { JWTBlacklist, TokenType, RevocationReason } from '../entities/jwt-blacklist.entity';
import { SSOAuditLog, SSOAction } from '../entities/sso-audit-log.entity';
import { SSOConfigService } from './sso-config.service';
import { LoggerService } from '../../../common/services/logger.service';

/**
 * Interface cho thông tin revoke token
 * Interface for token revocation information
 */
export interface RevokeTokenInfo {
  jti: string;
  userId: string;
  tokenType: TokenType;
  expiresAt: Date;
  reason?: RevocationReason;
  revokedBy?: string;
  sessionId?: string;
}

/**
 * Interface cho thống kê blacklist
 * Interface for blacklist statistics
 */
export interface BlacklistStatistics {
  totalBlacklisted: number;
  byTokenType: Record<TokenType, number>;
  byReason: Record<RevocationReason, number>;
  recentRevocations: number; // Last 24 hours
  expiredTokens: number;
}

/**
 * JWTBlacklistService - Quản lý danh sách đen JWT tokens
 * JWTBlacklistService - Manages JWT token blacklist
 */
@Injectable()
export class JWTBlacklistService {
  private readonly CACHE_PREFIX = 'jwt_blacklist:';
  private readonly CACHE_TTL = 3600; // 1 hour

  constructor(
    @InjectRepository(JWTBlacklist)
    private blacklistRepository: Repository<JWTBlacklist>,
    @InjectRepository(SSOAuditLog)
    private auditRepository: Repository<SSOAuditLog>,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private ssoConfigService: SSOConfigService,
    private logger: LoggerService,
  ) {}

  /**
   * Kiểm tra token có bị blacklist không
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(jti: string): Promise<boolean> {
    if (!this.ssoConfigService.isTokenRevocationEnabled()) {
      return false;
    }

    // Check cache first
    const cacheKey = `${this.CACHE_PREFIX}${jti}`;
    const cached = await this.cacheManager.get<boolean>(cacheKey);
    
    if (cached !== undefined) {
      return cached;
    }

    // Check database
    const blacklistedToken = await this.blacklistRepository.findOne({
      where: { jti },
    });

    const isBlacklisted = !!blacklistedToken;

    // Cache result
    await this.cacheManager.set(cacheKey, isBlacklisted, this.CACHE_TTL);

    return isBlacklisted;
  }

  /**
   * Revoke token
   * Revoke token
   */
  async revokeToken(revokeInfo: RevokeTokenInfo): Promise<void> {
    const {
      jti,
      userId,
      tokenType,
      expiresAt,
      reason = RevocationReason.USER_LOGOUT,
      revokedBy,
      sessionId,
    } = revokeInfo;

    // Check if already blacklisted
    const existing = await this.blacklistRepository.findOne({ where: { jti } });
    if (existing) {
      this.logger.logWithContext(
        `Token already blacklisted: ${jti}`,
        'JWTBlacklistService'
      );
      return;
    }

    // Create blacklist entry
    const blacklistEntry = JWTBlacklist.create(
      jti,
      userId,
      tokenType,
      expiresAt,
      reason,
      revokedBy,
      sessionId,
    );

    await this.blacklistRepository.save(blacklistEntry);

    // Update cache
    const cacheKey = `${this.CACHE_PREFIX}${jti}`;
    await this.cacheManager.set(cacheKey, true, this.CACHE_TTL);

    // Log audit
    await this.logAudit(SSOAction.TOKEN_VERIFY, true, {
      userId,
      sessionId,
      metadata: {
        action: 'TOKEN_REVOKED',
        jti,
        tokenType,
        reason,
        revokedBy,
      },
    });

    this.logger.logWithContext(
      `Token revoked: ${jti} (${reason})`,
      'JWTBlacklistService'
    );
  }

  /**
   * Revoke tất cả tokens của user
   * Revoke all user tokens
   */
  async revokeAllUserTokens(
    userId: string,
    reason: RevocationReason,
    revokedBy: string,
    excludeJti?: string,
  ): Promise<number> {
    // This is a simplified implementation
    // In a real scenario, you'd need to track all active tokens for a user
    
    const globalRevocationJti = `global_revoke_${userId}_${Date.now()}`;
    
    await this.revokeToken({
      jti: globalRevocationJti,
      userId,
      tokenType: TokenType.ACCESS,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      reason,
      revokedBy,
    });

    // Log audit
    await this.logAudit(SSOAction.GLOBAL_LOGOUT, true, {
      userId,
      metadata: {
        action: 'ALL_TOKENS_REVOKED',
        reason,
        revokedBy,
        excludeJti,
      },
    });

    this.logger.logWithContext(
      `All tokens revoked for user ${userId} (${reason})`,
      'JWTBlacklistService'
    );

    return 1; // Return number of revocation entries created
  }

  /**
   * Revoke tokens theo session
   * Revoke tokens by session
   */
  async revokeSessionTokens(
    sessionId: string,
    userId: string,
    reason: RevocationReason,
    revokedBy?: string,
  ): Promise<number> {
    const sessionRevocationJti = `session_revoke_${sessionId}_${Date.now()}`;
    
    await this.revokeToken({
      jti: sessionRevocationJti,
      userId,
      tokenType: TokenType.ACCESS,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      reason,
      revokedBy,
      sessionId,
    });

    // Log audit
    await this.logAudit(SSOAction.SESSION_TERMINATE, true, {
      userId,
      sessionId,
      metadata: {
        action: 'SESSION_TOKENS_REVOKED',
        reason,
        revokedBy,
      },
    });

    this.logger.logWithContext(
      `Session tokens revoked: ${sessionId} (${reason})`,
      'JWTBlacklistService'
    );

    return 1;
  }

  /**
   * Cleanup expired blacklist entries
   * Cleanup expired blacklist entries
   */
  async cleanupExpiredEntries(): Promise<number> {
    const result = await this.blacklistRepository
      .createQueryBuilder()
      .delete()
      .from(JWTBlacklist)
      .where('expires_at < :now', { now: new Date() })
      .execute();

    const deletedCount = result.affected || 0;

    if (deletedCount > 0) {
      this.logger.logWithContext(
        `Cleaned up ${deletedCount} expired blacklist entries`,
        'JWTBlacklistService'
      );

      // Log audit
      await this.logAudit(SSOAction.SESSION_CLEANUP, true, {
        metadata: {
          action: 'BLACKLIST_CLEANUP',
          deletedEntries: deletedCount,
        },
      });
    }

    return deletedCount;
  }

  /**
   * Lấy thống kê blacklist
   * Get blacklist statistics
   */
  async getStatistics(): Promise<BlacklistStatistics> {
    const [
      totalBlacklisted,
      tokenTypeStats,
      reasonStats,
      recentRevocations,
      expiredTokens,
    ] = await Promise.all([
      this.blacklistRepository.count(),
      this.getTokenTypeStatistics(),
      this.getReasonStatistics(),
      this.getRecentRevocations(),
      this.getExpiredTokensCount(),
    ]);

    return {
      totalBlacklisted,
      byTokenType: tokenTypeStats,
      byReason: reasonStats,
      recentRevocations,
      expiredTokens,
    };
  }

  /**
   * Lấy blacklist entries của user
   * Get user blacklist entries
   */
  async getUserBlacklistEntries(
    userId: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<JWTBlacklist[]> {
    return this.blacklistRepository.find({
      where: { userId },
      order: { revokedAt: 'DESC' },
      take: limit,
      skip: offset,
      relations: ['revoker'],
    });
  }

  /**
   * Kiểm tra user có tokens bị revoke không
   * Check if user has revoked tokens
   */
  async hasRevokedTokens(userId: string): Promise<boolean> {
    const count = await this.blacklistRepository.count({
      where: { userId },
    });

    return count > 0;
  }

  /**
   * Invalidate cache cho token
   * Invalidate cache for token
   */
  async invalidateTokenCache(jti: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${jti}`;
    await this.cacheManager.del(cacheKey);
  }

  /**
   * Invalidate tất cả cache
   * Invalidate all cache
   */
  async invalidateAllCache(): Promise<void> {
    // This would require a more sophisticated cache implementation
    // For now, we'll just log the action
    this.logger.logWithContext(
      'All blacklist cache invalidated',
      'JWTBlacklistService'
    );
  }

  /**
   * Lấy thống kê theo token type
   * Get statistics by token type
   */
  private async getTokenTypeStatistics(): Promise<Record<TokenType, number>> {
    const stats = await this.blacklistRepository
      .createQueryBuilder('blacklist')
      .select('blacklist.tokenType', 'tokenType')
      .addSelect('COUNT(*)', 'count')
      .groupBy('blacklist.tokenType')
      .getRawMany();

    const result: Record<TokenType, number> = {
      [TokenType.ACCESS]: 0,
      [TokenType.REFRESH]: 0,
      [TokenType.SSO]: 0,
    };

    stats.forEach(stat => {
      result[stat.tokenType as TokenType] = parseInt(stat.count);
    });

    return result;
  }

  /**
   * Lấy thống kê theo reason
   * Get statistics by reason
   */
  private async getReasonStatistics(): Promise<Record<RevocationReason, number>> {
    const stats = await this.blacklistRepository
      .createQueryBuilder('blacklist')
      .select('blacklist.reason', 'reason')
      .addSelect('COUNT(*)', 'count')
      .groupBy('blacklist.reason')
      .getRawMany();

    const result: Record<RevocationReason, number> = {} as Record<RevocationReason, number>;

    // Initialize all reasons with 0
    Object.values(RevocationReason).forEach(reason => {
      result[reason] = 0;
    });

    stats.forEach(stat => {
      if (stat.reason) {
        result[stat.reason as RevocationReason] = parseInt(stat.count);
      }
    });

    return result;
  }

  /**
   * Lấy số revocations gần đây (24h)
   * Get recent revocations (24h)
   */
  private async getRecentRevocations(): Promise<number> {
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    return this.blacklistRepository.count({
      where: {
        revokedAt: MoreThan(yesterday),
      },
    });
  }

  /**
   * Lấy số tokens đã hết hạn
   * Get expired tokens count
   */
  private async getExpiredTokensCount(): Promise<number> {
    return this.blacklistRepository.count({
      where: {
        expiresAt: LessThan(new Date()),
      },
    });
  }

  /**
   * Log audit event
   * Log audit event
   */
  private async logAudit(
    action: SSOAction,
    success: boolean,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<void> {
    if (!this.ssoConfigService.isAuditLoggingEnabled()) {
      return;
    }

    const auditLog = SSOAuditLog.create(action, success, options);
    await this.auditRepository.save(auditLog);
  }
}
