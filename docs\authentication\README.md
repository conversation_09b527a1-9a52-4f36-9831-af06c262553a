# Authentication System Documentation

## 📚 Documentation Index

### 🚀 [Quick Start Guide](./QUICK_START_GUIDE.md)
**Quick start for developers**
- System overview
- Core API endpoints
- Basic code examples
- Error handling
- Best practices

### 📖 [Authentication Flow Guide](./AUTHENTICATION_FLOW_GUIDE.md)
**Detailed authentication flow guide**
- Step-by-step processes for each security method
- API endpoint sequences
- Token management lifecycle
- Detailed error handling
- Database state changes
- SQL queries and best practices

### 📊 [Authentication Diagrams](./AUTHENTICATION_DIAGRAMS.md)
**Authentication flow diagrams**
- Sequence diagrams for all flows
- State transition diagrams
- Error handling flowcharts
- Database relationship diagrams

### 🔧 [Enhanced Authentication Features](../features/ENHANCED_AUTHENTICATION.md)
**Detailed technical documentation**
- Implementation details
- Database schema
- Security considerations
- Troubleshooting guide
- Dependencies and setup

## 🔐 System Overview

### Security Methods

| Method | Description | Use Case |
|--------|-------------|----------|
| **DISABLED** | Email/password only | Development, internal apps |
| **EMAIL_VERIFICATION** | 6-digit code via email | Standard security |
| **TWO_FACTOR_AUTH** | TOTP with authenticator app | High security |
| **FIXED_CODE** | User-defined fixed code | User convenience |

### Token System

| Token Type | Lifespan | Purpose |
|------------|----------|---------|
| **Access Token** | 15 minutes | API authentication |
| **Refresh Token** | 7 days | Token renewal |

## 🛠️ Quick Setup

### 1. Database Migration
```bash
npm run migration:run
```

### 2. Environment Variables
```env
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
EMAIL_SERVICE_API_KEY=your-email-api-key
```

### 3. Test Implementation
```bash
npm test -- auth.service.spec.ts
```

## 📋 API Endpoints Summary

### Core Authentication
```
POST /auth/register          # Register new user
POST /auth/login             # Login (may be partial)
POST /auth/verify-login      # Verify login
POST /auth/refresh           # Refresh access token
POST /auth/revoke            # Logout (revoke refresh token)
POST /auth/revoke-all        # Logout from all devices
```

### Security Management
```
GET  /auth/security-method   # View current method
POST /auth/security-method   # Update security method
POST /auth/change-fixed-code # Change fixed code
```

### Password Management
```
POST /auth/forgot-password   # Forgot password
POST /auth/reset-password    # Reset password
POST /auth/change-password   # Change password (with verification)
```

## 🔄 Basic Authentication Flow

### Simple Login (DISABLED method)
```javascript
const response = await fetch('/auth/login', {
  method: 'POST',
  body: JSON.stringify({ emailOrUsername, password })
});
const { user, accessToken, refreshToken } = await response.json();
```

### Verified Login (EMAIL/2FA/FIXED_CODE methods)
```javascript
// Step 1: Initial login
const loginResponse = await fetch('/auth/login', { ... });
const { requiresVerification, sessionId } = await loginResponse.json();

// Step 2: Verification
const verifyResponse = await fetch('/auth/verify-login', {
  method: 'POST',
  body: JSON.stringify({ sessionId, verificationCode })
});
const { user, accessToken, refreshToken } = await verifyResponse.json();
```

## 🔧 Integration Examples

### React Hook
```javascript
function useAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const login = async (credentials) => {
    const response = await authAPI.login(credentials);
    if (response.requiresVerification) {
      return { requiresVerification: true, sessionId: response.sessionId };
    }
    setUser(response.user);
    localStorage.setItem('accessToken', response.accessToken);
    localStorage.setItem('refreshToken', response.refreshToken);
    return { success: true };
  };

  const logout = async () => {
    const refreshToken = localStorage.getItem('refreshToken');
    await authAPI.revoke({ refreshToken });
    setUser(null);
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  };

  return { user, login, logout, loading };
}
```

### Vue Composable
```javascript
export function useAuth() {
  const user = ref(null);
  const isAuthenticated = computed(() => !!user.value);

  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      if (response.requiresVerification) {
        return { requiresVerification: true, sessionId: response.sessionId };
      }
      user.value = response.user;
      return { success: true };
    } catch (error) {
      throw new Error('Login failed');
    }
  };

  return { user, isAuthenticated, login };
}
```

## 🚨 Error Codes Reference

| Code | Message | Action |
|------|---------|--------|
| 401 | Invalid credentials | Show login error |
| 401 | Invalid verification code | Ask for code again |
| 401 | Token expired | Refresh token |
| 403 | Account inactive | Contact support |
| 429 | Rate limited | Wait and retry |
| 400 | Weak fixed code | Choose stronger code |

## 🔒 Security Features

### ✅ Implemented
- JWT access tokens (15 min expiry)
- Refresh token rotation
- Multiple security methods
- Rate limiting protection
- Device session management
- Email notifications
- Input validation
- SQL injection protection
- XSS protection

### 🔄 Recommended Additions
- Device fingerprinting
- Geolocation tracking
- Suspicious activity detection
- Account lockout policies
- Password strength requirements
- Session timeout warnings

## 📊 Monitoring & Analytics

### Key Metrics to Track
- Login success/failure rates
- Token refresh frequency
- Security method adoption
- Failed verification attempts
- Device session patterns
- Geographic login distribution

### Logging Events
```javascript
// Authentication events to log
- User registration
- Login attempts (success/failure)
- Token refresh
- Security method changes
- Password changes
- Suspicious activities
```

## 🧪 Testing Strategy

### Unit Tests
```bash
npm test -- auth.service.spec.ts
npm test -- verification.service.spec.ts
npm test -- users.service.spec.ts
```

### Integration Tests
```bash
npm test -- auth.controller.spec.ts
npm test -- auth.e2e-spec.ts
```

### Manual Testing Checklist
- [ ] Registration flow
- [ ] All 4 security methods
- [ ] Token refresh mechanism
- [ ] Error handling
- [ ] Rate limiting
- [ ] Email notifications
- [ ] Device management

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] Run all tests
- [ ] Update environment variables
- [ ] Backup database
- [ ] Review security settings

### Post-deployment
- [ ] Run database migrations
- [ ] Verify API endpoints
- [ ] Test authentication flows
- [ ] Monitor error logs
- [ ] Check email delivery

## 📞 Support & Troubleshooting

### Common Issues
1. **Tokens expiring too quickly** → Check system time sync
2. **Email codes not received** → Verify email service config
3. **2FA codes rejected** → Ensure device time sync
4. **Database connection errors** → Check migration status

### Getting Help
- Check [Troubleshooting Guide](../features/ENHANCED_AUTHENTICATION.md#troubleshooting)
- Review [Error Handling](./AUTHENTICATION_FLOW_GUIDE.md#error-handling)
- Examine server logs for detailed error messages

---

## 📝 Changelog

### v2.0.0 - Refresh Token Implementation
- ✅ Added refresh token system
- ✅ Token rotation on refresh
- ✅ Enhanced security with short-lived access tokens
- ✅ Multiple device support

### v1.0.0 - Enhanced Authentication
- ✅ 4 security methods (DISABLED, EMAIL, 2FA, FIXED_CODE)
- ✅ Email verification system
- ✅ Two-factor authentication with TOTP
- ✅ Fixed code verification
- ✅ Device session management

---

**🎯 Goal: Provide a secure, flexible, and user-friendly authentication system for developers and end users.**
