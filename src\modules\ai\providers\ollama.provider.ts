import axios, { AxiosInstance } from 'axios';
import { BaseAIProvider, AIMessage, AIResponse, AIProviderConfig } from './base-ai.provider';

export interface OllamaModel {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  system?: string;
  template?: string;
  context?: number[];
  stream?: boolean;
  raw?: boolean;
  format?: string;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    seed?: number;
    num_predict?: number;
    num_ctx?: number;
  };
}

export interface OllamaChatRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  stream?: boolean;
  format?: string;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    seed?: number;
    num_predict?: number;
    num_ctx?: number;
  };
}

export class OllamaProvider extends BaseAIProvider {
  private client: AxiosInstance;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = axios.create({
      baseURL: config.baseURL || 'http://localhost:11434',
      timeout: config.timeout || 120000, // 2 minutes for local models
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      // Convert messages to OLLAMA chat format
      const ollamaMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      const request: OllamaChatRequest = {
        model: config.model,
        messages: ollamaMessages,
        stream: false,
        options: {
          temperature: config.temperature || 0.7,
          num_predict: config.maxTokens || 1000,
          top_p: config.topP || 1,
          num_ctx: 4096, // Context window
        },
      };

      const response = await this.client.post('/api/chat', request);
      const responseTime = Date.now() - startTime;
      const data = response.data;

      // Calculate token usage (estimation for local models)
      const promptTokens = this.calculateTokens(messages.map(m => m.content).join(' '));
      const completionTokens = this.calculateTokens(data.message?.content || '');

      return {
        content: data.message?.content || '',
        usage: {
          promptTokens,
          completionTokens,
          totalTokens: promptTokens + completionTokens,
        },
        model: config.model,
        finishReason: data.done ? 'stop' : 'length',
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'OLLAMA');
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    const config = this.mergeConfig(options);

    try {
      const ollamaMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      const request: OllamaChatRequest = {
        model: config.model,
        messages: ollamaMessages,
        stream: true,
        options: {
          temperature: config.temperature || 0.7,
          num_predict: config.maxTokens || 1000,
          top_p: config.topP || 1,
        },
      };

      const response = await this.client.post('/api/chat', request, {
        responseType: 'stream',
      });

      for await (const chunk of response.data) {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              if (parsed.message?.content) {
                yield parsed.message.content;
              }
              if (parsed.done) {
                return;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'OLLAMA');
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    // OLLAMA supports vision models like llava
    const config = this.mergeConfig(options);

    try {
      // Download image and convert to base64
      const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const base64Image = Buffer.from(imageResponse.data).toString('base64');

      const request = {
        model: config.model || 'llava:latest',
        prompt: prompt,
        images: [base64Image],
        stream: false,
        options: {
          temperature: config.temperature || 0.7,
          num_predict: config.maxTokens || 1000,
        },
      };

      const startTime = Date.now();
      const response = await this.client.post('/api/generate', request);
      const responseTime = Date.now() - startTime;
      const data = response.data;

      return {
        content: data.response || '',
        usage: {
          promptTokens: this.calculateTokens(prompt),
          completionTokens: this.calculateTokens(data.response || ''),
          totalTokens: 0,
        },
        model: config.model || 'llava:latest',
        finishReason: data.done ? 'stop' : 'length',
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'OLLAMA');
    }
  }

  async embedText(text: string): Promise<number[]> {
    try {
      const response = await this.client.post('/api/embeddings', {
        model: this.config.model || 'nomic-embed-text:latest',
        prompt: text,
      });

      return response.data.embedding || [];
    } catch (error) {
      this.handleError(error, 'OLLAMA');
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.get('/api/tags');
      return true;
    } catch (error) {
      return false;
    }
  }

  // OLLAMA specific methods
  async listModels(): Promise<OllamaModel[]> {
    try {
      const response = await this.client.get('/api/tags');
      return response.data.models || [];
    } catch (error) {
      this.handleError(error, 'OLLAMA');
    }
  }

  async pullModel(modelName: string): Promise<{
    success: boolean;
    status: string;
    progress?: number;
  }> {
    try {
      const response = await this.client.post('/api/pull', {
        name: modelName,
        stream: false,
      });

      return {
        success: true,
        status: response.data.status || 'completed',
      };
    } catch (error) {
      return {
        success: false,
        status: error.message || 'Failed to pull model',
      };
    }
  }

  async deleteModel(modelName: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      await this.client.delete('/api/delete', {
        data: { name: modelName },
      });

      return {
        success: true,
        message: `Model ${modelName} deleted successfully`,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to delete model',
      };
    }
  }

  async getModelInfo(modelName: string): Promise<{
    name: string;
    size: number;
    modified_at: string;
    details: any;
  } | null> {
    try {
      const response = await this.client.post('/api/show', {
        name: modelName,
      });

      return response.data;
    } catch (error) {
      return null;
    }
  }

  async checkServerHealth(): Promise<{
    isHealthy: boolean;
    version?: string;
    models?: number;
    error?: string;
  }> {
    try {
      const [versionResponse, modelsResponse] = await Promise.all([
        this.client.get('/api/version').catch(() => null),
        this.client.get('/api/tags').catch(() => null),
      ]);

      return {
        isHealthy: true,
        version: versionResponse?.data?.version || 'unknown',
        models: modelsResponse?.data?.models?.length || 0,
      };
    } catch (error) {
      return {
        isHealthy: false,
        error: error.message || 'OLLAMA server not accessible',
      };
    }
  }

  async generateCode(
    prompt: string,
    language: string = 'python',
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const codePrompt = `You are an expert ${language} programmer. Generate clean, efficient, and well-documented code for the following request:\n\n${prompt}\n\nProvide only the code with appropriate comments.`;

    return this.generateText([
      {
        role: 'system',
        content: `You are a professional ${language} developer. Write clean, efficient code with proper documentation.`,
      },
      {
        role: 'user',
        content: codePrompt,
      },
    ], options);
  }

  async explainCode(
    code: string,
    language: string = 'auto-detect',
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const explainPrompt = `Please explain the following ${language} code in detail:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nExplain what it does, how it works, and any important concepts.`;

    return this.generateText([
      {
        role: 'system',
        content: 'You are a code explanation expert. Provide clear, detailed explanations of code functionality.',
      },
      {
        role: 'user',
        content: explainPrompt,
      },
    ], options);
  }

  protected handleError(error: any, provider: string): never {
    console.error(`${provider} API Error:`, error);
    
    if (error.code === 'ECONNREFUSED') {
      throw new Error(`${provider}: Server not running. Please start OLLAMA server first.`);
    } else if (error.response?.status === 404) {
      throw new Error(`${provider}: Model not found. Please pull the model first.`);
    } else if (error.response?.status === 400) {
      throw new Error(`${provider}: Bad request - ${error.response?.data?.error || 'Invalid parameters'}`);
    } else if (error.code === 'ETIMEDOUT') {
      throw new Error(`${provider}: Request timeout - model may be loading`);
    }
    
    throw new Error(`${provider}: ${error.message || 'Unknown error'}`);
  }
}
