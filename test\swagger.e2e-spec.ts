import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Swagger Documentation (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('should serve Swagger documentation', () => {
    return request(app.getHttpServer())
      .get('/api/v1/docs')
      .expect(200)
      .expect((res) => {
        expect(res.text).toContain('Swagger UI');
        expect(res.text).toContain('Delify Platform API');
      });
  });

  it('should serve Swagger JSON schema', () => {
    return request(app.getHttpServer())
      .get('/api/v1/docs-json')
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('openapi');
        expect(res.body).toHaveProperty('info');
        expect(res.body.info.title).toBe('Delify Platform API');
        expect(res.body).toHaveProperty('paths');
        
        // Check that auth endpoints are documented
        expect(res.body.paths).toHaveProperty('/auth/login');
        expect(res.body.paths).toHaveProperty('/auth/register');
        
        // Check that register endpoint has proper request body schema
        const registerEndpoint = res.body.paths['/auth/register'];
        expect(registerEndpoint).toHaveProperty('post');
        expect(registerEndpoint.post).toHaveProperty('requestBody');
        expect(registerEndpoint.post.requestBody).toHaveProperty('content');
        expect(registerEndpoint.post.requestBody.content).toHaveProperty('application/json');
        
        const registerSchema = registerEndpoint.post.requestBody.content['application/json'];
        expect(registerSchema).toHaveProperty('schema');
        expect(registerSchema.schema).toHaveProperty('$ref');
        expect(registerSchema.schema.$ref).toContain('RegisterDto');
      });
  });

  it('should have RegisterDto schema with all required fields', () => {
    return request(app.getHttpServer())
      .get('/api/v1/docs-json')
      .expect(200)
      .expect((res) => {
        const schemas = res.body.components?.schemas;
        expect(schemas).toHaveProperty('RegisterDto');
        
        const registerDto = schemas.RegisterDto;
        expect(registerDto).toHaveProperty('type', 'object');
        expect(registerDto).toHaveProperty('properties');
        
        const properties = registerDto.properties;
        
        // Check required fields
        expect(properties).toHaveProperty('email');
        expect(properties).toHaveProperty('username');
        expect(properties).toHaveProperty('password');
        expect(properties).toHaveProperty('firstName');
        expect(properties).toHaveProperty('lastName');
        
        // Check optional fields
        expect(properties).toHaveProperty('phone');
        expect(properties).toHaveProperty('company');
        expect(properties).toHaveProperty('website');
        expect(properties).toHaveProperty('accountType');
        
        // Check field types and examples
        expect(properties.email).toHaveProperty('type', 'string');
        expect(properties.email).toHaveProperty('example');
        expect(properties.username).toHaveProperty('type', 'string');
        expect(properties.password).toHaveProperty('type', 'string');
        expect(properties.firstName).toHaveProperty('type', 'string');
        expect(properties.lastName).toHaveProperty('type', 'string');
        
        // Check required array
        expect(registerDto).toHaveProperty('required');
        expect(registerDto.required).toContain('email');
        expect(registerDto.required).toContain('username');
        expect(registerDto.required).toContain('password');
        expect(registerDto.required).toContain('firstName');
        expect(registerDto.required).toContain('lastName');
      });
  });

  it('should have LoginDto schema with proper fields', () => {
    return request(app.getHttpServer())
      .get('/api/v1/docs-json')
      .expect(200)
      .expect((res) => {
        const schemas = res.body.components?.schemas;
        expect(schemas).toHaveProperty('LoginDto');
        
        const loginDto = schemas.LoginDto;
        expect(loginDto).toHaveProperty('type', 'object');
        expect(loginDto).toHaveProperty('properties');
        
        const properties = loginDto.properties;
        expect(properties).toHaveProperty('emailOrUsername');
        expect(properties).toHaveProperty('password');
        
        expect(properties.emailOrUsername).toHaveProperty('type', 'string');
        expect(properties.password).toHaveProperty('type', 'string');
        
        // Check required array
        expect(loginDto).toHaveProperty('required');
        expect(loginDto.required).toContain('emailOrUsername');
        expect(loginDto.required).toContain('password');
      });
  });

  it('should have proper API tags', () => {
    return request(app.getHttpServer())
      .get('/api/v1/docs-json')
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('tags');
        const tags = res.body.tags.map((tag: any) => tag.name);
        
        expect(tags).toContain('Authentication');
        expect(tags).toContain('Test API');
        expect(tags).toContain('Users');
      });
  });
});
