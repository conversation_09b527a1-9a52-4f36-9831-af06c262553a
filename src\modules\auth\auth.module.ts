import { Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { VerificationService } from './services/verification.service';
import { EmailVerificationService } from './services/email-verification.service';
import { UsersModule } from '../users/users.module';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '7d'),
        },
      }),
      inject: [ConfigService],
    }),
    // Forward reference to avoid circular dependency with RBAC module
    forwardRef(() => import('../rbac/rbac.module').then(m => m.RBACModule)),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    VerificationService,
    EmailVerificationService,
    LocalStrategy,
    JwtStrategy,
    // Provide RBACService token for injection
    {
      provide: 'RBACService',
      useFactory: async (rbacModule: any) => {
        try {
          const { RBACService } = await import('../rbac/services/rbac.service');
          return rbacModule?.get?.(RBACService);
        } catch {
          return null; // Return null if RBAC module is not available
        }
      },
      inject: [],
    },
  ],
  exports: [AuthService],
})
export class AuthModule {}
