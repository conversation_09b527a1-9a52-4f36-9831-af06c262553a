import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DocumentWorkflow, WorkflowStatus, WorkflowStepStatus } from '../entities/document-workflow.entity';
import { LoggerService } from '../../../common/services/logger.service';
import { UtilsService } from '../../../common/services/utils.service';

@Injectable()
export class DocumentWorkflowService {
  constructor(
    @InjectRepository(DocumentWorkflow)
    private documentWorkflowRepository: Repository<DocumentWorkflow>,
    private logger: LoggerService,
    private utilsService: UtilsService,
  ) {}

  async createWorkflow(userId: string, data: {
    documentId?: string;
    name: string;
    description?: string;
    steps: Array<{
      name: string;
      type: string;
      assignee: {
        email: string;
        name: string;
        role?: string;
      };
      order: number;
      dueDate?: string;
      settings?: any;
    }>;
    dueDate?: Date;
    settings?: any;
  }): Promise<DocumentWorkflow> {
    const workflow = this.documentWorkflowRepository.create({
      userId,
      name: data.name,
      description: data.description,
      documentId: data.documentId,
      steps: data.steps.map(step => ({
        ...step,
        id: this.utilsService.generateUuid(),
        status: WorkflowStepStatus.PENDING,
      })) as any,
      currentStepIndex: 0,
      startedAt: new Date(),
      dueDate: data.dueDate,
      settings: data.settings,
    });

    const savedWorkflow = await this.documentWorkflowRepository.save(workflow);

    this.logger.logWithContext(`Document workflow created: ${savedWorkflow.id}`, 'DocumentWorkflowService');

    // Start the first step
    await this.startNextStep(savedWorkflow.id);

    return savedWorkflow;
  }

  async getWorkflows(userId: string, documentId?: string): Promise<DocumentWorkflow[]> {
    const where: any = { userId };
    if (documentId) {
      where.documentId = documentId;
    }

    return this.documentWorkflowRepository.find({
      where,
      order: { createdAt: 'DESC' },
    });
  }

  async getWorkflow(userId: string, workflowId: string): Promise<DocumentWorkflow> {
    return this.documentWorkflowRepository.findOne({
      where: { id: workflowId, userId },
    });
  }

  async updateWorkflow(userId: string, workflowId: string, data: Partial<DocumentWorkflow>): Promise<DocumentWorkflow> {
    await this.documentWorkflowRepository.update(
      { id: workflowId, userId },
      data
    );

    const updatedWorkflow = await this.getWorkflow(userId, workflowId);

    this.logger.logWithContext(`Document workflow updated: ${workflowId}`, 'DocumentWorkflowService');

    return updatedWorkflow;
  }

  async completeStep(userId: string, workflowId: string, stepId: string, data: {
    comments?: string;
    completedBy: string;
  }): Promise<DocumentWorkflow> {
    const workflow = await this.getWorkflow(userId, workflowId);

    if (!workflow) {
      throw new Error('Workflow not found');
    }

    const stepIndex = workflow.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      throw new Error('Step not found');
    }

    const step = workflow.steps[stepIndex];
    if (step.status !== WorkflowStepStatus.IN_PROGRESS) {
      throw new Error('Step is not in progress');
    }

    // Update step status
    step.status = WorkflowStepStatus.COMPLETED;
    step.completedAt = new Date().toISOString();
    step.completedBy = data.completedBy;
    step.comments = data.comments;

    // Add to history
    if (!workflow.history) {
      workflow.history = [];
    }
    workflow.history.push({
      stepId,
      action: 'completed',
      performedBy: data.completedBy,
      timestamp: new Date().toISOString(),
      comments: data.comments,
    });

    // Check if workflow is complete
    const allStepsCompleted = workflow.steps.every(s =>
      s.status === WorkflowStepStatus.COMPLETED || s.status === WorkflowStepStatus.SKIPPED
    );

    if (allStepsCompleted) {
      workflow.status = WorkflowStatus.COMPLETED;
      workflow.completedAt = new Date();
    } else {
      // Start next step
      workflow.currentStepIndex = stepIndex + 1;
    }

    const updatedWorkflow = await this.documentWorkflowRepository.save(workflow);

    this.logger.logWithContext(`Workflow step completed: ${stepId}`, 'DocumentWorkflowService');

    if (!allStepsCompleted) {
      await this.startNextStep(workflowId);
    }

    return updatedWorkflow;
  }

  async skipStep(userId: string, workflowId: string, stepId: string, data: {
    reason: string;
    performedBy: string;
  }): Promise<DocumentWorkflow> {
    const workflow = await this.getWorkflow(userId, workflowId);

    if (!workflow) {
      throw new Error('Workflow not found');
    }

    const stepIndex = workflow.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      throw new Error('Step not found');
    }

    const step = workflow.steps[stepIndex];
    step.status = WorkflowStepStatus.SKIPPED;
    step.comments = data.reason;

    // Add to history
    if (!workflow.history) {
      workflow.history = [];
    }
    workflow.history.push({
      stepId,
      action: 'skipped',
      performedBy: data.performedBy,
      timestamp: new Date().toISOString(),
      comments: data.reason,
    });

    // Move to next step
    workflow.currentStepIndex = stepIndex + 1;

    const updatedWorkflow = await this.documentWorkflowRepository.save(workflow);

    this.logger.logWithContext(`Workflow step skipped: ${stepId}`, 'DocumentWorkflowService');

    await this.startNextStep(workflowId);

    return updatedWorkflow;
  }

  async rejectStep(userId: string, workflowId: string, stepId: string, data: {
    reason: string;
    performedBy: string;
    backToStep?: number;
  }): Promise<DocumentWorkflow> {
    const workflow = await this.getWorkflow(userId, workflowId);

    if (!workflow) {
      throw new Error('Workflow not found');
    }

    const stepIndex = workflow.steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) {
      throw new Error('Step not found');
    }

    const step = workflow.steps[stepIndex];
    step.status = WorkflowStepStatus.FAILED;
    step.comments = data.reason;

    // Add to history
    if (!workflow.history) {
      workflow.history = [];
    }
    workflow.history.push({
      stepId,
      action: 'rejected',
      performedBy: data.performedBy,
      timestamp: new Date().toISOString(),
      comments: data.reason,
    });

    // Reset workflow to specified step or beginning
    const backToIndex = data.backToStep ?? 0;
    workflow.currentStepIndex = backToIndex;

    // Reset all steps after the back-to step
    for (let i = backToIndex; i < workflow.steps.length; i++) {
      if (workflow.steps[i].status !== WorkflowStepStatus.COMPLETED) {
        workflow.steps[i].status = WorkflowStepStatus.PENDING;
        workflow.steps[i].completedAt = undefined;
        workflow.steps[i].completedBy = undefined;
        workflow.steps[i].comments = undefined;
      }
    }

    const updatedWorkflow = await this.documentWorkflowRepository.save(workflow);

    this.logger.logWithContext(`Workflow step rejected: ${stepId}`, 'DocumentWorkflowService');

    await this.startNextStep(workflowId);

    return updatedWorkflow;
  }

  async pauseWorkflow(userId: string, workflowId: string): Promise<DocumentWorkflow> {
    const workflow = await this.updateWorkflow(userId, workflowId, {
      status: WorkflowStatus.PAUSED,
    });

    this.logger.logWithContext(`Workflow paused: ${workflowId}`, 'DocumentWorkflowService');

    return workflow;
  }

  async resumeWorkflow(userId: string, workflowId: string): Promise<DocumentWorkflow> {
    const workflow = await this.updateWorkflow(userId, workflowId, {
      status: WorkflowStatus.ACTIVE,
    });

    this.logger.logWithContext(`Workflow resumed: ${workflowId}`, 'DocumentWorkflowService');

    await this.startNextStep(workflowId);

    return workflow;
  }

  async cancelWorkflow(userId: string, workflowId: string, reason: string): Promise<DocumentWorkflow> {
    const workflow = await this.getWorkflow(userId, workflowId);

    if (!workflow) {
      throw new Error('Workflow not found');
    }

    workflow.status = WorkflowStatus.CANCELLED;

    // Add to history
    if (!workflow.history) {
      workflow.history = [];
    }
    workflow.history.push({
      stepId: 'workflow',
      action: 'cancelled',
      performedBy: userId,
      timestamp: new Date().toISOString(),
      comments: reason,
    });

    const updatedWorkflow = await this.documentWorkflowRepository.save(workflow);

    this.logger.logWithContext(`Workflow cancelled: ${workflowId}`, 'DocumentWorkflowService');

    return updatedWorkflow;
  }

  private async startNextStep(workflowId: string): Promise<void> {
    const workflow = await this.documentWorkflowRepository.findOne({
      where: { id: workflowId },
    });

    if (!workflow || workflow.status !== WorkflowStatus.ACTIVE) {
      return;
    }

    const currentStep = workflow.currentStep;
    if (!currentStep) {
      // No more steps, complete workflow
      workflow.status = WorkflowStatus.COMPLETED;
      workflow.completedAt = new Date();
      await this.documentWorkflowRepository.save(workflow);
      return;
    }

    // Update current step status
    currentStep.status = WorkflowStepStatus.IN_PROGRESS;

    // Add to history
    if (!workflow.history) {
      workflow.history = [];
    }
    workflow.history.push({
      stepId: currentStep.id,
      action: 'started',
      performedBy: 'system',
      timestamp: new Date().toISOString(),
    });

    await this.documentWorkflowRepository.save(workflow);

    // TODO: Send notification to assignee
    this.logger.logWithContext(
      `Workflow step started: ${currentStep.id} assigned to ${currentStep.assignee.email}`,
      'DocumentWorkflowService'
    );
  }

  async getWorkflowStats(userId: string): Promise<{
    total: number;
    active: number;
    completed: number;
    paused: number;
    cancelled: number;
    averageCompletionTime: number; // in hours
  }> {
    const workflows = await this.getWorkflows(userId);

    const stats = {
      total: workflows.length,
      active: workflows.filter(w => w.status === WorkflowStatus.ACTIVE).length,
      completed: workflows.filter(w => w.status === WorkflowStatus.COMPLETED).length,
      paused: workflows.filter(w => w.status === WorkflowStatus.PAUSED).length,
      cancelled: workflows.filter(w => w.status === WorkflowStatus.CANCELLED).length,
      averageCompletionTime: 0,
    };

    // Calculate average completion time
    const completedWorkflows = workflows.filter(w => w.isCompleted && w.startedAt && w.completedAt);
    if (completedWorkflows.length > 0) {
      const totalTime = completedWorkflows.reduce((sum, w) => {
        const duration = w.completedAt.getTime() - w.startedAt.getTime();
        return sum + duration;
      }, 0);

      stats.averageCompletionTime = Math.round(totalTime / completedWorkflows.length / (1000 * 60 * 60)); // Convert to hours
    }

    return stats;
  }
}
