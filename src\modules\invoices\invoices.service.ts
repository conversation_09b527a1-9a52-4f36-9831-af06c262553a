import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Invoice } from './entities/invoice.entity';
import { InvoiceItem } from './entities/invoice-item.entity';
import { Customer } from './entities/customer.entity';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class InvoicesService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepository: Repository<Invoice>,
    @InjectRepository(InvoiceItem)
    private invoiceItemRepository: Repository<InvoiceItem>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    private logger: LoggerService,
  ) {}

  async getInvoices(userId: string) {
    return this.invoiceRepository.find({
      where: { userId },
      relations: ['customer', 'items'],
      order: { createdAt: 'DESC' },
    });
  }

  async createInvoice(userId: string, data: any) {
    this.logger.logWithContext(`Invoice creation requested for user: ${userId}`, 'InvoicesService');
    return { message: 'Invoice created successfully' };
  }

  async getInvoice(userId: string, invoiceId: string) {
    return this.invoiceRepository.findOne({
      where: { id: invoiceId, userId },
      relations: ['customer', 'items'],
    });
  }

  async updateInvoice(userId: string, invoiceId: string, data: any) {
    this.logger.logWithContext(`Invoice update requested for user: ${userId}, invoice: ${invoiceId}`, 'InvoicesService');
    return { message: 'Invoice updated successfully' };
  }

  async sendInvoice(userId: string, invoiceId: string) {
    this.logger.logWithContext(`Invoice send requested for user: ${userId}, invoice: ${invoiceId}`, 'InvoicesService');
    return { message: 'Invoice sent successfully' };
  }

  async getCustomers(userId: string) {
    return this.customerRepository.find({
      where: { userId },
      order: { name: 'ASC' },
    });
  }

  async createCustomer(userId: string, data: any) {
    this.logger.logWithContext(`Customer creation requested for user: ${userId}`, 'InvoicesService');
    return { message: 'Customer created successfully' };
  }
}
