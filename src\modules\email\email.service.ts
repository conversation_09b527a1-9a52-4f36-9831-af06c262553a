import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailTemplate } from './entities/email-template.entity';
import { EmailCampaign } from './entities/email-campaign.entity';
import { EmailLog } from './entities/email-log.entity';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class EmailService {
  constructor(
    @InjectRepository(EmailTemplate)
    private emailTemplateRepository: Repository<EmailTemplate>,
    @InjectRepository(EmailCampaign)
    private emailCampaignRepository: Repository<EmailCampaign>,
    @InjectRepository(EmailLog)
    private emailLogRepository: Repository<EmailLog>,
    private logger: LoggerService,
  ) {}

  async getTemplates(userId: string) {
    return this.emailTemplateRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async createTemplate(userId: string, data: any) {
    this.logger.logWithContext(`Email template creation requested for user: ${userId}`, 'EmailService');
    return { message: 'Email template created successfully' };
  }

  async getCampaigns(userId: string) {
    return this.emailCampaignRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async createCampaign(userId: string, data: any) {
    this.logger.logWithContext(`Email campaign creation requested for user: ${userId}`, 'EmailService');
    return { message: 'Email campaign created successfully' };
  }

  async sendCampaign(userId: string, campaignId: string) {
    this.logger.logWithContext(`Email campaign send requested for user: ${userId}, campaign: ${campaignId}`, 'EmailService');
    return { message: 'Email campaign sent successfully' };
  }

  async getAnalytics(userId: string) {
    this.logger.logWithContext(`Email analytics requested for user: ${userId}`, 'EmailService');
    return { message: 'Email analytics retrieved successfully' };
  }

  async sendSingleEmail(userId: string, data: any) {
    this.logger.logWithContext(`Single email send requested for user: ${userId}`, 'EmailService');
    return { message: 'Email sent successfully' };
  }
}
