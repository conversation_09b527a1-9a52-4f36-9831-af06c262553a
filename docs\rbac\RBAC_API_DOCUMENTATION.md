# RBAC API Documentation

## Overview

This document provides comprehensive API documentation for the RBAC (Role-Based Access Control) system endpoints.

## Base URL

```
https://api.yourapp.com/api
```

## Authentication

All RBAC endpoints require JWT authentication. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Role Management Endpoints

### Create Role

Creates a new role in the system.

**Endpoint:** `POST /roles`

**Required Permission:** `ROLE_MANAGEMENT_CREATE`

**Request Body:**
```json
{
  "name": "CUSTOM_ROLE",
  "displayName": "Custom Role",
  "description": "Custom role description",
  "level": 4,
  "parentRoleId": "uuid-parent-role",
  "isSystemRole": false,
  "isActive": true
}
```

**Response:**
```json
{
  "id": "uuid-role-id",
  "name": "CUSTOM_ROLE",
  "displayName": "Custom Role",
  "description": "Custom role description",
  "level": 4,
  "parentRoleId": "uuid-parent-role",
  "isSystemRole": false,
  "isActive": true,
  "createdBy": "uuid-creator-id",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `400 Bad Request` - Invalid input data
- `403 Forbidden` - Insufficient permissions
- `409 Conflict` - Role name already exists

### Get Roles List

Retrieves a list of roles with optional filtering and pagination.

**Endpoint:** `GET /roles`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Query Parameters:**
- `search` (optional) - Search by role name
- `level` (optional) - Filter by role level
- `isActive` (optional) - Filter by active status
- `isSystemRole` (optional) - Filter by system role status
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 10)

**Example Request:**
```
GET /roles?search=marketing&level=3&page=1&limit=10
```

**Response:**
```json
{
  "roles": [
    {
      "id": "uuid-role-id",
      "name": "MARKETING_LEAD",
      "displayName": "Marketing Lead",
      "description": "Marketing team leader",
      "level": 3,
      "parentRoleId": "uuid-manager-id",
      "isSystemRole": false,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "permissionCount": 15,
      "userCount": 5
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Get Role by ID

Retrieves detailed information about a specific role.

**Endpoint:** `GET /roles/:id`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Path Parameters:**
- `id` - Role UUID

**Response:**
```json
{
  "id": "uuid-role-id",
  "name": "MARKETING_LEAD",
  "displayName": "Marketing Lead",
  "description": "Marketing team leader",
  "level": 3,
  "parentRoleId": "uuid-manager-id",
  "isSystemRole": false,
  "isActive": true,
  "createdBy": "uuid-creator-id",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "parentRole": {
    "id": "uuid-manager-id",
    "name": "MANAGER",
    "displayName": "Manager"
  },
  "childRoles": [],
  "permissionCount": 15,
  "userCount": 5
}
```

### Update Role

Updates an existing role.

**Endpoint:** `PUT /roles/:id`

**Required Permission:** `ROLE_MANAGEMENT_UPDATE`

**Path Parameters:**
- `id` - Role UUID

**Request Body:**
```json
{
  "displayName": "Updated Role Name",
  "description": "Updated description",
  "isActive": true
}
```

**Response:**
```json
{
  "id": "uuid-role-id",
  "name": "CUSTOM_ROLE",
  "displayName": "Updated Role Name",
  "description": "Updated description",
  "level": 4,
  "isActive": true,
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### Delete Role

Deletes a role from the system.

**Endpoint:** `DELETE /roles/:id`

**Required Permission:** `ROLE_MANAGEMENT_DELETE`

**Path Parameters:**
- `id` - Role UUID

**Response:** `204 No Content`

**Error Responses:**
- `403 Forbidden` - Cannot delete system role or role in use
- `404 Not Found` - Role not found

### Assign Permissions to Role

Assigns one or more permissions to a role.

**Endpoint:** `POST /roles/:id/permissions`

**Required Permission:** `ROLE_MANAGEMENT_ASSIGN`

**Path Parameters:**
- `id` - Role UUID

**Request Body:**
```json
{
  "permissionIds": ["uuid1", "uuid2", "uuid3"],
  "replace": false
}
```

**Response:**
```json
{
  "message": "Permissions assigned successfully"
}
```

### Revoke Permissions from Role

Revokes permissions from a role.

**Endpoint:** `DELETE /roles/:id/permissions`

**Required Permission:** `ROLE_MANAGEMENT_ASSIGN`

**Path Parameters:**
- `id` - Role UUID

**Request Body:**
```json
{
  "permissionIds": ["uuid1", "uuid2"]
}
```

**Response:**
```json
{
  "message": "Permissions revoked successfully"
}
```

### Get Role Permissions

Retrieves all permissions assigned to a role.

**Endpoint:** `GET /roles/:id/permissions`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Path Parameters:**
- `id` - Role UUID

**Response:**
```json
{
  "permissions": [
    {
      "id": "uuid-permission-id",
      "code": "USER_MANAGEMENT_READ",
      "module": "USER_MANAGEMENT",
      "action": "READ",
      "description": "Permission to read user data"
    }
  ]
}
```

### Assign Role to User

Assigns a role to a user.

**Endpoint:** `POST /roles/assign`

**Required Permission:** `ROLE_MANAGEMENT_ASSIGN`

**Request Body:**
```json
{
  "userId": "uuid-user-id",
  "roleId": "uuid-role-id",
  "expiresAt": "2024-12-31T23:59:59.999Z"
}
```

**Response:**
```json
{
  "message": "Role assigned successfully"
}
```

### Revoke Role from User

Revokes a role from a user.

**Endpoint:** `POST /roles/revoke`

**Required Permission:** `ROLE_MANAGEMENT_ASSIGN`

**Request Body:**
```json
{
  "userId": "uuid-user-id",
  "roleId": "uuid-role-id"
}
```

**Response:**
```json
{
  "message": "Role revoked successfully"
}
```

### Get Role Hierarchy

Retrieves the complete role hierarchy tree.

**Endpoint:** `GET /roles/hierarchy/tree`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Response:**
```json
{
  "hierarchy": [
    {
      "parentRoleId": "uuid-admin-id",
      "childRoleId": "uuid-manager-id",
      "inheritanceType": "FULL",
      "parentRole": {
        "name": "ADMIN",
        "level": 1
      },
      "childRole": {
        "name": "MANAGER",
        "level": 2
      }
    }
  ]
}
```

## Permission Management Endpoints

### Create Permission

Creates a new permission in the system.

**Endpoint:** `POST /permissions`

**Required Permission:** `ROLE_MANAGEMENT_CREATE`

**Request Body:**
```json
{
  "code": "CUSTOM_MODULE_READ",
  "module": "CUSTOM_MODULE",
  "action": "READ",
  "resource": "profile",
  "description": "Permission to read custom module data",
  "isActive": true
}
```

**Response:**
```json
{
  "id": "uuid-permission-id",
  "code": "CUSTOM_MODULE_READ",
  "module": "CUSTOM_MODULE",
  "action": "READ",
  "resource": "profile",
  "description": "Permission to read custom module data",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

### Get Permissions List

Retrieves a list of permissions with optional filtering.

**Endpoint:** `GET /permissions`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Query Parameters:**
- `search` (optional) - Search by code or description
- `module` (optional) - Filter by module
- `action` (optional) - Filter by action
- `isActive` (optional) - Filter by active status
- `page` (optional) - Page number
- `limit` (optional) - Items per page

**Response:**
```json
{
  "permissions": [
    {
      "id": "uuid-permission-id",
      "code": "USER_MANAGEMENT_READ",
      "module": "USER_MANAGEMENT",
      "action": "READ",
      "description": "Permission to read user data",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "roleCount": 5
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Check User Permission

Checks if a user has a specific permission.

**Endpoint:** `POST /permissions/check`

**Required Permission:** Authenticated user

**Request Body:**
```json
{
  "userId": "uuid-user-id",
  "permission": "USER_MANAGEMENT_READ",
  "resource": "user:123"
}
```

**Response:**
```json
{
  "allowed": true,
  "reason": "User has required permission: USER_MANAGEMENT_READ",
  "roleLevel": 2,
  "conflictingRoles": []
}
```

### Get Permissions by Module

Retrieves all permissions for a specific module.

**Endpoint:** `GET /permissions/module/:module`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Path Parameters:**
- `module` - Module name (e.g., USER_MANAGEMENT)

**Response:**
```json
{
  "module": "USER_MANAGEMENT",
  "permissions": [
    {
      "id": "uuid1",
      "code": "USER_MANAGEMENT_READ",
      "action": "READ"
    },
    {
      "id": "uuid2",
      "code": "USER_MANAGEMENT_CREATE",
      "action": "CREATE"
    }
  ]
}
```

### Get System Modules

Retrieves all available system modules.

**Endpoint:** `GET /permissions/meta/modules`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Response:**
```json
{
  "modules": [
    "USER_MANAGEMENT",
    "ROLE_MANAGEMENT",
    "CONTENT_MANAGEMENT",
    "MARKETING_CAMPAIGNS",
    "SALES_PIPELINE",
    "ANALYTICS_REPORTS",
    "FINANCIAL_DATA",
    "SYSTEM_SETTINGS",
    "AI_FEATURES",
    "EXPORT_TOOLS"
  ]
}
```

### Get Permission Actions

Retrieves all available permission actions.

**Endpoint:** `GET /permissions/meta/actions`

**Required Permission:** `ROLE_MANAGEMENT_READ`

**Response:**
```json
{
  "actions": [
    "READ",
    "CREATE",
    "UPDATE",
    "DELETE",
    "EXPORT",
    "APPROVE",
    "MANAGE",
    "ASSIGN"
  ]
}
```

## Error Responses

### Standard Error Format

All error responses follow this format:

```json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Error type",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/roles"
}
```

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Resource deleted successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource already exists
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Role Management**: 100 requests per minute per user
- **Permission Checks**: 1000 requests per minute per user
- **General Endpoints**: 500 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination with the following parameters:

- `page` - Page number (1-based, default: 1)
- `limit` - Items per page (default: 10, max: 100)

Pagination information is included in the response:

```json
{
  "data": [...],
  "total": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15
}
```

---

**For implementation details, refer to [Implementation Guide](./RBAC_IMPLEMENTATION_GUIDE.md).**
