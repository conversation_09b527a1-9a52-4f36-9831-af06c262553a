import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Appointment } from './appointment.entity';

export enum ServiceCategory {
  TECHNICAL_SUPPORT = 'technical_support',
  CONSULTATION = 'consultation',
  TRAINING = 'training',
  MAINTENANCE = 'maintenance',
  INSTALLATION = 'installation',
  OTHER = 'other',
}

@Entity('service_types')
@Index(['userId', 'isActive'])
export class ServiceType {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @OneToMany(() => Appointment, appointment => appointment.serviceType)
  appointments: Appointment[];

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ServiceCategory,
    default: ServiceCategory.OTHER,
  })
  category: ServiceCategory;

  @Column()
  duration: number; // in minutes

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  price: number;

  @Column({ default: 'VND' })
  currency: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  requiresApproval: boolean;

  @Column({ type: 'jsonb', nullable: true })
  requirements: {
    customerInfo?: string[];
    preparation?: string[];
    equipment?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  availability: {
    daysOfWeek?: number[]; // 0-6 (Sunday-Saturday)
    timeRanges?: Array<{
      start: string; // HH:mm
      end: string; // HH:mm
    }>;
    excludeDates?: string[]; // ISO dates
  };

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    bufferBefore?: number; // minutes
    bufferAfter?: number; // minutes
    maxAdvanceBooking?: number; // days
    minAdvanceBooking?: number; // hours
    allowRescheduling?: boolean;
    allowCancellation?: boolean;
    cancellationDeadline?: number; // hours before appointment
  };

  @Column({ type: 'text', nullable: true })
  instructions: string; // Instructions for customers

  @Column({ default: 0 })
  bookingCount: number;

  @Column({ nullable: true })
  lastBookedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isAvailable(): boolean {
    return this.isActive;
  }

  get formattedPrice(): string {
    return this.price ? `${this.price.toLocaleString()} ${this.currency}` : 'Free';
  }

  get formattedDuration(): string {
    const hours = Math.floor(this.duration / 60);
    const minutes = this.duration % 60;
    
    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${minutes}m`;
    }
  }
}
