import { IsS<PERSON>, IsOptional, IsBoolean, IsU<PERSON>D, IsArray, IsEnum, MinLength, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SystemModule, PermissionAction } from '../entities/permission.entity';

/**
 * DTO tạo quyền mới - Create permission DTO
 */
export class CreatePermissionDto {
  @ApiPropertyOptional({
    description: 'Mã quyền hạn (tự động tạo nếu không có) - Permission code (auto-generated if not provided)',
    example: 'USER_MANAGEMENT_READ',
    minLength: 2,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  code?: string;

  @ApiProperty({
    description: 'Module/tính năng - Module/feature',
    example: SystemModule.USER_MANAGEMENT,
    enum: SystemModule,
  })
  @IsEnum(SystemModule)
  module: SystemModule;

  @ApiProperty({
    description: 'Hành động - Action',
    example: PermissionAction.READ,
    enum: PermissionAction,
  })
  @IsEnum(PermissionAction)
  action: PermissionAction;

  @ApiPropertyOptional({
    description: 'Tài nguyên cụ thể - Specific resource',
    example: 'user:123',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  resource?: string;

  @ApiPropertyOptional({
    description: 'Mô tả quyền hạn - Permission description',
    example: 'Quyền xem danh sách người dùng',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động - Active status',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO cập nhật quyền - Update permission DTO
 */
export class UpdatePermissionDto {
  @ApiPropertyOptional({
    description: 'Mã quyền hạn - Permission code',
    example: 'USER_MANAGEMENT_READ',
    minLength: 2,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  code?: string;

  @ApiPropertyOptional({
    description: 'Module/tính năng - Module/feature',
    example: SystemModule.USER_MANAGEMENT,
    enum: SystemModule,
  })
  @IsOptional()
  @IsEnum(SystemModule)
  module?: SystemModule;

  @ApiPropertyOptional({
    description: 'Hành động - Action',
    example: PermissionAction.READ,
    enum: PermissionAction,
  })
  @IsOptional()
  @IsEnum(PermissionAction)
  action?: PermissionAction;

  @ApiPropertyOptional({
    description: 'Tài nguyên cụ thể - Specific resource',
    example: 'user:123',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  resource?: string;

  @ApiPropertyOptional({
    description: 'Mô tả quyền hạn - Permission description',
    example: 'Quyền xem danh sách người dùng',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động - Active status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO tạo nhóm quyền - Create permission group DTO
 */
export class CreatePermissionGroupDto {
  @ApiProperty({
    description: 'Tên nhóm quyền (unique) - Group name (unique)',
    example: 'USER_MANAGEMENT_GROUP',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả nhóm quyền - Group description',
    example: 'Nhóm quyền quản lý người dùng',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Module liên quan - Related module',
    example: SystemModule.USER_MANAGEMENT,
    enum: SystemModule,
  })
  @IsEnum(SystemModule)
  module: SystemModule;

  @ApiPropertyOptional({
    description: 'Danh sách ID quyền hạn - List of permission IDs',
    example: ['uuid-1', 'uuid-2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds?: string[];
}

/**
 * DTO kiểm tra quyền - Check permission DTO
 */
export class CheckPermissionDto {
  @ApiProperty({
    description: 'ID người dùng - User ID',
    example: 'uuid-string',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Mã quyền hạn - Permission code',
    example: 'USER_MANAGEMENT_READ',
  })
  @IsString()
  permission: string;

  @ApiPropertyOptional({
    description: 'Tài nguyên cụ thể - Specific resource',
    example: 'user:123',
  })
  @IsOptional()
  @IsString()
  resource?: string;
}

/**
 * DTO phản hồi quyền - Permission response DTO
 */
export class PermissionResponseDto {
  @ApiProperty({ description: 'ID quyền hạn - Permission ID' })
  id: string;

  @ApiProperty({ description: 'Mã quyền hạn - Permission code' })
  code: string;

  @ApiProperty({ description: 'Module - Module', enum: SystemModule })
  module: SystemModule;

  @ApiProperty({ description: 'Hành động - Action', enum: PermissionAction })
  action: PermissionAction;

  @ApiPropertyOptional({ description: 'Tài nguyên - Resource' })
  resource?: string;

  @ApiPropertyOptional({ description: 'Mô tả - Description' })
  description?: string;

  @ApiProperty({ description: 'Trạng thái hoạt động - Active status' })
  isActive: boolean;

  @ApiProperty({ description: 'Ngày tạo - Created date' })
  createdAt: Date;

  @ApiPropertyOptional({ description: 'Số lượng vai trò - Role count' })
  roleCount?: number;
}

/**
 * DTO phản hồi nhóm quyền - Permission group response DTO
 */
export class PermissionGroupResponseDto {
  @ApiProperty({ description: 'ID nhóm - Group ID' })
  id: string;

  @ApiProperty({ description: 'Tên nhóm - Group name' })
  name: string;

  @ApiPropertyOptional({ description: 'Mô tả - Description' })
  description?: string;

  @ApiProperty({ description: 'Module - Module', enum: SystemModule })
  module: SystemModule;

  @ApiProperty({ description: 'Ngày tạo - Created date' })
  createdAt: Date;

  @ApiPropertyOptional({ description: 'Danh sách quyền - Permissions', type: [PermissionResponseDto] })
  permissions?: PermissionResponseDto[];

  @ApiPropertyOptional({ description: 'Số lượng quyền - Permission count' })
  permissionCount?: number;
}

/**
 * DTO phản hồi kiểm tra quyền - Permission check response DTO
 */
export class PermissionCheckResponseDto {
  @ApiProperty({ description: 'Được phép - Allowed' })
  allowed: boolean;

  @ApiProperty({ description: 'Lý do - Reason' })
  reason: string;

  @ApiPropertyOptional({ description: 'Cấp độ vai trò - Role level' })
  roleLevel?: number;

  @ApiPropertyOptional({ description: 'Vai trò xung đột - Conflicting roles' })
  conflictingRoles?: string[];
}

/**
 * DTO danh sách quyền - Permission list DTO
 */
export class PermissionListResponseDto {
  @ApiProperty({ description: 'Danh sách quyền - Permission list', type: [PermissionResponseDto] })
  permissions: PermissionResponseDto[];

  @ApiProperty({ description: 'Tổng số - Total count' })
  total: number;

  @ApiPropertyOptional({ description: 'Trang hiện tại - Current page' })
  page?: number;

  @ApiPropertyOptional({ description: 'Số lượng mỗi trang - Items per page' })
  limit?: number;
}

/**
 * DTO truy vấn quyền - Permission query DTO
 */
export class PermissionQueryDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo mã hoặc mô tả - Search by code or description',
    example: 'user',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo module - Filter by module',
    example: SystemModule.USER_MANAGEMENT,
    enum: SystemModule,
  })
  @IsOptional()
  @IsEnum(SystemModule)
  module?: SystemModule;

  @ApiPropertyOptional({
    description: 'Lọc theo hành động - Filter by action',
    example: PermissionAction.READ,
    enum: PermissionAction,
  })
  @IsOptional()
  @IsEnum(PermissionAction)
  action?: PermissionAction;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái - Filter by active status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Số trang - Page number',
    example: 1,
  })
  @IsOptional()
  @IsString()
  page?: string;

  @ApiPropertyOptional({
    description: 'Số lượng mỗi trang - Items per page',
    example: 10,
  })
  @IsOptional()
  @IsString()
  limit?: string;
}

/**
 * DTO thêm quyền vào nhóm - Add permissions to group DTO
 */
export class AddPermissionsToGroupDto {
  @ApiProperty({
    description: 'Danh sách ID quyền hạn - List of permission IDs',
    example: ['uuid-1', 'uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}

/**
 * DTO xóa quyền khỏi nhóm - Remove permissions from group DTO
 */
export class RemovePermissionsFromGroupDto {
  @ApiProperty({
    description: 'Danh sách ID quyền hạn - List of permission IDs',
    example: ['uuid-1', 'uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];
}
