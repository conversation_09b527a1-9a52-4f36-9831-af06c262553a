import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Organization } from './organization.entity';
import { Role } from './role.entity';

export enum MemberStatus {
  PENDING = 'pending', // Invitation sent but not accepted
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  LEFT = 'left',
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

@Entity('organization_members')
@Index(['organizationId', 'userId'], { unique: true })
@Index(['organizationId', 'status'])
@Index(['userId', 'status'])
@Index(['invitationToken'], { unique: true })
export class OrganizationMember {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  organizationId: string;

  @ManyToOne(() => Organization, organization => organization.members, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId' })
  organization: Organization;

  @Column({ nullable: true })
  userId: string; // null if invitation not accepted yet

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  email: string; // Email used for invitation

  @Column()
  roleId: string;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @Column({
    type: 'enum',
    enum: MemberStatus,
    default: MemberStatus.PENDING,
  })
  status: MemberStatus;

  @Column({
    type: 'enum',
    enum: InvitationStatus,
    default: InvitationStatus.PENDING,
  })
  invitationStatus: InvitationStatus;

  @Column({ nullable: true })
  invitationToken: string; // Token for accepting invitation

  @Column({ nullable: true })
  invitedBy: string; // User ID who sent the invitation

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'invitedBy' })
  inviter: User;

  @Column({ nullable: true })
  invitedAt: Date;

  @Column({ nullable: true })
  acceptedAt: Date;

  @Column({ nullable: true })
  expiresAt: Date; // Invitation expiry

  @Column({ nullable: true })
  leftAt: Date;

  @Column({ type: 'text', nullable: true })
  invitationMessage: string; // Custom message with invitation

  @Column({ type: 'jsonb', nullable: true })
  permissions: string[]; // Additional permissions beyond role

  @Column({ type: 'jsonb', nullable: true })
  restrictions: {
    canInviteMembers?: boolean;
    canManageRoles?: boolean;
    canAccessBilling?: boolean;
    canExportData?: boolean;
    ipWhitelist?: string[];
    timeRestrictions?: {
      allowedHours?: string; // e.g., "09:00-17:00"
      allowedDays?: number[]; // 0-6 (Sunday-Saturday)
      timezone?: string;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    department?: string;
    jobTitle?: string;
    phoneNumber?: string;
    notes?: string;
    customFields?: Record<string, any>;
  };

  @Column({ nullable: true })
  lastActiveAt: Date;

  @Column({ default: 0 })
  loginCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === MemberStatus.ACTIVE;
  }

  get isPending(): boolean {
    return this.status === MemberStatus.PENDING;
  }

  get isSuspended(): boolean {
    return this.status === MemberStatus.SUSPENDED;
  }

  get hasLeft(): boolean {
    return this.status === MemberStatus.LEFT;
  }

  get isInvitationPending(): boolean {
    return this.invitationStatus === InvitationStatus.PENDING;
  }

  get isInvitationExpired(): boolean {
    return this.invitationStatus === InvitationStatus.EXPIRED || 
           (this.expiresAt && new Date() > this.expiresAt);
  }

  get canAcceptInvitation(): boolean {
    return this.isInvitationPending && !this.isInvitationExpired;
  }

  get displayName(): string {
    return this.user?.fullName || this.email;
  }

  get memberSince(): string {
    const date = this.acceptedAt || this.createdAt;
    return date.toLocaleDateString();
  }

  get daysSinceMember(): number {
    const date = this.acceptedAt || this.createdAt;
    const diff = new Date().getTime() - date.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  }

  get lastActiveAgo(): string {
    if (!this.lastActiveAt) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - this.lastActiveAt.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  }

  hasPermission(permissionName: string): boolean {
    // Check role permissions
    const hasRolePermission = this.role?.hasPermission(permissionName);
    
    // Check additional permissions
    const hasAdditionalPermission = this.permissions?.includes(permissionName);
    
    return hasRolePermission || hasAdditionalPermission;
  }

  getAllPermissions(): string[] {
    const rolePermissions = this.role?.getPermissionNames() || [];
    const additionalPermissions = this.permissions || [];
    
    return [...new Set([...rolePermissions, ...additionalPermissions])];
  }

  canPerformAction(action: string): boolean {
    if (!this.isActive) return false;
    
    // Check time restrictions
    if (this.restrictions?.timeRestrictions) {
      const now = new Date();
      const restrictions = this.restrictions.timeRestrictions;
      
      if (restrictions.allowedDays) {
        const currentDay = now.getDay();
        if (!restrictions.allowedDays.includes(currentDay)) {
          return false;
        }
      }
      
      if (restrictions.allowedHours) {
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM
        const [start, end] = restrictions.allowedHours.split('-');
        if (currentTime < start || currentTime > end) {
          return false;
        }
      }
    }
    
    return this.hasPermission(action);
  }
}
