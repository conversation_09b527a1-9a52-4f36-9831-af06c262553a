# 🚀 Deployment Guide

## 📋 Tổng quan

Comprehensive deployment guide cho Delify Platform, bao gồm **production deployment**, **CI/CD setup**, **monitoring**, và **maintenance procedures**.

## 🎯 Deployment Environments

### Environment Types
```
Development  → Staging → Production
     ↓           ↓          ↓
Local Dev   → UAT     → Live System
```

### Environment Configuration
```bash
# Development
NODE_ENV=development
DATABASE_SSL=false
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000

# Staging
NODE_ENV=staging
DATABASE_SSL=true
LOG_LEVEL=info
CORS_ORIGIN=https://staging.delify.com

# Production
NODE_ENV=production
DATABASE_SSL=true
LOG_LEVEL=warn
CORS_ORIGIN=https://app.delify.com
```

## 🐳 Docker Deployment

### Production Dockerfile
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Create uploads directory
RUN mkdir -p uploads && chown nestjs:nodejs uploads

# Switch to non-root user
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["node", "dist/main.js"]
```

### Docker Compose Production
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: delify-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=${DB_USERNAME}
      - DATABASE_PASSWORD=${DB_PASSWORD}
      - DATABASE_NAME=${DB_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - delify-network

  postgres:
    image: postgres:15-alpine
    container_name: delify-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - delify-network

  redis:
    image: redis:7-alpine
    container_name: delify-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - delify-network

  nginx:
    image: nginx:alpine
    container_name: delify-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - delify-network

volumes:
  postgres_data:
  redis_data:

networks:
  delify-network:
    driver: bridge
```

### Nginx Configuration
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream delify_app {
        server app:3000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    server {
        listen 80;
        server_name delify.com www.delify.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name delify.com www.delify.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/delify.crt;
        ssl_certificate_key /etc/nginx/ssl/delify.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://delify_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Auth routes with stricter rate limiting
        location /api/v1/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://delify_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://delify_app;
            access_log off;
        }

        # Static files
        location /uploads/ {
            alias /app/uploads/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## ☁️ Cloud Deployment

### AWS ECS Deployment
```yaml
# ecs-task-definition.json
{
  "family": "delify-platform",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "delify-app",
      "image": "your-registry/delify-platform:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_PASSWORD",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:delify/db-password"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:delify/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/delify-platform",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### Kubernetes Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: delify-platform
  labels:
    app: delify-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: delify-platform
  template:
    metadata:
      labels:
        app: delify-platform
    spec:
      containers:
      - name: delify-app
        image: your-registry/delify-platform:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_HOST
          valueFrom:
            secretKeyRef:
              name: delify-secrets
              key: database-host
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: delify-secrets
              key: database-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: delify-platform-service
spec:
  selector:
    app: delify-platform
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: delify_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm run test:ci
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/delify_test
    
    - name: Build application
      run: npm run build

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    
    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: delify-platform
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ecs-task-definition.json
        service: delify-platform-service
        cluster: delify-cluster
        wait-for-service-stability: true
```

## 📊 Monitoring & Logging

### Application Monitoring
```typescript
// src/common/interceptors/monitoring.interceptor.ts
@Injectable()
export class MonitoringInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();
    
    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        
        // Log performance metrics
        console.log(JSON.stringify({
          timestamp: new Date().toISOString(),
          method: request.method,
          url: request.url,
          duration,
          statusCode: context.switchToHttp().getResponse().statusCode,
        }));
      }),
      catchError((error) => {
        // Log errors
        console.error(JSON.stringify({
          timestamp: new Date().toISOString(),
          method: request.method,
          url: request.url,
          error: error.message,
          stack: error.stack,
        }));
        
        throw error;
      }),
    );
  }
}
```

### Health Check Endpoint
```typescript
// src/health/health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private redis: RedisHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.redis.checkHealth('redis'),
      () => this.checkExternalServices(),
    ]);
  }

  private async checkExternalServices(): Promise<HealthIndicatorResult> {
    try {
      // Check AI providers availability
      const ollamaHealth = await this.checkOllamaHealth();
      
      return {
        external_services: {
          status: 'up',
          ollama: ollamaHealth,
        },
      };
    } catch (error) {
      return {
        external_services: {
          status: 'down',
          error: error.message,
        },
      };
    }
  }
}
```

## 🔧 Environment Management

### Environment Variables
```bash
# Production environment variables
NODE_ENV=production
PORT=3000

# Database
DATABASE_HOST=prod-db.cluster-xxx.us-east-1.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_USERNAME=delify_prod
DATABASE_PASSWORD=${DB_PASSWORD}
DATABASE_NAME=delify_platform
DATABASE_SSL=true
DATABASE_POOL_SIZE=20

# Security
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Redis
REDIS_HOST=prod-redis.xxx.cache.amazonaws.com
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_PASSWORD}

# AI Providers
OPENAI_API_KEY=${OPENAI_API_KEY}
GROK_API_KEY=${GROK_API_KEY}
GEMINI_API_KEY=${GEMINI_API_KEY}
OLLAMA_BASE_URL=http://ollama-service:11434

# Email
SMTP_HOST=email-smtp.us-east-1.amazonaws.com
SMTP_PORT=587
SMTP_USER=${SMTP_USER}
SMTP_PASS=${SMTP_PASS}

# File Storage
AWS_S3_BUCKET=delify-uploads-prod
AWS_REGION=us-east-1

# Monitoring
LOG_LEVEL=warn
SENTRY_DSN=${SENTRY_DSN}
```

### Secrets Management
```bash
# AWS Secrets Manager
aws secretsmanager create-secret \
  --name "delify/database-password" \
  --description "Database password for Delify Platform" \
  --secret-string "your-secure-database-password"

aws secretsmanager create-secret \
  --name "delify/jwt-secret" \
  --description "JWT secret for Delify Platform" \
  --secret-string "your-super-secret-jwt-key"

# Kubernetes Secrets
kubectl create secret generic delify-secrets \
  --from-literal=database-password=your-secure-password \
  --from-literal=jwt-secret=your-jwt-secret \
  --from-literal=openai-api-key=your-openai-key
```

## 🔄 Database Migration in Production

### Migration Strategy
```bash
# Pre-deployment migration check
npm run migration:show

# Run migrations
npm run migration:run

# Verify migration success
npm run migration:show

# Rollback if needed (emergency only)
npm run migration:revert
```

### Zero-Downtime Deployment
```bash
#!/bin/bash
# deploy.sh

set -e

echo "Starting deployment..."

# 1. Run database migrations
echo "Running database migrations..."
npm run migration:run

# 2. Build new image
echo "Building new image..."
docker build -t delify-platform:$BUILD_NUMBER .

# 3. Update service with rolling update
echo "Updating service..."
docker service update \
  --image delify-platform:$BUILD_NUMBER \
  --update-parallelism 1 \
  --update-delay 30s \
  delify-platform

# 4. Wait for deployment to complete
echo "Waiting for deployment to complete..."
docker service ls

# 5. Run health checks
echo "Running health checks..."
curl -f http://localhost/health || exit 1

echo "Deployment completed successfully!"
```

## 📈 Performance Optimization

### Production Optimizations
```typescript
// main.ts - Production optimizations
async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: process.env.NODE_ENV === 'production' 
      ? ['error', 'warn'] 
      : ['log', 'debug', 'error', 'verbose', 'warn'],
  });

  // Enable compression
  app.use(compression());

  // Security headers
  app.use(helmet());

  // Rate limiting
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: process.env.CORS_ORIGIN?.split(',') || false,
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  await app.listen(process.env.PORT || 3000);
}
```

## 🚨 Troubleshooting

### Common Deployment Issues
```bash
# Container won't start
docker logs delify-app

# Database connection issues
kubectl exec -it delify-pod -- npm run migration:show

# Memory issues
docker stats delify-app

# Network connectivity
kubectl exec -it delify-pod -- curl http://postgres:5432

# Check environment variables
kubectl exec -it delify-pod -- env | grep DATABASE
```

### Rollback Procedures
```bash
# Docker rollback
docker service rollback delify-platform

# Kubernetes rollback
kubectl rollout undo deployment/delify-platform

# Database rollback (if needed)
npm run migration:revert
```

## 🎯 Best Practices

### Deployment Checklist
- [ ] **Tests pass** - All tests green
- [ ] **Security scan** - No vulnerabilities
- [ ] **Performance test** - Meets requirements
- [ ] **Database backup** - Recent backup available
- [ ] **Migration tested** - Migrations work on staging
- [ ] **Monitoring ready** - Alerts configured
- [ ] **Rollback plan** - Rollback procedure tested
- [ ] **Documentation updated** - Deployment docs current

### Security Considerations
- Use secrets management for sensitive data
- Enable SSL/TLS encryption
- Implement proper authentication
- Regular security updates
- Network security (VPC, security groups)
- Container security scanning
- Access logging and monitoring

**Following this deployment guide ensures reliable, secure, và scalable production deployment của Delify Platform.** 🚀✨
