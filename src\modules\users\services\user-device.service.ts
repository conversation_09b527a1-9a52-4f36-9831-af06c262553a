import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserDevice, DeviceStatus, DeviceType } from '../entities/user-device.entity';
import { LoggerService } from '../../../common/services/logger.service';
import { UtilsService } from '../../../common/services/utils.service';

@Injectable()
export class UserDeviceService {
  constructor(
    @InjectRepository(UserDevice)
    private deviceRepository: Repository<UserDevice>,
    private logger: LoggerService,
    private utilsService: UtilsService,
  ) {}

  async createDeviceSession(userId: string, deviceInfo: {
    userAgent: string;
    ipAddress: string;
    deviceName?: string;
    location?: string;
  }): Promise<UserDevice> {
    // Parse device information from user agent
    const parsedDevice = this.parseUserAgent(deviceInfo.userAgent);
    
    // Generate session token
    const sessionToken = this.utilsService.generateRandomString(64);
    
    // Set expiry (30 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Check if this is a new device/location
    const securityInfo = await this.analyzeSecurityRisk(userId, deviceInfo);

    const device = this.deviceRepository.create({
      userId,
      sessionToken,
      deviceName: deviceInfo.deviceName || parsedDevice.deviceName,
      deviceType: parsedDevice.deviceType,
      userAgent: deviceInfo.userAgent,
      ipAddress: deviceInfo.ipAddress,
      location: deviceInfo.location,
      status: DeviceStatus.ACTIVE,
      deviceInfo: parsedDevice.deviceInfo,
      loginAt: new Date(),
      lastActiveAt: new Date(),
      expiresAt,
      isCurrentDevice: true,
      securityInfo,
    });

    // Mark other devices as not current
    await this.deviceRepository.update(
      { userId, isCurrentDevice: true },
      { isCurrentDevice: false }
    );

    const savedDevice = await this.deviceRepository.save(device);

    this.logger.logWithContext(
      `New device session created for user ${userId}: ${savedDevice.id}`,
      'UserDeviceService'
    );

    return savedDevice;
  }

  async getUserDevices(userId: string): Promise<UserDevice[]> {
    return this.deviceRepository.find({
      where: { userId },
      order: { lastActiveAt: 'DESC' },
    });
  }

  async getActiveDevices(userId: string): Promise<UserDevice[]> {
    return this.deviceRepository.find({
      where: { 
        userId, 
        status: DeviceStatus.ACTIVE,
      },
      order: { lastActiveAt: 'DESC' },
    });
  }

  async getDeviceByToken(sessionToken: string): Promise<UserDevice | null> {
    return this.deviceRepository.findOne({
      where: { sessionToken, status: DeviceStatus.ACTIVE },
    });
  }

  async updateDeviceActivity(sessionToken: string): Promise<void> {
    await this.deviceRepository.update(
      { sessionToken },
      { lastActiveAt: new Date() }
    );
  }

  async logoutDevice(sessionToken: string): Promise<void> {
    const device = await this.deviceRepository.findOne({
      where: { sessionToken },
    });

    if (device) {
      await this.deviceRepository.update(
        { sessionToken },
        {
          status: DeviceStatus.INACTIVE,
          logoutAt: new Date(),
          isCurrentDevice: false,
        }
      );

      this.logger.logWithContext(
        `Device logged out: ${device.id}`,
        'UserDeviceService'
      );
    }
  }

  async logoutAllDevices(userId: string, exceptToken?: string): Promise<number> {
    const query = this.deviceRepository
      .createQueryBuilder()
      .update(UserDevice)
      .set({
        status: DeviceStatus.INACTIVE,
        logoutAt: new Date(),
        isCurrentDevice: false,
      })
      .where('userId = :userId', { userId })
      .andWhere('status = :status', { status: DeviceStatus.ACTIVE });

    if (exceptToken) {
      query.andWhere('sessionToken != :exceptToken', { exceptToken });
    }

    const result = await query.execute();

    this.logger.logWithContext(
      `Logged out ${result.affected} devices for user ${userId}`,
      'UserDeviceService'
    );

    return result.affected || 0;
  }

  async revokeDevice(userId: string, deviceId: string): Promise<void> {
    const device = await this.deviceRepository.findOne({
      where: { id: deviceId, userId },
    });

    if (!device) {
      throw new NotFoundException('Device not found');
    }

    await this.deviceRepository.update(deviceId, {
      status: DeviceStatus.REVOKED,
      logoutAt: new Date(),
      isCurrentDevice: false,
    });

    this.logger.logWithContext(
      `Device revoked: ${deviceId}`,
      'UserDeviceService'
    );
  }

  async cleanupExpiredSessions(): Promise<void> {
    const expiredDevices = await this.deviceRepository.find({
      where: {
        status: DeviceStatus.ACTIVE,
      },
    });

    const now = new Date();
    const toExpire = expiredDevices.filter(device => 
      device.expiresAt && device.expiresAt < now
    );

    if (toExpire.length > 0) {
      await this.deviceRepository.update(
        toExpire.map(device => device.id),
        { 
          status: DeviceStatus.INACTIVE,
          logoutAt: now,
        }
      );

      this.logger.logWithContext(
        `Cleaned up ${toExpire.length} expired sessions`,
        'UserDeviceService'
      );
    }
  }

  async getDeviceStats(userId: string): Promise<any> {
    const devices = await this.getUserDevices(userId);

    const stats = {
      total: devices.length,
      active: devices.filter(d => d.isActive).length,
      inactive: devices.filter(d => !d.isActive).length,
      byType: {},
      byLocation: {},
      recentActivity: devices
        .filter(d => d.lastActiveAt)
        .sort((a, b) => b.lastActiveAt.getTime() - a.lastActiveAt.getTime())
        .slice(0, 5),
      securityAlerts: devices.filter(d => d.isHighRisk || d.needsVerification),
    };

    // Group by device type
    devices.forEach(device => {
      const type = device.deviceType || 'unknown';
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    });

    // Group by location
    devices.forEach(device => {
      const location = device.location || 'Unknown';
      stats.byLocation[location] = (stats.byLocation[location] || 0) + 1;
    });

    return stats;
  }

  private parseUserAgent(userAgent: string): {
    deviceName: string;
    deviceType: DeviceType;
    deviceInfo: any;
  } {
    // Simple user agent parsing - in production, use a library like ua-parser-js
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isTablet = /iPad|Tablet/.test(userAgent);
    const isDesktop = !isMobile && !isTablet;

    let browser = 'Unknown';
    let os = 'Unknown';

    // Extract browser
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';

    // Extract OS
    if (userAgent.includes('Windows')) os = 'Windows';
    else if (userAgent.includes('Mac')) os = 'macOS';
    else if (userAgent.includes('Linux')) os = 'Linux';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iOS')) os = 'iOS';

    const deviceType = isTablet ? DeviceType.TABLET : 
                      isMobile ? DeviceType.MOBILE : 
                      DeviceType.DESKTOP;

    return {
      deviceName: `${browser} on ${os}`,
      deviceType,
      deviceInfo: {
        browser,
        os,
        isMobile,
        isTablet,
        isDesktop,
        userAgent,
      },
    };
  }

  private async analyzeSecurityRisk(userId: string, deviceInfo: any): Promise<any> {
    // Get user's previous devices
    const previousDevices = await this.getActiveDevices(userId);

    const isNewDevice = !previousDevices.some(device => 
      device.userAgent === deviceInfo.userAgent
    );

    const isNewLocation = deviceInfo.location && !previousDevices.some(device => 
      device.location === deviceInfo.location
    );

    let riskScore = 0;
    if (isNewDevice) riskScore += 30;
    if (isNewLocation) riskScore += 40;

    // Additional risk factors could be added here
    // - Suspicious IP ranges
    // - Unusual login times
    // - Geographic distance from previous logins

    return {
      isTrusted: riskScore < 50,
      riskScore,
      isNewLocation,
      isNewDevice,
      requiresVerification: riskScore > 70,
    };
  }
}
