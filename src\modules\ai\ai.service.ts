import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ai<PERSON><PERSON><PERSON>, AIProvider, AIModelName } from './entities/ai-model.entity';
import { ChatSession } from './entities/chat-session.entity';
import { CvAnalysis } from './entities/cv-analysis.entity';
import { OpenAiService } from './services/openai.service';
import { ContentOptimizationService } from './services/content-optimization.service';
import { CvScoringService } from './services/cv-scoring.service';
import { ChatbotService } from './services/chatbot.service';
import { AIProviderFactory } from './providers/ai-provider.factory';
import { BaseAIProvider, AIMessage, AIResponse } from './providers/base-ai.provider';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class AiService {
  constructor(
    @InjectRepository(AiModel)
    private aiModelRepository: Repository<AiModel>,
    @InjectRepository(ChatSession)
    private chatSessionRepository: Repository<ChatSession>,
    @InjectRepository(CvAnalysis)
    private cvAnalysisRepository: Repository<CvAnalysis>,
    private openAiService: OpenAiService,
    private contentOptimizationService: ContentOptimizationService,
    private cvScoringService: CvScoringService,
    private chatbotService: ChatbotService,
    private aiProviderFactory: AIProviderFactory,
    private logger: LoggerService,
  ) {}

  async optimizeContent(userId: string, data: { content: string; platform: string; target: string }) {
    this.logger.logWithContext(`Content optimization requested for user: ${userId}`, 'AiService');
    return this.contentOptimizationService.optimizeContent(data.content, data.platform, data.target);
  }

  async generateContent(userId: string, data: { prompt: string; type: string; platform: string }) {
    this.logger.logWithContext(`Content generation requested for user: ${userId}`, 'AiService');
    return this.contentOptimizationService.generateContent(data.prompt, data.type, data.platform);
  }

  async analyzeCv(userId: string, file: Express.Multer.File, data: { jobDescription?: string; requirements?: string }) {
    this.logger.logWithContext(`CV analysis requested for user: ${userId}`, 'AiService');

    const analysis = await this.cvScoringService.analyzeCv(file, data.jobDescription, data.requirements);

    // Save analysis to database
    const cvAnalysis = this.cvAnalysisRepository.create({
      userId,
      fileName: file.originalname,
      fileSize: file.size,
      jobDescription: data.jobDescription,
      requirements: data.requirements,
      ...analysis,
    });

    await this.cvAnalysisRepository.save(cvAnalysis);

    return cvAnalysis;
  }

  async getCvAnalyses(userId: string) {
    return this.cvAnalysisRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: 50,
    });
  }

  async processChatbotMessage(userId: string, data: { message: string; sessionId?: string; context?: any }) {
    this.logger.logWithContext(`Chatbot message received for user: ${userId}`, 'AiService');
    return this.chatbotService.processMessage(userId, data.message, data.sessionId, data.context);
  }

  async getChatbotSessions(userId: string) {
    return this.chatSessionRepository.find({
      where: { userId },
      order: { updatedAt: 'DESC' },
      take: 20,
    });
  }

  async analyzeComment(userId: string, data: { comment: string; platform: string }) {
    this.logger.logWithContext(`Comment analysis requested for user: ${userId}`, 'AiService');
    return this.openAiService.analyzeComment(data.comment, data.platform);
  }

  async analyzeEmail(userId: string, data: { subject: string; content: string; sender: string }) {
    this.logger.logWithContext(`Email analysis requested for user: ${userId}`, 'AiService');
    return this.openAiService.analyzeEmail(data.subject, data.content, data.sender);
  }

  async getAvailableModels() {
    return this.aiModelRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' },
    });
  }

  async configureModel(userId: string, data: { modelType: string; settings: any }) {
    this.logger.logWithContext(`AI model configuration requested for user: ${userId}`, 'AiService');

    // Find or create model configuration for user
    let model = await this.aiModelRepository.findOne({
      where: { userId, modelType: data.modelType as any },
    });

    if (!model) {
      model = this.aiModelRepository.create({
        userId,
        name: data.modelType,
        modelType: data.modelType as any,
        settings: data.settings,
        isActive: true,
      });
    } else {
      model.settings = { ...model.settings, ...data.settings };
      model.updatedAt = new Date();
    }

    return this.aiModelRepository.save(model);
  }

  // New methods for multi-provider support
  async generateText(
    provider: AIProvider,
    model: AIModelName,
    messages: AIMessage[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      topP?: number;
    }
  ): Promise<AIResponse> {
    try {
      const aiProvider = this.aiProviderFactory.createProviderFromEnv(provider, model);
      const response = await aiProvider.generateText(messages, options);

      this.logger.logWithContext(
        `Text generated using ${provider}:${model} - ${response.usage.totalTokens} tokens`,
        'AiService'
      );

      return response;
    } catch (error) {
      this.logger.logError(error, `Error generating text with ${provider}:${model}`);
      throw error;
    }
  }

  async generateStream(
    provider: AIProvider,
    model: AIModelName,
    messages: AIMessage[],
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<AsyncGenerator<string, void, unknown>> {
    try {
      const aiProvider = this.aiProviderFactory.createProviderFromEnv(provider, model);
      return aiProvider.generateStream(messages, options);
    } catch (error) {
      this.logger.logError(error, `Error generating stream with ${provider}:${model}`);
      throw error;
    }
  }

  async analyzeImage(
    provider: AIProvider,
    model: AIModelName,
    imageUrl: string,
    prompt: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
    }
  ): Promise<AIResponse> {
    try {
      const aiProvider = this.aiProviderFactory.createProviderFromEnv(provider, model);
      const response = await aiProvider.analyzeImage(imageUrl, prompt, options);

      this.logger.logWithContext(
        `Image analyzed using ${provider}:${model}`,
        'AiService'
      );

      return response;
    } catch (error) {
      this.logger.logError(error, `Error analyzing image with ${provider}:${model}`);
      throw error;
    }
  }

  async getAvailableProviders(): Promise<{
    provider: AIProvider;
    models: AIModelName[];
    isConfigured: boolean;
    capabilities: any;
  }[]> {
    const availableProviders = this.aiProviderFactory.getAvailableProviders();

    const result = [];
    for (const provider of Object.values(AIProvider)) {
      const models = this.aiProviderFactory.getProviderModels(provider);
      const isConfigured = availableProviders.includes(provider);

      result.push({
        provider,
        models,
        isConfigured,
        capabilities: models.map(model => ({
          model,
          ...this.aiProviderFactory.getModelCapabilities(model),
        })),
      });
    }

    return result;
  }

  async validateProvider(provider: AIProvider): Promise<{
    isValid: boolean;
    error?: string;
    models?: string[];
  }> {
    return this.aiProviderFactory.validateProvider(provider);
  }

  async getBestProviderForTask(task: 'text' | 'image' | 'code' | 'embedding' | 'creative'): Promise<{
    provider: AIProvider;
    model: AIModelName;
    reason: string;
  }> {
    const providers = await this.getAvailableProviders();
    const configuredProviders = providers.filter(p => p.isConfigured);

    if (configuredProviders.length === 0) {
      throw new Error('No AI providers are configured');
    }

    // Simple logic to choose best provider for task
    switch (task) {
      case 'text':
        // Prefer Grok for general text, then OLLAMA for privacy, then GPT-4
        for (const provider of [AIProvider.GROK, AIProvider.OLLAMA, AIProvider.OPENAI, AIProvider.GEMINI]) {
          const found = configuredProviders.find(p => p.provider === provider);
          if (found) {
            const model = this.aiProviderFactory.getDefaultModel(provider);
            return {
              provider,
              model,
              reason: `${provider} is optimized for text generation`,
            };
          }
        }
        break;

      case 'image':
        // Prefer GPT-4 Vision, then Gemini Pro Vision
        for (const provider of [AIProvider.OPENAI, AIProvider.GEMINI, AIProvider.GROK]) {
          const found = configuredProviders.find(p => p.provider === provider);
          if (found) {
            const visionModels = {
              [AIProvider.OPENAI]: AIModelName.GPT_4_TURBO,
              [AIProvider.GEMINI]: AIModelName.GEMINI_PRO_VISION,
              [AIProvider.GROK]: AIModelName.GROK_2,
            };
            return {
              provider,
              model: visionModels[provider],
              reason: `${provider} supports image analysis`,
            };
          }
        }
        break;

      case 'code':
        // Prefer Code Llama for code, then GPT-4, then Grok
        for (const provider of [AIProvider.OLLAMA, AIProvider.OPENAI, AIProvider.GROK, AIProvider.GEMINI]) {
          const found = configuredProviders.find(p => p.provider === provider);
          if (found) {
            const model = this.aiProviderFactory.getDefaultModel(provider);
            return {
              provider,
              model,
              reason: `${provider} excels at code generation and analysis`,
            };
          }
        }
        break;

      case 'creative':
        // Prefer Grok for creative tasks, then OLLAMA for privacy, then GPT-4
        for (const provider of [AIProvider.GROK, AIProvider.OLLAMA, AIProvider.OPENAI, AIProvider.GEMINI]) {
          const found = configuredProviders.find(p => p.provider === provider);
          if (found) {
            const model = this.aiProviderFactory.getDefaultModel(provider);
            return {
              provider,
              model,
              reason: `${provider} excels at creative content generation`,
            };
          }
        }
        break;

      case 'embedding':
        // Prefer OpenAI for embeddings, then Gemini
        for (const provider of [AIProvider.OPENAI, AIProvider.GEMINI]) {
          const found = configuredProviders.find(p => p.provider === provider);
          if (found) {
            const model = this.aiProviderFactory.getDefaultModel(provider);
            return {
              provider,
              model,
              reason: `${provider} provides high-quality embeddings`,
            };
          }
        }
        break;
    }

    // Fallback to first available provider
    const fallback = configuredProviders[0];
    return {
      provider: fallback.provider,
      model: this.aiProviderFactory.getDefaultModel(fallback.provider),
      reason: 'Fallback to available provider',
    };
  }
}
