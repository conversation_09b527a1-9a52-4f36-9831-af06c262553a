import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailLog, EmailStatus } from '../entities/email-log.entity';
import { EmailCampaign } from '../entities/email-campaign.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class EmailAnalyticsService {
  constructor(
    @InjectRepository(EmailLog)
    private emailLogRepository: Repository<EmailLog>,
    @InjectRepository(EmailCampaign)
    private emailCampaignRepository: Repository<EmailCampaign>,
    private logger: LoggerService,
  ) {}

  async getOverallStats(userId: string, dateRange?: { from: Date; to: Date }): Promise<{
    totalSent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
    complained: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    bounceRate: number;
    unsubscribeRate: number;
    complaintRate: number;
  }> {
    let query = this.emailLogRepository
      .createQueryBuilder('log')
      .where('log.userId = :userId', { userId });

    if (dateRange) {
      query = query.andWhere('log.createdAt BETWEEN :from AND :to', {
        from: dateRange.from,
        to: dateRange.to,
      });
    }

    const logs = await query.getMany();

    const stats = {
      totalSent: logs.filter(log => log.status !== EmailStatus.FAILED).length,
      delivered: logs.filter(log => log.wasDelivered).length,
      opened: logs.filter(log => log.wasOpened).length,
      clicked: logs.filter(log => log.wasClicked).length,
      bounced: logs.filter(log => log.hasBounced).length,
      unsubscribed: logs.filter(log => log.status === EmailStatus.UNSUBSCRIBED).length,
      complained: logs.filter(log => log.status === EmailStatus.COMPLAINED).length,
      deliveryRate: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
      unsubscribeRate: 0,
      complaintRate: 0,
    };

    // Calculate rates
    if (stats.totalSent > 0) {
      stats.deliveryRate = Math.round((stats.delivered / stats.totalSent) * 100);
      stats.bounceRate = Math.round((stats.bounced / stats.totalSent) * 100);
      stats.unsubscribeRate = Math.round((stats.unsubscribed / stats.totalSent) * 100);
      stats.complaintRate = Math.round((stats.complained / stats.totalSent) * 100);
    }

    if (stats.delivered > 0) {
      stats.openRate = Math.round((stats.opened / stats.delivered) * 100);
      stats.clickRate = Math.round((stats.clicked / stats.delivered) * 100);
    }

    return stats;
  }

  async getCampaignStats(userId: string, campaignId: string): Promise<{
    totalSent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
    complained: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    bounceRate: number;
    timeline: Array<{
      date: string;
      sent: number;
      delivered: number;
      opened: number;
      clicked: number;
    }>;
  }> {
    const logs = await this.emailLogRepository.find({
      where: { userId, campaignId },
      order: { createdAt: 'ASC' },
    });

    const stats = {
      totalSent: logs.filter(log => log.status !== EmailStatus.FAILED).length,
      delivered: logs.filter(log => log.wasDelivered).length,
      opened: logs.filter(log => log.wasOpened).length,
      clicked: logs.filter(log => log.wasClicked).length,
      bounced: logs.filter(log => log.hasBounced).length,
      unsubscribed: logs.filter(log => log.status === EmailStatus.UNSUBSCRIBED).length,
      complained: logs.filter(log => log.status === EmailStatus.COMPLAINED).length,
      deliveryRate: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
      timeline: [],
    };

    // Calculate rates
    if (stats.totalSent > 0) {
      stats.deliveryRate = Math.round((stats.delivered / stats.totalSent) * 100);
      stats.bounceRate = Math.round((stats.bounced / stats.totalSent) * 100);
    }

    if (stats.delivered > 0) {
      stats.openRate = Math.round((stats.opened / stats.delivered) * 100);
      stats.clickRate = Math.round((stats.clicked / stats.delivered) * 100);
    }

    // Generate timeline
    const timelineMap = new Map<string, any>();

    logs.forEach(log => {
      const date = log.createdAt.toISOString().split('T')[0];

      if (!timelineMap.has(date)) {
        timelineMap.set(date, {
          date,
          sent: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
        });
      }

      const dayStats = timelineMap.get(date);

      if (log.status !== EmailStatus.FAILED) {
        dayStats.sent++;
      }
      if (log.wasDelivered) {
        dayStats.delivered++;
      }
      if (log.wasOpened) {
        dayStats.opened++;
      }
      if (log.wasClicked) {
        dayStats.clicked++;
      }
    });

    stats.timeline = Array.from(timelineMap.values()).sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    return stats;
  }

  async getTopPerformingCampaigns(userId: string, limit: number = 10): Promise<Array<{
    campaignId: string;
    campaignName: string;
    openRate: number;
    clickRate: number;
    totalSent: number;
  }>> {
    const campaigns = await this.emailCampaignRepository.find({
      where: { userId },
      select: ['id', 'name', 'analytics', 'sentCount'],
    });

    return campaigns
      .filter(campaign => campaign.analytics && campaign.sentCount > 0)
      .map(campaign => ({
        campaignId: campaign.id,
        campaignName: campaign.name,
        openRate: campaign.analytics.openRate || 0,
        clickRate: campaign.analytics.clickRate || 0,
        totalSent: campaign.sentCount,
      }))
      .sort((a, b) => b.openRate - a.openRate)
      .slice(0, limit);
  }

  async getEmailEngagementTrends(userId: string, days: number = 30): Promise<Array<{
    date: string;
    sent: number;
    opened: number;
    clicked: number;
    openRate: number;
    clickRate: number;
  }>> {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - days);

    const logs = await this.emailLogRepository
      .createQueryBuilder('log')
      .where('log.userId = :userId', { userId })
      .andWhere('log.createdAt >= :fromDate', { fromDate })
      .orderBy('log.createdAt', 'ASC')
      .getMany();

    const trendsMap = new Map<string, any>();

    // Initialize all dates in range
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      const dateStr = date.toISOString().split('T')[0];

      trendsMap.set(dateStr, {
        date: dateStr,
        sent: 0,
        opened: 0,
        clicked: 0,
        openRate: 0,
        clickRate: 0,
      });
    }

    // Populate with actual data
    logs.forEach(log => {
      const date = log.createdAt.toISOString().split('T')[0];

      if (trendsMap.has(date)) {
        const dayStats = trendsMap.get(date);

        if (log.status !== EmailStatus.FAILED) {
          dayStats.sent++;
        }
        if (log.wasOpened) {
          dayStats.opened++;
        }
        if (log.wasClicked) {
          dayStats.clicked++;
        }
      }
    });

    // Calculate rates
    const trends = Array.from(trendsMap.values());
    trends.forEach(day => {
      if (day.sent > 0) {
        day.openRate = Math.round((day.opened / day.sent) * 100);
        day.clickRate = Math.round((day.clicked / day.sent) * 100);
      }
    });

    return trends;
  }

  async getDeviceAndClientStats(userId: string, campaignId?: string): Promise<{
    devices: Array<{ name: string; count: number; percentage: number }>;
    emailClients: Array<{ name: string; count: number; percentage: number }>;
  }> {
    let query = this.emailLogRepository
      .createQueryBuilder('log')
      .where('log.userId = :userId', { userId })
      .andWhere('log.metadata IS NOT NULL');

    if (campaignId) {
      query = query.andWhere('log.campaignId = :campaignId', { campaignId });
    }

    const logs = await query.getMany();

    const deviceCounts = new Map<string, number>();
    const clientCounts = new Map<string, number>();

    logs.forEach(log => {
      if (log.metadata) {
        if (log.metadata.device) {
          const device = log.metadata.device;
          deviceCounts.set(device, (deviceCounts.get(device) || 0) + 1);
        }

        if (log.metadata.userAgent) {
          // Simple email client detection based on user agent
          const userAgent = log.metadata.userAgent.toLowerCase();
          let client = 'Unknown';

          if (userAgent.includes('outlook')) client = 'Outlook';
          else if (userAgent.includes('gmail')) client = 'Gmail';
          else if (userAgent.includes('apple mail')) client = 'Apple Mail';
          else if (userAgent.includes('thunderbird')) client = 'Thunderbird';
          else if (userAgent.includes('yahoo')) client = 'Yahoo Mail';

          clientCounts.set(client, (clientCounts.get(client) || 0) + 1);
        }
      }
    });

    const totalLogs = logs.length;

    const devices = Array.from(deviceCounts.entries())
      .map(([name, count]) => ({
        name,
        count,
        percentage: Math.round((count / totalLogs) * 100),
      }))
      .sort((a, b) => b.count - a.count);

    const emailClients = Array.from(clientCounts.entries())
      .map(([name, count]) => ({
        name,
        count,
        percentage: Math.round((count / totalLogs) * 100),
      }))
      .sort((a, b) => b.count - a.count);

    return { devices, emailClients };
  }

  async getUnsubscribeReasons(userId: string): Promise<Array<{
    reason: string;
    count: number;
    percentage: number;
  }>> {
    const unsubscribeLogs = await this.emailLogRepository.find({
      where: {
        userId,
        status: EmailStatus.UNSUBSCRIBED
      },
    });

    const reasonCounts = new Map<string, number>();

    unsubscribeLogs.forEach(log => {
      if (log.metadata && (log.metadata as any).unsubscribeReason) {
        const reason = (log.metadata as any).unsubscribeReason;
        reasonCounts.set(reason, (reasonCounts.get(reason) || 0) + 1);
      } else {
        reasonCounts.set('No reason provided', (reasonCounts.get('No reason provided') || 0) + 1);
      }
    });

    const total = unsubscribeLogs.length;

    return Array.from(reasonCounts.entries())
      .map(([reason, count]) => ({
        reason,
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0,
      }))
      .sort((a, b) => b.count - a.count);
  }
}
