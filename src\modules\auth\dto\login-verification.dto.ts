import { IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SecurityMethod } from '../../users/entities/user.entity';

export class LoginVerificationDto {
  @ApiProperty({ 
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Temporary login session ID'
  })
  @IsUUID()
  sessionId: string;

  @ApiProperty({ 
    example: '123456',
    description: 'Verification code from email or authenticator app'
  })
  @IsString()
  verificationCode: string;
}

export class PartialLoginResponse {
  @ApiProperty({ 
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Temporary session ID for verification'
  })
  sessionId: string;

  @ApiProperty({ 
    enum: SecurityMethod,
    example: SecurityMethod.EMAIL_VERIFICATION,
    description: 'Required verification method'
  })
  requiredVerification: SecurityMethod;

  @ApiProperty({ 
    example: 'Verification code sent to your email',
    description: 'Message describing the verification step'
  })
  message: string;

  @ApiProperty({ 
    example: 300,
    description: 'Time in seconds until verification expires'
  })
  expiresIn: number;
}

export class SendVerificationCodeDto {
  @ApiProperty({ 
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Temporary login session ID'
  })
  @IsUUID()
  sessionId: string;
}
