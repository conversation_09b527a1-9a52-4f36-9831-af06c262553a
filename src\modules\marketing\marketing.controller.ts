import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { MarketingService } from './marketing.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Marketing')
@Controller('marketing')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MarketingController {
  constructor(private readonly marketingService: MarketingService) {}

  @Get('social-accounts')
  async getSocialAccounts(@CurrentUser() user: User) {
    return this.marketingService.getSocialAccounts(user.id);
  }

  @Post('social-accounts/facebook/connect')
  async connectFacebook(@CurrentUser() user: User, @Body() data: any) {
    return this.marketingService.connectFacebookAccount(user.id, data);
  }

  @Post('social-accounts/tiktok/connect')
  async connectTikTok(@CurrentUser() user: User, @Body() data: any) {
    return this.marketingService.connectTikTokAccount(user.id, data);
  }

  @Get('posts')
  async getPosts(@CurrentUser() user: User) {
    return this.marketingService.getUserPosts(user.id);
  }

  @Post('posts')
  async createPost(@CurrentUser() user: User, @Body() data: any) {
    return this.marketingService.createPost(user.id, data);
  }

  @Post('posts/:id/schedule')
  async schedulePost(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.marketingService.schedulePost(user.id, id, data);
  }

  @Get('comment-filters')
  async getCommentFilters(@CurrentUser() user: User) {
    return this.marketingService.getCommentFilters(user.id);
  }

  @Post('comment-filters')
  async createCommentFilter(@CurrentUser() user: User, @Body() data: any) {
    return this.marketingService.createCommentFilter(user.id, data);
  }
}
