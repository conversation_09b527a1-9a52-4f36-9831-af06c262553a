import { Injectable, Logger, ForbiddenException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Role } from '../entities/role.entity';
import { Permission, SystemModule, PermissionAction } from '../entities/permission.entity';
import { UserRole } from '../entities/user-role.entity';
import { RolePermission } from '../entities/role-permission.entity';
import { RoleHierarchy } from '../entities/role-hierarchy.entity';
import { CacheService, CACHE_PREFIXES, CACHE_TTL } from '../../cache';

/**
 * Interface cho cache quyền người dùng
 * Interface for user permission cache
 */
interface UserPermissionCache {
  userId: string;
  permissions: string[];
  roles: string[];
  roleLevel: number;
  isMasterAccount: boolean;
  cachedAt: Date;
  expiresAt: Date;
}

/**
 * Interface cho kết quả kiểm tra quyền
 * Interface for permission check result
 */
export interface PermissionCheckResult {
  allowed: boolean;
  reason: string;
  roleLevel?: number;
  conflictingRoles?: string[];
}

/**
 * RBAC Service - Dịch vụ quản lý hệ thống phân quyền
 * RBAC Service - Role-Based Access Control service
 */
@Injectable()
export class RBACService {
  private readonly logger = new Logger(RBACService.name);
  private readonly CACHE_TTL = CACHE_TTL.MEDIUM; // 15 minutes
  private readonly CACHE_PREFIX = CACHE_PREFIXES.RBAC;

  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    @InjectRepository(RolePermission)
    private readonly rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(RoleHierarchy)
    private readonly roleHierarchyRepository: Repository<RoleHierarchy>,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Kiểm tra quyền của người dùng - Check user permission
   */
  async checkPermission(
    userId: string,
    permissionCode: string,
    resource?: string,
    application?: string,
  ): Promise<PermissionCheckResult> {
    try {
      // Lấy permissions từ cache hoặc database
      const userPermissions = await this.getUserPermissions(userId);

      // Kiểm tra quyền MASTER_ACCOUNT
      if (userPermissions.isMasterAccount) {
        return {
          allowed: true,
          reason: 'Master account has all permissions',
          roleLevel: 0,
        };
      }

      // Kiểm tra quyền cụ thể
      const hasPermission = this.checkSpecificPermission(
        userPermissions.permissions,
        permissionCode,
        resource,
        application,
      );

      if (hasPermission) {
        return {
          allowed: true,
          reason: `User has required permission: ${permissionCode}`,
          roleLevel: userPermissions.roleLevel,
        };
      }

      // Kiểm tra quyền kế thừa
      const inheritedPermission = await this.checkInheritedPermission(
        userId,
        permissionCode,
        resource,
      );

      if (inheritedPermission.allowed) {
        return inheritedPermission;
      }

      return {
        allowed: false,
        reason: `User lacks required permission: ${permissionCode}`,
        roleLevel: userPermissions.roleLevel,
      };
    } catch (error) {
      this.logger.error(`Error checking permission for user ${userId}:`, error);
      return {
        allowed: false,
        reason: 'Error occurred while checking permission',
      };
    }
  }

  /**
   * Lấy tất cả quyền của người dùng - Get all user permissions
   */
  async getUserPermissions(userId: string): Promise<UserPermissionCache> {
    // Kiểm tra cache trước
    const cacheKey = `${this.CACHE_PREFIX}:${userId}`;
    const cached = await this.cacheService.get<UserPermissionCache>(cacheKey, this.CACHE_PREFIX);

    if (cached && new Date() < cached.expiresAt) {
      return cached;
    }

    // Lấy từ database
    const userPermissions = await this.calculateUserPermissions(userId);

    // Lưu vào cache
    const cacheData: UserPermissionCache = {
      ...userPermissions,
      cachedAt: new Date(),
      expiresAt: new Date(Date.now() + this.CACHE_TTL * 1000),
    };

    await this.cacheService.set(cacheKey, cacheData, {
      ttl: this.CACHE_TTL,
      prefix: this.CACHE_PREFIX
    });

    return cacheData;
  }

  /**
   * Tính toán quyền của người dùng từ database - Calculate user permissions from database
   */
  private async calculateUserPermissions(userId: string): Promise<Omit<UserPermissionCache, 'cachedAt' | 'expiresAt'>> {
    // Lấy tất cả roles trực tiếp của user
    const userRoles = await this.userRoleRepository.find({
      where: { userId, isActive: true },
      relations: ['role'],
    });

    if (userRoles.length === 0) {
      return {
        userId,
        permissions: [],
        roles: [],
        roleLevel: 999,
        isMasterAccount: false,
      };
    }

    const directRoles = userRoles.map(ur => ur.role);
    const isMasterAccount = directRoles.some(role => role.name === 'MASTER_ACCOUNT');

    // Tính toán inherited roles
    const inheritedRoles = await this.calculateInheritedRoles(directRoles);
    const allRoles = [...directRoles, ...inheritedRoles];

    // Lấy tất cả permissions
    const allPermissions = new Set<string>();
    const roleIds = allRoles.map(role => role.id);

    const rolePermissions = await this.rolePermissionRepository.find({
      where: { roleId: In(roleIds) },
      relations: ['permission'],
    });

    rolePermissions.forEach(rp => {
      if (rp.permission.isActive) {
        allPermissions.add(rp.permission.code);
      }
    });

    // Tính role level thấp nhất (quyền cao nhất)
    const roleLevel = Math.min(...allRoles.map(role => role.level));

    return {
      userId,
      permissions: Array.from(allPermissions),
      roles: allRoles.map(role => role.name),
      roleLevel,
      isMasterAccount,
    };
  }

  /**
   * Tính toán các vai trò kế thừa - Calculate inherited roles
   */
  private async calculateInheritedRoles(directRoles: Role[]): Promise<Role[]> {
    const inheritedRoles: Role[] = [];
    const processedRoleIds = new Set<string>();

    for (const role of directRoles) {
      await this.getInheritedRolesRecursive(role, inheritedRoles, processedRoleIds);
    }

    return inheritedRoles;
  }

  /**
   * Đệ quy lấy vai trò kế thừa - Recursively get inherited roles
   */
  private async getInheritedRolesRecursive(
    role: Role,
    inheritedRoles: Role[],
    processedRoleIds: Set<string>,
  ): Promise<void> {
    if (processedRoleIds.has(role.id)) {
      return; // Tránh vòng lặp vô hạn
    }

    processedRoleIds.add(role.id);

    // Lấy các role hierarchy mà role này là parent
    const hierarchies = await this.roleHierarchyRepository.find({
      where: { parentRoleId: role.id },
      relations: ['childRole'],
    });

    for (const hierarchy of hierarchies) {
      if (hierarchy.hasInheritance && !inheritedRoles.some(r => r.id === hierarchy.childRole.id)) {
        inheritedRoles.push(hierarchy.childRole);
        
        // Đệ quy cho child roles
        await this.getInheritedRolesRecursive(hierarchy.childRole, inheritedRoles, processedRoleIds);
      }
    }
  }

  /**
   * Kiểm tra quyền cụ thể - Check specific permission
   */
  private checkSpecificPermission(
    userPermissions: string[],
    requiredPermission: string,
    resource?: string,
    application?: string,
  ): boolean {
    // Kiểm tra quyền chính xác
    if (userPermissions.includes(requiredPermission)) {
      return true;
    }

    // Kiểm tra quyền application-specific
    if (application) {
      const appSpecificPermission = `${application.toUpperCase()}_${requiredPermission}`;
      if (userPermissions.includes(appSpecificPermission)) {
        return true;
      }
    }

    // Kiểm tra quyền MANAGE (bao gồm tất cả quyền trong module)
    const [module, action] = requiredPermission.split('_');
    const managePermission = `${module}_MANAGE`;

    if (userPermissions.includes(managePermission)) {
      return true;
    }

    // Kiểm tra quyền application-specific MANAGE
    if (application) {
      const appManagePermission = `${application.toUpperCase()}_${module}_MANAGE`;
      if (userPermissions.includes(appManagePermission)) {
        return true;
      }
    }

    // Kiểm tra quyền resource-specific
    if (resource) {
      const resourcePermission = `${requiredPermission}_${resource.toUpperCase()}`;
      if (userPermissions.includes(resourcePermission)) {
        return true;
      }

      // Kiểm tra quyền application + resource specific
      if (application) {
        const appResourcePermission = `${application.toUpperCase()}_${requiredPermission}_${resource.toUpperCase()}`;
        if (userPermissions.includes(appResourcePermission)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Kiểm tra quyền kế thừa - Check inherited permission
   */
  private async checkInheritedPermission(
    userId: string,
    permissionCode: string,
    resource?: string,
  ): Promise<PermissionCheckResult> {
    // Logic kiểm tra quyền kế thừa phức tạp hơn
    // Có thể implement sau nếu cần
    return {
      allowed: false,
      reason: 'No inherited permission found',
    };
  }

  /**
   * Gán vai trò cho người dùng - Assign role to user
   */
  async assignRoleToUser(
    userId: string,
    roleId: string,
    assignedBy: string,
    expiresAt?: Date,
  ): Promise<UserRole> {
    // Kiểm tra quyền của người gán
    const canAssign = await this.checkPermission(assignedBy, 'ROLE_MANAGEMENT_ASSIGN');
    if (!canAssign.allowed) {
      throw new ForbiddenException('You do not have permission to assign roles');
    }

    // Kiểm tra role tồn tại
    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    if (!role) {
      throw new NotFoundException('Role not found');
    }

    // Kiểm tra level role (không thể gán role cao hơn mình)
    const assignerPermissions = await this.getUserPermissions(assignedBy);
    if (role.level < assignerPermissions.roleLevel && !assignerPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot assign role higher than your own level');
    }

    // Kiểm tra user role đã tồn tại
    const existingUserRole = await this.userRoleRepository.findOne({
      where: { userId, roleId },
    });

    if (existingUserRole) {
      if (existingUserRole.isActive) {
        throw new ForbiddenException('User already has this role');
      } else {
        // Kích hoạt lại role
        existingUserRole.isActive = true;
        existingUserRole.assignedBy = assignedBy;
        existingUserRole.assignedAt = new Date();
        existingUserRole.expiresAt = expiresAt;
        
        const savedUserRole = await this.userRoleRepository.save(existingUserRole);
        await this.invalidateUserCache(userId);
        return savedUserRole;
      }
    }

    // Tạo user role mới
    const userRole = this.userRoleRepository.create({
      userId,
      roleId,
      assignedBy,
      expiresAt,
    });

    const savedUserRole = await this.userRoleRepository.save(userRole);
    await this.invalidateUserCache(userId);

    this.logger.log(`Role ${role.name} assigned to user ${userId} by ${assignedBy}`);
    return savedUserRole;
  }

  /**
   * Thu hồi vai trò từ người dùng - Revoke role from user
   */
  async revokeRoleFromUser(userId: string, roleId: string, revokedBy: string): Promise<void> {
    // Kiểm tra quyền
    const canRevoke = await this.checkPermission(revokedBy, 'ROLE_MANAGEMENT_ASSIGN');
    if (!canRevoke.allowed) {
      throw new ForbiddenException('You do not have permission to revoke roles');
    }

    const userRole = await this.userRoleRepository.findOne({
      where: { userId, roleId, isActive: true },
      relations: ['role'],
    });

    if (!userRole) {
      throw new NotFoundException('User role assignment not found');
    }

    // Kiểm tra không thể thu hồi role MASTER_ACCOUNT
    if (userRole.role.name === 'MASTER_ACCOUNT') {
      throw new ForbiddenException('Cannot revoke MASTER_ACCOUNT role');
    }

    // Kiểm tra level (không thể thu hồi role cao hơn mình)
    const revokerPermissions = await this.getUserPermissions(revokedBy);
    if (userRole.role.level < revokerPermissions.roleLevel && !revokerPermissions.isMasterAccount) {
      throw new ForbiddenException('Cannot revoke role higher than your own level');
    }

    userRole.isActive = false;
    await this.userRoleRepository.save(userRole);
    await this.invalidateUserCache(userId);

    this.logger.log(`Role ${userRole.role.name} revoked from user ${userId} by ${revokedBy}`);
  }

  /**
   * Xóa cache quyền người dùng - Invalidate user permission cache
   */
  async invalidateUserCache(userId: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}:${userId}`;
    await this.cacheService.del(cacheKey, this.CACHE_PREFIX);
  }

  /**
   * Xóa tất cả cache quyền - Invalidate all permission caches
   */
  async invalidateAllCaches(): Promise<void> {
    await this.cacheService.delPattern(`${this.CACHE_PREFIX}:*`);
  }

  /**
   * Lấy danh sách vai trò của người dùng - Get user roles
   */
  async getUserRoles(userId: string): Promise<Role[]> {
    const userRoles = await this.userRoleRepository.find({
      where: { userId, isActive: true },
      relations: ['role'],
    });

    return userRoles.map(ur => ur.role);
  }

  /**
   * Kiểm tra người dùng có vai trò cụ thể không - Check if user has specific role
   */
  async hasRole(userId: string, roleName: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.roles.includes(roleName);
  }

  /**
   * Lấy level cao nhất của người dùng - Get user's highest role level
   */
  async getUserRoleLevel(userId: string): Promise<number> {
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.roleLevel;
  }

  /**
   * Kiểm tra xung đột quyền - Check permission conflicts
   */
  async checkPermissionConflicts(userId: string): Promise<any[]> {
    const userRoles = await this.getUserRoles(userId);
    const conflicts = [];

    // Logic kiểm tra xung đột sẽ được implement sau
    // Ví dụ: nếu user có cả ADMIN và GUEST role

    return conflicts;
  }
}
