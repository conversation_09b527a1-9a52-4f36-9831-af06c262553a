import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ServiceType } from './service-type.entity';

export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

@Entity('appointments')
@Index(['userId', 'status'])
@Index(['scheduledAt', 'status'])
export class Appointment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  serviceTypeId: string;

  @ManyToOne(() => ServiceType)
  @JoinColumn({ name: 'serviceTypeId' })
  serviceType: ServiceType;

  @Column()
  customerName: string;

  @Column()
  customerEmail: string;

  @Column({ nullable: true })
  customerPhone: string;

  @Column({
    type: 'enum',
    enum: AppointmentStatus,
    default: AppointmentStatus.SCHEDULED,
  })
  status: AppointmentStatus;

  @Column()
  scheduledAt: Date;

  @Column()
  duration: number; // in minutes

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'jsonb', nullable: true })
  customerInfo: {
    company?: string;
    address?: string;
    requirements?: string;
    previousIssues?: string;
  };

  @Column({ nullable: true })
  confirmedAt: Date;

  @Column({ nullable: true })
  startedAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ nullable: true })
  cancelledAt: Date;

  @Column({ type: 'text', nullable: true })
  cancellationReason: string;

  @Column({ type: 'jsonb', nullable: true })
  reminderSettings: {
    emailReminder?: boolean;
    smsReminder?: boolean;
    reminderTime?: number; // hours before appointment
  };

  @Column({ default: 0 })
  remindersSent: number;

  @Column({ nullable: true })
  lastReminderSentAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isScheduled(): boolean {
    return this.status === AppointmentStatus.SCHEDULED;
  }

  get isConfirmed(): boolean {
    return this.status === AppointmentStatus.CONFIRMED;
  }

  get isCompleted(): boolean {
    return this.status === AppointmentStatus.COMPLETED;
  }

  get isCancelled(): boolean {
    return this.status === AppointmentStatus.CANCELLED;
  }

  get canBeModified(): boolean {
    return [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED].includes(this.status);
  }

  get endTime(): Date {
    return new Date(this.scheduledAt.getTime() + this.duration * 60000);
  }
}
