import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Workflow } from './workflow.entity';

export enum ExecutionStatus {
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused',
}

@Entity('workflow_executions')
@Index(['workflowId', 'status'])
@Index(['status', 'startedAt'])
export class WorkflowExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  workflowId: string;

  @ManyToOne(() => Workflow, workflow => workflow.executions)
  @JoinColumn({ name: 'workflowId' })
  workflow: Workflow;

  @Column({
    type: 'enum',
    enum: ExecutionStatus,
    default: ExecutionStatus.RUNNING,
  })
  status: ExecutionStatus;

  @Column({ type: 'jsonb', nullable: true })
  input: any; // Input data that triggered the workflow

  @Column({ type: 'jsonb', nullable: true })
  output: any; // Final output of the workflow

  @Column({ type: 'jsonb', nullable: true })
  variables: Record<string, any>; // Runtime variables

  @Column({ type: 'jsonb', nullable: true })
  nodeExecutions: Array<{
    nodeId: string;
    nodeName: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
    startedAt?: string;
    completedAt?: string;
    input?: any;
    output?: any;
    error?: string;
    duration?: number; // milliseconds
  }>;

  @Column({ nullable: true })
  currentNodeId: string; // Currently executing node

  @Column()
  startedAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ nullable: true })
  duration: number; // milliseconds

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'jsonb', nullable: true })
  errorDetails: {
    nodeId?: string;
    nodeName?: string;
    errorType?: string;
    stackTrace?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  logs: Array<{
    timestamp: string;
    level: 'info' | 'warn' | 'error' | 'debug';
    message: string;
    nodeId?: string;
    data?: any;
  }>;

  @Column({ nullable: true })
  triggeredBy: string; // User ID or system trigger

  @Column({ type: 'jsonb', nullable: true })
  triggerData: {
    type: string;
    source: string;
    metadata?: any;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isRunning(): boolean {
    return this.status === ExecutionStatus.RUNNING;
  }

  get isCompleted(): boolean {
    return this.status === ExecutionStatus.COMPLETED;
  }

  get hasFailed(): boolean {
    return this.status === ExecutionStatus.FAILED;
  }

  get progress(): number {
    if (!this.nodeExecutions || this.nodeExecutions.length === 0) return 0;
    
    const completedNodes = this.nodeExecutions.filter(
      node => ['completed', 'skipped'].includes(node.status)
    ).length;
    
    return Math.round((completedNodes / this.nodeExecutions.length) * 100);
  }

  get executionTime(): number {
    if (this.duration) return this.duration;
    if (this.completedAt) {
      return this.completedAt.getTime() - this.startedAt.getTime();
    }
    return Date.now() - this.startedAt.getTime();
  }

  get formattedDuration(): string {
    const ms = this.executionTime;
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}
