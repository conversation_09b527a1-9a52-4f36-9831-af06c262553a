import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOrganizationMembersTable1700000007 implements MigrationInterface {
  name = 'CreateOrganizationMembersTable1700000007';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'organization_members',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'organizationId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'userId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'email',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'roleId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'active', 'inactive', 'suspended', 'left'],
            default: "'pending'",
          },
          {
            name: 'invitationStatus',
            type: 'enum',
            enum: ['pending', 'accepted', 'declined', 'expired', 'cancelled'],
            default: "'pending'",
          },
          {
            name: 'invitationToken',
            type: 'varchar',
            length: '255',
            isNullable: true,
            isUnique: true,
          },
          {
            name: 'invitedBy',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'invitedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'acceptedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'expiresAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'leftAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'invitationMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'permissions',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'restrictions',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'lastActiveAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'loginCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_organization_members_organizationId_userId" ON "organization_members" ("organizationId", "userId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_organization_members_organizationId_status" ON "organization_members" ("organizationId", "status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_organization_members_userId_status" ON "organization_members" ("userId", "status")
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_organization_members_invitationToken" ON "organization_members" ("invitationToken")
    `);

    // Create foreign keys
    await queryRunner.query(`
      ALTER TABLE "organization_members" ADD CONSTRAINT "FK_organization_members_organizationId"
      FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "organization_members" ADD CONSTRAINT "FK_organization_members_userId"
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "organization_members" ADD CONSTRAINT "FK_organization_members_roleId"
      FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE RESTRICT
    `);

    await queryRunner.query(`
      ALTER TABLE "organization_members" ADD CONSTRAINT "FK_organization_members_invitedBy"
      FOREIGN KEY ("invitedBy") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    // Add unique constraint for organizationId + userId
    await queryRunner.query(`
      CREATE UNIQUE INDEX "UQ_organization_members_organizationId_userId" ON "organization_members" ("organizationId", "userId")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('organization_members');
  }
}
