# Sơ Đồ Luồng <PERSON> (Authentication Flow Diagrams)

Tài liệu này chứa các sơ đồ minh họa chi tiết về luồng xác thực trong hệ thống.

## 1. Tổng <PERSON>uan Hệ Thống <PERSON>c <PERSON>

```mermaid
graph TB
    A[Client Application] --> B[Auth API]
    B --> C[Database]
    B --> D[Email Service]
    B --> E[Verification Service]
    B --> F[Device Service]
    
    C --> G[(Users Table)]
    C --> H[(User Devices Table)]
    
    E --> I[TOTP Generator]
    E --> J[Fixed Code Validator]
    E --> K[Email Code Generator]
    
    D --> L[Email Provider]
    
    subgraph "Security Methods"
        M[DISABLED]
        N[EMAIL_VERIFICATION]
        O[TWO_FACTOR_AUTH]
        P[FIXED_CODE]
    end
```

## 2. Luồng <PERSON> (Registration Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Device as Device Service

    C->>API: POST /auth/register
    Note over C,API: { email, username, password, accountType }
    
    API->>DB: Kiểm tra email/username tồn tại
    alt Email/Username đã tồn tại
        API->>C: 409 Conflict
    else Thành công
        API->>DB: Tạo user mới
        Note over DB: INSERT INTO users
        
        API->>Device: Tạo device session
        Note over Device: Lưu thông tin device
        
        API->>API: Tạo access & refresh tokens
        Note over API: JWT với 15m & 7d expiry
        
        API->>DB: Lưu refresh token
        Note over DB: UPDATE users SET refreshToken
        
        API->>Email: Gửi email chào mừng (optional)
        
        API->>C: 201 Created
        Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
    end
```

## 3. Luồng Đăng Nhập Cơ Bản (Basic Login Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service

    C->>API: POST /auth/login
    Note over C,API: { emailOrUsername, password }
    
    API->>DB: Tìm user
    Note over DB: SELECT * FROM users WHERE email/username
    
    alt User không tồn tại hoặc inactive
        API->>C: 401 Unauthorized
    else Password sai
        API->>C: 401 Unauthorized
    else Security Method = DISABLED
        API->>API: Tạo tokens ngay lập tức
        API->>DB: Lưu refresh token & device session
        API->>C: 200 OK (Complete Login)
        Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
    else Security Method Enabled
        API->>VS: Tạo verification session
        Note over VS: Lưu session trong memory/Redis
        
        alt EMAIL_VERIFICATION
            API->>API: Tạo verification code
            API->>DB: Lưu verification code
            API->>Email: Gửi email với code
        end
        
        API->>C: 200 OK (Partial Login)
        Note over API,C: { requiresVerification: true, sessionId, message }
    end
```

## 4. Luồng Xác Minh Đăng Nhập (Login Verification Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    C->>API: POST /auth/verify-login
    Note over C,API: { sessionId, verificationCode }
    
    API->>VS: Kiểm tra verification session
    alt Session không tồn tại hoặc expired
        API->>C: 401 Unauthorized
    else Session hợp lệ
        API->>DB: Lấy thông tin user
        API->>API: Verify code theo security method
        
        alt EMAIL_VERIFICATION
            API->>DB: Kiểm tra verification code
            Note over DB: user.isVerificationCodeValid()
        else TWO_FACTOR_AUTH
            API->>VS: Verify TOTP code
            Note over VS: speakeasy.verify()
        else FIXED_CODE
            API->>VS: Verify fixed code
            Note over VS: bcrypt.compare()
        end
        
        alt Code không hợp lệ
            API->>C: 401 Unauthorized
        else Code hợp lệ
            API->>API: Tạo access & refresh tokens
            API->>DB: Lưu refresh token & device session
            
            alt EMAIL_VERIFICATION
                API->>DB: Clear verification code
            end
            
            API->>VS: Remove verification session
            API->>C: 200 OK (Complete Login)
            Note over API,C: { user, accessToken, refreshToken, expiresIn, tokenType }
        end
    end
```

## 5. Luồng Refresh Token

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    C->>API: POST /auth/refresh
    Note over C,API: { refreshToken }
    
    API->>API: Verify refresh token JWT
    alt Token không hợp lệ hoặc expired
        API->>C: 401 Unauthorized
    else Token hợp lệ
        API->>DB: Tìm user bằng refresh token
        Note over DB: SELECT * FROM users WHERE refreshToken = ?
        
        alt User không tồn tại
            API->>C: 401 Unauthorized
        else Token expired trong DB
            API->>C: 401 Unauthorized
        else Thành công
            API->>API: Tạo tokens mới
            API->>DB: Cập nhật refresh token mới
            Note over DB: UPDATE users SET refreshToken = new_token
            
            API->>C: 200 OK
            Note over API,C: { accessToken, refreshToken, expiresIn, tokenType }
        end
    end
```

## 6. Luồng Enable Security Methods

### 6.1 Enable Email Verification

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service

    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "email_verification" }
    
    API->>DB: Update user security method
    Note over DB: UPDATE users SET securityMethod = 'email_verification'
    
    API->>Email: Gửi notification email
    
    API->>C: 200 OK
    Note over API,C: { securityMethod: "email_verification", twoFactorEnabled: false, hasFixedCode: false }
```

### 6.2 Enable Two-Factor Authentication

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    Note over C,API: Bước 1: Tạo 2FA Secret
    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "two_factor_auth" }
    
    API->>VS: Tạo TOTP secret
    API->>VS: Tạo QR code
    API->>DB: Lưu secret tạm thời
    Note over DB: UPDATE users SET twoFactorSecret = secret
    
    API->>C: 200 OK
    Note over API,C: { qrCodeUrl, manualEntryKey, securityMethod: "disabled" }
    
    Note over C: User scan QR code vào authenticator app
    
    Note over C,API: Bước 2: Verify và Enable
    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "two_factor_auth", verificationCode: "123456" }
    
    API->>VS: Verify TOTP code
    alt Code không hợp lệ
        API->>C: 400 Bad Request
    else Code hợp lệ
        API->>DB: Enable 2FA
        Note over DB: UPDATE users SET securityMethod = 'two_factor_auth', twoFactorEnabled = true
        
        API->>Email: Gửi notification email
        
        API->>C: 200 OK
        Note over API,C: { securityMethod: "two_factor_auth", twoFactorEnabled: true }
    end
```

### 6.3 Enable Fixed Code

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service
    participant Email as Email Service

    C->>API: POST /auth/security-method
    Note over C,API: { securityMethod: "fixed_code", fixedCode: "789123" }
    
    API->>VS: Validate fixed code format
    Note over VS: Kiểm tra độ dài, pattern, weak codes
    
    alt Code không hợp lệ
        API->>C: 400 Bad Request
        Note over API,C: "Fixed code is too weak"
    else Code hợp lệ
        API->>VS: Hash fixed code
        Note over VS: SHA-256 hashing
        
        API->>DB: Update user
        Note over DB: UPDATE users SET securityMethod = 'fixed_code', fixedCode = hashed_code
        
        API->>Email: Gửi notification email
        
        API->>C: 200 OK
        Note over API,C: { securityMethod: "fixed_code", hasFixedCode: true }
    end
```

## 7. Luồng Token Revocation (Logout)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    Note over C,API: Logout từ device hiện tại
    C->>API: POST /auth/revoke
    Note over C,API: { refreshToken }
    
    API->>API: Verify refresh token
    API->>DB: Tìm user bằng refresh token
    API->>DB: Clear refresh token
    Note over DB: UPDATE users SET refreshToken = null
    
    API->>C: 200 OK
    Note over API,C: { message: "Token revoked successfully" }
    
    Note over C,API: Logout từ tất cả devices
    C->>API: POST /auth/revoke-all
    Note over C,API: Authorization: Bearer access_token
    
    API->>DB: Clear tất cả refresh tokens của user
    API->>DB: Deactivate tất cả device sessions
    Note over DB: UPDATE user_devices SET isActive = false
    
    API->>C: 200 OK
    Note over API,C: { message: "All tokens revoked successfully" }
```

## 8. Luồng Error Handling

```mermaid
flowchart TD
    A[API Request] --> B{Validate Request}
    B -->|Invalid| C[400 Bad Request]
    B -->|Valid| D{Authenticate User}
    
    D -->|Invalid Credentials| E[401 Unauthorized]
    D -->|Account Inactive| F[403 Forbidden]
    D -->|Too Many Attempts| G[429 Rate Limited]
    D -->|Valid| H{Check Security Method}
    
    H -->|DISABLED| I[Complete Login]
    H -->|Enabled| J{Verification Required}
    
    J -->|Code Invalid| K[401 Unauthorized]
    J -->|Session Expired| L[401 Unauthorized]
    J -->|Code Valid| I
    
    I --> M[200 Success]
    
    C --> N[Error Response]
    E --> N
    F --> N
    G --> N
    K --> N
    L --> N
    M --> O[Success Response]
```

## 9. Database State Transitions

```mermaid
stateDiagram-v2
    [*] --> UserCreated: Register
    UserCreated --> SecurityDisabled: Default State
    
    SecurityDisabled --> EmailVerification: Enable Email
    SecurityDisabled --> TwoFactorSetup: Enable 2FA (Step 1)
    SecurityDisabled --> FixedCode: Enable Fixed Code
    
    TwoFactorSetup --> TwoFactorAuth: Verify Code (Step 2)
    TwoFactorSetup --> SecurityDisabled: Cancel Setup
    
    EmailVerification --> SecurityDisabled: Disable
    EmailVerification --> TwoFactorSetup: Switch to 2FA
    EmailVerification --> FixedCode: Switch to Fixed Code
    
    TwoFactorAuth --> SecurityDisabled: Disable
    TwoFactorAuth --> EmailVerification: Switch to Email
    TwoFactorAuth --> FixedCode: Switch to Fixed Code
    
    FixedCode --> SecurityDisabled: Disable
    FixedCode --> EmailVerification: Switch to Email
    FixedCode --> TwoFactorSetup: Switch to 2FA
    
    FixedCode --> FixedCode: Change Fixed Code
```

---

**Các sơ đồ này minh họa chi tiết luồng xác thực trong hệ thống. Để hiểu rõ hơn về implementation, tham khảo [Authentication Flow Guide](./AUTHENTICATION_FLOW_GUIDE.md).**
