import { Controller, Get, Post, Delete, Param, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CacheService } from '../services/cache.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

/**
 * Cache Controller - Endpoints để quản lý và kiểm tra cache
 * Cache Controller - Endpoints for cache management and testing
 */
@ApiTags('Cache Management')
@Controller('cache')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CacheController {
  constructor(private readonly cacheService: CacheService) {}

  /**
   * Kiểm tra sức khỏe cache
   * Check cache health
   */
  @Get('health')
  @ApiOperation({ summary: 'Check cache health status' })
  @ApiResponse({ status: 200, description: 'Cache health status' })
  async healthCheck() {
    const isHealthy = await this.cacheService.healthCheck();
    const stats = this.cacheService.getStats();

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      stats,
    };
  }

  /**
   * Lấy thống kê cache
   * Get cache statistics
   */
  @Get('stats')
  @ApiOperation({ summary: 'Get cache statistics' })
  @ApiResponse({ status: 200, description: 'Cache statistics' })
  getStats() {
    return this.cacheService.getStats();
  }

  /**
   * Reset thống kê cache
   * Reset cache statistics
   */
  @Post('stats/reset')
  @ApiOperation({ summary: 'Reset cache statistics' })
  @ApiResponse({ status: 200, description: 'Statistics reset successfully' })
  resetStats() {
    this.cacheService.resetStats();
    return { message: 'Cache statistics reset successfully' };
  }

  /**
   * Test cache operations
   * Kiểm tra hoạt động cache
   */
  @Post('test')
  @ApiOperation({ summary: 'Test cache operations' })
  @ApiResponse({ status: 200, description: 'Cache test results' })
  async testCache(@Body() data: { key: string; value: any; ttl?: number }) {
    const { key, value, ttl } = data;
    const testKey = `test:${key}`;

    try {
      // Test SET operation
      await this.cacheService.set(testKey, value, { ttl: ttl || 60 });

      // Test GET operation
      const retrieved = await this.cacheService.get(testKey);

      // Test EXISTS operation
      const exists = await this.cacheService.exists(testKey);

      return {
        success: true,
        operations: {
          set: true,
          get: retrieved === value,
          exists,
        },
        data: {
          original: value,
          retrieved,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Lấy giá trị từ cache
   * Get value from cache
   */
  @Get('get/:key')
  @ApiOperation({ summary: 'Get value from cache by key' })
  @ApiResponse({ status: 200, description: 'Cache value or null if not found' })
  async getValue(@Param('key') key: string) {
    const value = await this.cacheService.get(key);
    return {
      key,
      value,
      found: value !== undefined,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Đặt giá trị vào cache
   * Set value in cache
   */
  @Post('set')
  @ApiOperation({ summary: 'Set value in cache' })
  @ApiResponse({ status: 200, description: 'Value set successfully' })
  async setValue(@Body() data: { key: string; value: any; ttl?: number; prefix?: string }) {
    const { key, value, ttl, prefix } = data;
    
    await this.cacheService.set(key, value, { ttl, prefix });
    
    return {
      success: true,
      key,
      ttl: ttl || 900,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Xóa giá trị khỏi cache
   * Delete value from cache
   */
  @Delete('delete/:key')
  @ApiOperation({ summary: 'Delete value from cache' })
  @ApiResponse({ status: 200, description: 'Value deleted successfully' })
  async deleteValue(@Param('key') key: string) {
    await this.cacheService.del(key);
    
    return {
      success: true,
      key,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Kiểm tra key có tồn tại không
   * Check if key exists
   */
  @Get('exists/:key')
  @ApiOperation({ summary: 'Check if key exists in cache' })
  @ApiResponse({ status: 200, description: 'Key existence status' })
  async checkExists(@Param('key') key: string) {
    const exists = await this.cacheService.exists(key);
    
    return {
      key,
      exists,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Xóa tất cả cache (chỉ dành cho development)
   * Clear all cache (development only)
   */
  @Delete('clear')
  @ApiOperation({ summary: 'Clear all cache (development only)' })
  @ApiResponse({ status: 200, description: 'All cache cleared' })
  async clearAll() {
    await this.cacheService.clear();
    
    return {
      success: true,
      message: 'All cache cleared',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Xóa cache theo pattern
   * Delete cache by pattern
   */
  @Delete('pattern/:pattern')
  @ApiOperation({ summary: 'Delete cache keys by pattern' })
  @ApiResponse({ status: 200, description: 'Pattern-based deletion completed' })
  async deleteByPattern(@Param('pattern') pattern: string) {
    await this.cacheService.delPattern(pattern);
    
    return {
      success: true,
      pattern,
      message: 'Pattern-based deletion completed',
      timestamp: new Date().toISOString(),
    };
  }
}
