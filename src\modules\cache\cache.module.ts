import { Module, Global } from '@nestjs/common';
import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';

// Services
import { CacheService } from './services/cache.service';

// Controllers
import { CacheController } from './controllers/cache.controller';

/**
 * Cache Module - Module quản lý hệ thống cache với Redis
 * Cache Module - Cache system module with Redis integration
 */
@Global()
@Module({
  imports: [
    // NestJS Cache Module with Redis store
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const redisConfig = {
          store: redisStore,
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD') || undefined,
          db: configService.get('REDIS_DB', 0),
          ttl: configService.get('SSO_CACHE_TTL', 900), // 15 minutes default
          max: 1000, // Maximum number of items in cache
          // Connection options
          connectTimeout: configService.get('REDIS_CONNECT_TIMEOUT', 10000),
          commandTimeout: configService.get('REDIS_COMMAND_TIMEOUT', 5000),
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: null,
        };

        // Log cache configuration in development
        if (configService.get('NODE_ENV') === 'development') {
          console.log('Cache Module Configuration:', {
            host: redisConfig.host,
            port: redisConfig.port,
            db: redisConfig.db,
            ttl: redisConfig.ttl,
          });
        }

        return redisConfig;
      },
      inject: [ConfigService],
      isGlobal: true,
    }),
  ],
  
  controllers: [
    CacheController,
  ],

  providers: [
    CacheService,
  ],

  exports: [
    // Export NestJS Cache Module for direct CACHE_MANAGER injection
    NestCacheModule,
    // Export our custom cache service
    CacheService,
  ],
})
export class CacheModule {
  /**
   * Cấu hình module cho root application
   * Configure module for root application
   */
  static forRoot() {
    return {
      module: CacheModule,
      global: true,
    };
  }

  /**
   * Cấu hình module cho feature modules
   * Configure module for feature modules
   */
  static forFeature() {
    return {
      module: CacheModule,
    };
  }
}
