import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateRolesTable1700000004 implements MigrationInterface {
  name = 'CreateRolesTable1700000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'roles',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['system', 'custom'],
            default: "'custom'",
          },
          {
            name: 'organizationId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'isDefault',
            type: 'boolean',
            default: false,
          },
          {
            name: 'priority',
            type: 'integer',
            default: 0,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_roles_name_organizationId" ON "roles" ("name", "organizationId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_roles_type" ON "roles" ("type")
    `);

    // Insert system roles
    await this.insertSystemRoles(queryRunner);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('roles');
  }

  private async insertSystemRoles(queryRunner: QueryRunner): Promise<void> {
    const roles = [
      {
        name: 'owner',
        description: 'Full access to organization',
        priority: 100,
        metadata: { canBeDeleted: false, canBeModified: false, color: '#dc2626' }
      },
      {
        name: 'admin',
        description: 'Administrative access',
        priority: 90,
        metadata: { canBeDeleted: false, canBeModified: false, color: '#ea580c' }
      },
      {
        name: 'manager',
        description: 'Management access',
        priority: 70,
        metadata: { canBeDeleted: false, canBeModified: false, color: '#ca8a04' }
      },
      {
        name: 'member',
        description: 'Standard member access',
        priority: 50,
        isDefault: true,
        metadata: { canBeDeleted: false, canBeModified: false, color: '#16a34a' }
      },
      {
        name: 'viewer',
        description: 'Read-only access',
        priority: 10,
        metadata: { canBeDeleted: false, canBeModified: false, color: '#6b7280' }
      },
    ];

    for (const role of roles) {
      await queryRunner.query(`
        INSERT INTO roles (name, description, type, "organizationId", "isActive", "isDefault", priority, metadata)
        VALUES ($1, $2, 'system', NULL, true, $3, $4, $5)
      `, [
        role.name,
        role.description,
        role.isDefault || false,
        role.priority,
        JSON.stringify(role.metadata),
      ]);
    }
  }
}
