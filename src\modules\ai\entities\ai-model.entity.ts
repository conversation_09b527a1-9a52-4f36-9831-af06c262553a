import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum ModelType {
  CONTENT_OPTIMIZATION = 'content_optimization',
  CV_SCORING = 'cv_scoring',
  CHATBOT = 'chatbot',
  COMMENT_ANALYSIS = 'comment_analysis',
  EMAIL_ANALYSIS = 'email_analysis',
  SENTIMENT_ANALYSIS = 'sentiment_analysis',
}

export enum AIProvider {
  OPENAI = 'openai',
  GROK = 'grok',
  GEMINI = 'gemini',
  ANTHROPIC = 'anthropic',
  OLLAMA = 'ollama',
  CUSTOM = 'custom',
}

export enum AIModelName {
  // OpenAI Models
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo',
  GPT_4O = 'gpt-4o',

  // Grok Models
  GROK_1 = 'grok-1',
  GROK_1_5 = 'grok-1.5',
  GROK_2 = 'grok-2',

  // Gemini Models
  GEMINI_PRO = 'gemini-pro',
  GEMINI_PRO_VISION = 'gemini-pro-vision',
  GEMINI_ULTRA = 'gemini-ultra',
  GEMINI_1_5_PRO = 'gemini-1.5-pro',

  // Anthropic Models
  CLAUDE_3_HAIKU = 'claude-3-haiku',
  CLAUDE_3_SONNET = 'claude-3-sonnet',
  CLAUDE_3_OPUS = 'claude-3-opus',

  // OLLAMA Models
  LLAMA_2_7B = 'llama2:7b',
  LLAMA_2_13B = 'llama2:13b',
  LLAMA_2_70B = 'llama2:70b',
  CODE_LLAMA_7B = 'codellama:7b',
  CODE_LLAMA_13B = 'codellama:13b',
  CODE_LLAMA_34B = 'codellama:34b',
  MISTRAL_7B = 'mistral:7b',
  MISTRAL_INSTRUCT = 'mistral:instruct',
  PHI_3_MINI = 'phi3:mini',
  PHI_3_MEDIUM = 'phi3:medium',
  NEURAL_CHAT = 'neural-chat:7b',
  STARCODE = 'starcoder:7b',
  VICUNA_7B = 'vicuna:7b',
  VICUNA_13B = 'vicuna:13b',
}

@Entity('ai_models')
@Index(['userId', 'modelType'])
export class AiModel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ModelType,
  })
  modelType: ModelType;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'jsonb' })
  settings: {
    provider?: string; // openai, anthropic, etc.
    model?: string; // gpt-4, claude-3, etc.
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    customInstructions?: string;
    apiKey?: string;
    endpoint?: string;
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  usageCount: number;

  @Column({ nullable: true })
  lastUsedAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  performance: {
    averageResponseTime?: number;
    successRate?: number;
    errorCount?: number;
    totalRequests?: number;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
