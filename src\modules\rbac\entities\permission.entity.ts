import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { RolePermission } from './role-permission.entity';
import { PermissionGroupItem } from './permission-group-item.entity';

/**
 * Enum định nghĩa các hành động quyền hạn
 * Enum defining permission actions
 */
export enum PermissionAction {
  READ = 'READ',
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  EXPORT = 'EXPORT',
  APPROVE = 'APPROVE',
  MANAGE = 'MANAGE',
  ASSIGN = 'ASSIGN',
}

/**
 * Enum định nghĩa các module hệ thống
 * Enum defining system modules
 */
export enum SystemModule {
  // Main application modules
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  ROLE_MANAGEMENT = 'ROLE_MANAGEMENT',
  CONTENT_MANAGEMENT = 'CONTENT_MANAGEMENT',
  MARKETING_CAMPAIGNS = 'MARKETING_CAMPAIGNS',
  SALES_PIPELINE = 'SALES_PIPELINE',
  ANALYTICS_REPORTS = 'ANALYTICS_REPORTS',
  FINANCIAL_DATA = 'FINANCIAL_DATA',
  SYSTEM_SETTINGS = 'SYSTEM_SETTINGS',
  AI_FEATURES = 'AI_FEATURES',
  EXPORT_TOOLS = 'EXPORT_TOOLS',

  // SSO application modules
  MAIL_MANAGEMENT = 'MAIL_MANAGEMENT',
  CORE_SERVICES = 'CORE_SERVICES',
  API_GATEWAY = 'API_GATEWAY',
  SSO_MANAGEMENT = 'SSO_MANAGEMENT',
}

/**
 * Permission Entity - Định nghĩa quyền hạn trong hệ thống RBAC
 * Permission Entity - Defines permissions in the RBAC system
 */
@Entity('permissions')
@Index(['module', 'action'])
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Mã quyền hạn (unique) - Permission code (unique)
   * VD: USER_MANAGEMENT_READ, CONTENT_MANAGEMENT_CREATE
   */
  @Column({ type: 'varchar', length: 100, unique: true })
  code: string;

  /**
   * Module/tính năng - Module/feature
   */
  @Column({ type: 'varchar', length: 50 })
  module: SystemModule;

  /**
   * Hành động - Action
   */
  @Column({ type: 'varchar', length: 20 })
  action: PermissionAction;

  /**
   * Tài nguyên cụ thể (tùy chọn) - Specific resource (optional)
   * VD: "user:123", "content:456"
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  resource?: string;

  /**
   * Ứng dụng áp dụng - Application context
   */
  @Column({ type: 'varchar', length: 100, default: 'MAIN_APP' })
  application: string;

  /**
   * Mô tả quyền hạn - Permission description
   */
  @Column({ type: 'text', nullable: true })
  description?: string;

  /**
   * Trạng thái hoạt động - Active status
   */
  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations

  /**
   * Vai trò có quyền này - Roles with this permission
   */
  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.permission)
  rolePermissions: RolePermission[];

  /**
   * Nhóm quyền chứa quyền này - Permission groups containing this permission
   */
  @OneToMany(() => PermissionGroupItem, (groupItem) => groupItem.permission)
  permissionGroupItems: PermissionGroupItem[];

  // Virtual properties

  /**
   * Lấy tên hiển thị đầy đủ - Get full display name
   */
  get displayName(): string {
    return `${this.module}_${this.action}${this.resource ? `_${this.resource}` : ''}`;
  }

  /**
   * Kiểm tra có phải quyền quản lý không - Check if management permission
   */
  get isManagementPermission(): boolean {
    return this.action === PermissionAction.MANAGE;
  }

  /**
   * Kiểm tra có phải quyền đọc không - Check if read permission
   */
  get isReadPermission(): boolean {
    return this.action === PermissionAction.READ;
  }

  /**
   * Kiểm tra có phải quyền ghi không - Check if write permission
   */
  get isWritePermission(): boolean {
    return [
      PermissionAction.CREATE,
      PermissionAction.UPDATE,
      PermissionAction.DELETE,
    ].includes(this.action);
  }

  /**
   * Lấy mức độ quyền hạn - Get permission level
   * 0 = READ, 1 = WRITE, 2 = MANAGE
   */
  get permissionLevel(): number {
    switch (this.action) {
      case PermissionAction.READ:
        return 0;
      case PermissionAction.CREATE:
      case PermissionAction.UPDATE:
      case PermissionAction.DELETE:
      case PermissionAction.EXPORT:
      case PermissionAction.APPROVE:
      case PermissionAction.ASSIGN:
        return 1;
      case PermissionAction.MANAGE:
        return 2;
      default:
        return 0;
    }
  }

  /**
   * Tạo mã quyền từ module và action - Generate permission code from module and action
   */
  static generateCode(module: SystemModule, action: PermissionAction, resource?: string): string {
    return `${module}_${action}${resource ? `_${resource.toUpperCase()}` : ''}`;
  }

  /**
   * Kiểm tra quyền có bao gồm quyền khác không - Check if permission includes another permission
   */
  includes(otherPermission: Permission): boolean {
    // MANAGE permission includes all other permissions in the same module
    if (this.action === PermissionAction.MANAGE && this.module === otherPermission.module) {
      return true;
    }

    // Exact match
    return this.code === otherPermission.code;
  }

  /**
   * So sánh mức độ quyền hạn - Compare permission levels
   */
  isHigherThan(otherPermission: Permission): boolean {
    if (this.module !== otherPermission.module) {
      return false;
    }
    return this.permissionLevel > otherPermission.permissionLevel;
  }
}
