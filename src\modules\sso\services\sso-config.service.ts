import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Interface cho cấu hình SSO
 * Interface for SSO configuration
 */
export interface SSOConfig {
  enabled: boolean;
  baseDomain: string;
  cookieDomain: string;
  issuer: string;
  allowedApplications: string[];
  sessionTimeout: number; // minutes
  maxConcurrentSessions: number;
  requireDeviceVerification: boolean;
  enableAuditLogging: boolean;
  tokenRevocationEnabled: boolean;
  crossDomainCookies: boolean;
  secureOnly: boolean;
  sameSite: 'strict' | 'lax' | 'none';
}

/**
 * Interface cho cấu hình cookie
 * Interface for cookie configuration
 */
export interface CookieConfig {
  domain: string;
  path: string;
  httpOnly: boolean;
  secure: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  maxAge: number;
}

/**
 * SSOConfigService - Quản lý cấu hình SSO
 * SSOConfigService - Manages SSO configuration
 */
@Injectable()
export class SSOConfigService {
  private readonly config: SSOConfig;

  constructor(private configService: ConfigService) {
    this.config = this.loadConfiguration();
  }

  /**
   * Tải cấu hình từ environment variables
   * Load configuration from environment variables
   */
  private loadConfiguration(): SSOConfig {
    return {
      enabled: this.configService.get<boolean>('SSO_ENABLED', true),
      baseDomain: this.configService.get<string>('SSO_BASE_DOMAIN', 'yourcompany.com'),
      cookieDomain: this.configService.get<string>('SSO_COOKIE_DOMAIN', '.yourcompany.com'),
      issuer: this.configService.get<string>('SSO_ISSUER', 'auth.yourcompany.com'),
      allowedApplications: this.parseAllowedApplications(),
      sessionTimeout: this.configService.get<number>('SSO_SESSION_TIMEOUT', 480), // 8 hours
      maxConcurrentSessions: this.configService.get<number>('SSO_MAX_CONCURRENT_SESSIONS', 5),
      requireDeviceVerification: this.configService.get<boolean>('SSO_REQUIRE_DEVICE_VERIFICATION', false),
      enableAuditLogging: this.configService.get<boolean>('SSO_ENABLE_AUDIT_LOGGING', true),
      tokenRevocationEnabled: this.configService.get<boolean>('SSO_TOKEN_REVOCATION_ENABLED', true),
      crossDomainCookies: this.configService.get<boolean>('SSO_CROSS_DOMAIN_COOKIES', true),
      secureOnly: this.configService.get<boolean>('SSO_SECURE_ONLY', true),
      sameSite: this.configService.get<'strict' | 'lax' | 'none'>('SSO_SAME_SITE', 'lax'),
    };
  }

  /**
   * Parse allowed applications từ environment variable
   * Parse allowed applications from environment variable
   */
  private parseAllowedApplications(): string[] {
    const appsString = this.configService.get<string>('SSO_ALLOWED_APPLICATIONS', 
      'app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com'
    );
    
    return appsString
      .split(',')
      .map(app => app.trim())
      .filter(app => app.length > 0);
  }

  /**
   * Lấy cấu hình SSO
   * Get SSO configuration
   */
  getConfig(): SSOConfig {
    return { ...this.config };
  }

  /**
   * Kiểm tra SSO có được bật không
   * Check if SSO is enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Lấy base domain
   * Get base domain
   */
  getBaseDomain(): string {
    return this.config.baseDomain;
  }

  /**
   * Lấy cookie domain
   * Get cookie domain
   */
  getCookieDomain(): string {
    return this.config.cookieDomain;
  }

  /**
   * Lấy issuer
   * Get issuer
   */
  getIssuer(): string {
    return this.config.issuer;
  }

  /**
   * Lấy danh sách ứng dụng được phép
   * Get allowed applications
   */
  getAllowedApplications(): string[] {
    return [...this.config.allowedApplications];
  }

  /**
   * Kiểm tra ứng dụng có được phép không
   * Check if application is allowed
   */
  isApplicationAllowed(application: string): boolean {
    return this.config.allowedApplications.includes(application);
  }

  /**
   * Lấy cấu hình cookie cho access token
   * Get cookie configuration for access token
   */
  getAccessTokenCookieConfig(): CookieConfig {
    return {
      domain: this.config.cookieDomain,
      path: '/',
      httpOnly: true,
      secure: this.config.secureOnly,
      sameSite: this.config.sameSite,
      maxAge: 15 * 60 * 1000, // 15 minutes
    };
  }

  /**
   * Lấy cấu hình cookie cho refresh token
   * Get cookie configuration for refresh token
   */
  getRefreshTokenCookieConfig(): CookieConfig {
    return {
      domain: this.config.cookieDomain,
      path: '/',
      httpOnly: true,
      secure: this.config.secureOnly,
      sameSite: this.config.sameSite,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    };
  }

  /**
   * Lấy timeout phiên (milliseconds)
   * Get session timeout (milliseconds)
   */
  getSessionTimeout(): number {
    return this.config.sessionTimeout * 60 * 1000; // Convert to milliseconds
  }

  /**
   * Lấy số phiên tối đa
   * Get maximum concurrent sessions
   */
  getMaxConcurrentSessions(): number {
    return this.config.maxConcurrentSessions;
  }

  /**
   * Kiểm tra có yêu cầu xác minh thiết bị không
   * Check if device verification is required
   */
  requiresDeviceVerification(): boolean {
    return this.config.requireDeviceVerification;
  }

  /**
   * Kiểm tra có bật audit logging không
   * Check if audit logging is enabled
   */
  isAuditLoggingEnabled(): boolean {
    return this.config.enableAuditLogging;
  }

  /**
   * Kiểm tra có bật token revocation không
   * Check if token revocation is enabled
   */
  isTokenRevocationEnabled(): boolean {
    return this.config.tokenRevocationEnabled;
  }

  /**
   * Tạo URL đầy đủ cho subdomain
   * Generate full URL for subdomain
   */
  generateSubdomainUrl(subdomain: string, path: string = ''): string {
    const protocol = this.config.secureOnly ? 'https' : 'http';
    const url = `${protocol}://${subdomain}.${this.config.baseDomain}`;
    return path ? `${url}${path}` : url;
  }

  /**
   * Parse subdomain từ URL
   * Parse subdomain from URL
   */
  parseSubdomain(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      
      if (hostname.endsWith(`.${this.config.baseDomain}`)) {
        const subdomain = hostname.replace(`.${this.config.baseDomain}`, '');
        return subdomain || null;
      }
      
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Kiểm tra URL có thuộc domain được phép không
   * Check if URL belongs to allowed domain
   */
  isAllowedDomain(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      
      // Check exact match
      if (this.config.allowedApplications.includes(hostname)) {
        return true;
      }
      
      // Check if it's a subdomain of base domain
      return hostname.endsWith(`.${this.config.baseDomain}`);
    } catch {
      return false;
    }
  }

  /**
   * Lấy audience list cho JWT
   * Get audience list for JWT
   */
  getJWTAudience(): string[] {
    return this.config.allowedApplications;
  }

  /**
   * Validate cấu hình
   * Validate configuration
   */
  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.config.baseDomain) {
      errors.push('Base domain is required');
    }

    if (!this.config.cookieDomain) {
      errors.push('Cookie domain is required');
    }

    if (!this.config.issuer) {
      errors.push('Issuer is required');
    }

    if (this.config.allowedApplications.length === 0) {
      errors.push('At least one allowed application is required');
    }

    if (this.config.sessionTimeout <= 0) {
      errors.push('Session timeout must be positive');
    }

    if (this.config.maxConcurrentSessions <= 0) {
      errors.push('Max concurrent sessions must be positive');
    }

    // Validate allowed applications format
    for (const app of this.config.allowedApplications) {
      try {
        new URL(`https://${app}`);
      } catch {
        errors.push(`Invalid application format: ${app}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Lấy CORS origins
   * Get CORS origins
   */
  getCORSOrigins(): string[] {
    const protocol = this.config.secureOnly ? 'https' : 'http';
    return this.config.allowedApplications.map(app => `${protocol}://${app}`);
  }

  /**
   * Lấy cấu hình CORS
   * Get CORS configuration
   */
  getCORSConfig(): any {
    return {
      origin: this.getCORSOrigins(),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Session-ID',
        'X-Device-ID',
        'X-Application',
      ],
      exposedHeaders: [
        'X-Session-ID',
        'X-Token-Expires',
        'X-Refresh-Token',
      ],
      maxAge: 86400, // 24 hours
    };
  }
}
