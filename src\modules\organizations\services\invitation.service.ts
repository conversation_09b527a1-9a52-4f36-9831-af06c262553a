import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { OrganizationMember, MemberStatus, InvitationStatus } from '../entities/organization-member.entity';
import { Organization } from '../entities/organization.entity';
import { User } from '../../users/entities/user.entity';
import { RolesService } from './roles.service';
import { LoggerService } from '../../../common/services/logger.service';
import { UtilsService } from '../../../common/services/utils.service';
import { generateInvitationEmailHtml, generateInvitationEmailText } from '../templates/invitation-email.template';
import { InviteMemberDto } from '../dto/invite-member.dto';

@Injectable()
export class InvitationService {
  constructor(
    @InjectRepository(OrganizationMember)
    private memberRepository: Repository<OrganizationMember>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectQueue('email')
    private emailQueue: Queue,
    private rolesService: RolesService,
    private logger: LoggerService,
    private utilsService: UtilsService,
  ) {}

  async inviteMember(organizationId: string, invitedBy: string, inviteDto: InviteMemberDto): Promise<OrganizationMember> {
    const organization = await this.organizationRepository.findOne({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if organization can invite more members
    if (!organization.canInviteMembers) {
      throw new BadRequestException('Organization has reached member limit');
    }

    // Check if user is already a member
    const existingMember = await this.memberRepository.findOne({
      where: { organizationId, email: inviteDto.email },
    });

    if (existingMember) {
      if (existingMember.status === MemberStatus.ACTIVE) {
        throw new BadRequestException('User is already a member of this organization');
      } else if (existingMember.isPending) {
        throw new BadRequestException('Invitation already sent to this email');
      }
    }

    // Get default role if not specified
    let roleId = inviteDto.roleId;
    if (!roleId) {
      const defaultRole = await this.rolesService.getDefaultMemberRole();
      roleId = defaultRole.id;
    }

    // Generate invitation token
    const invitationToken = this.utilsService.generateRandomString(32);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    // Create or update invitation
    let invitation: OrganizationMember;

    if (existingMember) {
      // Update existing invitation
      await this.memberRepository.update(existingMember.id, {
        roleId,
        invitationToken,
        invitedBy,
        invitedAt: new Date(),
        expiresAt,
        invitationMessage: inviteDto.message,
        permissions: inviteDto.metadata ? [] : undefined,
        restrictions: inviteDto.restrictions,
        metadata: inviteDto.metadata,
        status: MemberStatus.PENDING,
        invitationStatus: InvitationStatus.PENDING,
      });
      invitation = await this.memberRepository.findOne({
        where: { id: existingMember.id },
        relations: ['organization', 'role', 'inviter'],
      });
    } else {
      // Create new invitation
      invitation = this.memberRepository.create({
        organizationId,
        email: inviteDto.email,
        roleId,
        invitationToken,
        invitedBy,
        invitedAt: new Date(),
        expiresAt,
        invitationMessage: inviteDto.message,
        permissions: inviteDto.metadata ? [] : undefined,
        restrictions: inviteDto.restrictions,
        metadata: inviteDto.metadata,
        status: MemberStatus.PENDING,
        invitationStatus: InvitationStatus.PENDING,
      });
      invitation = await this.memberRepository.save(invitation);

      // Load relations
      invitation = await this.memberRepository.findOne({
        where: { id: invitation.id },
        relations: ['organization', 'role', 'inviter'],
      });
    }

    // Send invitation email
    await this.sendInvitationEmail(invitation);

    this.logger.logWithContext(
      `Member invitation sent: ${inviteDto.email} to organization ${organizationId}`,
      'InvitationService'
    );

    return invitation;
  }

  async acceptInvitation(token: string, userId: string): Promise<OrganizationMember> {
    const invitation = await this.memberRepository.findOne({
      where: { invitationToken: token },
      relations: ['organization', 'role'],
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (!invitation.canAcceptInvitation) {
      throw new BadRequestException('Invitation is no longer valid');
    }

    // Get user
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if email matches
    if (user.email !== invitation.email) {
      throw new BadRequestException('Invitation email does not match user email');
    }

    // Check if user is already a member of this organization
    const existingMembership = await this.memberRepository.findOne({
      where: {
        userId,
        organizationId: invitation.organizationId,
        status: MemberStatus.ACTIVE,
      },
    });

    if (existingMembership) {
      throw new BadRequestException('User is already a member of this organization');
    }

    // Accept invitation
    await this.memberRepository.update(invitation.id, {
      userId,
      status: MemberStatus.ACTIVE,
      invitationStatus: InvitationStatus.ACCEPTED,
      acceptedAt: new Date(),
      invitationToken: null, // Clear token after acceptance
    });

    // Update organization member count
    await this.organizationRepository.increment(
      { id: invitation.organizationId },
      'memberCount',
      1
    );

    this.logger.logWithContext(
      `Invitation accepted: ${userId} joined organization ${invitation.organizationId}`,
      'InvitationService'
    );

    return this.memberRepository.findOne({
      where: { id: invitation.id },
      relations: ['organization', 'role', 'user'],
    });
  }

  async declineInvitation(token: string): Promise<void> {
    const invitation = await this.memberRepository.findOne({
      where: { invitationToken: token },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (!invitation.canAcceptInvitation) {
      throw new BadRequestException('Invitation is no longer valid');
    }

    await this.memberRepository.update(invitation.id, {
      invitationStatus: InvitationStatus.DECLINED,
      invitationToken: null,
    });

    this.logger.logWithContext(
      `Invitation declined: ${invitation.email} for organization ${invitation.organizationId}`,
      'InvitationService'
    );
  }

  async cancelInvitation(organizationId: string, invitationId: string): Promise<void> {
    const invitation = await this.memberRepository.findOne({
      where: { id: invitationId, organizationId },
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (invitation.status !== MemberStatus.PENDING) {
      throw new BadRequestException('Can only cancel pending invitations');
    }

    await this.memberRepository.update(invitationId, {
      invitationStatus: InvitationStatus.CANCELLED,
      invitationToken: null,
    });

    this.logger.logWithContext(
      `Invitation cancelled: ${invitationId}`,
      'InvitationService'
    );
  }

  async resendInvitation(organizationId: string, invitationId: string): Promise<void> {
    const invitation = await this.memberRepository.findOne({
      where: { id: invitationId, organizationId },
      relations: ['organization', 'role', 'inviter'],
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    if (invitation.status !== MemberStatus.PENDING) {
      throw new BadRequestException('Can only resend pending invitations');
    }

    // Generate new token and extend expiry
    const newToken = this.utilsService.generateRandomString(32);
    const newExpiresAt = new Date();
    newExpiresAt.setDate(newExpiresAt.getDate() + 7);

    await this.memberRepository.update(invitationId, {
      invitationToken: newToken,
      expiresAt: newExpiresAt,
      invitedAt: new Date(),
    });

    // Reload invitation with new token
    const updatedInvitation = await this.memberRepository.findOne({
      where: { id: invitationId },
      relations: ['organization', 'role', 'inviter'],
    });

    // Send new invitation email
    await this.sendInvitationEmail(updatedInvitation);

    this.logger.logWithContext(
      `Invitation resent: ${invitationId}`,
      'InvitationService'
    );
  }

  async getPendingInvitations(organizationId: string): Promise<OrganizationMember[]> {
    return this.memberRepository.find({
      where: {
        organizationId,
        status: MemberStatus.PENDING,
        invitationStatus: InvitationStatus.PENDING,
      },
      relations: ['role', 'inviter'],
      order: { invitedAt: 'DESC' },
    });
  }

  async getInvitationByToken(token: string): Promise<OrganizationMember> {
    const invitation = await this.memberRepository.findOne({
      where: { invitationToken: token },
      relations: ['organization', 'role'],
    });

    if (!invitation) {
      throw new NotFoundException('Invitation not found');
    }

    return invitation;
  }

  async cleanupExpiredInvitations(): Promise<void> {
    const expiredInvitations = await this.memberRepository.find({
      where: {
        status: MemberStatus.PENDING,
        invitationStatus: InvitationStatus.PENDING,
      },
    });

    const now = new Date();
    const toExpire = expiredInvitations.filter(inv =>
      inv.expiresAt && inv.expiresAt < now
    );

    if (toExpire.length > 0) {
      await this.memberRepository.update(
        toExpire.map(inv => inv.id),
        { invitationStatus: InvitationStatus.EXPIRED }
      );

      this.logger.logWithContext(
        `Expired ${toExpire.length} invitations`,
        'InvitationService'
      );
    }
  }

  private async sendInvitationEmail(invitation: OrganizationMember): Promise<void> {
    const inviteUrl = `${process.env.FRONTEND_URL}/invitations/${invitation.invitationToken}`;

    const emailData = {
      organizationName: invitation.organization.name,
      inviterName: invitation.inviter?.fullName || 'Someone',
      inviterEmail: invitation.inviter?.email || '',
      roleName: invitation.role.displayName,
      invitationMessage: invitation.invitationMessage,
      inviteUrl,
      expiryDate: invitation.expiresAt.toLocaleDateString(),
      organizationLogo: invitation.organization.logo,
    };

    await this.emailQueue.add('send-single-email', {
      to: invitation.email,
      subject: `You're invited to join ${invitation.organization.name}`,
      htmlContent: generateInvitationEmailHtml(emailData),
      textContent: generateInvitationEmailText(emailData),
      userId: invitation.invitedBy,
    });
  }


}
