import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PullModelDto {
  @ApiProperty({
    example: 'llama2:7b',
    description: 'Name of the model to pull from OLLAMA registry'
  })
  @IsString()
  modelName: string;

  @ApiPropertyOptional({
    default: false,
    description: 'Whether to stream the download progress'
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;
}

export class DeleteModelDto {
  @ApiProperty({
    example: 'llama2:7b',
    description: 'Name of the model to delete'
  })
  @IsString()
  modelName: string;
}

export class OllamaOptionsDto {
  @ApiPropertyOptional({
    example: 0.7,
    description: 'Temperature for randomness (0.0 to 2.0)',
    minimum: 0,
    maximum: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({
    example: 1,
    description: 'Top-p sampling (0.0 to 1.0)',
    minimum: 0,
    maximum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p?: number;

  @ApiPropertyOptional({
    example: 40,
    description: 'Top-k sampling',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  top_k?: number;

  @ApiPropertyOptional({
    example: 1.1,
    description: 'Repeat penalty (1.0 = no penalty)',
    minimum: 0.1,
    maximum: 2.0
  })
  @IsOptional()
  @IsNumber()
  @Min(0.1)
  @Max(2.0)
  repeat_penalty?: number;

  @ApiPropertyOptional({
    example: 1000,
    description: 'Maximum number of tokens to predict',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  num_predict?: number;

  @ApiPropertyOptional({
    example: 4096,
    description: 'Context window size',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  num_ctx?: number;

  @ApiPropertyOptional({
    description: 'Random seed for reproducible results'
  })
  @IsOptional()
  @IsNumber()
  seed?: number;
}

export class OllamaGenerateDto {
  @ApiProperty({
    example: 'llama2:7b',
    description: 'Model name to use for generation'
  })
  @IsString()
  model: string;

  @ApiProperty({
    example: 'Explain quantum computing',
    description: 'Prompt for text generation'
  })
  @IsString()
  prompt: string;

  @ApiPropertyOptional({
    example: 'You are a helpful AI assistant.',
    description: 'System prompt to set behavior'
  })
  @IsOptional()
  @IsString()
  system?: string;

  @ApiPropertyOptional({
    default: false,
    description: 'Whether to stream the response'
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({
    description: 'Generation options'
  })
  @IsOptional()
  options?: OllamaOptionsDto;
}

export class OllamaCodeGenerateDto {
  @ApiProperty({
    example: 'codellama:7b',
    description: 'Code model to use'
  })
  @IsString()
  model: string;

  @ApiProperty({
    example: 'Create a function to calculate fibonacci numbers',
    description: 'Code generation prompt'
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    example: 'python',
    description: 'Programming language'
  })
  @IsString()
  language: string;

  @ApiPropertyOptional({
    description: 'Generation options'
  })
  @IsOptional()
  options?: OllamaOptionsDto;
}

export class OllamaCodeExplainDto {
  @ApiProperty({
    example: 'codellama:7b',
    description: 'Code model to use'
  })
  @IsString()
  model: string;

  @ApiProperty({
    example: 'def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)',
    description: 'Code to explain'
  })
  @IsString()
  code: string;

  @ApiPropertyOptional({
    example: 'python',
    description: 'Programming language (auto-detect if not provided)'
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({
    description: 'Generation options'
  })
  @IsOptional()
  options?: OllamaOptionsDto;
}

export class OllamaEmbeddingDto {
  @ApiProperty({
    example: 'nomic-embed-text:latest',
    description: 'Embedding model to use'
  })
  @IsString()
  model: string;

  @ApiProperty({
    example: 'This is a sample text for embedding',
    description: 'Text to generate embeddings for'
  })
  @IsString()
  text: string;
}

export class OllamaImageAnalyzeDto {
  @ApiProperty({
    example: 'llava:latest',
    description: 'Vision model to use'
  })
  @IsString()
  model: string;

  @ApiProperty({
    example: 'https://example.com/image.jpg',
    description: 'URL of the image to analyze'
  })
  @IsString()
  imageUrl: string;

  @ApiProperty({
    example: 'Describe what you see in this image',
    description: 'Prompt for image analysis'
  })
  @IsString()
  prompt: string;

  @ApiPropertyOptional({
    description: 'Generation options'
  })
  @IsOptional()
  options?: Pick<OllamaOptionsDto, 'temperature' | 'num_predict'>;
}

export class ModelInfoDto {
  @ApiProperty({
    example: 'llama2:7b',
    description: 'Model name to get info for'
  })
  @IsString()
  modelName: string;
}

// Response DTOs
export class OllamaModelResponse {
  @ApiProperty()
  name: string;

  @ApiProperty()
  modified_at: string;

  @ApiProperty()
  size: number;

  @ApiProperty()
  digest: string;

  @ApiProperty()
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export class OllamaHealthResponse {
  @ApiProperty()
  isHealthy: boolean;

  @ApiProperty({ required: false })
  version?: string;

  @ApiProperty({ required: false })
  models?: number;

  @ApiProperty({ required: false })
  error?: string;
}

export class OllamaGenerateResponse {
  @ApiProperty()
  content: string;

  @ApiProperty()
  model: string;

  @ApiProperty()
  done: boolean;

  @ApiProperty()
  total_duration?: number;

  @ApiProperty()
  load_duration?: number;

  @ApiProperty()
  prompt_eval_count?: number;

  @ApiProperty()
  prompt_eval_duration?: number;

  @ApiProperty()
  eval_count?: number;

  @ApiProperty()
  eval_duration?: number;
}
