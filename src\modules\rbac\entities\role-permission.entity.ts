import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { Role } from './role.entity';
import { Permission } from './permission.entity';
import { User } from '../../users/entities/user.entity';

/**
 * RolePermission Entity - Ánh xạ vai trò và quyền hạn
 * RolePermission Entity - Maps roles to permissions
 */
@Entity('role_permissions')
@Unique('UQ_role_permission', ['roleId', 'permissionId'])
@Index(['roleId'])
@Index(['permissionId'])
export class RolePermission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID vai trò - Role ID
   */
  @Column({ type: 'uuid', name: 'role_id' })
  roleId: string;

  /**
   * ID quyền hạn - Permission ID
   */
  @Column({ type: 'uuid', name: 'permission_id' })
  permissionId: string;

  /**
   * ID người cấp quyền - Granter ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'granted_by' })
  grantedBy?: string;

  @CreateDateColumn({ name: 'granted_at' })
  grantedAt: Date;

  // Relations

  /**
   * Vai trò - Role
   */
  @ManyToOne(() => Role, (role) => role.rolePermissions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  /**
   * Quyền hạn - Permission
   */
  @ManyToOne(() => Permission, (permission) => permission.rolePermissions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;

  /**
   * Người cấp quyền - Granter
   */
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'granted_by' })
  granter?: User;

  // Virtual properties

  /**
   * Lấy thông tin tóm tắt - Get summary info
   */
  get summary(): string {
    return `Role: ${this.role?.name} - Permission: ${this.permission?.code}`;
  }

  /**
   * Kiểm tra có phải quyền hệ thống không - Check if system permission
   */
  get isSystemPermission(): boolean {
    return this.role?.isSystemRole || false;
  }

  /**
   * Kiểm tra có thể thu hồi không - Check if revocable
   */
  get isRevocable(): boolean {
    return !this.isSystemPermission;
  }
}
