import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRbacTables1703000001 implements MigrationInterface {
  name = 'CreateRbacTables1703000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create roles table
    await queryRunner.query(`
      CREATE TABLE "roles" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "display_name" character varying(200) NOT NULL,
        "description" text,
        "level" integer NOT NULL,
        "parent_role_id" uuid,
        "is_system_role" boolean NOT NULL DEFAULT false,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_by" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE ("name")
      )
    `);

    // Create permissions table
    await queryRunner.query(`
      CREATE TABLE "permissions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "code" character varying(100) NOT NULL,
        "module" character varying(50) NOT NULL,
        "action" character varying(20) NOT NULL,
        "resource" character varying(100),
        "description" text,
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_920331560282b8bd21bb02290df" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_48ce552495d14eae9b187bb6716" UNIQUE ("code")
      )
    `);

    // Create role_permissions table
    await queryRunner.query(`
      CREATE TABLE "role_permissions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "role_id" uuid NOT NULL,
        "permission_id" uuid NOT NULL,
        "granted_by" uuid,
        "granted_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_84059017c90bfcb701b8fa42297" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_role_permission" UNIQUE ("role_id", "permission_id")
      )
    `);

    // Create user_roles table
    await queryRunner.query(`
      CREATE TABLE "user_roles" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "role_id" uuid NOT NULL,
        "assigned_by" uuid,
        "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP,
        "is_active" boolean NOT NULL DEFAULT true,
        CONSTRAINT "PK_8acd5cf26ebd158416f477de799" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_user_role" UNIQUE ("user_id", "role_id")
      )
    `);

    // Create permission_groups table
    await queryRunner.query(`
      CREATE TABLE "permission_groups" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "description" text,
        "module" character varying(50) NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_f5e1e866b8c3b9e4b8b8b8b8b8b" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_permission_groups_name" UNIQUE ("name")
      )
    `);

    // Create permission_group_items table
    await queryRunner.query(`
      CREATE TABLE "permission_group_items" (
        "group_id" uuid NOT NULL,
        "permission_id" uuid NOT NULL,
        CONSTRAINT "PK_permission_group_items" PRIMARY KEY ("group_id", "permission_id")
      )
    `);

    // Create role_hierarchy table
    await queryRunner.query(`
      CREATE TABLE "role_hierarchy" (
        "parent_role_id" uuid NOT NULL,
        "child_role_id" uuid NOT NULL,
        "inheritance_type" character varying(20) NOT NULL DEFAULT 'FULL',
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_role_hierarchy" PRIMARY KEY ("parent_role_id", "child_role_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "roles" ADD CONSTRAINT "FK_roles_parent_role" 
      FOREIGN KEY ("parent_role_id") REFERENCES "roles"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "roles" ADD CONSTRAINT "FK_roles_created_by" 
      FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_role_permissions_role" 
      FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_role_permissions_permission" 
      FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_role_permissions_granted_by" 
      FOREIGN KEY ("granted_by") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "user_roles" ADD CONSTRAINT "FK_user_roles_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "user_roles" ADD CONSTRAINT "FK_user_roles_role" 
      FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "user_roles" ADD CONSTRAINT "FK_user_roles_assigned_by" 
      FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_group_items" ADD CONSTRAINT "FK_permission_group_items_group" 
      FOREIGN KEY ("group_id") REFERENCES "permission_groups"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "permission_group_items" ADD CONSTRAINT "FK_permission_group_items_permission" 
      FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "role_hierarchy" ADD CONSTRAINT "FK_role_hierarchy_parent" 
      FOREIGN KEY ("parent_role_id") REFERENCES "roles"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "role_hierarchy" ADD CONSTRAINT "FK_role_hierarchy_child" 
      FOREIGN KEY ("child_role_id") REFERENCES "roles"("id") ON DELETE CASCADE
    `);

    // Create indexes for performance
    await queryRunner.query(`CREATE INDEX "IDX_roles_level" ON "roles" ("level")`);
    await queryRunner.query(`CREATE INDEX "IDX_roles_parent" ON "roles" ("parent_role_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_permissions_module_action" ON "permissions" ("module", "action")`);
    await queryRunner.query(`CREATE INDEX "IDX_role_permissions_role" ON "role_permissions" ("role_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_role_permissions_permission" ON "role_permissions" ("permission_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_roles_user" ON "user_roles" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_roles_role" ON "user_roles" ("role_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_roles_active" ON "user_roles" ("is_active")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order
    await queryRunner.query(`DROP TABLE "role_hierarchy"`);
    await queryRunner.query(`DROP TABLE "permission_group_items"`);
    await queryRunner.query(`DROP TABLE "permission_groups"`);
    await queryRunner.query(`DROP TABLE "user_roles"`);
    await queryRunner.query(`DROP TABLE "role_permissions"`);
    await queryRunner.query(`DROP TABLE "permissions"`);
    await queryRunner.query(`DROP TABLE "roles"`);
  }
}
