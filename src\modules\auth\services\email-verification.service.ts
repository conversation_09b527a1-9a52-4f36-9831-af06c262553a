import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class EmailVerificationService {
  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {}

  /**
   * Send verification code via email
   * TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
   */
  async sendVerificationCode(email: string, code: string, purpose: 'login' | 'password_change' = 'login'): Promise<void> {
    const appName = this.configService.get<string>('APP_NAME', 'Delify');
    
    // For now, we'll just log the code (in production, send actual email)
    const subject = purpose === 'login' 
      ? `${appName} - Login Verification Code`
      : `${appName} - Password Change Verification Code`;

    const message = this.generateEmailTemplate(code, purpose, appName);

    // TODO: Replace with actual email sending logic
    this.logger.logWithContext(
      `Email verification code for ${email}: ${code} (Purpose: ${purpose})`,
      'EmailVerificationService'
    );

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));

    this.logger.logWithContext(
      `Verification email sent to ${email} for ${purpose}`,
      'EmailVerificationService'
    );
  }

  /**
   * Generate email template for verification code
   */
  private generateEmailTemplate(code: string, purpose: 'login' | 'password_change', appName: string): string {
    const action = purpose === 'login' ? 'complete your login' : 'change your password';
    
    return `
      <html>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
            <h1 style="color: #333;">${appName}</h1>
          </div>
          
          <div style="padding: 30px 20px;">
            <h2 style="color: #333;">Verification Code Required</h2>
            
            <p>You are receiving this email because you requested to ${action}.</p>
            
            <div style="background-color: #f8f9fa; padding: 20px; margin: 20px 0; text-align: center; border-radius: 5px;">
              <h3 style="margin: 0; color: #007bff; font-size: 32px; letter-spacing: 5px;">${code}</h3>
            </div>
            
            <p><strong>This code will expire in 5 minutes.</strong></p>
            
            <p>If you did not request this verification, please ignore this email or contact support if you have concerns.</p>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            
            <p style="color: #666; font-size: 12px;">
              This is an automated message from ${appName}. Please do not reply to this email.
            </p>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Send password change notification email
   */
  async sendPasswordChangeNotification(email: string): Promise<void> {
    const appName = this.configService.get<string>('APP_NAME', 'Delify');
    
    // TODO: Replace with actual email sending logic
    this.logger.logWithContext(
      `Password change notification sent to ${email}`,
      'EmailVerificationService'
    );
  }

  /**
   * Send security method change notification email
   */
  async sendSecurityMethodChangeNotification(email: string, newMethod: string): Promise<void> {
    const appName = this.configService.get<string>('APP_NAME', 'Delify');
    
    // TODO: Replace with actual email sending logic
    this.logger.logWithContext(
      `Security method change notification sent to ${email}: ${newMethod}`,
      'EmailVerificationService'
    );
  }
}
