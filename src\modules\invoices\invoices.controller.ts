import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTag<PERSON>, ApiBearerAuth } from '@nestjs/swagger';
import { InvoicesService } from './invoices.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Invoices')
@Controller('invoices')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class InvoicesController {
  constructor(private readonly invoicesService: InvoicesService) {}

  @Get()
  async getInvoices(@CurrentUser() user: User) {
    return this.invoicesService.getInvoices(user.id);
  }

  @Post()
  async createInvoice(@CurrentUser() user: User, @Body() data: any) {
    return this.invoicesService.createInvoice(user.id, data);
  }

  @Get(':id')
  async getInvoice(@CurrentUser() user: User, @Param('id') id: string) {
    return this.invoicesService.getInvoice(user.id, id);
  }

  @Patch(':id')
  async updateInvoice(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.invoicesService.updateInvoice(user.id, id, data);
  }

  @Post(':id/send')
  async sendInvoice(@CurrentUser() user: User, @Param('id') id: string) {
    return this.invoicesService.sendInvoice(user.id, id);
  }

  @Get('customers/list')
  async getCustomers(@CurrentUser() user: User) {
    return this.invoicesService.getCustomers(user.id);
  }

  @Post('customers')
  async createCustomer(@CurrentUser() user: User, @Body() data: any) {
    return this.invoicesService.createCustomer(user.id, data);
  }
}
