import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSsoSupport1703000002 implements MigrationInterface {
  name = 'AddSsoSupport1703000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create user_sessions table for global session tracking
    await queryRunner.query(`
      CREATE TABLE "user_sessions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "session_id" character varying(255) NOT NULL,
        "user_id" uuid NOT NULL,
        "device_id" character varying(255),
        "device_fingerprint" text,
        "device_name" character varying(200),
        "device_type" character varying(50),
        "ip_address" character varying(45),
        "user_agent" text,
        "location" character varying(200),
        "is_active" boolean NOT NULL DEFAULT true,
        "last_activity_at" TIMESTAMP NOT NULL DEFAULT now(),
        "expires_at" TIMESTAMP NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_user_sessions" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_user_sessions_session_id" UNIQUE ("session_id")
      )
    `);

    // Create sso_applications table for managing SSO-enabled applications
    await queryRunner.query(`
      CREATE TABLE "sso_applications" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying(100) NOT NULL,
        "subdomain" character varying(100) NOT NULL,
        "display_name" character varying(200) NOT NULL,
        "description" text,
        "base_url" character varying(500) NOT NULL,
        "allowed_origins" text[],
        "is_active" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_sso_applications" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_sso_applications_name" UNIQUE ("name"),
        CONSTRAINT "UQ_sso_applications_subdomain" UNIQUE ("subdomain")
      )
    `);

    // Create jwt_blacklist table for token revocation
    await queryRunner.query(`
      CREATE TABLE "jwt_blacklist" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "jti" character varying(255) NOT NULL,
        "user_id" uuid NOT NULL,
        "session_id" character varying(255),
        "token_type" character varying(20) NOT NULL,
        "expires_at" TIMESTAMP NOT NULL,
        "revoked_at" TIMESTAMP NOT NULL DEFAULT now(),
        "revoked_by" uuid,
        "reason" character varying(200),
        CONSTRAINT "PK_jwt_blacklist" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_jwt_blacklist_jti" UNIQUE ("jti")
      )
    `);

    // Create sso_audit_logs table for comprehensive audit logging
    await queryRunner.query(`
      CREATE TABLE "sso_audit_logs" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid,
        "session_id" character varying(255),
        "application" character varying(100),
        "action" character varying(100) NOT NULL,
        "resource" character varying(200),
        "ip_address" character varying(45),
        "user_agent" text,
        "device_id" character varying(255),
        "success" boolean NOT NULL,
        "error_message" text,
        "metadata" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_sso_audit_logs" PRIMARY KEY ("id")
      )
    `);

    // Add application field to permissions table
    await queryRunner.query(`
      ALTER TABLE "permissions" 
      ADD COLUMN "application" character varying(100) DEFAULT 'MAIN_APP'
    `);

    // Add SSO-related fields to users table
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN "sso_enabled" boolean DEFAULT true,
      ADD COLUMN "max_concurrent_sessions" integer DEFAULT 5,
      ADD COLUMN "require_device_verification" boolean DEFAULT false,
      ADD COLUMN "last_password_change" TIMESTAMP,
      ADD COLUMN "password_expires_at" TIMESTAMP
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "user_sessions" 
      ADD CONSTRAINT "FK_user_sessions_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "jwt_blacklist" 
      ADD CONSTRAINT "FK_jwt_blacklist_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "jwt_blacklist" 
      ADD CONSTRAINT "FK_jwt_blacklist_revoked_by" 
      FOREIGN KEY ("revoked_by") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "sso_audit_logs" 
      ADD CONSTRAINT "FK_sso_audit_logs_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL
    `);

    // Create indexes for performance
    await queryRunner.query(`CREATE INDEX "IDX_user_sessions_user_id" ON "user_sessions" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_sessions_session_id" ON "user_sessions" ("session_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_sessions_device_id" ON "user_sessions" ("device_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_sessions_active" ON "user_sessions" ("is_active")`);
    await queryRunner.query(`CREATE INDEX "IDX_user_sessions_expires_at" ON "user_sessions" ("expires_at")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_sso_applications_subdomain" ON "sso_applications" ("subdomain")`);
    await queryRunner.query(`CREATE INDEX "IDX_sso_applications_active" ON "sso_applications" ("is_active")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_jwt_blacklist_jti" ON "jwt_blacklist" ("jti")`);
    await queryRunner.query(`CREATE INDEX "IDX_jwt_blacklist_user_id" ON "jwt_blacklist" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_jwt_blacklist_expires_at" ON "jwt_blacklist" ("expires_at")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_sso_audit_logs_user_id" ON "sso_audit_logs" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_sso_audit_logs_session_id" ON "sso_audit_logs" ("session_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_sso_audit_logs_application" ON "sso_audit_logs" ("application")`);
    await queryRunner.query(`CREATE INDEX "IDX_sso_audit_logs_action" ON "sso_audit_logs" ("action")`);
    await queryRunner.query(`CREATE INDEX "IDX_sso_audit_logs_created_at" ON "sso_audit_logs" ("created_at")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_permissions_application" ON "permissions" ("application")`);
    await queryRunner.query(`CREATE INDEX "IDX_permissions_module_application" ON "permissions" ("module", "application")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_permissions_module_application"`);
    await queryRunner.query(`DROP INDEX "IDX_permissions_application"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_audit_logs_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_audit_logs_action"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_audit_logs_application"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_audit_logs_session_id"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_audit_logs_user_id"`);
    await queryRunner.query(`DROP INDEX "IDX_jwt_blacklist_expires_at"`);
    await queryRunner.query(`DROP INDEX "IDX_jwt_blacklist_user_id"`);
    await queryRunner.query(`DROP INDEX "IDX_jwt_blacklist_jti"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_applications_active"`);
    await queryRunner.query(`DROP INDEX "IDX_sso_applications_subdomain"`);
    await queryRunner.query(`DROP INDEX "IDX_user_sessions_expires_at"`);
    await queryRunner.query(`DROP INDEX "IDX_user_sessions_active"`);
    await queryRunner.query(`DROP INDEX "IDX_user_sessions_device_id"`);
    await queryRunner.query(`DROP INDEX "IDX_user_sessions_session_id"`);
    await queryRunner.query(`DROP INDEX "IDX_user_sessions_user_id"`);

    // Remove foreign key constraints
    await queryRunner.query(`ALTER TABLE "sso_audit_logs" DROP CONSTRAINT "FK_sso_audit_logs_user"`);
    await queryRunner.query(`ALTER TABLE "jwt_blacklist" DROP CONSTRAINT "FK_jwt_blacklist_revoked_by"`);
    await queryRunner.query(`ALTER TABLE "jwt_blacklist" DROP CONSTRAINT "FK_jwt_blacklist_user"`);
    await queryRunner.query(`ALTER TABLE "user_sessions" DROP CONSTRAINT "FK_user_sessions_user"`);

    // Remove added columns
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "password_expires_at"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "last_password_change"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "require_device_verification"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "max_concurrent_sessions"`);
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "sso_enabled"`);
    await queryRunner.query(`ALTER TABLE "permissions" DROP COLUMN "application"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "sso_audit_logs"`);
    await queryRunner.query(`DROP TABLE "jwt_blacklist"`);
    await queryRunner.query(`DROP TABLE "sso_applications"`);
    await queryRunner.query(`DROP TABLE "user_sessions"`);
  }
}
