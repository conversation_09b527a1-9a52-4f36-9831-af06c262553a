import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role, RoleType, SystemRole } from '../entities/role.entity';
import { Permission, PermissionCategory, PermissionAction } from '../entities/permission.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    private logger: LoggerService,
  ) {}

  async onModuleInit() {
    // Initialize system roles and permissions
    await this.initializeSystemRolesAndPermissions();
  }

  async getOrganizationRoles(organizationId: string): Promise<Role[]> {
    // Get both system roles and organization-specific roles
    return this.roleRepository.find({
      where: [
        { organizationId: null, type: RoleType.SYSTEM }, // System roles
        { organizationId, type: RoleType.CUSTOM }, // Custom roles
      ],
      relations: ['permissions'],
      order: { priority: 'DESC', name: 'ASC' },
    });
  }

  async getRoleById(roleId: string): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  async getOwnerRole(): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { name: SystemRole.OWNER, type: RoleType.SYSTEM },
      relations: ['permissions'],
    });

    if (!role) {
      throw new Error('Owner role not found');
    }

    return role;
  }

  async getDefaultMemberRole(): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { name: SystemRole.MEMBER, type: RoleType.SYSTEM },
      relations: ['permissions'],
    });

    if (!role) {
      throw new Error('Member role not found');
    }

    return role;
  }

  async createCustomRole(organizationId: string, createRoleDto: {
    name: string;
    description?: string;
    permissionIds: string[];
    priority?: number;
  }): Promise<Role> {
    // Check if role name already exists in organization
    const existingRole = await this.roleRepository.findOne({
      where: { name: createRoleDto.name, organizationId },
    });

    if (existingRole) {
      throw new BadRequestException('Role name already exists in this organization');
    }

    // Get permissions
    const permissions = await this.permissionRepository.findByIds(createRoleDto.permissionIds);

    const role = this.roleRepository.create({
      name: createRoleDto.name,
      description: createRoleDto.description,
      organizationId,
      type: RoleType.CUSTOM,
      priority: createRoleDto.priority || 0,
      permissions,
      metadata: {
        canBeDeleted: true,
        canBeModified: true,
      },
    });

    const savedRole = await this.roleRepository.save(role);

    this.logger.logWithContext(`Custom role created: ${savedRole.id}`, 'RolesService');

    return savedRole;
  }

  async updateRole(roleId: string, updateRoleDto: {
    name?: string;
    description?: string;
    permissionIds?: string[];
    priority?: number;
  }): Promise<Role> {
    const role = await this.getRoleById(roleId);

    if (!role.canBeModified) {
      throw new BadRequestException('This role cannot be modified');
    }

    // Update permissions if provided
    if (updateRoleDto.permissionIds) {
      const permissions = await this.permissionRepository.findByIds(updateRoleDto.permissionIds);
      role.permissions = permissions;
    }

    // Update other fields
    Object.assign(role, {
      name: updateRoleDto.name || role.name,
      description: updateRoleDto.description || role.description,
      priority: updateRoleDto.priority !== undefined ? updateRoleDto.priority : role.priority,
    });

    const savedRole = await this.roleRepository.save(role);

    this.logger.logWithContext(`Role updated: ${roleId}`, 'RolesService');

    return savedRole;
  }

  async deleteRole(roleId: string): Promise<void> {
    const role = await this.getRoleById(roleId);

    if (!role.canBeDeleted) {
      throw new BadRequestException('This role cannot be deleted');
    }

    await this.roleRepository.delete(roleId);

    this.logger.logWithContext(`Role deleted: ${roleId}`, 'RolesService');
  }

  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { isActive: true },
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  async getPermissionsByCategory(): Promise<Record<PermissionCategory, Permission[]>> {
    const permissions = await this.getAllPermissions();
    
    const grouped = permissions.reduce((acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    }, {} as Record<PermissionCategory, Permission[]>);

    return grouped;
  }

  private async initializeSystemRolesAndPermissions(): Promise<void> {
    // Initialize permissions first
    await this.initializePermissions();
    
    // Initialize system roles
    await this.initializeSystemRoles();
  }

  private async initializePermissions(): Promise<void> {
    const permissions = [
      // Organization permissions
      { name: 'organization:read', displayName: 'View Organization', category: PermissionCategory.ORGANIZATION, action: PermissionAction.READ },
      { name: 'organization:update', displayName: 'Edit Organization', category: PermissionCategory.ORGANIZATION, action: PermissionAction.UPDATE },
      { name: 'organization:delete', displayName: 'Delete Organization', category: PermissionCategory.ORGANIZATION, action: PermissionAction.DELETE },
      
      // User management permissions
      { name: 'users:read', displayName: 'View Users', category: PermissionCategory.USERS, action: PermissionAction.READ },
      { name: 'users:create', displayName: 'Invite Users', category: PermissionCategory.USERS, action: PermissionAction.CREATE },
      { name: 'users:update', displayName: 'Edit Users', category: PermissionCategory.USERS, action: PermissionAction.UPDATE },
      { name: 'users:delete', displayName: 'Remove Users', category: PermissionCategory.USERS, action: PermissionAction.DELETE },
      
      // Marketing permissions
      { name: 'marketing:campaigns:read', displayName: 'View Campaigns', category: PermissionCategory.MARKETING, action: PermissionAction.READ, resource: 'campaigns' },
      { name: 'marketing:campaigns:create', displayName: 'Create Campaigns', category: PermissionCategory.MARKETING, action: PermissionAction.CREATE, resource: 'campaigns' },
      { name: 'marketing:campaigns:update', displayName: 'Edit Campaigns', category: PermissionCategory.MARKETING, action: PermissionAction.UPDATE, resource: 'campaigns' },
      { name: 'marketing:campaigns:delete', displayName: 'Delete Campaigns', category: PermissionCategory.MARKETING, action: PermissionAction.DELETE, resource: 'campaigns' },
      
      // AI permissions
      { name: 'ai:models:read', displayName: 'View AI Models', category: PermissionCategory.AI, action: PermissionAction.READ, resource: 'models' },
      { name: 'ai:models:create', displayName: 'Create AI Models', category: PermissionCategory.AI, action: PermissionAction.CREATE, resource: 'models' },
      { name: 'ai:models:execute', displayName: 'Use AI Features', category: PermissionCategory.AI, action: PermissionAction.EXECUTE, resource: 'models' },
      
      // Settings permissions
      { name: 'settings:read', displayName: 'View Settings', category: PermissionCategory.SETTINGS, action: PermissionAction.READ },
      { name: 'settings:manage', displayName: 'Manage Settings', category: PermissionCategory.SETTINGS, action: PermissionAction.MANAGE },
      
      // Analytics permissions
      { name: 'analytics:read', displayName: 'View Analytics', category: PermissionCategory.ANALYTICS, action: PermissionAction.READ },
      { name: 'analytics:export', displayName: 'Export Analytics', category: PermissionCategory.ANALYTICS, action: PermissionAction.EXPORT },
    ];

    for (const permissionData of permissions) {
      const existing = await this.permissionRepository.findOne({
        where: { name: permissionData.name },
      });

      if (!existing) {
        const permission = this.permissionRepository.create({
          ...permissionData,
          isSystemPermission: true,
        });
        await this.permissionRepository.save(permission);
      }
    }
  }

  private async initializeSystemRoles(): Promise<void> {
    const allPermissions = await this.getAllPermissions();
    
    const roleDefinitions = [
      {
        name: SystemRole.OWNER,
        description: 'Full access to organization',
        priority: 100,
        permissions: allPermissions, // Owner gets all permissions
      },
      {
        name: SystemRole.ADMIN,
        description: 'Administrative access',
        priority: 90,
        permissions: allPermissions.filter(p => !p.name.includes('organization:delete')),
      },
      {
        name: SystemRole.MANAGER,
        description: 'Management access',
        priority: 70,
        permissions: allPermissions.filter(p => 
          !p.name.includes('organization:') && 
          !p.name.includes('settings:manage') &&
          !p.name.includes('users:delete')
        ),
      },
      {
        name: SystemRole.MEMBER,
        description: 'Standard member access',
        priority: 50,
        permissions: allPermissions.filter(p => 
          p.action === PermissionAction.READ || 
          p.action === PermissionAction.CREATE ||
          p.action === PermissionAction.EXECUTE
        ),
      },
      {
        name: SystemRole.VIEWER,
        description: 'Read-only access',
        priority: 10,
        permissions: allPermissions.filter(p => p.action === PermissionAction.READ),
      },
    ];

    for (const roleData of roleDefinitions) {
      const existing = await this.roleRepository.findOne({
        where: { name: roleData.name, type: RoleType.SYSTEM },
      });

      if (!existing) {
        const role = this.roleRepository.create({
          name: roleData.name,
          description: roleData.description,
          type: RoleType.SYSTEM,
          priority: roleData.priority,
          permissions: roleData.permissions,
          metadata: {
            canBeDeleted: false,
            canBeModified: false,
          },
        });
        await this.roleRepository.save(role);
      }
    }
  }
}
