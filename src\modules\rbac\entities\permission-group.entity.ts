import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  OneToMany,
} from 'typeorm';
import { PermissionGroupItem } from './permission-group-item.entity';
import { SystemModule } from './permission.entity';

/**
 * PermissionGroup Entity - Nhóm quyền hạn
 * PermissionGroup Entity - Permission groups
 */
@Entity('permission_groups')
export class PermissionGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên nhóm quyền (unique) - Group name (unique)
   */
  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  /**
   * Mô tả nhóm quyền - Group description
   */
  @Column({ type: 'text', nullable: true })
  description?: string;

  /**
   * Module liên quan - Related module
   */
  @Column({ type: 'varchar', length: 50 })
  module: SystemModule;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations

  /**
   * <PERSON><PERSON><PERSON> quyền trong nhóm - Permissions in group
   */
  @OneToMany(() => PermissionGroupItem, (groupItem) => groupItem.group)
  permissionGroupItems: PermissionGroupItem[];

  // Virtual properties

  /**
   * Lấy tên hiển thị - Get display name
   */
  get displayName(): string {
    return `${this.name} (${this.module})`;
  }
}
