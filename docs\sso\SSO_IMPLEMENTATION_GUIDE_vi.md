# Hướng Dẫn Triển <PERSON>hai Hệ Thống Single Sign-On (SSO)

## Tổng Quan

Hệ thống Single Sign-On (SSO) cho phép người dùng đăng nhập một lần và truy cập nhiều ứng dụng trên các subdomain khác nhau. Hệ thống được tích hợp hoàn toàn với RBAC và authentication hiện tại.

## 🚀 Tính Năng Chính

- **Cross-Subdomain Authentication**: Đ<PERSON>ng nhập một lần, truy cập tất cả subdomains
- **Session Management**: Quản lý phiên toàn cục với device fingerprinting
- **Token Revocation**: <PERSON><PERSON> thống blacklist JWT tokens với Redis cache
- **Device Security**: Phát hiện thiết bị đáng ngờ và xác minh thiết bị
- **Audit Logging**: Ghi log đầy đủ cho tất cả hoạt động SSO
- **Application Management**: <PERSON>u<PERSON>n lý các ứng dụng SSO được phép
- **RBAC Integration**: <PERSON><PERSON><PERSON> hợp hoàn toàn với hệ thống phân quyền

## 📋 Cấu Hình Hệ Thống

### 1. Environment Variables

```env
# SSO Configuration
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ISSUER=auth.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com

# Session Configuration
SSO_SESSION_TIMEOUT=480
SSO_MAX_CONCURRENT_SESSIONS=5
SSO_REQUIRE_DEVICE_VERIFICATION=false

# Security Configuration
SSO_ENABLE_AUDIT_LOGGING=true
SSO_TOKEN_REVOCATION_ENABLED=true
SSO_CROSS_DOMAIN_COOKIES=true
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax

# JWT Configuration (existing)
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=15m

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

### 2. Database Migration

Chạy migration để tạo bảng SSO:

```bash
npm run migration:run
```

### 3. Seed Data

Chạy seed để tạo dữ liệu mặc định:

```bash
npm run seed:sso
```

## 🏗️ Kiến Trúc Hệ Thống

### Database Schema

```sql
-- Bảng quản lý phiên đăng nhập
user_sessions (
  id, session_id, user_id, device_id, device_fingerprint,
  device_name, device_type, ip_address, user_agent, location,
  is_active, last_activity_at, expires_at, created_at, updated_at
)

-- Bảng quản lý ứng dụng SSO
sso_applications (
  id, name, subdomain, display_name, description,
  base_url, allowed_origins, is_active, created_at, updated_at
)

-- Bảng blacklist JWT tokens
jwt_blacklist (
  id, jti, user_id, session_id, token_type,
  expires_at, revoked_at, revoked_by, reason
)

-- Bảng audit logs
sso_audit_logs (
  id, user_id, session_id, application, action, resource,
  ip_address, user_agent, device_id, success, error_message,
  metadata, created_at
)
```

### JWT Payload Mở Rộng

```typescript
interface SSOJwtPayload extends JwtPayload {
  // Existing fields
  sub: string;
  email: string;
  username: string;
  roles: string[];
  permissions: string[];
  
  // SSO fields
  iss: string; // auth.yourcompany.com
  aud: string[]; // ['app.yourcompany.com', 'mail.yourcompany.com']
  sessionId: string; // Global session ID
  deviceId: string; // Device fingerprint
  jti: string; // JWT ID for revocation
  domain: string; // yourcompany.com
  ssoEnabled: boolean;
  applications: string[];
}
```

## 🔧 Sử Dụng API

### 1. SSO Login

```typescript
POST /auth/sso/login
{
  "userId": "user-uuid",
  "application": "app.yourcompany.com",
  "deviceName": "Chrome on Windows",
  "location": "Ho Chi Minh City, Vietnam",
  "rbacInfo": {
    "email": "<EMAIL>",
    "username": "user123",
    "role": "MANAGER",
    "roles": ["MANAGER", "USER"],
    "permissions": ["USER_MANAGEMENT_READ", "CONTENT_MANAGEMENT_CREATE"]
  }
}

Response:
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "sessionId": "sso_1703000000_abc123",
  "expiresIn": 900,
  "tokenType": "Bearer",
  "ssoEnabled": true,
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ]
}
```

### 2. Token Verification

```typescript
POST /auth/sso/verify
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "application": "mail.yourcompany.com"
}

Response:
{
  "valid": true,
  "payload": {
    "sub": "user-uuid",
    "sessionId": "sso_1703000000_abc123",
    "permissions": ["MAIL_MANAGEMENT_READ", "MAIL_MANAGEMENT_CREATE"],
    "aud": ["app.yourcompany.com", "mail.yourcompany.com"]
  },
  "sessionValid": true
}
```

### 3. Session Management

```typescript
// Lấy thông tin session hiện tại
GET /auth/sso/session
Authorization: Bearer <access_token>

// Lấy tất cả sessions của user
GET /auth/sso/sessions
Authorization: Bearer <access_token>

// Kết thúc session cụ thể
DELETE /auth/sso/sessions/{sessionId}
Authorization: Bearer <access_token>

// Kết thúc tất cả sessions khác
DELETE /auth/sso/sessions/all
Authorization: Bearer <access_token>
```

### 4. SSO Logout

```typescript
POST /auth/sso/logout
Authorization: Bearer <access_token>
{
  "globalLogout": true // Đăng xuất khỏi tất cả thiết bị
}

Response:
{
  "message": "Global logout successful",
  "sessionsTerminated": 3
}
```

## 🔒 Bảo Mật

### Cookie Configuration

```typescript
// Access Token Cookie
{
  domain: '.yourcompany.com',
  path: '/',
  httpOnly: true,
  secure: true,
  sameSite: 'lax',
  maxAge: 15 * 60 * 1000 // 15 minutes
}

// Refresh Token Cookie
{
  domain: '.yourcompany.com',
  path: '/',
  httpOnly: true,
  secure: true,
  sameSite: 'lax',
  maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
}
```

### Device Fingerprinting

```typescript
const deviceInfo = {
  userAgent: req.headers['user-agent'],
  ipAddress: req.ip,
  acceptLanguage: req.headers['accept-language'],
  acceptEncoding: req.headers['accept-encoding'],
  viewport: { width: 1920, height: 1080 },
  timezone: 'Asia/Ho_Chi_Minh',
  platform: 'Win32',
  cookieEnabled: true,
  javaEnabled: false
};

const fingerprint = deviceFingerprintService.generateFingerprint(deviceInfo);
```

### Token Revocation

```typescript
// Thu hồi token cụ thể
await jwtBlacklistService.revokeToken({
  jti: 'token-id',
  userId: 'user-uuid',
  tokenType: TokenType.ACCESS,
  expiresAt: new Date(),
  reason: RevocationReason.USER_LOGOUT,
  sessionId: 'session-id'
});

// Thu hồi tất cả tokens của user
await jwtBlacklistService.revokeAllUserTokens(
  'user-uuid',
  RevocationReason.GLOBAL_LOGOUT,
  'admin-user-id'
);
```

## 🎯 Tích Hợp Frontend

### 1. Cookie-Based Authentication

```javascript
// Frontend không cần xử lý token manually
// Cookies được set tự động bởi server

// Kiểm tra trạng thái đăng nhập
fetch('/auth/sso/session', {
  credentials: 'include' // Quan trọng: gửi cookies
})
.then(response => {
  if (response.ok) {
    // User đã đăng nhập
    return response.json();
  } else {
    // Redirect to login
    window.location.href = '/login';
  }
});
```

### 2. Cross-Domain Requests

```javascript
// Từ mail.yourcompany.com gọi API app.yourcompany.com
fetch('https://app.yourcompany.com/api/users', {
  credentials: 'include', // Gửi SSO cookies
  headers: {
    'Content-Type': 'application/json',
    'X-Application': 'mail.yourcompany.com'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### 3. Global Logout

```javascript
// Đăng xuất khỏi tất cả thiết bị
fetch('/auth/sso/logout', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    globalLogout: true
  })
})
.then(() => {
  // Redirect to login page
  window.location.href = '/login';
});
```

## 📊 Monitoring và Analytics

### 1. Audit Logs

```typescript
// Lấy logs bảo mật
GET /auth/sso/audit/security
Authorization: Bearer <admin_token>

// Lấy thống kê đăng nhập
GET /auth/sso/audit/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer <admin_token>
```

### 2. Session Statistics

```typescript
// Thống kê sessions
GET /auth/sso/sessions/stats
Authorization: Bearer <admin_token>

Response:
{
  "totalActiveSessions": 1250,
  "deviceTypeBreakdown": {
    "DESKTOP": 800,
    "MOBILE": 350,
    "TABLET": 100
  },
  "applicationBreakdown": {
    "app.yourcompany.com": 600,
    "mail.yourcompany.com": 400,
    "core.yourcompany.com": 250
  }
}
```

### 3. Blacklist Statistics

```typescript
GET /auth/sso/blacklist/stats
Authorization: Bearer <admin_token>

Response:
{
  "totalBlacklisted": 150,
  "byTokenType": {
    "ACCESS": 120,
    "REFRESH": 30
  },
  "byReason": {
    "USER_LOGOUT": 80,
    "GLOBAL_LOGOUT": 40,
    "SECURITY_BREACH": 20,
    "ADMIN_REVOKE": 10
  },
  "recentRevocations": 25,
  "expiredTokens": 50
}
```

## 🔧 Troubleshooting

### Common Issues

**1. Cookies không được set:**
- Kiểm tra `SSO_COOKIE_DOMAIN` configuration
- Đảm bảo `credentials: 'include'` trong fetch requests
- Kiểm tra CORS configuration

**2. Token verification fails:**
- Kiểm tra JWT secret consistency
- Verify token không bị blacklisted
- Kiểm tra audience (aud) claim

**3. Session expired:**
- Kiểm tra `SSO_SESSION_TIMEOUT` configuration
- Verify session cleanup job đang chạy
- Kiểm tra Redis connection

**4. Device fingerprint mismatch:**
- Kiểm tra `SSO_REQUIRE_DEVICE_VERIFICATION` setting
- Review device fingerprinting logic
- Check for browser/device changes

### Debug Commands

```bash
# Kiểm tra SSO configuration
curl -X GET http://localhost:3000/auth/sso/config

# Verify token
curl -X POST http://localhost:3000/auth/sso/verify \
  -H "Content-Type: application/json" \
  -d '{"token":"your-jwt-token"}'

# Check session
curl -X GET http://localhost:3000/auth/sso/session \
  -H "Authorization: Bearer your-jwt-token"

# Cleanup expired sessions
curl -X POST http://localhost:3000/auth/sso/cleanup/sessions \
  -H "Authorization: Bearer admin-token"
```

## 📈 Performance Optimization

### 1. Redis Caching

```typescript
// Cache configuration
const CACHE_TTL = 15 * 60; // 15 minutes
const CACHE_PREFIX = 'sso:';

// Cache keys
user_permissions:${userId}
jwt_blacklist:${jti}
session:${sessionId}
device_fingerprint:${deviceId}
```

### 2. Database Indexing

```sql
-- Performance indexes
CREATE INDEX idx_user_sessions_user_active ON user_sessions(user_id, is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_jwt_blacklist_jti ON jwt_blacklist(jti);
CREATE INDEX idx_jwt_blacklist_expires ON jwt_blacklist(expires_at);
CREATE INDEX idx_sso_audit_created ON sso_audit_logs(created_at);
```

### 3. Cleanup Jobs

```typescript
// Scheduled cleanup (chạy mỗi giờ)
@Cron('0 0 * * * *')
async cleanupExpiredData() {
  const expiredSessions = await sessionService.cleanupExpiredSessions();
  const expiredTokens = await blacklistService.cleanupExpiredEntries();
  const oldLogs = await auditService.cleanupOldLogs(90); // 90 days retention
  
  logger.log(`Cleanup completed: ${expiredSessions} sessions, ${expiredTokens} tokens, ${oldLogs} logs`);
}
```

---

**Để biết thêm chi tiết, tham khảo [API Documentation](./SSO_API_DOCUMENTATION_vi.md) và [User Guide](./SSO_USER_GUIDE_vi.md).**
