/**
 * Interface cho cache configuration
 * Interface for cache configuration
 */
export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  ttl: number;
  max: number;
  connectTimeout: number;
  commandTimeout: number;
}

/**
 * Interface cho cache key patterns
 * Interface for cache key patterns
 */
export interface CacheKeyPatterns {
  // SSO related cache keys
  SSO_SESSION: string;
  SSO_JWT_BLACKLIST: string;
  SSO_DEVICE_FINGERPRINT: string;
  SSO_APPLICATION: string;
  
  // RBAC related cache keys
  USER_PERMISSIONS: string;
  USER_ROLES: string;
  ROLE_PERMISSIONS: string;
  
  // General cache keys
  USER_PROFILE: string;
  AI_RESPONSE: string;
}

/**
 * Cache key patterns constants
 * Hằng số cho cache key patterns
 */
export const CACHE_KEYS: CacheKeyPatterns = {
  // SSO patterns
  SSO_SESSION: 'sso:session:',
  SSO_JWT_BLACKLIST: 'sso:jwt_blacklist:',
  SSO_DEVICE_FINGERPRINT: 'sso:device:',
  SSO_APPLICATION: 'sso:app:',
  
  // RBAC patterns
  USER_PERMISSIONS: 'rbac:user_permissions:',
  USER_ROLES: 'rbac:user_roles:',
  ROLE_PERMISSIONS: 'rbac:role_permissions:',
  
  // General patterns
  USER_PROFILE: 'user:profile:',
  AI_RESPONSE: 'ai:response:',
};

/**
 * Interface cho cache operation result
 * Interface for cache operation result
 */
export interface CacheOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Interface cho cache metrics
 * Interface for cache metrics
 */
export interface CacheMetrics {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  hitRate: number;
  missRate: number;
  averageResponseTime: number;
}

/**
 * Enum cho cache operation types
 * Enum for cache operation types
 */
export enum CacheOperation {
  GET = 'GET',
  SET = 'SET',
  DEL = 'DELETE',
  EXISTS = 'EXISTS',
  EXPIRE = 'EXPIRE',
  CLEAR = 'CLEAR',
  PATTERN_DELETE = 'PATTERN_DELETE',
}

/**
 * Interface cho cache event
 * Interface for cache event
 */
export interface CacheEvent {
  operation: CacheOperation;
  key: string;
  success: boolean;
  timestamp: Date;
  duration: number;
  error?: string;
}

/**
 * Type cho cache value serializer
 * Type for cache value serializer
 */
export type CacheSerializer<T> = {
  serialize: (value: T) => string;
  deserialize: (value: string) => T;
};

/**
 * Interface cho cache middleware options
 * Interface for cache middleware options
 */
export interface CacheMiddlewareOptions {
  ttl?: number;
  keyGenerator?: (req: any) => string;
  condition?: (req: any) => boolean;
  skipCache?: (req: any) => boolean;
}

/**
 * Interface cho distributed cache
 * Interface for distributed cache
 */
export interface DistributedCacheOptions {
  nodes: string[];
  password?: string;
  keyPrefix?: string;
  retryDelayOnFailover?: number;
  enableOfflineQueue?: boolean;
}

/**
 * Cache decorator options
 * Tùy chọn cho cache decorator
 */
export interface CacheDecoratorOptions {
  key?: string;
  ttl?: number;
  prefix?: string;
  condition?: (...args: any[]) => boolean;
}

/**
 * Cache invalidation options
 * Tùy chọn cho cache invalidation
 */
export interface CacheInvalidationOptions {
  pattern?: string;
  keys?: string[];
  tags?: string[];
  immediate?: boolean;
}

/**
 * Cache warming options
 * Tùy chọn cho cache warming
 */
export interface CacheWarmingOptions {
  keys: string[];
  batchSize?: number;
  delay?: number;
  retryOnFailure?: boolean;
}

/**
 * Cache compression options
 * Tùy chọn cho cache compression
 */
export interface CacheCompressionOptions {
  enabled: boolean;
  algorithm?: 'gzip' | 'deflate' | 'brotli';
  threshold?: number; // Minimum size in bytes to compress
}

/**
 * Cache monitoring options
 * Tùy chọn cho cache monitoring
 */
export interface CacheMonitoringOptions {
  enabled: boolean;
  metricsInterval?: number;
  alertThresholds?: {
    hitRate?: number;
    responseTime?: number;
    errorRate?: number;
  };
}
