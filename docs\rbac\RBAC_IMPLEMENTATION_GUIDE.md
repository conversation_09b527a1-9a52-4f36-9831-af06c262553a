# RBAC System Implementation Guide

## Overview

The RBAC (Role-Based Access Control) system provides role-based access control with the following features:

- **Role Hierarchy**: Hierarchical system from MASTER_ACCOUNT to GUEST
- **Granular Permissions**: 8 permission types (READ, CREATE, UPDATE, DELETE, EXPORT, APPROVE, MANAGE, ASSIGN)
- **10 System Modules**: From user management to AI features
- **Permission Inheritance**: Higher roles inherit permissions from lower roles
- **Performance Caching**: Redis cache for permissions with 15-minute TTL
- **JWT Integration**: RBAC information embedded in access tokens

## Database Structure

### Main Tables

```sql
-- Roles table
CREATE TABLE roles (
  id UUID PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  display_name VARCHAR(200) NOT NULL,
  description TEXT,
  level INTEGER NOT NULL,
  parent_role_id UUID REFERENCES roles(id),
  is_system_role BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Permissions table
CREATE TABLE permissions (
  id UUID PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  module VARCHAR(50) NOT NULL,
  action VARCHAR(20) NOT NULL,
  resource VARCHAR(100),
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Role-permission mapping table
CREATE TABLE role_permissions (
  id UUID PRIMARY KEY,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  granted_by UUID REFERENCES users(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);

-- User role assignment table
CREATE TABLE user_roles (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(user_id, role_id)
);
```

### Default Data

The system is initialized with 12 roles and 80 permissions (8 actions × 10 modules):

**System Roles:**
- MASTER_ACCOUNT (Level 0) - Highest authority
- ADMIN (Level 1) - Administrator
- MANAGER (Level 2) - Manager
- VIEWER (Level 5) - Viewer
- GUEST (Level 6) - Guest

**Specialized Roles:**
- MARKETING_LEAD/STAFF (Level 3-4)
- SALES_LEAD/STAFF (Level 3-4)
- CONTENT_LEAD/EDITOR (Level 3-4)
- ANALYST (Level 3)

## API Endpoints

### Role Management

```typescript
// Create new role
POST /api/roles
{
  "name": "CUSTOM_ROLE",
  "displayName": "Custom Role",
  "description": "Custom role description",
  "level": 4,
  "parentRoleId": "uuid-parent-role"
}

// Get roles list
GET /api/roles?search=marketing&level=3&page=1&limit=10

// Update role
PUT /api/roles/:id
{
  "displayName": "Updated Role Name",
  "description": "New description"
}

// Delete role
DELETE /api/roles/:id

// Assign permissions to role
POST /api/roles/:id/permissions
{
  "permissionIds": ["uuid1", "uuid2"],
  "replace": false
}

// Revoke permissions from role
DELETE /api/roles/:id/permissions
{
  "permissionIds": ["uuid1", "uuid2"]
}
```

### Permission Management

```typescript
// Create new permission
POST /api/permissions
{
  "module": "USER_MANAGEMENT",
  "action": "READ",
  "resource": "profile",
  "description": "Permission to view user profile"
}

// Get permissions by module
GET /api/permissions/module/USER_MANAGEMENT

// Check user permission
POST /api/permissions/check
{
  "userId": "uuid-user",
  "permission": "USER_MANAGEMENT_READ",
  "resource": "user:123"
}

// Create default permissions for module
POST /api/permissions/module/CONTENT_MANAGEMENT/defaults
```

### Role Assignment

```typescript
// Assign role to user
POST /api/roles/assign
{
  "userId": "uuid-user",
  "roleId": "uuid-role",
  "expiresAt": "2024-12-31T23:59:59.999Z"
}

// Revoke role from user
POST /api/roles/revoke
{
  "userId": "uuid-user",
  "roleId": "uuid-role"
}
```

## Using Decorators

### Basic Decorators

```typescript
import { 
  RequirePermission, 
  RequireRole, 
  RequireRoleLevel,
  RBAC,
  AdminOnly,
  ManagerOnly 
} from '../rbac/decorators/rbac.decorators';

// Require specific permission
@RequirePermission('USER_MANAGEMENT_READ')
@Get('/users')
async getUsers() {
  return this.usersService.findAll();
}

// Require specific role
@RequireRole('ADMIN')
@Delete('/users/:id')
async deleteUser(@Param('id') id: string) {
  return this.usersService.delete(id);
}

// Require role level
@RequireRoleLevel(2) // Manager and above
@Post('/approve')
async approveContent() {
  // Approval logic
}

// Combined decorator
@RBAC({
  permission: 'CONTENT_MANAGEMENT_APPROVE',
  roleLevel: 2,
  description: 'Approve content'
})
@Post('/content/approve')
async approveContent() {
  // Approval logic
}
```

### Specialized Decorators

```typescript
// Admin only
@AdminOnly()
@Get('/system/settings')
async getSystemSettings() {
  return this.settingsService.getAll();
}

// Manager and above only
@ManagerOnly()
@Get('/reports/financial')
async getFinancialReports() {
  return this.reportsService.getFinancial();
}

// Module-specific decorators
@UserManagement.Create()
@Post('/users')
async createUser(@Body() createUserDto: CreateUserDto) {
  return this.usersService.create(createUserDto);
}

@ContentManagement.Approve()
@Post('/content/:id/approve')
async approveContent(@Param('id') id: string) {
  return this.contentService.approve(id);
}
```

## Authentication Integration

### Extended JWT Payload

```typescript
interface JwtPayload {
  sub: string;
  email: string;
  username: string;
  role: string;
  sessionToken?: string;
  // RBAC fields
  roles?: string[];
  permissions?: string[];
  roleLevel?: number;
  isMasterAccount?: boolean;
  permissionVersion?: number;
}
```

### Middleware Integration

```typescript
// In controller
@UseGuards(JwtAuthGuard, RBACGuard)
@RequirePermission('USER_MANAGEMENT_READ')
@Get('/users')
async getUsers(@Request() req) {
  // req.user contains RBAC information
  const userLevel = req.user.roleLevel;
  const permissions = req.user.permissions;
  
  return this.usersService.findAll();
}
```

## Cache Strategy

### Redis Cache Configuration

```typescript
// Cache key pattern
const CACHE_KEY = `user_permissions:${userId}`;

// Cache structure
interface UserPermissionCache {
  userId: string;
  permissions: string[];
  roles: string[];
  roleLevel: number;
  isMasterAccount: boolean;
  cachedAt: Date;
  expiresAt: Date;
}

// TTL: 15 minutes
const CACHE_TTL = 15 * 60; // seconds
```

### Cache Invalidation

```typescript
// Invalidate when role/permission changes
await this.rbacService.invalidateUserCache(userId);

// Invalidate all caches
await this.rbacService.invalidateAllCaches();
```

## Migration and Seeding

### Run Migration

```bash
# Create RBAC tables
npm run migration:run

# Seed default data
npm run seed:rbac
```

### Custom Seeding

```typescript
// Create custom role
const customRole = await this.roleService.createRole({
  name: 'CUSTOM_MANAGER',
  displayName: 'Custom Manager',
  level: 3,
  parentRoleId: managerRole.id
}, createdBy);

// Assign permissions to role
await this.roleService.assignPermissionsToRole(
  customRole.id,
  {
    permissionIds: [permission1.id, permission2.id],
    replace: false
  },
  createdBy
);
```

## Error Handling

### Common Errors

```typescript
// 403 Forbidden - Insufficient permissions
{
  "statusCode": 403,
  "message": "User lacks required permission: USER_MANAGEMENT_DELETE",
  "error": "Forbidden"
}

// 404 Not Found - Role/permission not found
{
  "statusCode": 404,
  "message": "Role not found",
  "error": "Not Found"
}

// 409 Conflict - Role name already exists
{
  "statusCode": 409,
  "message": "Role name already exists",
  "error": "Conflict"
}
```

### Error Handling Best Practices

```typescript
try {
  await this.rbacService.assignRoleToUser(userId, roleId, assignedBy);
} catch (error) {
  if (error instanceof ForbiddenException) {
    // Handle permission errors
    return { error: 'Insufficient permissions' };
  }
  if (error instanceof NotFoundException) {
    // Handle not found errors
    return { error: 'Resource not found' };
  }
  // Handle other errors
  throw error;
}
```

## Performance Optimization

### Database Indexing

```sql
-- Important indexes
CREATE INDEX idx_roles_level ON roles(level);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_active ON user_roles(is_active);
CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);
```

### Query Optimization

```typescript
// Use eager loading
const roles = await this.roleRepository.find({
  relations: ['rolePermissions', 'rolePermissions.permission'],
  where: { isActive: true }
});

// Batch operations
const userRoles = await this.userRoleRepository.find({
  where: { userId: In(userIds), isActive: true },
  relations: ['role']
});
```

## Security Considerations

### Best Practices

1. **Principle of Least Privilege**: Grant only minimum necessary permissions
2. **Regular Audit**: Periodically review and cleanup permissions
3. **Role Expiration**: Use expires_at for temporary roles
4. **Monitoring**: Log all permission changes
5. **Rate Limiting**: Limit API calls for role management

### Audit Logging

```typescript
// Log permission changes
this.logger.log(`Role ${roleName} assigned to user ${userId} by ${assignedBy}`, {
  action: 'ROLE_ASSIGNED',
  userId,
  roleId,
  assignedBy,
  timestamp: new Date(),
  ipAddress: req.ip
});
```

## Testing

### Unit Tests

```typescript
describe('RBACService', () => {
  it('should check user permission correctly', async () => {
    const result = await rbacService.checkPermission(
      userId, 
      'USER_MANAGEMENT_READ'
    );
    
    expect(result.allowed).toBe(true);
    expect(result.reason).toContain('User has required permission');
  });
});
```

### Integration Tests

```typescript
describe('Role Assignment', () => {
  it('should assign role to user successfully', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/roles/assign')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        userId: testUser.id,
        roleId: managerRole.id
      })
      .expect(200);
      
    expect(response.body.message).toBe('Role assigned successfully');
  });
});
```

## Troubleshooting

### Common Issues

**Permission denied errors:**
- Check user has required role/permission
- Verify role is active and not expired
- Check role hierarchy and inheritance

**Cache issues:**
- Clear Redis cache: `redis-cli FLUSHDB`
- Restart application to refresh cache
- Check cache TTL configuration

**Performance issues:**
- Monitor database query performance
- Check Redis connection
- Review permission calculation logic

---

**For more details, refer to [API Documentation](./RBAC_API_DOCUMENTATION.md) and [User Guide](./RBAC_USER_GUIDE.md).**
