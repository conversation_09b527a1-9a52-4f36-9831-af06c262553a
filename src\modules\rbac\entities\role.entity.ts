import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { RolePermission } from './role-permission.entity';
import { UserRole } from './user-role.entity';
import { RoleHierarchy } from './role-hierarchy.entity';

/**
 * Role Entity - Định nghĩa vai trò trong hệ thống RBAC
 * Role Entity - Defines roles in the RBAC system
 */
@Entity('roles')
@Index(['level'])
@Index(['parentRoleId'])
export class Role {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên vai trò (unique) - Role name (unique)
   * VD: ADMIN, MANAGER, MARKETING_LEAD
   */
  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  /**
   * Tên hiển thị - Display name
   * VD: "<PERSON><PERSON>ản trị viên", "Administrator"
   */
  @Column({ type: 'varchar', length: 200, name: 'display_name' })
  displayName: string;

  /**
   * Mô tả vai trò - Role description
   */
  @Column({ type: 'text', nullable: true })
  description?: string;

  /**
   * Cấp độ phân cấp (0 = cao nhất) - Hierarchy level (0 = highest)
   */
  @Column({ type: 'int' })
  level: number;

  /**
   * ID vai trò cha - Parent role ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'parent_role_id' })
  parentRoleId?: string;

  /**
   * Vai trò hệ thống (không thể xóa) - System role (cannot be deleted)
   */
  @Column({ type: 'boolean', default: false, name: 'is_system_role' })
  isSystemRole: boolean;

  /**
   * Trạng thái hoạt động - Active status
   */
  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  /**
   * ID người tạo - Creator ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'created_by' })
  createdBy?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations

  /**
   * Vai trò cha - Parent role
   */
  @ManyToOne(() => Role, (role) => role.childRoles, { nullable: true })
  @JoinColumn({ name: 'parent_role_id' })
  parentRole?: Role;

  /**
   * Các vai trò con - Child roles
   */
  @OneToMany(() => Role, (role) => role.parentRole)
  childRoles: Role[];

  /**
   * Người tạo vai trò - Role creator
   */
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  /**
   * Quyền hạn của vai trò - Role permissions
   */
  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];

  /**
   * Người dùng có vai trò này - Users with this role
   */
  @OneToMany(() => UserRole, (userRole) => userRole.role)
  userRoles: UserRole[];

  /**
   * Phân cấp vai trò (là cha) - Role hierarchy (as parent)
   */
  @OneToMany(() => RoleHierarchy, (hierarchy) => hierarchy.parentRole)
  parentHierarchies: RoleHierarchy[];

  /**
   * Phân cấp vai trò (là con) - Role hierarchy (as child)
   */
  @OneToMany(() => RoleHierarchy, (hierarchy) => hierarchy.childRole)
  childHierarchies: RoleHierarchy[];

  // Virtual properties

  /**
   * Kiểm tra có phải vai trò master không - Check if master role
   */
  get isMasterRole(): boolean {
    return this.name === 'MASTER_ACCOUNT';
  }

  /**
   * Kiểm tra có phải vai trò admin không - Check if admin role
   */
  get isAdminRole(): boolean {
    return this.name === 'ADMIN';
  }

  /**
   * Lấy tên hiển thị đầy đủ - Get full display name
   */
  get fullDisplayName(): string {
    return `${this.displayName} (${this.name})`;
  }

  /**
   * Kiểm tra có thể xóa không - Check if deletable
   */
  get isDeletable(): boolean {
    return !this.isSystemRole && !this.isMasterRole;
  }

  /**
   * Kiểm tra có thể chỉnh sửa không - Check if editable
   */
  get isEditable(): boolean {
    return !this.isMasterRole;
  }
}
