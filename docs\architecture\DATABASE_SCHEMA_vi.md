# 🗄️ Lược Đồ Cơ Sở Dữ Liệu & <PERSON><PERSON><PERSON>uan Hệ

## 📋 Tổng Quan

Delify Platform sử dụng **PostgreSQL** với **TypeORM** để quản lý lược đồ cơ sở dữ liệu. Thiết kế cơ sở dữ liệu theo **cấu trúc chuẩn hóa** với mối quan hệ rõ ràng và tối ưu hóa indexing.

## 🏗️ Kiến Trúc Cơ Sở Dữ Liệu

```
┌─────────────────────────────────────────────────────────────┐
│                    Lược Đồ Cơ Sở Dữ Liệu                   │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │    Users    │────│Organization │────│    Teams    │     │
│  │             │    │   Members   │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         │            ┌─────────────┐            │          │
│         │────────────│Organization │────────────│          │
│         │            │             │            │          │
│         │            └─────────────┘            │          │
│         │                   │                   │          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  Sessions   │    │ Invitations │    │    Roles    │     │
│  │             │    │             │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ AI Models   │    │Chat Sessions│    │Permissions  │     │
│  │             │    │             │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

## 📊 Bảng Cốt Lõi

### 1. Bảng Users
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    account_type account_type_enum DEFAULT 'personal',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    email_verified_at TIMESTAMP,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_account_type ON users(account_type);
CREATE INDEX idx_users_is_active ON users(is_active);
```

### 2. Bảng Organizations
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    type organization_type_enum DEFAULT 'company',
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_type ON organizations(type);
CREATE INDEX idx_organizations_is_active ON organizations(is_active);
CREATE INDEX idx_organizations_settings ON organizations USING GIN(settings);
```

### 3. Bảng Organization Members
```sql
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    role member_role_enum DEFAULT 'member',
    is_active BOOLEAN DEFAULT true,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, organization_id)
);

-- Indexes
CREATE INDEX idx_org_members_user_id ON organization_members(user_id);
CREATE INDEX idx_org_members_org_id ON organization_members(organization_id);
CREATE INDEX idx_org_members_role ON organization_members(role);
CREATE INDEX idx_org_members_is_active ON organization_members(is_active);
```

### 4. Bảng Teams
```sql
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    lead_id UUID REFERENCES organization_members(id),
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_teams_name ON teams(name);
CREATE INDEX idx_teams_org_id ON teams(organization_id);
CREATE INDEX idx_teams_lead_id ON teams(lead_id);
CREATE INDEX idx_teams_is_active ON teams(is_active);
```

### 5. Bảng Liên Kết Team Members
```sql
CREATE TABLE team_members (
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES organization_members(id) ON DELETE CASCADE,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY(team_id, member_id)
);

-- Indexes
CREATE INDEX idx_team_members_team_id ON team_members(team_id);
CREATE INDEX idx_team_members_member_id ON team_members(member_id);
```

## 🔐 Bảng Xác Thực

### 6. Bảng Sessions
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token VARCHAR(500) UNIQUE NOT NULL,
    device_info JSONB,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_refresh_token ON sessions(refresh_token);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_sessions_is_active ON sessions(is_active);
```

### 7. Bảng Invitations
```sql
CREATE TABLE invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role member_role_enum DEFAULT 'member',
    token VARCHAR(255) UNIQUE NOT NULL,
    message TEXT,
    invited_by_id UUID NOT NULL REFERENCES users(id),
    status invitation_status_enum DEFAULT 'pending',
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_invitations_org_id ON invitations(organization_id);
CREATE INDEX idx_invitations_email ON invitations(email);
CREATE INDEX idx_invitations_token ON invitations(token);
CREATE INDEX idx_invitations_status ON invitations(status);
CREATE INDEX idx_invitations_expires_at ON invitations(expires_at);
```

## 🤖 Bảng Hệ Thống AI

### 8. Bảng AI Models
```sql
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider ai_provider_enum NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    description TEXT,
    capabilities JSONB,
    pricing JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(provider, model_name)
);

-- Indexes
CREATE INDEX idx_ai_models_provider ON ai_models(provider);
CREATE INDEX idx_ai_models_is_active ON ai_models(is_active);
CREATE INDEX idx_ai_models_capabilities ON ai_models USING GIN(capabilities);
```

### 9. Bảng Chat Sessions
```sql
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    title VARCHAR(255),
    model_id UUID REFERENCES ai_models(id),
    settings JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX idx_chat_sessions_org_id ON chat_sessions(organization_id);
CREATE INDEX idx_chat_sessions_model_id ON chat_sessions(model_id);
CREATE INDEX idx_chat_sessions_is_active ON chat_sessions(is_active);
```

### 10. Bảng CV Analysis
```sql
CREATE TABLE cv_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    file_path VARCHAR(500),
    analysis_result JSONB,
    score INTEGER,
    model_used VARCHAR(255),
    processing_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_cv_analysis_user_id ON cv_analysis(user_id);
CREATE INDEX idx_cv_analysis_org_id ON cv_analysis(organization_id);
CREATE INDEX idx_cv_analysis_score ON cv_analysis(score);
CREATE INDEX idx_cv_analysis_created_at ON cv_analysis(created_at);
```

## 🔑 Bảng Hệ Thống Quyền Hạn

### 11. Bảng Roles
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    level INTEGER NOT NULL,
    is_system_role BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_level ON roles(level);
CREATE INDEX idx_roles_is_system_role ON roles(is_system_role);
```

### 12. Bảng Permissions
```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(100),
    action VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_permissions_name ON permissions(name);
CREATE INDEX idx_permissions_resource ON permissions(resource);
CREATE INDEX idx_permissions_action ON permissions(action);
```

### 13. Bảng Liên Kết Member Permissions
```sql
CREATE TABLE organization_member_permissions (
    member_id UUID NOT NULL REFERENCES organization_members(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by_id UUID REFERENCES users(id),
    
    PRIMARY KEY(member_id, permission_id)
);

-- Indexes
CREATE INDEX idx_member_permissions_member_id ON organization_member_permissions(member_id);
CREATE INDEX idx_member_permissions_permission_id ON organization_member_permissions(permission_id);
```

## 📈 Enums

### Enums Cơ Sở Dữ Liệu
```sql
-- Loại tài khoản
CREATE TYPE account_type_enum AS ENUM ('personal', 'business');

-- Loại tổ chức
CREATE TYPE organization_type_enum AS ENUM ('company', 'agency', 'freelancer', 'nonprofit');

-- Vai trò thành viên
CREATE TYPE member_role_enum AS ENUM ('owner', 'admin', 'manager', 'member', 'viewer');

-- Trạng thái lời mời
CREATE TYPE invitation_status_enum AS ENUM ('pending', 'accepted', 'declined', 'expired');

-- Nhà cung cấp AI
CREATE TYPE ai_provider_enum AS ENUM ('openai', 'grok', 'gemini', 'ollama', 'anthropic');
```

## 🔗 Mối Quan Hệ Entity

### Mối Quan Hệ TypeORM Entity
```typescript
// User -> Organization Members (One-to-Many)
@OneToMany(() => OrganizationMember, member => member.user)
organizationMemberships: OrganizationMember[];

// Organization -> Members (One-to-Many)
@OneToMany(() => OrganizationMember, member => member.organization)
members: OrganizationMember[];

// Organization -> Teams (One-to-Many)
@OneToMany(() => Team, team => team.organization)
teams: Team[];

// Team -> Members (Many-to-Many through junction table)
@ManyToMany(() => OrganizationMember)
@JoinTable({
  name: 'team_members',
  joinColumn: { name: 'team_id' },
  inverseJoinColumn: { name: 'member_id' },
})
members: OrganizationMember[];

// User -> Sessions (One-to-Many)
@OneToMany(() => Session, session => session.user)
sessions: Session[];

// User -> Chat Sessions (One-to-Many)
@OneToMany(() => ChatSession, session => session.user)
chatSessions: ChatSession[];
```

## 🚀 Migration Cơ Sở Dữ Liệu

### Cấu Trúc Migration
```typescript
// Ví dụ migration: CreateUsersTable
export class CreateUsersTable1640995200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum types
    await queryRunner.query(`
      CREATE TYPE account_type_enum AS ENUM ('personal', 'business')
    `);

    // Tạo bảng users
    await queryRunner.query(`
      CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100),
        last_name VARCHAR(100),
        account_type account_type_enum DEFAULT 'personal',
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        email_verified_at TIMESTAMP,
        last_login_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Tạo indexes
    await queryRunner.query(`
      CREATE INDEX idx_users_email ON users(email)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE users`);
    await queryRunner.query(`DROP TYPE account_type_enum`);
  }
}
```

## 📊 Tối Ưu Cơ Sở Dữ Liệu

### Chiến Lược Indexing
```sql
-- Primary indexes (tự động tạo)
-- Tất cả primary keys có unique indexes

-- Foreign key indexes
CREATE INDEX idx_org_members_user_id ON organization_members(user_id);
CREATE INDEX idx_org_members_org_id ON organization_members(organization_id);
CREATE INDEX idx_teams_org_id ON teams(organization_id);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);

-- Query optimization indexes
CREATE INDEX idx_users_email_active ON users(email, is_active);
CREATE INDEX idx_org_members_org_role ON organization_members(organization_id, role);
CREATE INDEX idx_invitations_email_status ON invitations(email, status);

-- JSON indexes cho cột JSONB
CREATE INDEX idx_organizations_settings ON organizations USING GIN(settings);
CREATE INDEX idx_ai_models_capabilities ON ai_models USING GIN(capabilities);

-- Partial indexes cho bản ghi hoạt động
CREATE INDEX idx_active_organizations ON organizations(id) WHERE is_active = true;
CREATE INDEX idx_active_teams ON teams(id) WHERE is_active = true;
```

### Hiệu Suất Query
```sql
-- Truy vấn hiệu quả với indexing phù hợp

-- Lấy tổ chức của user với vai trò
SELECT o.*, om.role
FROM organizations o
JOIN organization_members om ON o.id = om.organization_id
WHERE om.user_id = $1 AND om.is_active = true AND o.is_active = true;

-- Lấy thành viên team với chi tiết user
SELECT u.first_name, u.last_name, u.email, om.role
FROM teams t
JOIN team_members tm ON t.id = tm.team_id
JOIN organization_members om ON tm.member_id = om.id
JOIN users u ON om.user_id = u.id
WHERE t.id = $1 AND t.is_active = true;

-- Lấy quyền hạn user trong tổ chức
SELECT DISTINCT p.name
FROM organization_members om
JOIN organization_member_permissions omp ON om.id = omp.member_id
JOIN permissions p ON omp.permission_id = p.id
WHERE om.user_id = $1 AND om.organization_id = $2 AND om.is_active = true;
```

## 🔧 Cấu Hình Cơ Sở Dữ Liệu

### Cấu Hình TypeORM
```typescript
// ormconfig.ts
export default {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  synchronize: false, // Luôn false trong production
  logging: process.env.NODE_ENV === 'development',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  extra: {
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
};
```

### Cài Đặt Connection Pool
```typescript
// Cấu hình Database module
@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DATABASE_HOST'),
        port: configService.get('DATABASE_PORT'),
        username: configService.get('DATABASE_USERNAME'),
        password: configService.get('DATABASE_PASSWORD'),
        database: configService.get('DATABASE_NAME'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false,
        logging: configService.get('NODE_ENV') === 'development',
        extra: {
          max: 20, // Số kết nối tối đa trong pool
          min: 5,  // Số kết nối tối thiểu trong pool
          idle: 10000, // Thời gian idle trước khi đóng kết nối
          acquire: 30000, // Thời gian chờ tối đa để lấy kết nối
          evict: 1000, // Khoảng thời gian kiểm tra kết nối idle
        },
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
```

## 🔍 Monitoring & Maintenance

### Database Health Checks
```sql
-- Kiểm tra kích thước database
SELECT
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Kiểm tra index usage
SELECT
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- Kiểm tra slow queries
SELECT
  query,
  calls,
  total_time,
  mean_time,
  rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### Backup Strategy
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/postgresql"
DB_NAME="delify_production"

# Create backup
pg_dump -h localhost -U postgres -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

## 🎯 Best Practices

### Schema Design Principles
1. **Normalization**: Tránh dữ liệu trùng lặp
2. **Indexing**: Index các cột được query thường xuyên
3. **Constraints**: Sử dụng foreign keys và check constraints
4. **Data Types**: Chọn data types phù hợp
5. **Naming Convention**: Sử dụng naming convention nhất quán

### Performance Guidelines
1. **Query Optimization**: Sử dụng EXPLAIN ANALYZE
2. **Connection Pooling**: Cấu hình pool size phù hợp
3. **Monitoring**: Theo dõi slow queries và index usage
4. **Maintenance**: Thực hiện VACUUM và ANALYZE thường xuyên
5. **Backup**: Backup thường xuyên và test restore

**Lược đồ cơ sở dữ liệu này cung cấp nền tảng vững chắc cho ứng dụng enterprise với khả năng mở rộng và hiệu suất cao.** 🗄️✨
