# 🚀 API Development Guide

## 📋 Tổng quan

Hướng dẫn chi tiết để **phát triển RESTful APIs** trong Delify Platform với NestJS, bao gồm authentication, validation, documentation, và best practices.

## 🎯 API Design Principles

### RESTful Design
```
Resource-Based URLs:
✅ GET    /api/v1/users              # Get all users
✅ GET    /api/v1/users/:id          # Get specific user
✅ POST   /api/v1/users              # Create user
✅ PUT    /api/v1/users/:id          # Update user
✅ DELETE /api/v1/users/:id          # Delete user

Nested Resources:
✅ GET    /api/v1/organizations/:id/members
✅ POST   /api/v1/organizations/:id/teams
✅ PUT    /api/v1/teams/:id/members/:memberId
```

### HTTP Status Codes
```typescript
// Success responses
200 OK          // Successful GET, PUT
201 Created     // Successful POST
204 No Content  // Successful DELETE

// Client errors
400 Bad Request      // Invalid request data
401 Unauthorized     // Missing/invalid authentication
403 Forbidden        // Insufficient permissions
404 Not Found        // Resource not found
409 Conflict         // Resource conflict
422 Unprocessable    // Validation errors

// Server errors
500 Internal Error   // Server error
503 Service Unavailable // Service down
```

## 🔧 Controller Implementation

### Basic Controller Structure
```typescript
// src/modules/users/users.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUsersDto } from './dto/query-users.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from './entities/user.entity';
import { MemberRole } from '../organizations/enums/member-role.enum';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(MemberRole.ADMIN, MemberRole.OWNER)
  @ApiOperation({ summary: 'Create new user' })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'User created successfully',
    type: User 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input data' 
  })
  @ApiResponse({ 
    status: HttpStatus.CONFLICT, 
    description: 'Email already exists' 
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users with pagination' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Users retrieved successfully' 
  })
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 20 })
  @ApiQuery({ name: 'search', required: false, example: 'john' })
  async findAll(@Query() query: QueryUsersDto) {
    return this.usersService.findAll(query);
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Profile retrieved successfully',
    type: User 
  })
  async getProfile(@CurrentUser() user: User): Promise<User> {
    return this.usersService.findById(user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiParam({ name: 'id', description: 'User UUID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'User found',
    type: User 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'User not found' 
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<User> {
    return this.usersService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update user' })
  @ApiParam({ name: 'id', description: 'User UUID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'User updated successfully',
    type: User 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'User not found' 
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() currentUser: User
  ): Promise<User> {
    return this.usersService.update(id, updateUserDto, currentUser.id);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(MemberRole.ADMIN, MemberRole.OWNER)
  @ApiOperation({ summary: 'Delete user' })
  @ApiParam({ name: 'id', description: 'User UUID' })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'User deleted successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'User not found' 
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User
  ): Promise<void> {
    return this.usersService.remove(id, currentUser.id);
  }
}
```

## 📝 DTO Design & Validation

### Create DTO with Validation
```typescript
// src/modules/users/dto/create-user.dto.ts
import {
  IsEmail,
  IsString,
  IsEnum,
  IsOptional,
  MinLength,
  MaxLength,
  Matches,
  IsNotEmpty,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AccountType } from '../enums/account-type.enum';

export class CreateUserDto {
  @ApiProperty({ 
    example: '<EMAIL>',
    description: 'User email address' 
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @Transform(({ value }) => value.toLowerCase().trim())
  email: string;

  @ApiProperty({ 
    example: 'SecurePassword123!',
    description: 'User password (min 8 characters, must contain uppercase, lowercase, number, and special character)' 
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(128, { message: 'Password must not exceed 128 characters' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    { message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character' }
  )
  password: string;

  @ApiProperty({ 
    example: 'John',
    description: 'User first name' 
  })
  @IsString()
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  firstName: string;

  @ApiProperty({ 
    example: 'Doe',
    description: 'User last name' 
  })
  @IsString()
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  lastName: string;

  @ApiPropertyOptional({ 
    enum: AccountType,
    example: AccountType.BUSINESS,
    description: 'Account type' 
  })
  @IsOptional()
  @IsEnum(AccountType, { message: 'Account type must be either personal or business' })
  accountType?: AccountType = AccountType.PERSONAL;
}
```

### Update DTO
```typescript
// src/modules/users/dto/update-user.dto.ts
import { PartialType, OmitType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['email', 'password'] as const)
) {
  // Email and password updates handled separately for security
}
```

### Query DTO with Pagination
```typescript
// src/modules/users/dto/query-users.dto.ts
import { IsOptional, IsString, IsEnum, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AccountType } from '../enums/account-type.enum';

export class QueryUsersDto {
  @ApiPropertyOptional({ 
    example: 1,
    description: 'Page number (starts from 1)' 
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({ 
    example: 20,
    description: 'Number of items per page (max 100)' 
  })
  @IsOptional()
  @Type(() => Number)
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit must not exceed 100' })
  limit?: number = 20;

  @ApiPropertyOptional({ 
    example: 'john',
    description: 'Search term for name or email' 
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  search?: string;

  @ApiPropertyOptional({ 
    enum: AccountType,
    description: 'Filter by account type' 
  })
  @IsOptional()
  @IsEnum(AccountType)
  accountType?: AccountType;

  @ApiPropertyOptional({ 
    example: 'firstName',
    description: 'Sort field' 
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({ 
    example: 'ASC',
    description: 'Sort order' 
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
```

## 🔒 Authentication & Authorization

### Guards Implementation
```typescript
// Custom permission guard for specific endpoints
@Injectable()
export class ResourceOwnerGuard implements CanActivate {
  constructor(private usersService: UsersService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const resourceId = request.params.id;

    // Allow if user is admin/owner or accessing their own resource
    if (user.role === MemberRole.ADMIN || user.role === MemberRole.OWNER) {
      return true;
    }

    return user.id === resourceId;
  }
}

// Usage in controller
@Put(':id')
@UseGuards(JwtAuthGuard, ResourceOwnerGuard)
async updateProfile(
  @Param('id') id: string,
  @Body() updateDto: UpdateUserDto
) {
  return this.usersService.update(id, updateDto);
}
```

### Custom Decorators
```typescript
// src/common/decorators/api-paginated-response.decorator.ts
import { applyDecorators, Type } from '@nestjs/common';
import { ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

export const ApiPaginatedResponse = <TModel extends Type<any>>(
  model: TModel,
) => {
  return applyDecorators(
    ApiOkResponse({
      schema: {
        allOf: [
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(model) },
              },
              meta: {
                type: 'object',
                properties: {
                  total: { type: 'number' },
                  page: { type: 'number' },
                  limit: { type: 'number' },
                  totalPages: { type: 'number' },
                },
              },
            },
          },
        ],
      },
    }),
  );
};

// Usage
@Get()
@ApiPaginatedResponse(User)
async findAll(@Query() query: QueryUsersDto) {
  return this.usersService.findAll(query);
}
```

## 📊 Response Formatting

### Standard Response Format
```typescript
// src/common/interfaces/api-response.interface.ts
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    totalPages?: number;
  };
}

// Response interceptor
@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiResponse<T>> {
    return next.handle().pipe(
      map((data) => ({
        success: true,
        data,
        message: this.getSuccessMessage(context),
      })),
    );
  }

  private getSuccessMessage(context: ExecutionContext): string {
    const method = context.switchToHttp().getRequest().method;
    const messages = {
      GET: 'Data retrieved successfully',
      POST: 'Resource created successfully',
      PUT: 'Resource updated successfully',
      PATCH: 'Resource updated successfully',
      DELETE: 'Resource deleted successfully',
    };
    return messages[method] || 'Operation completed successfully';
  }
}
```

### Pagination Helper
```typescript
// src/common/utils/pagination.util.ts
export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginationResult<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export class PaginationUtil {
  static paginate<T>(
    data: T[],
    total: number,
    options: PaginationOptions,
  ): PaginationResult<T> {
    const totalPages = Math.ceil(total / options.limit);
    
    return {
      data,
      meta: {
        total,
        page: options.page,
        limit: options.limit,
        totalPages,
        hasNextPage: options.page < totalPages,
        hasPreviousPage: options.page > 1,
      },
    };
  }

  static getSkip(page: number, limit: number): number {
    return (page - 1) * limit;
  }
}
```

## 🚨 Error Handling

### Custom Exception Filters
```typescript
// src/common/filters/http-exception.filter.ts
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { LoggerService } from '../services/logger.service';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private logger: LoggerService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    const errorResponse = {
      success: false,
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: exception.message,
      error: this.getErrorDetails(exception),
    };

    // Log error
    this.logger.logError(exception, 'HTTP Exception', {
      url: request.url,
      method: request.method,
      statusCode: status,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
    });

    response.status(status).json(errorResponse);
  }

  private getErrorDetails(exception: HttpException): any {
    const response = exception.getResponse();
    
    if (typeof response === 'object' && response !== null) {
      return response;
    }
    
    return { message: response };
  }
}
```

### Validation Exception Filter
```typescript
// src/common/filters/validation-exception.filter.ts
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
} from '@nestjs/common';
import { Response } from 'express';

@Catch(BadRequestException)
export class ValidationExceptionFilter implements ExceptionFilter {
  catch(exception: BadRequestException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const exceptionResponse = exception.getResponse() as any;

    const errorResponse = {
      success: false,
      statusCode: 400,
      message: 'Validation failed',
      errors: this.formatValidationErrors(exceptionResponse.message),
    };

    response.status(400).json(errorResponse);
  }

  private formatValidationErrors(errors: string[]): any {
    if (!Array.isArray(errors)) {
      return errors;
    }

    return errors.reduce((acc, error) => {
      const [field, ...messageParts] = error.split(' ');
      const message = messageParts.join(' ');
      
      if (!acc[field]) {
        acc[field] = [];
      }
      acc[field].push(message);
      
      return acc;
    }, {});
  }
}
```

## 📚 API Documentation

### Swagger Configuration
```typescript
// src/main.ts
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Swagger setup
  const config = new DocumentBuilder()
    .setTitle('Delify Platform API')
    .setDescription('Comprehensive business automation platform with AI integration')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('Authentication', 'User authentication and authorization')
    .addTag('Users', 'User management operations')
    .addTag('Organizations', 'Organization and team management')
    .addTag('AI', 'AI services and multi-provider integration')
    .addServer('http://localhost:3000', 'Development server')
    .addServer('https://api.delify.com', 'Production server')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  await app.listen(3000);
}
```

### Advanced Swagger Decorators
```typescript
// Custom API decorators
export const ApiStandardResponses = () => {
  return applyDecorators(
    ApiResponse({ status: 401, description: 'Unauthorized' }),
    ApiResponse({ status: 403, description: 'Forbidden' }),
    ApiResponse({ status: 500, description: 'Internal server error' }),
  );
};

export const ApiCrudOperation = (resource: string) => {
  return {
    create: applyDecorators(
      ApiOperation({ summary: `Create ${resource}` }),
      ApiResponse({ status: 201, description: `${resource} created successfully` }),
      ApiResponse({ status: 400, description: 'Invalid input data' }),
      ApiStandardResponses(),
    ),
    findAll: applyDecorators(
      ApiOperation({ summary: `Get all ${resource}s` }),
      ApiResponse({ status: 200, description: `${resource}s retrieved successfully` }),
      ApiStandardResponses(),
    ),
    findOne: applyDecorators(
      ApiOperation({ summary: `Get ${resource} by ID` }),
      ApiResponse({ status: 200, description: `${resource} found` }),
      ApiResponse({ status: 404, description: `${resource} not found` }),
      ApiStandardResponses(),
    ),
    update: applyDecorators(
      ApiOperation({ summary: `Update ${resource}` }),
      ApiResponse({ status: 200, description: `${resource} updated successfully` }),
      ApiResponse({ status: 404, description: `${resource} not found` }),
      ApiStandardResponses(),
    ),
    remove: applyDecorators(
      ApiOperation({ summary: `Delete ${resource}` }),
      ApiResponse({ status: 204, description: `${resource} deleted successfully` }),
      ApiResponse({ status: 404, description: `${resource} not found` }),
      ApiStandardResponses(),
    ),
  };
};

// Usage
const UserApiDocs = ApiCrudOperation('User');

@Post()
@UserApiDocs.create
async create(@Body() createDto: CreateUserDto) {
  return this.service.create(createDto);
}
```

## 🧪 API Testing

### Controller Testing
```typescript
// src/modules/users/users.controller.spec.ts
describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            create: jest.fn(),
            findAll: jest.fn(),
            findById: jest.fn(),
            update: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  describe('create', () => {
    it('should create a user', async () => {
      const createDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'John',
        lastName: 'Doe',
      };

      const expectedResult = { id: '1', ...createDto };
      jest.spyOn(service, 'create').mockResolvedValue(expectedResult as any);

      const result = await controller.create(createDto);

      expect(service.create).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(expectedResult);
    });
  });
});
```

### E2E API Testing
```typescript
// test/users.e2e-spec.ts
describe('Users API (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });
    
    authToken = loginResponse.body.tokens.accessToken;
  });

  describe('/users (POST)', () => {
    it('should create user with valid data', () => {
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: 'New',
          lastName: 'User',
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.email).toBe('<EMAIL>');
        });
    });

    it('should return 400 for invalid email', () => {
      return request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: 'invalid-email',
          password: 'Password123!',
          firstName: 'New',
          lastName: 'User',
        })
        .expect(400);
    });
  });
});
```

## 🎯 Best Practices

### 1. API Design
- **Consistent naming** - Use plural nouns for resources
- **Proper HTTP methods** - GET, POST, PUT, DELETE
- **Meaningful status codes** - Use appropriate HTTP status codes
- **Versioning** - Include version in URL (/api/v1/)

### 2. Validation
- **Input validation** - Validate all incoming data
- **Output sanitization** - Clean data before sending
- **Type safety** - Use TypeScript strictly
- **Custom validators** - Create reusable validators

### 3. Security
- **Authentication** - Protect all non-public endpoints
- **Authorization** - Check permissions properly
- **Rate limiting** - Prevent abuse
- **Input sanitization** - Prevent injection attacks

### 4. Documentation
- **Swagger annotations** - Document all endpoints
- **Example requests** - Provide clear examples
- **Error responses** - Document error scenarios
- **Changelog** - Track API changes

**Following this guide ensures consistent, secure, và well-documented APIs trong Delify Platform.** 🚀✨
