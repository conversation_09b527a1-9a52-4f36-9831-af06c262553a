import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class FacebookService {
  private readonly appId: string;
  private readonly appSecret: string;
  private readonly apiVersion: string;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {
    this.appId = this.configService.get('FACEBOOK_APP_ID');
    this.appSecret = this.configService.get('FACEBOOK_APP_SECRET');
    this.apiVersion = this.configService.get('FACEBOOK_GRAPH_API_VERSION', 'v18.0');
  }

  async getLoginUrl(redirectUri: string, scopes: string[] = []): Promise<string> {
    const defaultScopes = [
      'pages_manage_posts',
      'pages_read_engagement',
      'groups_access_member_info',
      'publish_to_groups',
    ];

    const allScopes = [...defaultScopes, ...scopes].join(',');
    
    const params = new URLSearchParams({
      client_id: this.appId,
      redirect_uri: redirectUri,
      scope: allScopes,
      response_type: 'code',
      state: this.generateState(),
    });

    return `https://www.facebook.com/${this.apiVersion}/dialog/oauth?${params.toString()}`;
  }

  async exchangeCodeForToken(code: string, redirectUri: string): Promise<{
    accessToken: string;
    tokenType: string;
    expiresIn: number;
  }> {
    const url = `https://graph.facebook.com/${this.apiVersion}/oauth/access_token`;
    
    const params = new URLSearchParams({
      client_id: this.appId,
      client_secret: this.appSecret,
      redirect_uri: redirectUri,
      code,
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return {
        accessToken: data.access_token,
        tokenType: data.token_type || 'bearer',
        expiresIn: data.expires_in,
      };
    } catch (error) {
      this.logger.logError(error, 'FacebookService - exchangeCodeForToken');
      throw error;
    }
  }

  async getUserInfo(accessToken: string): Promise<{
    id: string;
    name: string;
    email?: string;
    picture?: string;
  }> {
    const url = `https://graph.facebook.com/${this.apiVersion}/me`;
    const params = new URLSearchParams({
      access_token: accessToken,
      fields: 'id,name,email,picture',
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return {
        id: data.id,
        name: data.name,
        email: data.email,
        picture: data.picture?.data?.url,
      };
    } catch (error) {
      this.logger.logError(error, 'FacebookService - getUserInfo');
      throw error;
    }
  }

  async getUserPages(accessToken: string): Promise<Array<{
    id: string;
    name: string;
    accessToken: string;
    permissions: string[];
  }>> {
    const url = `https://graph.facebook.com/${this.apiVersion}/me/accounts`;
    const params = new URLSearchParams({
      access_token: accessToken,
      fields: 'id,name,access_token,perms',
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return data.data.map(page => ({
        id: page.id,
        name: page.name,
        accessToken: page.access_token,
        permissions: page.perms || [],
      }));
    } catch (error) {
      this.logger.logError(error, 'FacebookService - getUserPages');
      throw error;
    }
  }

  async postToPage(pageAccessToken: string, pageId: string, content: {
    message: string;
    link?: string;
    picture?: string;
  }): Promise<{ id: string }> {
    const url = `https://graph.facebook.com/${this.apiVersion}/${pageId}/feed`;
    
    const body = {
      message: content.message,
      access_token: pageAccessToken,
      ...(content.link && { link: content.link }),
      ...(content.picture && { picture: content.picture }),
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return { id: data.id };
    } catch (error) {
      this.logger.logError(error, 'FacebookService - postToPage');
      throw error;
    }
  }

  async postToGroup(accessToken: string, groupId: string, content: {
    message: string;
    link?: string;
  }): Promise<{ id: string }> {
    const url = `https://graph.facebook.com/${this.apiVersion}/${groupId}/feed`;
    
    const body = {
      message: content.message,
      access_token: accessToken,
      ...(content.link && { link: content.link }),
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return { id: data.id };
    } catch (error) {
      this.logger.logError(error, 'FacebookService - postToGroup');
      throw error;
    }
  }

  async getPostComments(accessToken: string, postId: string): Promise<Array<{
    id: string;
    message: string;
    from: { id: string; name: string };
    createdTime: string;
  }>> {
    const url = `https://graph.facebook.com/${this.apiVersion}/${postId}/comments`;
    const params = new URLSearchParams({
      access_token: accessToken,
      fields: 'id,message,from,created_time',
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return data.data.map(comment => ({
        id: comment.id,
        message: comment.message,
        from: comment.from,
        createdTime: comment.created_time,
      }));
    } catch (error) {
      this.logger.logError(error, 'FacebookService - getPostComments');
      throw error;
    }
  }

  async replyToComment(accessToken: string, commentId: string, message: string): Promise<{ id: string }> {
    const url = `https://graph.facebook.com/${this.apiVersion}/${commentId}/comments`;
    
    const body = {
      message,
      access_token: accessToken,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`Facebook API Error: ${data.error.message}`);
      }

      return { id: data.id };
    } catch (error) {
      this.logger.logError(error, 'FacebookService - replyToComment');
      throw error;
    }
  }

  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
}
