import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { SchedulingService } from './scheduling.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Scheduling')
@Controller('scheduling')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SchedulingController {
  constructor(private readonly schedulingService: SchedulingService) {}

  @Get('appointments')
  async getAppointments(@CurrentUser() user: User) {
    return this.schedulingService.getAppointments(user.id);
  }

  @Post('appointments')
  async createAppointment(@CurrentUser() user: User, @Body() data: any) {
    return this.schedulingService.createAppointment(user.id, data);
  }

  @Get('time-slots/available')
  async getAvailableTimeSlots(@CurrentUser() user: User) {
    return this.schedulingService.getAvailableTimeSlots(user.id);
  }

  @Post('time-slots')
  async createTimeSlot(@CurrentUser() user: User, @Body() data: any) {
    return this.schedulingService.createTimeSlot(user.id, data);
  }

  @Get('services')
  async getServices(@CurrentUser() user: User) {
    return this.schedulingService.getServices(user.id);
  }

  @Post('services')
  async createService(@CurrentUser() user: User, @Body() data: any) {
    return this.schedulingService.createService(user.id, data);
  }
}
