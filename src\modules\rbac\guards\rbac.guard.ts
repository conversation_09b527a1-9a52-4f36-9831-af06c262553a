import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RBACService } from '../services/rbac.service';
import { PERMISSION_KEY, ROLE_KEY, ROLE_LEVEL_KEY } from '../decorators/rbac.decorators';

/**
 * RBAC Guard - Bảo vệ endpoint dựa trên quyền hạn và vai trò
 * RBAC Guard - Protects endpoints based on permissions and roles
 */
@Injectable()
export class RBACGuard implements CanActivate {
  private readonly logger = new Logger(RBACGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RBACService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      this.logger.warn('No user found in request');
      return false;
    }

    try {
      // <PERSON><PERSON><PERSON> tra quyền cụ thể - Check specific permission
      const requiredPermission = this.reflector.get<string>(PERMISSION_KEY, context.getHandler());
      if (requiredPermission) {
        const permissionResult = await this.rbacService.checkPermission(
          user.id,
          requiredPermission,
          this.extractResource(request),
        );

        if (!permissionResult.allowed) {
          this.logger.warn(
            `User ${user.id} denied access: ${permissionResult.reason}`,
            { permission: requiredPermission, userId: user.id }
          );
          throw new ForbiddenException(permissionResult.reason);
        }

        this.logger.debug(
          `User ${user.id} granted access: ${permissionResult.reason}`,
          { permission: requiredPermission, userId: user.id }
        );
        return true;
      }

      // Kiểm tra vai trò cụ thể - Check specific role
      const requiredRole = this.reflector.get<string>(ROLE_KEY, context.getHandler());
      if (requiredRole) {
        const hasRole = await this.rbacService.hasRole(user.id, requiredRole);
        
        if (!hasRole) {
          this.logger.warn(
            `User ${user.id} denied access: Missing required role ${requiredRole}`,
            { role: requiredRole, userId: user.id }
          );
          throw new ForbiddenException(`Required role: ${requiredRole}`);
        }

        this.logger.debug(
          `User ${user.id} granted access: Has required role ${requiredRole}`,
          { role: requiredRole, userId: user.id }
        );
        return true;
      }

      // Kiểm tra cấp độ vai trò - Check role level
      const requiredLevel = this.reflector.get<number>(ROLE_LEVEL_KEY, context.getHandler());
      if (requiredLevel !== undefined) {
        const userLevel = await this.rbacService.getUserRoleLevel(user.id);
        
        if (userLevel > requiredLevel) {
          this.logger.warn(
            `User ${user.id} denied access: Insufficient role level (${userLevel} > ${requiredLevel})`,
            { requiredLevel, userLevel, userId: user.id }
          );
          throw new ForbiddenException(`Required role level: ${requiredLevel} or higher`);
        }

        this.logger.debug(
          `User ${user.id} granted access: Sufficient role level (${userLevel} <= ${requiredLevel})`,
          { requiredLevel, userLevel, userId: user.id }
        );
        return true;
      }

      // Nếu không có yêu cầu cụ thể, cho phép truy cập
      // If no specific requirements, allow access
      return true;

    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(
        `Error checking permissions for user ${user.id}:`,
        error.stack,
        { userId: user.id }
      );
      
      throw new ForbiddenException('Access denied due to system error');
    }
  }

  /**
   * Trích xuất resource từ request - Extract resource from request
   */
  private extractResource(request: any): string | undefined {
    // Lấy resource từ params (ví dụ: /users/:id -> user:id)
    if (request.params && request.params.id) {
      const path = request.route?.path || request.url;
      const resourceType = this.extractResourceType(path);
      return resourceType ? `${resourceType}:${request.params.id}` : undefined;
    }

    // Lấy resource từ query params
    if (request.query && request.query.resource) {
      return request.query.resource;
    }

    // Lấy resource từ body
    if (request.body && request.body.resource) {
      return request.body.resource;
    }

    return undefined;
  }

  /**
   * Trích xuất loại resource từ path - Extract resource type from path
   */
  private extractResourceType(path: string): string | undefined {
    const pathSegments = path.split('/').filter(segment => segment && !segment.startsWith(':'));
    
    // Lấy segment cuối cùng làm resource type
    const lastSegment = pathSegments[pathSegments.length - 1];
    
    // Mapping các path phổ biến
    const resourceMapping: Record<string, string> = {
      'users': 'user',
      'roles': 'role',
      'permissions': 'permission',
      'content': 'content',
      'campaigns': 'campaign',
      'sales': 'sale',
      'reports': 'report',
      'settings': 'setting',
    };

    return resourceMapping[lastSegment] || lastSegment;
  }
}

/**
 * Permission Guard - Chỉ kiểm tra quyền hạn
 * Permission Guard - Only checks permissions
 */
@Injectable()
export class PermissionGuard implements CanActivate {
  private readonly logger = new Logger(PermissionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RBACService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.get<string>(PERMISSION_KEY, context.getHandler());
    
    if (!requiredPermission) {
      return true; // Không có yêu cầu quyền cụ thể
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      return false;
    }

    try {
      const permissionResult = await this.rbacService.checkPermission(
        user.id,
        requiredPermission,
      );

      if (!permissionResult.allowed) {
        this.logger.warn(
          `Permission denied for user ${user.id}: ${permissionResult.reason}`,
          { permission: requiredPermission, userId: user.id }
        );
        throw new ForbiddenException(permissionResult.reason);
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(`Error checking permission for user ${user.id}:`, error.stack);
      throw new ForbiddenException('Access denied due to system error');
    }
  }
}

/**
 * Role Guard - Chỉ kiểm tra vai trò
 * Role Guard - Only checks roles
 */
@Injectable()
export class RoleGuard implements CanActivate {
  private readonly logger = new Logger(RoleGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RBACService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRole = this.reflector.get<string>(ROLE_KEY, context.getHandler());
    
    if (!requiredRole) {
      return true; // Không có yêu cầu vai trò cụ thể
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      return false;
    }

    try {
      const hasRole = await this.rbacService.hasRole(user.id, requiredRole);
      
      if (!hasRole) {
        this.logger.warn(
          `Role denied for user ${user.id}: Missing required role ${requiredRole}`,
          { role: requiredRole, userId: user.id }
        );
        throw new ForbiddenException(`Required role: ${requiredRole}`);
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(`Error checking role for user ${user.id}:`, error.stack);
      throw new ForbiddenException('Access denied due to system error');
    }
  }
}

/**
 * Role Level Guard - Kiểm tra cấp độ vai trò
 * Role Level Guard - Checks role level
 */
@Injectable()
export class RoleLevelGuard implements CanActivate {
  private readonly logger = new Logger(RoleLevelGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly rbacService: RBACService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredLevel = this.reflector.get<number>(ROLE_LEVEL_KEY, context.getHandler());
    
    if (requiredLevel === undefined) {
      return true; // Không có yêu cầu cấp độ cụ thể
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      return false;
    }

    try {
      const userLevel = await this.rbacService.getUserRoleLevel(user.id);
      
      if (userLevel > requiredLevel) {
        this.logger.warn(
          `Role level denied for user ${user.id}: Insufficient level (${userLevel} > ${requiredLevel})`,
          { requiredLevel, userLevel, userId: user.id }
        );
        throw new ForbiddenException(`Required role level: ${requiredLevel} or higher`);
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(`Error checking role level for user ${user.id}:`, error.stack);
      throw new ForbiddenException('Access denied due to system error');
    }
  }
}
