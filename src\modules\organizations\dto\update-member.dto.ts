import { IsOptional, IsUUID, IsArray, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateMemberDto {
  @ApiPropertyOptional({ example: 'role-uuid-here' })
  @IsOptional()
  @IsUUID()
  roleId?: string;

  @ApiPropertyOptional({ 
    example: ['permission-1', 'permission-2'],
    description: 'Additional permissions beyond role permissions'
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];

  @ApiPropertyOptional({
    example: {
      department: 'Marketing',
      jobTitle: 'Senior Marketing Specialist',
      phoneNumber: '******-0123',
      notes: 'Team lead for social media campaigns'
    }
  })
  @IsOptional()
  metadata?: {
    department?: string;
    jobTitle?: string;
    phoneNumber?: string;
    notes?: string;
    customFields?: Record<string, any>;
  };

  @ApiPropertyOptional({
    example: {
      canInviteMembers: true,
      canManageRoles: false,
      canAccessBilling: false,
      canExportData: true,
      timeRestrictions: {
        allowedHours: '09:00-17:00',
        allowedDays: [1, 2, 3, 4, 5],
        timezone: 'America/New_York'
      }
    }
  })
  @IsOptional()
  restrictions?: {
    canInviteMembers?: boolean;
    canManageRoles?: boolean;
    canAccessBilling?: boolean;
    canExportData?: boolean;
    ipWhitelist?: string[];
    timeRestrictions?: {
      allowedHours?: string;
      allowedDays?: number[];
      timezone?: string;
    };
  };
}
