import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, Api<PERSON>earerAuth } from '@nestjs/swagger';
import { EmailService } from './email.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Email')
@Controller('email')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Get('templates')
  async getTemplates(@CurrentUser() user: User) {
    return this.emailService.getTemplates(user.id);
  }

  @Post('templates')
  async createTemplate(@CurrentUser() user: User, @Body() data: any) {
    return this.emailService.createTemplate(user.id, data);
  }

  @Get('campaigns')
  async getCampaigns(@CurrentUser() user: User) {
    return this.emailService.getCampaigns(user.id);
  }

  @Post('campaigns')
  async createCampaign(@CurrentUser() user: User, @Body() data: any) {
    return this.emailService.createCampaign(user.id, data);
  }

  @Post('campaigns/:id/send')
  async sendCampaign(@CurrentUser() user: User, @Param('id') id: string) {
    return this.emailService.sendCampaign(user.id, id);
  }

  @Get('analytics')
  async getAnalytics(@CurrentUser() user: User) {
    return this.emailService.getAnalytics(user.id);
  }

  @Post('send')
  async sendEmail(@CurrentUser() user: User, @Body() data: any) {
    return this.emailService.sendSingleEmail(user.id, data);
  }
}
