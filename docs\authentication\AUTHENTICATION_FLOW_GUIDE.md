# Comprehensive Authentication Flow Guide

## Overview

Our authentication system supports 4 different security methods and uses refresh tokens for enhanced security. This document describes in detail all authentication flows and token management.

## Table of Contents

1. [Authentication Flow Diagrams](#authentication-flow-diagrams)
2. [Step-by-Step Processes](#step-by-step-processes)
3. [API Endpoint Sequences](#api-endpoint-sequences)
4. [Token Management Lifecycle](#token-management-lifecycle)
5. [Security Method Flows](#security-method-flows)
6. [Error Handling](#error-handling)
7. [Database State Changes](#database-state-changes)

## Authentication Flow Diagrams

### 1. Registration Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Device as Device Service

    C->>API: POST /auth/register
    API->>DB: Tạo user mới
    API->>Device: Tạo device session
    API->>API: Tạo access & refresh tokens
    API->>DB: Lưu refresh token
    API->>Email: Gửi email chào mừng (optional)
    API->>C: Tr<PERSON> về tokens + user info
```

### 2. <PERSON><PERSON><PERSON>ậ<PERSON>n (Basic Login Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service

    C->>API: POST /auth/login
    API->>DB: Kiểm tra credentials

    alt Security Method = DISABLED
        API->>API: Tạo tokens ngay lập tức
        API->>C: Trả về complete login response
    else Security Method Enabled
        API->>VS: Tạo verification session
        API->>C: Trả về partial login response
        Note over C: Client cần verification code
    end
```

### 3. Luồng Xác Minh Đăng Nhập (Login Verification Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant VS as Verification Service

    C->>API: POST /auth/verify-login
    API->>VS: Kiểm tra verification session
    API->>DB: Lấy thông tin user

    alt EMAIL_VERIFICATION
        API->>DB: Kiểm tra verification code
    else TWO_FACTOR_AUTH
        API->>VS: Verify TOTP code
    else FIXED_CODE
        API->>VS: Verify fixed code
    end

    API->>API: Tạo access & refresh tokens
    API->>DB: Lưu refresh token
    API->>C: Trả về complete login response
```

### 4. Luồng Refresh Token

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    C->>API: POST /auth/refresh
    API->>API: Verify refresh token JWT
    API->>DB: Tìm user bằng refresh token
    API->>DB: Kiểm tra token expiration
    API->>API: Tạo tokens mới
    API->>DB: Cập nhật refresh token mới
    API->>C: Trả về tokens mới
```

## Step-by-Step Processes

### Method 1: DISABLED (No Security)

**Characteristics:**
- Direct login with email/username + password
- No verification code required
- Suitable for development environment or internal applications

**Process:**
1. Client sends credentials
2. Server checks password
3. Create tokens immediately
4. Return complete login response

```javascript
// Request
POST /auth/login
{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}

// Response (Success)
{
  "user": { ... },
  "accessToken": "eyJ...",
  "refreshToken": "eyJ...",
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

### Method 2: EMAIL_VERIFICATION (Email Verification)

**Characteristics:**
- Send 6-digit verification code via email
- Code valid for 5 minutes
- Can resend code if needed

**Process:**
1. Client sends credentials
2. Server checks password
3. Create verification code and send email
4. Return partial login response with sessionId
5. Client sends verification code
6. Server verifies and creates tokens

```javascript
// Bước 1: Login
POST /auth/login
{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}

// Response (Partial)
{
  "requiresVerification": true,
  "sessionId": "session-123",
  "message": "Verification code sent to your email"
}

// Bước 2: Verify
POST /auth/verify-login
{
  "sessionId": "session-123",
  "verificationCode": "123456"
}

// Response (Complete)
{
  "user": { ... },
  "accessToken": "eyJ...",
  "refreshToken": "eyJ...",
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

### Method 3: TWO_FACTOR_AUTH (Two-Factor Authentication)

**Characteristics:**
- Uses TOTP (Time-based One-Time Password)
- Compatible with Google Authenticator, Authy
- Code changes every 30 seconds

**Setup Process:**
1. Client requests to enable 2FA
2. Server creates secret and QR code
3. Client scans QR code into authenticator app
4. Client sends verification code to confirm
5. Server enables 2FA for user

```javascript
// Step 1: Request 2FA setup
POST /auth/security-method
{
  "securityMethod": "two_factor_auth"
}

// Response
{
  "securityMethod": "disabled", // Not enabled yet
  "twoFactorEnabled": false,
  "qrCodeUrl": "data:image/png;base64,...",
  "manualEntryKey": "JBSWY3DPEHPK3PXP"
}

// Step 2: Confirm setup
POST /auth/security-method
{
  "securityMethod": "two_factor_auth",
  "verificationCode": "123456"
}

// Response
{
  "securityMethod": "two_factor_auth",
  "twoFactorEnabled": true
}
```

**Login Process:**
1. Client sends credentials
2. Server returns partial login
3. Client enters code from authenticator app
4. Server verifies TOTP and creates tokens

### Method 4: FIXED_CODE (Fixed Code)

**Characteristics:**
- 6-8 digit code set by user
- Can be reused multiple times
- Validation prevents weak patterns

**Setup Process:**
```javascript
POST /auth/security-method
{
  "securityMethod": "fixed_code",
  "fixedCode": "789123"
}
```

**Validation Rules:**
- Length: 6-8 digits
- Cannot be sequential numbers (123456, 654321)
- Cannot be repeated digits (111111, 222222)
- Cannot be common patterns (000000, 123123)

## API Endpoint Sequences

### Complete Registration
```
1. POST /auth/register
   → Create user + tokens immediately
```

### Login with EMAIL_VERIFICATION
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login
   → Complete response + tokens
```

### Login with TWO_FACTOR_AUTH
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login (with TOTP code)
   → Complete response + tokens
```

### Login with FIXED_CODE
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login (with fixed code)
   → Complete response + tokens
```

### Refresh Token Flow
```
1. POST /auth/refresh
   → New access token + new refresh token
```

### Logout Flow
```
1. POST /auth/revoke (with refresh token)
   → Revoke specific token

OR

1. POST /auth/revoke-all (with access token)
   → Revoke all tokens for user
```

## Token Management Lifecycle

### Token Creation
```javascript
// In AuthService.generateTokens()
const accessToken = jwt.sign(payload, { expiresIn: '15m' });
const refreshToken = jwt.sign(refreshPayload, { expiresIn: '7d' });

// Save refresh token to database
await usersService.setRefreshToken(userId, refreshToken, expiresAt);
```

### Token Usage
```javascript
// Client sends access token in header
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// Server verifies token through JwtAuthGuard
const user = await authService.validateJwtPayload(payload);
```

### Token Refresh
```javascript
// When access token expires (401 error)
const response = await fetch('/auth/refresh', {
  method: 'POST',
  body: JSON.stringify({ refreshToken })
});

const { accessToken, refreshToken: newRefreshToken } = await response.json();
```

### Token Revocation
```javascript
// Logout from current device
await fetch('/auth/revoke', {
  method: 'POST',
  body: JSON.stringify({ refreshToken })
});

// Logout from all devices
await fetch('/auth/revoke-all', {
  method: 'POST',
  headers: { Authorization: `Bearer ${accessToken}` }
});
```

### Token Expiration Handling
```javascript
// Automatic refresh with Axios interceptor
axios.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401 && !error.config._retry) {
      error.config._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/auth/refresh', {
          method: 'POST',
          body: JSON.stringify({ refreshToken })
        });

        const tokens = await response.json();
        localStorage.setItem('accessToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);

        // Retry original request
        error.config.headers.Authorization = `Bearer ${tokens.accessToken}`;
        return axios(error.config);
      } catch (refreshError) {
        // Redirect to login
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);
```

## Security Method Flows

### Enable Security Method

#### 1. Enable EMAIL_VERIFICATION
```javascript
// Request
POST /auth/security-method
{
  "securityMethod": "email_verification"
}

// Response
{
  "securityMethod": "email_verification",
  "twoFactorEnabled": false,
  "hasFixedCode": false
}

// Database Changes
UPDATE users SET
  securityMethod = 'email_verification',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = null
WHERE id = userId;
```

#### 2. Enable TWO_FACTOR_AUTH (2 steps)
```javascript
// Step 1: Create secret
POST /auth/security-method
{
  "securityMethod": "two_factor_auth"
}

// Response
{
  "securityMethod": "disabled", // Not changed yet
  "twoFactorEnabled": false,
  "qrCodeUrl": "data:image/png;base64,...",
  "manualEntryKey": "JBSWY3DPEHPK3PXP"
}

// Database Changes (temporary)
UPDATE users SET twoFactorSecret = 'encrypted_secret' WHERE id = userId;

// Step 2: Confirm with verification code
POST /auth/security-method
{
  "securityMethod": "two_factor_auth",
  "verificationCode": "123456"
}

// Response
{
  "securityMethod": "two_factor_auth",
  "twoFactorEnabled": true,
  "hasFixedCode": false
}

// Database Changes (final)
UPDATE users SET
  securityMethod = 'two_factor_auth',
  twoFactorEnabled = true
WHERE id = userId;
```

#### 3. Enable FIXED_CODE
```javascript
// Request
POST /auth/security-method
{
  "securityMethod": "fixed_code",
  "fixedCode": "789123"
}

// Validation Process
1. Check length (6-8 digits)
2. Check forbidden patterns
3. Hash fixed code with SHA-256
4. Save to database

// Response
{
  "securityMethod": "fixed_code",
  "twoFactorEnabled": false,
  "hasFixedCode": true
}

// Database Changes
UPDATE users SET
  securityMethod = 'fixed_code',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = 'hashed_fixed_code'
WHERE id = userId;
```

### Disable Security Method
```javascript
// Request
POST /auth/security-method
{
  "securityMethod": "disabled"
}

// Response
{
  "securityMethod": "disabled",
  "twoFactorEnabled": false,
  "hasFixedCode": false
}

// Database Changes
UPDATE users SET
  securityMethod = 'disabled',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = null
WHERE id = userId;
```

### Change Fixed Code
```javascript
// Request
POST /auth/change-fixed-code
{
  "currentCode": "789123",
  "newCode": "456789"
}

// Process
1. Verify current fixed code
2. Validate new fixed code format
3. Ensure new code ≠ current code
4. Hash và update database
5. Send notification email

// Response
{
  "message": "Fixed code changed successfully"
}

// Database Changes
UPDATE users SET fixedCode = 'new_hashed_code' WHERE id = userId;
```

## Xử Lý Lỗi

### 1. Login Errors

#### Invalid Credentials
```javascript
// Request
POST /auth/login
{
  "emailOrUsername": "<EMAIL>",
  "password": "wrong_password"
}

// Response (401)
{
  "statusCode": 401,
  "message": "Invalid credentials",
  "error": "Unauthorized"
}
```

#### Account Locked/Inactive
```javascript
// Response (403)
{
  "statusCode": 403,
  "message": "Account is inactive or locked",
  "error": "Forbidden"
}
```

### 2. Verification Errors

#### Invalid Verification Code
```javascript
// Request
POST /auth/verify-login
{
  "sessionId": "session-123",
  "verificationCode": "wrong_code"
}

// Response (401)
{
  "statusCode": 401,
  "message": "Invalid verification code",
  "error": "Unauthorized"
}
```

#### Expired Verification Session
```javascript
// Response (401)
{
  "statusCode": 401,
  "message": "Invalid or expired verification session",
  "error": "Unauthorized"
}
```

### 3. Token Errors

#### Expired Access Token
```javascript
// Response (401)
{
  "statusCode": 401,
  "message": "Token expired",
  "error": "Unauthorized"
}

// Client Action: Use refresh token
```

#### Invalid Refresh Token
```javascript
// Request
POST /auth/refresh
{
  "refreshToken": "invalid_or_expired_token"
}

// Response (401)
{
  "statusCode": 401,
  "message": "Invalid or expired refresh token",
  "error": "Unauthorized"
}

// Client Action: Redirect to login
```

### 4. Security Method Errors

#### Weak Fixed Code
```javascript
// Request
POST /auth/security-method
{
  "securityMethod": "fixed_code",
  "fixedCode": "123456" // Sequential pattern
}

// Response (400)
{
  "statusCode": 400,
  "message": "Fixed code is too weak. Avoid sequential numbers, repeated digits, or common patterns.",
  "error": "Bad Request"
}
```

#### 2FA Setup Not Initiated
```javascript
// Request
POST /auth/security-method
{
  "securityMethod": "two_factor_auth",
  "verificationCode": "123456"
}

// Response (400) - If secret not created yet
{
  "statusCode": 400,
  "message": "Two-factor setup not initiated",
  "error": "Bad Request"
}
```

### 5. Rate Limiting Errors
```javascript
// Response (429) - Too many requests
{
  "statusCode": 429,
  "message": "Too many requests. Please try again later.",
  "error": "Too Many Requests",
  "retryAfter": 60 // seconds
}
```

## Database State Changes

### User Registration
```sql
-- Create new user
INSERT INTO users (
  id, email, username, password, accountType,
  securityMethod, isActive, createdAt
) VALUES (
  'uuid', '<EMAIL>', 'username', 'hashed_password',
  'personal', 'disabled', true, NOW()
);

-- Create device session
INSERT INTO user_devices (
  id, userId, sessionToken, userAgent, ipAddress,
  deviceName, isActive, lastActivityAt, createdAt
) VALUES (
  'uuid', 'user_id', 'session_token', 'user_agent', 'ip_address',
  'device_name', true, NOW(), NOW()
);

-- Save refresh token
UPDATE users SET
  refreshToken = 'hashed_refresh_token',
  refreshTokenExpires = NOW() + INTERVAL '7 days',
  lastLoginAt = NOW()
WHERE id = 'user_id';
```

### Login Process
```sql
-- Check credentials
SELECT id, email, password, securityMethod, isActive
FROM users
WHERE (email = ? OR username = ?) AND isActive = true;

-- If security method enabled, create verification session (in-memory)
-- Verification sessions are stored in Redis/Memory, not in database

-- After successful verification:
-- 1. Create device session (as above)
-- 2. Update refresh token (as above)
-- 3. Clear verification code if email verification
UPDATE users SET
  verificationCode = null,
  verificationCodeExpires = null
WHERE id = 'user_id';
```

### Token Refresh
```sql
-- Tìm user bằng refresh token
SELECT id, email, refreshToken, refreshTokenExpires
FROM users
WHERE refreshToken = ? AND refreshTokenExpires > NOW();

-- Update với refresh token mới
UPDATE users SET
  refreshToken = 'new_hashed_refresh_token',
  refreshTokenExpires = NOW() + INTERVAL '7 days'
WHERE id = 'user_id';
```

### Security Method Changes
```sql
-- Enable Email Verification
UPDATE users SET
  securityMethod = 'email_verification',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = null
WHERE id = 'user_id';

-- Enable Two-Factor Auth (2 steps)
-- Step 1: Store secret temporarily
UPDATE users SET twoFactorSecret = 'encrypted_secret' WHERE id = 'user_id';

-- Step 2: Enable after verification
UPDATE users SET
  securityMethod = 'two_factor_auth',
  twoFactorEnabled = true
WHERE id = 'user_id';

-- Enable Fixed Code
UPDATE users SET
  securityMethod = 'fixed_code',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = 'hashed_fixed_code'
WHERE id = 'user_id';

-- Disable Security Method
UPDATE users SET
  securityMethod = 'disabled',
  twoFactorEnabled = false,
  twoFactorSecret = null,
  fixedCode = null
WHERE id = 'user_id';
```

### Token Revocation
```sql
-- Revoke specific refresh token
UPDATE users SET
  refreshToken = null,
  refreshTokenExpires = null
WHERE refreshToken = 'token_to_revoke';

-- Revoke all tokens for user
UPDATE users SET
  refreshToken = null,
  refreshTokenExpires = null
WHERE id = 'user_id';

-- Logout from specific device
UPDATE user_devices SET
  isActive = false,
  loggedOutAt = NOW()
WHERE sessionToken = 'session_token';

-- Logout from all devices
UPDATE user_devices SET
  isActive = false,
  loggedOutAt = NOW()
WHERE userId = 'user_id' AND isActive = true;
```

### Password Changes
```sql
-- Change password (với verification nếu có security method)
UPDATE users SET
  password = 'new_hashed_password',
  passwordChangedAt = NOW()
WHERE id = 'user_id';

-- Clear verification code sau khi đổi password
UPDATE users SET
  verificationCode = null,
  verificationCodeExpires = null
WHERE id = 'user_id';
```

### Cleanup Operations
```sql
-- Cleanup expired refresh tokens (chạy định kỳ)
UPDATE users SET
  refreshToken = null,
  refreshTokenExpires = null
WHERE refreshTokenExpires < NOW();

-- Cleanup expired verification codes (chạy định kỳ)
UPDATE users SET
  verificationCode = null,
  verificationCodeExpires = null
WHERE verificationCodeExpires < NOW();

-- Cleanup inactive device sessions (chạy định kỳ)
DELETE FROM user_devices
WHERE lastActivityAt < NOW() - INTERVAL '30 days'
  AND isActive = false;
```

## Best Practices

### 1. Token Storage
```javascript
// ✅ Secure storage
localStorage.setItem('accessToken', token); // OK for access token
httpOnly cookie cho refresh token; // Tốt nhất cho refresh token

// ❌ Không nên
sessionStorage.setItem('refreshToken', token); // Không an toàn
```

### 2. Error Handling
```javascript
// ✅ Comprehensive error handling
try {
  const response = await authAPI.login(credentials);
  handleSuccessfulLogin(response);
} catch (error) {
  if (error.status === 401) {
    showInvalidCredentialsError();
  } else if (error.status === 403) {
    showAccountLockedError();
  } else if (error.status === 429) {
    showRateLimitError(error.retryAfter);
  } else {
    showGenericError();
  }
}
```

### 3. Token Refresh Strategy
```javascript
// ✅ Proactive refresh (refresh before expiration)
const tokenExpiresAt = jwt.decode(accessToken).exp * 1000;
const refreshThreshold = 5 * 60 * 1000; // 5 minutes

if (Date.now() > tokenExpiresAt - refreshThreshold) {
  await refreshTokens();
}
```

### 4. Security Considerations
- Always use HTTPS in production
- Implement rate limiting for login attempts
- Log all authentication events
- Regular cleanup expired tokens
- Monitor suspicious login activities
- Implement device fingerprinting for enhanced security

---

**This document provides a comprehensive guide to the authentication system. For more implementation details, refer to [Enhanced Authentication Documentation](../features/ENHANCED_AUTHENTICATION.md).**