import axios, { AxiosInstance } from 'axios';
import { BaseAIProvider, AIMessage, AIResponse, AIProviderConfig } from './base-ai.provider';

export class GeminiProvider extends BaseAIProvider {
  private client: AxiosInstance;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = axios.create({
      baseURL: config.baseURL || 'https://generativelanguage.googleapis.com/v1beta',
      timeout: config.timeout || 30000,
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      // Convert messages to Gemini format
      const contents = this.convertMessagesToGeminiFormat(messages);

      const response = await this.client.post(
        `/models/${config.model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,
        {
          contents,
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 1000,
            topP: config.topP || 1,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }
      );

      const responseTime = Date.now() - startTime;
      const data = response.data;

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Gemini: No response generated');
      }

      const candidate = data.candidates[0];
      const content = candidate.content.parts[0].text;

      return {
        content,
        usage: {
          promptTokens: data.usageMetadata?.promptTokenCount || this.calculateTokens(messages.map(m => m.content).join(' ')),
          completionTokens: data.usageMetadata?.candidatesTokenCount || this.calculateTokens(content),
          totalTokens: data.usageMetadata?.totalTokenCount || 0,
        },
        model: config.model || 'gemini-pro',
        finishReason: candidate.finishReason || 'STOP',
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'Gemini');
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    const config = this.mergeConfig(options);

    try {
      const contents = this.convertMessagesToGeminiFormat(messages);

      const response = await this.client.post(
        `/models/${config.model || 'gemini-pro'}:streamGenerateContent?key=${config.apiKey}`,
        {
          contents,
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 1000,
            topP: config.topP || 1,
          },
        },
        {
          responseType: 'stream',
        }
      );

      for await (const chunk of response.data) {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.trim() && line.startsWith('{')) {
            try {
              const parsed = JSON.parse(line);
              if (parsed.candidates && parsed.candidates[0]?.content?.parts) {
                const content = parsed.candidates[0].content.parts[0]?.text;
                if (content) {
                  yield content;
                }
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'Gemini');
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      // Download image and convert to base64
      const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const base64Image = Buffer.from(imageResponse.data).toString('base64');
      const mimeType = imageResponse.headers['content-type'] || 'image/jpeg';

      const response = await this.client.post(
        `/models/${config.model || 'gemini-pro-vision'}:generateContent?key=${config.apiKey}`,
        {
          contents: [
            {
              parts: [
                { text: prompt },
                {
                  inline_data: {
                    mime_type: mimeType,
                    data: base64Image,
                  },
                },
              ],
            },
          ],
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 1000,
          },
        }
      );

      const responseTime = Date.now() - startTime;
      const data = response.data;

      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Gemini: No response generated for image analysis');
      }

      const candidate = data.candidates[0];
      const content = candidate.content.parts[0].text;

      return {
        content,
        usage: {
          promptTokens: data.usageMetadata?.promptTokenCount || this.calculateTokens(prompt),
          completionTokens: data.usageMetadata?.candidatesTokenCount || this.calculateTokens(content),
          totalTokens: data.usageMetadata?.totalTokenCount || 0,
        },
        model: config.model || 'gemini-pro-vision',
        finishReason: candidate.finishReason || 'STOP',
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'Gemini');
    }
  }

  async embedText(text: string): Promise<number[]> {
    try {
      const response = await this.client.post(
        `/models/embedding-001:embedContent?key=${this.config.apiKey}`,
        {
          content: {
            parts: [{ text }],
          },
        }
      );

      return response.data.embedding.values;
    } catch (error) {
      this.handleError(error, 'Gemini');
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.generateText([
        { role: 'user', content: 'Hello' }
      ], { maxTokens: 5 });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Gemini specific methods
  async countTokens(text: string): Promise<number> {
    try {
      const response = await this.client.post(
        `/models/${this.config.model || 'gemini-pro'}:countTokens?key=${this.config.apiKey}`,
        {
          contents: [
            {
              parts: [{ text }],
            },
          ],
        }
      );

      return response.data.totalTokens;
    } catch (error) {
      return this.calculateTokens(text);
    }
  }

  async listModels(): Promise<string[]> {
    try {
      const response = await this.client.get(`/models?key=${this.config.apiKey}`);
      return response.data.models
        .filter((model: any) => model.name.includes('gemini'))
        .map((model: any) => model.name.split('/').pop());
    } catch (error) {
      this.handleError(error, 'Gemini');
    }
  }

  private convertMessagesToGeminiFormat(messages: AIMessage[]): any[] {
    const contents: any[] = [];
    
    for (const message of messages) {
      if (message.role === 'system') {
        // Gemini doesn't have system role, prepend to first user message
        continue;
      }

      const role = message.role === 'assistant' ? 'model' : 'user';
      contents.push({
        role,
        parts: [{ text: message.content }],
      });
    }

    // Add system message to first user message if exists
    const systemMessage = messages.find(m => m.role === 'system');
    if (systemMessage && contents.length > 0 && contents[0].role === 'user') {
      contents[0].parts[0].text = `${systemMessage.content}\n\n${contents[0].parts[0].text}`;
    }

    return contents;
  }

  async generateWithSafety(
    messages: AIMessage[],
    safetyLevel: 'BLOCK_NONE' | 'BLOCK_FEW' | 'BLOCK_SOME' | 'BLOCK_MOST' = 'BLOCK_SOME'
  ): Promise<AIResponse> {
    const contents = this.convertMessagesToGeminiFormat(messages);

    const response = await this.client.post(
      `/models/${this.config.model || 'gemini-pro'}:generateContent?key=${this.config.apiKey}`,
      {
        contents,
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: safetyLevel
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: safetyLevel
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: safetyLevel
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: safetyLevel
          }
        ]
      }
    );

    const data = response.data;
    const candidate = data.candidates[0];

    return {
      content: candidate.content.parts[0].text,
      usage: {
        promptTokens: data.usageMetadata?.promptTokenCount || 0,
        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: data.usageMetadata?.totalTokenCount || 0,
      },
      model: this.config.model || 'gemini-pro',
      finishReason: candidate.finishReason || 'STOP',
      responseTime: 0,
    };
  }
}
