import { Injectable, Logger, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, In } from 'typeorm';
import { Permission, SystemModule, PermissionAction } from '../entities/permission.entity';
import { PermissionGroup } from '../entities/permission-group.entity';
import { PermissionGroupItem } from '../entities/permission-group-item.entity';
import { RBACService } from './rbac.service';
import { CreatePermissionDto, UpdatePermissionDto, CreatePermissionGroupDto } from '../dto/permission.dto';

/**
 * Permission Service - Dịch vụ quản lý quyền hạn
 * Permission Service - Permission management service
 */
@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);

  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(PermissionGroup)
    private readonly permissionGroupRepository: Repository<PermissionGroup>,
    @InjectRepository(PermissionGroupItem)
    private readonly permissionGroupItemRepository: Repository<PermissionGroupItem>,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Tạo quyền mới - Create new permission
   */
  async createPermission(createPermissionDto: CreatePermissionDto, createdBy: string): Promise<Permission> {
    // Kiểm tra quyền tạo permission
    const canCreate = await this.rbacService.checkPermission(createdBy, 'ROLE_MANAGEMENT_CREATE');
    if (!canCreate.allowed) {
      throw new ForbiddenException('You do not have permission to create permissions');
    }

    // Tạo code tự động nếu không có
    const code = createPermissionDto.code || 
      Permission.generateCode(createPermissionDto.module, createPermissionDto.action, createPermissionDto.resource);

    // Kiểm tra code đã tồn tại
    const existingPermission = await this.permissionRepository.findOne({
      where: { code },
    });

    if (existingPermission) {
      throw new ConflictException('Permission code already exists');
    }

    // Tạo permission mới
    const permission = this.permissionRepository.create({
      ...createPermissionDto,
      code,
    });

    const savedPermission = await this.permissionRepository.save(permission);

    // Invalidate cache
    await this.rbacService.invalidateAllCaches();

    this.logger.log(`Permission ${savedPermission.code} created by user ${createdBy}`);
    return savedPermission;
  }

  /**
   * Cập nhật quyền - Update permission
   */
  async updatePermission(id: string, updatePermissionDto: UpdatePermissionDto, updatedBy: string): Promise<Permission> {
    // Kiểm tra quyền cập nhật
    const canUpdate = await this.rbacService.checkPermission(updatedBy, 'ROLE_MANAGEMENT_UPDATE');
    if (!canUpdate.allowed) {
      throw new ForbiddenException('You do not have permission to update permissions');
    }

    const permission = await this.permissionRepository.findOne({ where: { id } });
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    // Kiểm tra code nếu thay đổi
    if (updatePermissionDto.code && updatePermissionDto.code !== permission.code) {
      const existingPermission = await this.permissionRepository.findOne({
        where: { code: updatePermissionDto.code },
      });

      if (existingPermission) {
        throw new ConflictException('Permission code already exists');
      }
    }

    // Cập nhật permission
    Object.assign(permission, updatePermissionDto);
    const updatedPermission = await this.permissionRepository.save(permission);

    // Invalidate cache
    await this.rbacService.invalidateAllCaches();

    this.logger.log(`Permission ${updatedPermission.code} updated by user ${updatedBy}`);
    return updatedPermission;
  }

  /**
   * Xóa quyền - Delete permission
   */
  async deletePermission(id: string, deletedBy: string): Promise<void> {
    // Kiểm tra quyền xóa
    const canDelete = await this.rbacService.checkPermission(deletedBy, 'ROLE_MANAGEMENT_DELETE');
    if (!canDelete.allowed) {
      throw new ForbiddenException('You do not have permission to delete permissions');
    }

    const permission = await this.permissionRepository.findOne({ where: { id } });
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    // Kiểm tra permission có đang được sử dụng không
    const rolePermissionCount = await this.permissionRepository
      .createQueryBuilder('permission')
      .leftJoin('permission.rolePermissions', 'rolePermission')
      .where('permission.id = :id', { id })
      .getCount();

    if (rolePermissionCount > 0) {
      throw new ForbiddenException('Cannot delete permission that is currently assigned to roles');
    }

    await this.permissionRepository.remove(permission);

    // Invalidate cache
    await this.rbacService.invalidateAllCaches();

    this.logger.log(`Permission ${permission.code} deleted by user ${deletedBy}`);
  }

  /**
   * Lấy danh sách quyền - Get permissions list
   */
  async getPermissions(options?: FindManyOptions<Permission>): Promise<Permission[]> {
    return this.permissionRepository.find({
      ...options,
      order: { module: 'ASC', action: 'ASC' },
    });
  }

  /**
   * Lấy quyền theo ID - Get permission by ID
   */
  async getPermissionById(id: string): Promise<Permission> {
    const permission = await this.permissionRepository.findOne({
      where: { id },
      relations: ['rolePermissions', 'rolePermissions.role'],
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  /**
   * Lấy quyền theo module - Get permissions by module
   */
  async getPermissionsByModule(module: SystemModule): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { module, isActive: true },
      order: { action: 'ASC' },
    });
  }

  /**
   * Lấy quyền theo action - Get permissions by action
   */
  async getPermissionsByAction(action: PermissionAction): Promise<Permission[]> {
    return this.permissionRepository.find({
      where: { action, isActive: true },
      order: { module: 'ASC' },
    });
  }

  /**
   * Tìm kiếm quyền - Search permissions
   */
  async searchPermissions(query: string): Promise<Permission[]> {
    return this.permissionRepository
      .createQueryBuilder('permission')
      .where('permission.code ILIKE :query', { query: `%${query}%` })
      .orWhere('permission.description ILIKE :query', { query: `%${query}%` })
      .andWhere('permission.isActive = :isActive', { isActive: true })
      .orderBy('permission.module', 'ASC')
      .addOrderBy('permission.action', 'ASC')
      .getMany();
  }

  /**
   * Tạo nhóm quyền - Create permission group
   */
  async createPermissionGroup(createGroupDto: CreatePermissionGroupDto, createdBy: string): Promise<PermissionGroup> {
    // Kiểm tra quyền tạo nhóm permission
    const canCreate = await this.rbacService.checkPermission(createdBy, 'ROLE_MANAGEMENT_CREATE');
    if (!canCreate.allowed) {
      throw new ForbiddenException('You do not have permission to create permission groups');
    }

    // Kiểm tra tên nhóm đã tồn tại
    const existingGroup = await this.permissionGroupRepository.findOne({
      where: { name: createGroupDto.name },
    });

    if (existingGroup) {
      throw new ConflictException('Permission group name already exists');
    }

    // Tạo nhóm mới
    const group = this.permissionGroupRepository.create(createGroupDto);
    const savedGroup = await this.permissionGroupRepository.save(group);

    // Thêm permissions vào nhóm nếu có
    if (createGroupDto.permissionIds && createGroupDto.permissionIds.length > 0) {
      await this.addPermissionsToGroup(savedGroup.id, createGroupDto.permissionIds);
    }

    this.logger.log(`Permission group ${savedGroup.name} created by user ${createdBy}`);
    return savedGroup;
  }

  /**
   * Thêm quyền vào nhóm - Add permissions to group
   */
  async addPermissionsToGroup(groupId: string, permissionIds: string[]): Promise<void> {
    // Kiểm tra nhóm tồn tại
    const group = await this.permissionGroupRepository.findOne({ where: { id: groupId } });
    if (!group) {
      throw new NotFoundException('Permission group not found');
    }

    // Kiểm tra permissions tồn tại
    const permissions = await this.permissionRepository.find({
      where: { id: In(permissionIds) }
    });
    if (permissions.length !== permissionIds.length) {
      throw new NotFoundException('Some permissions not found');
    }

    // Thêm permissions vào nhóm
    for (const permission of permissions) {
      const existingItem = await this.permissionGroupItemRepository.findOne({
        where: { groupId, permissionId: permission.id },
      });

      if (!existingItem) {
        const groupItem = this.permissionGroupItemRepository.create({
          groupId,
          permissionId: permission.id,
        });

        await this.permissionGroupItemRepository.save(groupItem);
      }
    }

    this.logger.log(`Permissions added to group ${group.name}`);
  }

  /**
   * Xóa quyền khỏi nhóm - Remove permissions from group
   */
  async removePermissionsFromGroup(groupId: string, permissionIds: string[]): Promise<void> {
    await this.permissionGroupItemRepository.delete({
      groupId,
      permissionId: In(permissionIds),
    });

    this.logger.log(`Permissions removed from group ${groupId}`);
  }

  /**
   * Lấy danh sách nhóm quyền - Get permission groups
   */
  async getPermissionGroups(): Promise<PermissionGroup[]> {
    return this.permissionGroupRepository.find({
      relations: ['permissionGroupItems', 'permissionGroupItems.permission'],
      order: { module: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Lấy nhóm quyền theo ID - Get permission group by ID
   */
  async getPermissionGroupById(id: string): Promise<PermissionGroup> {
    const group = await this.permissionGroupRepository.findOne({
      where: { id },
      relations: ['permissionGroupItems', 'permissionGroupItems.permission'],
    });

    if (!group) {
      throw new NotFoundException('Permission group not found');
    }

    return group;
  }

  /**
   * Lấy tất cả modules - Get all modules
   */
  getSystemModules(): SystemModule[] {
    return Object.values(SystemModule);
  }

  /**
   * Lấy tất cả actions - Get all actions
   */
  getPermissionActions(): PermissionAction[] {
    return Object.values(PermissionAction);
  }

  /**
   * Tạo permissions mặc định cho module - Create default permissions for module
   */
  async createDefaultPermissionsForModule(module: SystemModule): Promise<Permission[]> {
    const actions = Object.values(PermissionAction);
    const permissions: Permission[] = [];

    for (const action of actions) {
      const code = Permission.generateCode(module, action);
      
      // Kiểm tra đã tồn tại chưa
      const existingPermission = await this.permissionRepository.findOne({ where: { code } });
      
      if (!existingPermission) {
        const permission = this.permissionRepository.create({
          code,
          module,
          action,
          description: `${action} permission for ${module}`,
        });

        const savedPermission = await this.permissionRepository.save(permission);
        permissions.push(savedPermission);
      }
    }

    if (permissions.length > 0) {
      await this.rbacService.invalidateAllCaches();
      this.logger.log(`Created ${permissions.length} default permissions for module ${module}`);
    }

    return permissions;
  }
}
