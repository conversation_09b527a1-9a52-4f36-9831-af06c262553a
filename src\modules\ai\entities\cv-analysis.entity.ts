import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum AnalysisStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum RecommendationAction {
  ACCEPT = 'accept',
  REJECT = 'reject',
  INTERVIEW = 'interview',
  REVIEW = 'review',
}

@Entity('cv_analyses')
@Index(['userId', 'status'])
@Index(['overallScore', 'status'])
export class CvAnalysis {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  fileName: string;

  @Column()
  fileSize: number;

  @Column({ nullable: true })
  filePath: string;

  @Column({ type: 'text', nullable: true })
  jobDescription: string;

  @Column({ type: 'text', nullable: true })
  requirements: string;

  @Column({
    type: 'enum',
    enum: AnalysisStatus,
    default: AnalysisStatus.PENDING,
  })
  status: AnalysisStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  overallScore: number;

  @Column({ type: 'jsonb', nullable: true })
  scores: {
    experience: number;
    skills: number;
    education: number;
    achievements: number;
    communication: number;
    cultural_fit: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  extractedData: {
    personalInfo: {
      name?: string;
      email?: string;
      phone?: string;
      location?: string;
      linkedin?: string;
    };
    experience: Array<{
      company: string;
      position: string;
      duration: string;
      description: string;
      skills: string[];
    }>;
    education: Array<{
      institution: string;
      degree: string;
      field: string;
      year: string;
      gpa?: string;
    }>;
    skills: {
      technical: string[];
      soft: string[];
      languages: string[];
      certifications: string[];
    };
    achievements: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  analysis: {
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    keywordMatch: {
      matched: string[];
      missing: string[];
      score: number;
    };
    experienceAnalysis: {
      totalYears: number;
      relevantYears: number;
      careerProgression: string;
      industryFit: string;
    };
    redFlags: string[];
    culturalFit: {
      score: number;
      reasoning: string;
    };
  };

  @Column({
    type: 'enum',
    enum: RecommendationAction,
    nullable: true,
  })
  recommendation: RecommendationAction;

  @Column({ type: 'text', nullable: true })
  recommendationReason: string;

  @Column({ type: 'text', nullable: true })
  aiSummary: string;

  @Column({ type: 'text', nullable: true })
  interviewQuestions: string;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ nullable: true })
  processedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isCompleted(): boolean {
    return this.status === AnalysisStatus.COMPLETED;
  }

  get hasFailed(): boolean {
    return this.status === AnalysisStatus.FAILED;
  }

  get shouldAccept(): boolean {
    return this.recommendation === RecommendationAction.ACCEPT;
  }

  get shouldReject(): boolean {
    return this.recommendation === RecommendationAction.REJECT;
  }

  get shouldInterview(): boolean {
    return this.recommendation === RecommendationAction.INTERVIEW;
  }
}
