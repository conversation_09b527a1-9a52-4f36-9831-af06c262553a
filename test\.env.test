# Test Environment Configuration

# Application
NODE_ENV=test
PORT=3001

# Database (SQLite in-memory for tests)
DATABASE_TYPE=sqlite
DATABASE_DATABASE=:memory:
DATABASE_SYNCHRONIZE=true
DATABASE_LOGGING=false

# JWT
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=1h

# Redis (Mock in tests)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email (Mock in tests)
SMTP_HOST=smtp.test.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=test-password
FROM_EMAIL=<EMAIL>

# Frontend URL
FRONTEND_URL=http://localhost:3000

# AI Providers (Test configurations)
# Note: Use real API keys for integration tests, or mock for unit tests

# OpenAI (Optional - use real key for integration tests)
OPENAI_API_KEY=sk-test-openai-key-or-real-key-for-integration
OPENAI_BASE_URL=https://api.openai.com/v1

# Grok (Optional - use real key for integration tests)
GROK_API_KEY=xai-test-grok-key-or-real-key-for-integration
GROK_BASE_URL=https://api.x.ai/v1

# Gemini (Optional - use real key for integration tests)
GEMINI_API_KEY=test-gemini-key-or-real-key-for-integration
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

# OLLAMA (Local server for testing)
OLLAMA_BASE_URL=http://localhost:11434

# Test-specific settings
SILENT_TESTS=true
TEST_TIMEOUT=30000
MOCK_EXTERNAL_APIS=true

# Logging
LOG_LEVEL=error
LOG_FILE=false

# Security
BCRYPT_ROUNDS=4
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=1000

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./test/uploads

# Session
SESSION_SECRET=test-session-secret
SESSION_MAX_AGE=3600000
