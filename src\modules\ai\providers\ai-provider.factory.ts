import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseAIProvider, AIProviderConfig } from './base-ai.provider';
import { OpenAIProvider } from './openai.provider';
import { GrokProvider } from './grok.provider';
import { GeminiProvider } from './gemini.provider';
import { OllamaProvider } from './ollama.provider';
import { AIProvider, AIModelName } from '../entities/ai-model.entity';

export interface ProviderModelConfig {
  provider: AIProvider;
  model: AIModelName;
  apiKey: string;
  baseURL?: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

@Injectable()
export class AIProviderFactory {
  constructor(private configService: ConfigService) {}

  createProvider(config: ProviderModelConfig): BaseAIProvider {
    const providerConfig: AIProviderConfig = {
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      model: config.model,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
      topP: config.topP,
      frequencyPenalty: config.frequencyPenalty,
      presencePenalty: config.presencePenalty,
    };

    switch (config.provider) {
      case AIProvider.OPENAI:
        return new OpenAIProvider(providerConfig);

      case AIProvider.GROK:
        return new GrokProvider(providerConfig);

      case AIProvider.GEMINI:
        return new GeminiProvider(providerConfig);

      case AIProvider.OLLAMA:
        return new OllamaProvider(providerConfig);

      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  createProviderFromEnv(provider: AIProvider, model?: AIModelName): BaseAIProvider {
    const configs = this.getProviderConfigs();
    const providerConfig = configs[provider];

    if (!providerConfig || !providerConfig.apiKey) {
      throw new Error(`No configuration found for provider: ${provider}`);
    }

    return this.createProvider({
      provider,
      model: model || this.getDefaultModel(provider),
      apiKey: providerConfig.apiKey,
      baseURL: providerConfig.baseURL,
      temperature: providerConfig.temperature,
      maxTokens: providerConfig.maxTokens,
      topP: providerConfig.topP,
      frequencyPenalty: providerConfig.frequencyPenalty,
      presencePenalty: providerConfig.presencePenalty,
    });
  }

  getAvailableProviders(): AIProvider[] {
    const configs = this.getProviderConfigs();
    return Object.keys(configs)
      .filter(provider => configs[provider as AIProvider]?.apiKey)
      .map(provider => provider as AIProvider);
  }

  getProviderModels(provider: AIProvider): AIModelName[] {
    const modelMap: Record<AIProvider, AIModelName[]> = {
      [AIProvider.OPENAI]: [
        AIModelName.GPT_3_5_TURBO,
        AIModelName.GPT_4,
        AIModelName.GPT_4_TURBO,
        AIModelName.GPT_4O,
      ],
      [AIProvider.GROK]: [
        AIModelName.GROK_1,
        AIModelName.GROK_1_5,
        AIModelName.GROK_2,
      ],
      [AIProvider.GEMINI]: [
        AIModelName.GEMINI_PRO,
        AIModelName.GEMINI_PRO_VISION,
        AIModelName.GEMINI_ULTRA,
        AIModelName.GEMINI_1_5_PRO,
      ],
      [AIProvider.ANTHROPIC]: [
        AIModelName.CLAUDE_3_HAIKU,
        AIModelName.CLAUDE_3_SONNET,
        AIModelName.CLAUDE_3_OPUS,
      ],
      [AIProvider.OLLAMA]: [
        AIModelName.LLAMA_2_7B,
        AIModelName.LLAMA_2_13B,
        AIModelName.LLAMA_2_70B,
        AIModelName.CODE_LLAMA_7B,
        AIModelName.CODE_LLAMA_13B,
        AIModelName.CODE_LLAMA_34B,
        AIModelName.MISTRAL_7B,
        AIModelName.MISTRAL_INSTRUCT,
        AIModelName.PHI_3_MINI,
        AIModelName.PHI_3_MEDIUM,
        AIModelName.NEURAL_CHAT,
        AIModelName.STARCODE,
        AIModelName.VICUNA_7B,
        AIModelName.VICUNA_13B,
      ],
      [AIProvider.CUSTOM]: [],
    };

    return modelMap[provider] || [];
  }

  getDefaultModel(provider: AIProvider): AIModelName {
    const defaultModels: Record<AIProvider, AIModelName> = {
      [AIProvider.OPENAI]: AIModelName.GPT_4_TURBO,
      [AIProvider.GROK]: AIModelName.GROK_1_5,
      [AIProvider.GEMINI]: AIModelName.GEMINI_PRO,
      [AIProvider.ANTHROPIC]: AIModelName.CLAUDE_3_SONNET,
      [AIProvider.OLLAMA]: AIModelName.LLAMA_2_7B,
      [AIProvider.CUSTOM]: AIModelName.GPT_3_5_TURBO,
    };

    return defaultModels[provider];
  }

  getModelCapabilities(model: AIModelName): {
    textGeneration: boolean;
    imageAnalysis: boolean;
    codeGeneration: boolean;
    embedding: boolean;
    streaming: boolean;
    maxTokens: number;
    contextLength: number;
  } {
    const capabilities = {
      [AIModelName.GPT_3_5_TURBO]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16385,
      },
      [AIModelName.GPT_4]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 8192,
        contextLength: 8192,
      },
      [AIModelName.GPT_4_TURBO]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 128000,
      },
      [AIModelName.GPT_4O]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 128000,
      },
      [AIModelName.GROK_1]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 8192,
      },
      [AIModelName.GROK_1_5]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16384,
      },
      [AIModelName.GROK_2]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 8192,
        contextLength: 32768,
      },
      [AIModelName.GEMINI_PRO]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: true,
        streaming: true,
        maxTokens: 8192,
        contextLength: 32768,
      },
      [AIModelName.GEMINI_PRO_VISION]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16384,
      },
      [AIModelName.GEMINI_ULTRA]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: true,
        streaming: true,
        maxTokens: 8192,
        contextLength: 32768,
      },
      [AIModelName.GEMINI_1_5_PRO]: {
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: true,
        streaming: true,
        maxTokens: 8192,
        contextLength: 1000000, // 1M tokens
      },
      // OLLAMA Models
      [AIModelName.LLAMA_2_7B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.LLAMA_2_13B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.LLAMA_2_70B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.CODE_LLAMA_7B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16384,
      },
      [AIModelName.CODE_LLAMA_13B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16384,
      },
      [AIModelName.CODE_LLAMA_34B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 16384,
      },
      [AIModelName.MISTRAL_7B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 8192,
      },
      [AIModelName.MISTRAL_INSTRUCT]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 8192,
      },
      [AIModelName.PHI_3_MINI]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.PHI_3_MEDIUM]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.NEURAL_CHAT]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.STARCODE]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 8192,
      },
      [AIModelName.VICUNA_7B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
      [AIModelName.VICUNA_13B]: {
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      },
    };

    return capabilities[model] || {
      textGeneration: true,
      imageAnalysis: false,
      codeGeneration: false,
      embedding: false,
      streaming: false,
      maxTokens: 2048,
      contextLength: 4096,
    };
  }

  private getProviderConfigs(): Record<AIProvider, Partial<ProviderModelConfig>> {
    return {
      [AIProvider.OPENAI]: {
        apiKey: this.configService.get('OPENAI_API_KEY'),
        baseURL: this.configService.get('OPENAI_BASE_URL'),
      },
      [AIProvider.GROK]: {
        apiKey: this.configService.get('GROK_API_KEY'),
        baseURL: this.configService.get('GROK_BASE_URL'),
      },
      [AIProvider.GEMINI]: {
        apiKey: this.configService.get('GEMINI_API_KEY'),
        baseURL: this.configService.get('GEMINI_BASE_URL'),
      },
      [AIProvider.ANTHROPIC]: {
        apiKey: this.configService.get('ANTHROPIC_API_KEY'),
        baseURL: this.configService.get('ANTHROPIC_BASE_URL'),
      },
      [AIProvider.OLLAMA]: {
        apiKey: 'not-required', // OLLAMA doesn't require API key
        baseURL: this.configService.get('OLLAMA_BASE_URL') || 'http://localhost:11434',
      },
      [AIProvider.CUSTOM]: {},
    };
  }

  async validateProvider(provider: AIProvider): Promise<{
    isValid: boolean;
    error?: string;
    models?: string[];
  }> {
    try {
      const aiProvider = this.createProviderFromEnv(provider);
      const isValid = await aiProvider.validateConfig();

      if (!isValid) {
        return { isValid: false, error: 'Invalid configuration' };
      }

      // Try to get available models if provider supports it
      let models: string[] = [];
      if (provider === AIProvider.OPENAI && 'listModels' in aiProvider) {
        models = await (aiProvider as OpenAIProvider).listModels();
      } else if (provider === AIProvider.GEMINI && 'listModels' in aiProvider) {
        models = await (aiProvider as GeminiProvider).listModels();
      }

      return { isValid: true, models };
    } catch (error) {
      return {
        isValid: false,
        error: error.message || 'Unknown error'
      };
    }
  }
}
