import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSecurityFieldsToUsers1700000003 implements MigrationInterface {
  name = 'AddSecurityFieldsToUsers1700000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create security method enum type
    await queryRunner.query(`
      CREATE TYPE "security_method_enum" AS ENUM('disabled', 'email_verification', 'two_factor_auth')
    `);

    // Add security method column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'securityMethod',
        type: 'enum',
        enum: ['disabled', 'email_verification', 'two_factor_auth'],
        default: "'disabled'",
        isNullable: false,
      })
    );

    // Add two-factor secret column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'twoFactorSecret',
        type: 'varchar',
        length: '255',
        isNullable: true,
      })
    );

    // Add two-factor enabled column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'twoFactorEnabled',
        type: 'boolean',
        default: false,
        isNullable: false,
      })
    );

    // Add verification code column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'verificationCode',
        type: 'varchar',
        length: '10',
        isNullable: true,
      })
    );

    // Add verification code expires column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'verificationCodeExpires',
        type: 'timestamp',
        isNullable: true,
      })
    );

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_security_method ON users("securityMethod")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_verification_code ON users("verificationCode")
    `);

    // Update existing users to have disabled security method by default
    await queryRunner.query(`
      UPDATE users 
      SET "securityMethod" = 'disabled' 
      WHERE "securityMethod" IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_verification_code`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_security_method`);

    // Drop columns
    await queryRunner.dropColumn('users', 'verificationCodeExpires');
    await queryRunner.dropColumn('users', 'verificationCode');
    await queryRunner.dropColumn('users', 'twoFactorEnabled');
    await queryRunner.dropColumn('users', 'twoFactorSecret');
    await queryRunner.dropColumn('users', 'securityMethod');

    // Drop enum type
    await queryRunner.query(`DROP TYPE IF EXISTS "security_method_enum"`);
  }
}
