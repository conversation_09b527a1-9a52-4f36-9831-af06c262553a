import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, LessThan } from 'typeorm';
import { UserSession, DeviceType } from '../entities/user-session.entity';
import { SSOAuditLog, SSOAction } from '../entities/sso-audit-log.entity';
import { DeviceFingerprintService, DeviceInfo } from './device-fingerprint.service';
import { SSOConfigService } from './sso-config.service';
import { LoggerService } from '../../../common/services/logger.service';

/**
 * Interface cho thông tin tạo session
 * Interface for session creation information
 */
export interface CreateSessionInfo {
  userId: string;
  deviceInfo: DeviceInfo;
  deviceName?: string;
  location?: string;
}

/**
 * Interface cho thông tin session
 * Interface for session information
 */
export interface SessionInfo {
  sessionId: string;
  userId: string;
  deviceId: string;
  deviceName?: string;
  deviceType: DeviceType;
  ipAddress: string;
  location?: string;
  isActive: boolean;
  lastActivityAt: Date;
  expiresAt: Date;
  createdAt: Date;
  remainingMinutes: number;
  isCurrent: boolean;
}

/**
 * SessionManagementService - Quản lý phiên đăng nhập SSO
 * SessionManagementService - Manages SSO login sessions
 */
@Injectable()
export class SessionManagementService {
  constructor(
    @InjectRepository(UserSession)
    private sessionRepository: Repository<UserSession>,
    @InjectRepository(SSOAuditLog)
    private auditRepository: Repository<SSOAuditLog>,
    private deviceFingerprintService: DeviceFingerprintService,
    private ssoConfigService: SSOConfigService,
    private logger: LoggerService,
  ) {}

  /**
   * Tạo session mới
   * Create new session
   */
  async createSession(sessionInfo: CreateSessionInfo): Promise<UserSession> {
    const { userId, deviceInfo, deviceName, location } = sessionInfo;

    // Analyze device
    const deviceAnalysis = this.deviceFingerprintService.analyzeDevice(deviceInfo);

    // Check for suspicious device
    if (deviceAnalysis.isSuspicious && this.ssoConfigService.requiresDeviceVerification()) {
      await this.logAudit(SSOAction.SUSPICIOUS_ACTIVITY, false, {
        userId,
        ipAddress: deviceInfo.ipAddress,
        userAgent: deviceInfo.userAgent,
        deviceId: deviceAnalysis.deviceId,
        errorMessage: `Suspicious device detected: ${deviceAnalysis.suspiciousReasons.join(', ')}`,
        metadata: { trustScore: deviceAnalysis.trustScore },
      });

      throw new ForbiddenException('Device verification required for suspicious device');
    }

    // Check concurrent sessions limit
    await this.enforceSessionLimit(userId);

    // Create session
    const session = new UserSession();
    session.sessionId = UserSession.generateSessionId();
    session.userId = userId;
    session.deviceId = deviceAnalysis.deviceId;
    session.deviceFingerprint = deviceAnalysis.fingerprint;
    session.deviceName = deviceName || this.generateDeviceName(deviceAnalysis);
    session.deviceType = this.mapDeviceType(deviceAnalysis.deviceType);
    session.ipAddress = deviceInfo.ipAddress;
    session.userAgent = deviceInfo.userAgent;
    session.location = location;
    session.isActive = true;
    session.lastActivityAt = new Date();
    session.expiresAt = new Date(Date.now() + this.ssoConfigService.getSessionTimeout());

    const savedSession = await this.sessionRepository.save(session);

    // Log session creation
    await this.logAudit(SSOAction.SESSION_CREATE, true, {
      userId,
      sessionId: session.sessionId,
      ipAddress: deviceInfo.ipAddress,
      userAgent: deviceInfo.userAgent,
      deviceId: deviceAnalysis.deviceId,
      metadata: {
        deviceType: deviceAnalysis.deviceType,
        trustScore: deviceAnalysis.trustScore,
      },
    });

    this.logger.logWithContext(
      `Session created for user ${userId}: ${session.sessionId}`,
      'SessionManagementService'
    );

    return savedSession;
  }

  /**
   * Lấy session theo ID
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<UserSession | null> {
    return this.sessionRepository.findOne({
      where: { sessionId, isActive: true },
      relations: ['user'],
    });
  }

  /**
   * Lấy tất cả session của user
   * Get all user sessions
   */
  async getUserSessions(userId: string, includeInactive: boolean = false): Promise<SessionInfo[]> {
    const whereCondition: any = { userId };
    if (!includeInactive) {
      whereCondition.isActive = true;
    }

    const sessions = await this.sessionRepository.find({
      where: whereCondition,
      order: { lastActivityAt: 'DESC' },
    });

    return sessions.map(session => this.mapToSessionInfo(session));
  }

  /**
   * Cập nhật hoạt động session
   * Update session activity
   */
  async updateActivity(sessionId: string, deviceInfo?: DeviceInfo): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    // Check for suspicious activity
    if (deviceInfo && this.isSuspiciousActivity(session, deviceInfo)) {
      await this.logAudit(SSOAction.SUSPICIOUS_ACTIVITY, false, {
        userId: session.userId,
        sessionId,
        ipAddress: deviceInfo.ipAddress,
        userAgent: deviceInfo.userAgent,
        deviceId: session.deviceId,
        errorMessage: 'Suspicious activity detected: IP or User Agent changed',
      });

      // Optionally terminate session for security
      if (this.ssoConfigService.requiresDeviceVerification()) {
        await this.terminateSession(sessionId, 'Suspicious activity detected');
        return;
      }
    }

    session.updateActivity();
    await this.sessionRepository.save(session);
  }

  /**
   * Gia hạn session
   * Extend session
   */
  async extendSession(sessionId: string, minutes: number = 60): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    session.extend(minutes);
    await this.sessionRepository.save(session);

    await this.logAudit(SSOAction.SESSION_EXTEND, true, {
      userId: session.userId,
      sessionId,
      metadata: { extendedMinutes: minutes },
    });
  }

  /**
   * Terminate session
   * Terminate session
   */
  async terminateSession(sessionId: string, reason?: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    session.deactivate();
    await this.sessionRepository.save(session);

    await this.logAudit(SSOAction.SESSION_TERMINATE, true, {
      userId: session.userId,
      sessionId,
      metadata: { reason },
    });

    this.logger.logWithContext(
      `Session terminated: ${sessionId} (${reason || 'Manual termination'})`,
      'SessionManagementService'
    );
  }

  /**
   * Terminate tất cả session của user
   * Terminate all user sessions
   */
  async terminateAllUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    const whereCondition: any = { userId, isActive: true };
    if (excludeSessionId) {
      whereCondition.sessionId = { $ne: excludeSessionId };
    }

    const sessions = await this.sessionRepository.find({ where: whereCondition });

    for (const session of sessions) {
      session.deactivate();
    }

    await this.sessionRepository.save(sessions);

    await this.logAudit(SSOAction.GLOBAL_LOGOUT, true, {
      userId,
      metadata: { 
        terminatedSessions: sessions.length,
        excludedSession: excludeSessionId,
      },
    });

    this.logger.logWithContext(
      `All sessions terminated for user ${userId}: ${sessions.length} sessions`,
      'SessionManagementService'
    );

    return sessions.length;
  }

  /**
   * Validate session
   * Validate session
   */
  async validateSession(sessionId: string, deviceInfo?: DeviceInfo): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session || !session.isValid) {
      return false;
    }

    // Update activity if device info provided
    if (deviceInfo) {
      await this.updateActivity(sessionId, deviceInfo);
    }

    return true;
  }

  /**
   * Cleanup expired sessions
   * Cleanup expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const expiredSessions = await this.sessionRepository.find({
      where: {
        expiresAt: LessThan(new Date()),
        isActive: true,
      },
    });

    for (const session of expiredSessions) {
      session.deactivate();
    }

    await this.sessionRepository.save(expiredSessions);

    await this.logAudit(SSOAction.SESSION_CLEANUP, true, {
      metadata: { cleanedSessions: expiredSessions.length },
    });

    this.logger.logWithContext(
      `Cleaned up ${expiredSessions.length} expired sessions`,
      'SessionManagementService'
    );

    return expiredSessions.length;
  }

  /**
   * Lấy thống kê session
   * Get session statistics
   */
  async getSessionStatistics(userId?: string): Promise<any> {
    const query = this.sessionRepository.createQueryBuilder('session');

    if (userId) {
      query.where('session.userId = :userId', { userId });
    }

    const [
      totalSessions,
      activeSessions,
      expiredSessions,
      deviceTypeStats,
    ] = await Promise.all([
      query.getCount(),
      query.clone().andWhere('session.isActive = true').getCount(),
      query.clone().andWhere('session.expiresAt < :now', { now: new Date() }).getCount(),
      query.clone()
        .select('session.deviceType', 'deviceType')
        .addSelect('COUNT(*)', 'count')
        .groupBy('session.deviceType')
        .getRawMany(),
    ]);

    return {
      totalSessions,
      activeSessions,
      expiredSessions,
      deviceTypeStats,
    };
  }

  /**
   * Enforce session limit
   * Enforce session limit
   */
  private async enforceSessionLimit(userId: string): Promise<void> {
    const maxSessions = this.ssoConfigService.getMaxConcurrentSessions();
    const activeSessions = await this.sessionRepository.find({
      where: { userId, isActive: true },
      order: { lastActivityAt: 'ASC' },
    });

    if (activeSessions.length >= maxSessions) {
      // Terminate oldest sessions
      const sessionsToTerminate = activeSessions.slice(0, activeSessions.length - maxSessions + 1);
      
      for (const session of sessionsToTerminate) {
        session.deactivate();
      }

      await this.sessionRepository.save(sessionsToTerminate);

      await this.logAudit(SSOAction.MULTIPLE_SESSIONS, true, {
        userId,
        metadata: { 
          terminatedSessions: sessionsToTerminate.length,
          reason: 'Session limit exceeded',
        },
      });
    }
  }

  /**
   * Kiểm tra hoạt động đáng ngờ
   * Check for suspicious activity
   */
  private isSuspiciousActivity(session: UserSession, deviceInfo: DeviceInfo): boolean {
    return session.isSuspicious(deviceInfo.ipAddress, deviceInfo.userAgent);
  }

  /**
   * Map device type
   * Map device type
   */
  private mapDeviceType(deviceType: string): DeviceType {
    switch (deviceType.toUpperCase()) {
      case 'DESKTOP': return DeviceType.DESKTOP;
      case 'MOBILE': return DeviceType.MOBILE;
      case 'TABLET': return DeviceType.TABLET;
      case 'WEB': return DeviceType.WEB;
      case 'API': return DeviceType.API;
      default: return DeviceType.UNKNOWN;
    }
  }

  /**
   * Generate device name
   * Generate device name
   */
  private generateDeviceName(deviceAnalysis: any): string {
    const { browser, os, deviceType } = deviceAnalysis;
    return `${browser.name} on ${os.name} (${deviceType})`;
  }

  /**
   * Map to session info
   * Map to session info
   */
  private mapToSessionInfo(session: UserSession, currentSessionId?: string): SessionInfo {
    return {
      sessionId: session.sessionId,
      userId: session.userId,
      deviceId: session.deviceId || '',
      deviceName: session.deviceName,
      deviceType: session.deviceType || DeviceType.UNKNOWN,
      ipAddress: session.ipAddress || '',
      location: session.location,
      isActive: session.isActive,
      lastActivityAt: session.lastActivityAt,
      expiresAt: session.expiresAt,
      createdAt: session.createdAt,
      remainingMinutes: session.remainingMinutes,
      isCurrent: session.sessionId === currentSessionId,
    };
  }

  /**
   * Log audit event
   * Log audit event
   */
  private async logAudit(
    action: SSOAction,
    success: boolean,
    options: {
      userId?: string;
      sessionId?: string;
      application?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      deviceId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<void> {
    if (!this.ssoConfigService.isAuditLoggingEnabled()) {
      return;
    }

    const auditLog = SSOAuditLog.create(action, success, options);
    await this.auditRepository.save(auditLog);
  }
}
