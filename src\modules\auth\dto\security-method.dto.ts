import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SecurityMethod } from '../../users/entities/user.entity';

export class UpdateSecurityMethodDto {
  @ApiProperty({
    enum: SecurityMethod,
    example: SecurityMethod.EMAIL_VERIFICATION,
    description: 'Security verification method to enable'
  })
  @IsEnum(SecurityMethod)
  securityMethod: SecurityMethod;

  @ApiProperty({
    example: '123456',
    description: 'Verification code (required when enabling 2FA) or fixed code (required when enabling FIXED_CODE)',
    required: false
  })
  @IsOptional()
  @IsString()
  verificationCode?: string;

  @ApiProperty({
    example: '987654',
    description: 'Fixed code to set (6-8 digits, required when enabling FIXED_CODE method)',
    required: false
  })
  @IsOptional()
  @IsString()
  fixedCode?: string;
}

export class SecurityMethodResponse {
  @ApiProperty({
    enum: SecurityMethod,
    example: SecurityMethod.EMAIL_VERIFICATION,
    description: 'Current security verification method'
  })
  securityMethod: SecurityMethod;

  @ApiProperty({
    example: true,
    description: 'Whether two-factor authentication is enabled'
  })
  twoFactorEnabled: boolean;

  @ApiProperty({
    example: 'otpauth://totp/Delify:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Delify',
    description: 'QR code URL for setting up 2FA (only when enabling 2FA)',
    required: false
  })
  qrCodeUrl?: string;

  @ApiProperty({
    example: 'JBSWY3DPEHPK3PXP',
    description: 'Manual entry key for 2FA setup (only when enabling 2FA)',
    required: false
  })
  manualEntryKey?: string;

  @ApiProperty({
    example: true,
    description: 'Whether a fixed code is set for the user',
    required: false
  })
  hasFixedCode?: boolean;
}
