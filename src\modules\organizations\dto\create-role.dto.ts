import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, MaxLength, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateRoleDto {
  @ApiProperty({ example: 'Content Creator' })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ example: 'Can create and manage marketing content' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ 
    example: ['permission-uuid-1', 'permission-uuid-2'],
    description: 'Array of permission IDs to assign to this role'
  })
  @IsArray()
  @IsUUID(4, { each: true })
  permissionIds: string[];

  @ApiPropertyOptional({ 
    example: 60,
    description: 'Role priority (higher number = higher priority)'
  })
  @IsOptional()
  @IsNumber()
  priority?: number;

  @ApiPropertyOptional({
    example: {
      color: '#3b82f6',
      icon: 'user-edit',
      maxMembers: 10
    }
  })
  @IsOptional()
  metadata?: {
    color?: string;
    icon?: string;
    canBeDeleted?: boolean;
    canBeModified?: boolean;
    maxMembers?: number;
  };
}
