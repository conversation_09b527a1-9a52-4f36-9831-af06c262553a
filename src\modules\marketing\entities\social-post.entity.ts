import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum PostStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  PUBLISHED = 'published',
  FAILED = 'failed',
}

export enum SocialPlatform {
  FACEBOOK = 'facebook',
  TIKTOK = 'tiktok',
}

@Entity('social_posts')
@Index(['userId', 'platform'])
@Index(['status', 'scheduledAt'])
export class SocialPost {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'enum',
    enum: SocialPlatform,
  })
  platform: SocialPlatform;

  @Column({
    type: 'enum',
    enum: PostStatus,
    default: PostStatus.DRAFT,
  })
  status: PostStatus;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'jsonb', nullable: true })
  media: {
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  targetAccounts: {
    accountId: string;
    accountName: string;
    pageId?: string; // For Facebook pages
    groupId?: string; // For Facebook groups
  }[];

  @Column({ nullable: true })
  scheduledAt: Date;

  @Column({ nullable: true })
  publishedAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  platformPostId: {
    [key: string]: string; // platform-specific post IDs
  };

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    likes?: number;
    comments?: number;
    shares?: number;
    views?: number;
    engagement?: number;
  };

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    autoComment?: boolean;
    commentDelay?: number; // minutes
    commentText?: string;
    enableAnalytics?: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isScheduled(): boolean {
    return this.status === PostStatus.SCHEDULED && !!this.scheduledAt;
  }

  get isPublished(): boolean {
    return this.status === PostStatus.PUBLISHED;
  }

  get hasFailed(): boolean {
    return this.status === PostStatus.FAILED;
  }
}
