import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { PermissionService } from '../services/permission.service';
import { RBACService } from '../services/rbac.service';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  CreatePermissionGroupDto,
  CheckPermissionDto,
  PermissionResponseDto,
  PermissionGroupResponseDto,
  PermissionCheckResponseDto,
  PermissionListResponseDto,
  PermissionQueryDto,
  AddPermissionsToGroupDto,
  RemovePermissionsFromGroupDto,
} from '../dto/permission.dto';
import { RoleManagement, RBAC } from '../decorators/rbac.decorators';
import { SystemModule, PermissionAction } from '../entities/permission.entity';

/**
 * Permission Controller - Controller qu<PERSON>n lý quyền hạn
 * Permission Controller - Permission management controller
 */
@ApiTags('Permissions - Quản lý quyền hạn')
@Controller('permissions')
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Tạo quyền mới - Create new permission
   */
  @Post()
  @RoleManagement.Create()
  @ApiOperation({
    summary: 'Tạo quyền mới - Create new permission',
    description: 'Tạo một quyền mới trong hệ thống. Chỉ người có quyền ROLE_MANAGEMENT_CREATE mới có thể thực hiện.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Quyền được tạo thành công - Permission created successfully',
    type: PermissionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Mã quyền đã tồn tại - Permission code already exists',
  })
  @HttpCode(HttpStatus.CREATED)
  async createPermission(
    @Body() createPermissionDto: CreatePermissionDto,
    @Request() req: any,
  ): Promise<PermissionResponseDto> {
    const permission = await this.permissionService.createPermission(createPermissionDto, req.user.id);
    return this.mapToResponseDto(permission);
  }

  /**
   * Lấy danh sách quyền - Get permissions list
   */
  @Get()
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy danh sách quyền - Get permissions list',
    description: 'Lấy danh sách tất cả quyền trong hệ thống với khả năng lọc và phân trang.',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo mã hoặc mô tả - Search by code or description' })
  @ApiQuery({ name: 'module', required: false, description: 'Lọc theo module - Filter by module' })
  @ApiQuery({ name: 'action', required: false, description: 'Lọc theo hành động - Filter by action' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Lọc theo trạng thái - Filter by active status' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang - Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang - Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách quyền - Permissions list',
    type: PermissionListResponseDto,
  })
  async getPermissions(@Query() query: PermissionQueryDto): Promise<PermissionListResponseDto> {
    const queryBuilder = this.buildPermissionQuery(query);
    const permissions = await this.permissionService.getPermissions(queryBuilder);
    
    return {
      permissions: permissions.map(permission => this.mapToResponseDto(permission)),
      total: permissions.length,
      page: query.page ? parseInt(query.page) : undefined,
      limit: query.limit ? parseInt(query.limit) : undefined,
    };
  }

  /**
   * Lấy quyền theo ID - Get permission by ID
   */
  @Get(':id')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy quyền theo ID - Get permission by ID',
    description: 'Lấy thông tin chi tiết của một quyền cụ thể.',
  })
  @ApiParam({ name: 'id', description: 'ID quyền - Permission ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin quyền - Permission information',
    type: PermissionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy quyền - Permission not found',
  })
  async getPermissionById(@Param('id', ParseUUIDPipe) id: string): Promise<PermissionResponseDto> {
    const permission = await this.permissionService.getPermissionById(id);
    return this.mapToResponseDto(permission);
  }

  /**
   * Cập nhật quyền - Update permission
   */
  @Put(':id')
  @RoleManagement.Update()
  @ApiOperation({
    summary: 'Cập nhật quyền - Update permission',
    description: 'Cập nhật thông tin của một quyền.',
  })
  @ApiParam({ name: 'id', description: 'ID quyền - Permission ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Quyền được cập nhật thành công - Permission updated successfully',
    type: PermissionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy quyền - Permission not found',
  })
  async updatePermission(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
    @Request() req: any,
  ): Promise<PermissionResponseDto> {
    const permission = await this.permissionService.updatePermission(id, updatePermissionDto, req.user.id);
    return this.mapToResponseDto(permission);
  }

  /**
   * Xóa quyền - Delete permission
   */
  @Delete(':id')
  @RoleManagement.Delete()
  @ApiOperation({
    summary: 'Xóa quyền - Delete permission',
    description: 'Xóa một quyền khỏi hệ thống. Không thể xóa quyền đang được sử dụng.',
  })
  @ApiParam({ name: 'id', description: 'ID quyền - Permission ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Quyền được xóa thành công - Permission deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy quyền - Permission not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không thể xóa quyền này - Cannot delete this permission',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePermission(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.permissionService.deletePermission(id, req.user.id);
  }

  /**
   * Kiểm tra quyền của người dùng - Check user permission
   */
  @Post('check')
  @RBAC({ description: 'Kiểm tra quyền của người dùng - Check user permission' })
  @ApiOperation({
    summary: 'Kiểm tra quyền của người dùng - Check user permission',
    description: 'Kiểm tra xem người dùng có quyền cụ thể hay không.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết quả kiểm tra quyền - Permission check result',
    type: PermissionCheckResponseDto,
  })
  async checkPermission(@Body() checkPermissionDto: CheckPermissionDto): Promise<PermissionCheckResponseDto> {
    const result = await this.rbacService.checkPermission(
      checkPermissionDto.userId,
      checkPermissionDto.permission,
      checkPermissionDto.resource,
    );
    
    return {
      allowed: result.allowed,
      reason: result.reason,
      roleLevel: result.roleLevel,
      conflictingRoles: result.conflictingRoles,
    };
  }

  /**
   * Lấy quyền theo module - Get permissions by module
   */
  @Get('module/:module')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy quyền theo module - Get permissions by module',
    description: 'Lấy tất cả quyền thuộc về một module cụ thể.',
  })
  @ApiParam({ name: 'module', description: 'Tên module - Module name' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách quyền theo module - Permissions by module',
  })
  async getPermissionsByModule(@Param('module') module: SystemModule) {
    const permissions = await this.permissionService.getPermissionsByModule(module);
    return {
      module,
      permissions: permissions.map(permission => this.mapToResponseDto(permission)),
    };
  }

  /**
   * Lấy quyền theo action - Get permissions by action
   */
  @Get('action/:action')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy quyền theo action - Get permissions by action',
    description: 'Lấy tất cả quyền có cùng action.',
  })
  @ApiParam({ name: 'action', description: 'Tên action - Action name' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách quyền theo action - Permissions by action',
  })
  async getPermissionsByAction(@Param('action') action: PermissionAction) {
    const permissions = await this.permissionService.getPermissionsByAction(action);
    return {
      action,
      permissions: permissions.map(permission => this.mapToResponseDto(permission)),
    };
  }

  /**
   * Tìm kiếm quyền - Search permissions
   */
  @Get('search/:query')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Tìm kiếm quyền - Search permissions',
    description: 'Tìm kiếm quyền theo từ khóa.',
  })
  @ApiParam({ name: 'query', description: 'Từ khóa tìm kiếm - Search query' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết quả tìm kiếm - Search results',
  })
  async searchPermissions(@Param('query') query: string) {
    const permissions = await this.permissionService.searchPermissions(query);
    return {
      query,
      permissions: permissions.map(permission => this.mapToResponseDto(permission)),
    };
  }

  /**
   * Lấy tất cả modules - Get all modules
   */
  @Get('meta/modules')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy tất cả modules - Get all modules',
    description: 'Lấy danh sách tất cả modules trong hệ thống.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách modules - Modules list',
  })
  getSystemModules() {
    const modules = this.permissionService.getSystemModules();
    return { modules };
  }

  /**
   * Lấy tất cả actions - Get all actions
   */
  @Get('meta/actions')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy tất cả actions - Get all actions',
    description: 'Lấy danh sách tất cả actions trong hệ thống.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách actions - Actions list',
  })
  getPermissionActions() {
    const actions = this.permissionService.getPermissionActions();
    return { actions };
  }

  /**
   * Tạo permissions mặc định cho module - Create default permissions for module
   */
  @Post('module/:module/defaults')
  @RoleManagement.Create()
  @ApiOperation({
    summary: 'Tạo permissions mặc định cho module - Create default permissions for module',
    description: 'Tạo tất cả permissions mặc định cho một module.',
  })
  @ApiParam({ name: 'module', description: 'Tên module - Module name' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Permissions mặc định được tạo - Default permissions created',
  })
  async createDefaultPermissionsForModule(
    @Param('module') module: SystemModule,
  ) {
    const permissions = await this.permissionService.createDefaultPermissionsForModule(module);
    return {
      module,
      created: permissions.length,
      permissions: permissions.map(permission => this.mapToResponseDto(permission)),
    };
  }

  /**
   * Chuyển đổi entity thành response DTO - Convert entity to response DTO
   */
  private mapToResponseDto(permission: any): PermissionResponseDto {
    return {
      id: permission.id,
      code: permission.code,
      module: permission.module,
      action: permission.action,
      resource: permission.resource,
      description: permission.description,
      isActive: permission.isActive,
      createdAt: permission.createdAt,
      roleCount: permission.rolePermissions?.length || 0,
    };
  }

  /**
   * Xây dựng query cho quyền - Build permission query
   */
  private buildPermissionQuery(query: PermissionQueryDto): any {
    const where: any = {};
    
    if (query.search) {
      where.code = { $ilike: `%${query.search}%` };
    }
    
    if (query.module) {
      where.module = query.module;
    }
    
    if (query.action) {
      where.action = query.action;
    }
    
    if (query.isActive !== undefined) {
      where.isActive = query.isActive;
    }

    const options: any = { where };
    
    if (query.page && query.limit) {
      const page = parseInt(query.page);
      const limit = parseInt(query.limit);
      options.skip = (page - 1) * limit;
      options.take = limit;
    }

    return options;
  }
}
