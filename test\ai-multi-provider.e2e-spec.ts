import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { testConfig, TestUtils } from './test.config';
import { AIProvider, AIModelName } from '../src/modules/ai/entities/ai-model.entity';

describe('AI Multi-Provider System (E2E)', () => {
  let app: INestApplication;
  let authToken: string;
  let userId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create test user and get auth token
    const testUser = TestUtils.createMockUser();
    userId = testUser.id;
    authToken = TestUtils.createMockJwtToken();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Provider Discovery and Health Checks', () => {
    it('should list all available AI providers', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/providers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      const providers = response.body.map(p => p.provider);
      expect(providers).toContain(AIProvider.OPENAI);
      expect(providers).toContain(AIProvider.GROK);
      expect(providers).toContain(AIProvider.GEMINI);
      expect(providers).toContain(AIProvider.OLLAMA);

      // Check provider structure
      response.body.forEach(provider => {
        expect(provider).toHaveProperty('provider');
        expect(provider).toHaveProperty('models');
        expect(provider).toHaveProperty('isConfigured');
        expect(provider).toHaveProperty('capabilities');
      });
    });

    it('should validate OLLAMA provider configuration', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/v1/ai/providers/${AIProvider.OLLAMA}/validate`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('isValid');
      // Note: This might be false if OLLAMA server is not running in test environment
    });

    it('should check OLLAMA server health', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/ollama/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('isHealthy');
      if (response.body.isHealthy) {
        expect(response.body).toHaveProperty('version');
        expect(response.body).toHaveProperty('models');
      } else {
        expect(response.body).toHaveProperty('error');
      }
    });
  });

  describe('Smart Provider Selection', () => {
    it('should recommend best provider for text generation', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/providers/best?task=text')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('provider');
      expect(response.body).toHaveProperty('model');
      expect(response.body).toHaveProperty('reason');
      expect(Object.values(AIProvider)).toContain(response.body.provider);
    });

    it('should recommend best provider for code generation', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/providers/best?task=code')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('provider');
      expect(response.body).toHaveProperty('model');
      
      // Should prefer OLLAMA Code Llama or OpenAI for code
      expect([AIProvider.OLLAMA, AIProvider.OPENAI]).toContain(response.body.provider);
    });

    it('should recommend local provider for privacy', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/providers/best-local?task=text')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('provider', AIProvider.OLLAMA);
      expect(response.body).toHaveProperty('benefits');
      expect(response.body.benefits).toContain('Complete data privacy - nothing leaves your server');
    });
  });

  describe('OLLAMA Model Management', () => {
    it('should list OLLAMA models', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/ai/ollama/models')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Models array might be empty if no models are installed
    });

    it('should get recommended models for different tasks', async () => {
      const tasks = ['text', 'code', 'embedding', 'vision'];
      
      for (const task of tasks) {
        const response = await request(app.getHttpServer())
          .get(`/api/v1/ai/ollama/models/recommended?task=${task}`)
          .set('Authorization', `Bearer ${authToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('task', task);
        expect(response.body).toHaveProperty('recommended');
        expect(Array.isArray(response.body.recommended)).toBe(true);
        expect(response.body.recommended.length).toBeGreaterThan(0);
      }
    });

    it('should handle model installation requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/ollama/models/install-recommended')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          task: 'text',
          modelSize: 'small'
        })
        .expect(201);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('models');
      expect(Array.isArray(response.body.models)).toBe(true);
    });
  });

  describe('Text Generation Across Providers', () => {
    const testMessage = {
      messages: [
        {
          role: 'user',
          content: 'Say hello in a friendly way'
        }
      ],
      options: {
        temperature: 0.7,
        maxTokens: 50
      }
    };

    it('should generate text using smart selection', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/smart/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          ...testMessage,
          task: 'text'
        })
        .expect(201);

      expect(response.body).toHaveProperty('content');
      expect(response.body).toHaveProperty('providerUsed');
      expect(response.body.providerUsed).toHaveProperty('provider');
      expect(response.body.providerUsed).toHaveProperty('model');
      expect(response.body.providerUsed).toHaveProperty('reason');
    });

    it('should compare responses from multiple providers', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/compare/providers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          ...testMessage,
          providers: [
            { provider: AIProvider.OPENAI, model: AIModelName.GPT_3_5_TURBO },
            { provider: AIProvider.OLLAMA, model: AIModelName.LLAMA_2_7B }
          ]
        })
        .expect(201);

      expect(response.body).toHaveProperty('results');
      expect(response.body).toHaveProperty('comparison');
      expect(Array.isArray(response.body.results)).toBe(true);
      expect(response.body.results.length).toBe(2);

      response.body.results.forEach(result => {
        expect(result).toHaveProperty('provider');
        expect(result).toHaveProperty('model');
        expect(result).toHaveProperty('success');
        
        if (result.success) {
          expect(result).toHaveProperty('response');
          expect(result).toHaveProperty('responseTime');
        } else {
          expect(result).toHaveProperty('error');
        }
      });
    });
  });

  describe('OLLAMA-Specific Features', () => {
    it('should generate code using OLLAMA', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/ollama/code/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          model: 'codellama:7b',
          prompt: 'Create a simple hello world function',
          language: 'python',
          options: {
            temperature: 0.2,
            num_predict: 100
          }
        });

      // This might fail if OLLAMA server is not running or model is not available
      if (response.status === 201) {
        expect(response.body).toHaveProperty('content');
        expect(response.body).toHaveProperty('model');
      } else {
        // Should return appropriate error message
        expect(response.status).toBeGreaterThanOrEqual(400);
      }
    });

    it('should explain code using OLLAMA', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/ollama/code/explain')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          model: 'codellama:7b',
          code: 'def hello():\n    print("Hello, World!")',
          language: 'python'
        });

      // This might fail if OLLAMA server is not running
      if (response.status === 201) {
        expect(response.body).toHaveProperty('content');
      }
    });

    it('should handle OLLAMA server unavailable gracefully', async () => {
      // This test assumes OLLAMA server might not be running
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/ollama/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          model: 'llama2:7b',
          prompt: 'Hello',
          options: {
            temperature: 0.7
          }
        });

      if (response.status !== 201) {
        // Should return appropriate error message
        expect(response.status).toBeGreaterThanOrEqual(400);
        expect(response.body).toHaveProperty('message');
      }
    });
  });

  describe('Error Handling and Validation', () => {
    it('should validate request bodies', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/generate/text')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
          messages: []
        })
        .expect(400);

      expect(response.body).toHaveProperty('message');
    });

    it('should handle invalid provider selection', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/ai/generate/text')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          provider: 'invalid-provider',
          model: 'invalid-model',
          messages: [{ role: 'user', content: 'Hello' }]
        })
        .expect(400);
    });

    it('should require authentication for all endpoints', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/ai/providers')
        .expect(401);

      await request(app.getHttpServer())
        .get('/api/v1/ai/ollama/status')
        .expect(401);

      await request(app.getHttpServer())
        .post('/api/v1/ai/smart/generate')
        .send({ messages: [{ role: 'user', content: 'Hello' }] })
        .expect(401);
    });
  });

  describe('Performance and Rate Limiting', () => {
    it('should handle concurrent requests', async () => {
      const requests = Array(5).fill(null).map(() =>
        request(app.getHttpServer())
          .get('/api/v1/ai/providers')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should respond within reasonable time limits', async () => {
      const startTime = Date.now();
      
      await request(app.getHttpServer())
        .get('/api/v1/ai/providers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds
    });
  });
});
