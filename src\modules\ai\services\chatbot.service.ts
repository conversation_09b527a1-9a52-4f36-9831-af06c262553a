import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatSession, SessionStatus, SessionType } from '../entities/chat-session.entity';
import { OpenAiService } from './openai.service';
import { LoggerService } from '../../../common/services/logger.service';
import { UtilsService } from '../../../common/services/utils.service';

@Injectable()
export class ChatbotService {
  constructor(
    @InjectRepository(ChatSession)
    private chatSessionRepository: Repository<ChatSession>,
    private openAiService: OpenAiService,
    private logger: LoggerService,
    private utilsService: UtilsService,
  ) {}

  async processMessage(
    userId: string,
    message: string,
    sessionId?: string,
    context?: any
  ): Promise<{
    response: string;
    sessionId: string;
    intent?: string;
    requiresHumanHandoff?: boolean;
    suggestedActions?: string[];
  }> {
    try {
      // Get or create chat session
      let session = sessionId ? await this.getSession(sessionId) : null;

      if (!session) {
        session = await this.createSession(userId, context);
      }

      // Add user message to session
      await this.addMessageToSession(session.id, 'user', message);

      // Generate AI response
      const aiResponse = await this.generateResponse(session, message, context);

      // Add AI response to session
      await this.addMessageToSession(session.id, 'assistant', aiResponse.response);

      // Update session analytics
      await this.updateSessionAnalytics(session.id, aiResponse);

      this.logger.logWithContext(`Chatbot message processed for session: ${session.id}`, 'ChatbotService');

      return {
        response: aiResponse.response,
        sessionId: session.id,
        intent: aiResponse.intent,
        requiresHumanHandoff: aiResponse.requiresHumanHandoff,
        suggestedActions: aiResponse.suggestedActions,
      };
    } catch (error) {
      this.logger.logError(error, 'ChatbotService - processMessage');
      throw error;
    }
  }

  private async createSession(userId: string, context?: any): Promise<ChatSession> {
    const session = this.chatSessionRepository.create({
      userId,
      sessionType: this.determineSessionType(context),
      status: SessionStatus.ACTIVE,
      messages: [],
      context: context || {},
      startedAt: new Date(),
    });

    return this.chatSessionRepository.save(session);
  }

  private async getSession(sessionId: string): Promise<ChatSession | null> {
    return this.chatSessionRepository.findOne({
      where: { id: sessionId },
    });
  }

  private async addMessageToSession(
    sessionId: string,
    role: 'user' | 'assistant' | 'system',
    content: string
  ): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    const message = {
      id: this.utilsService.generateUuid(),
      role,
      content,
      timestamp: new Date().toISOString(),
    };

    session.messages = [...(session.messages || []), message];
    session.updatedAt = new Date();

    await this.chatSessionRepository.save(session);
  }

  private async generateResponse(
    session: ChatSession,
    message: string,
    context?: any
  ): Promise<{
    response: string;
    intent?: string;
    requiresHumanHandoff?: boolean;
    suggestedActions?: string[];
  }> {
    // Analyze message intent
    const intent = await this.analyzeIntent(message);

    // Check if human handoff is needed
    const requiresHumanHandoff = this.shouldHandoffToHuman(message, intent, session);

    if (requiresHumanHandoff) {
      return {
        response: "I'll connect you with a human agent who can better assist you with this request. Please hold on for a moment.",
        intent,
        requiresHumanHandoff: true,
        suggestedActions: ['handoff_to_human'],
      };
    }

    // Generate AI response based on context and conversation history
    const conversationContext = {
      ...session.context,
      ...context,
      conversationHistory: session.messages?.slice(-10) || [], // Last 10 messages
      sessionType: session.sessionType,
      intent,
    };

    const response = await this.openAiService.generateChatbotResponse(message, conversationContext);

    // Generate suggested actions based on intent
    const suggestedActions = this.generateSuggestedActions(intent, message);

    return {
      response,
      intent,
      requiresHumanHandoff: false,
      suggestedActions,
    };
  }

  private async analyzeIntent(message: string): Promise<string> {
    const systemPrompt = `Analyze the user message and determine the primary intent.

    Common intents:
    - greeting
    - product_inquiry
    - pricing_question
    - technical_support
    - complaint
    - compliment
    - booking_request
    - cancellation
    - refund_request
    - general_question
    - goodbye

    Respond with just the intent name.`;

    try {
      const intent = await this.openAiService.generateCompletion(message, {
        systemPrompt,
        temperature: 0.3,
        maxTokens: 50,
      });

      return intent.trim().toLowerCase();
    } catch (error) {
      this.logger.logError(error, 'ChatbotService - analyzeIntent');
      return 'general_question';
    }
  }

  private shouldHandoffToHuman(message: string, intent: string, session: ChatSession): boolean {
    // Define conditions for human handoff
    const handoffIntents = ['complaint', 'refund_request', 'complex_technical_issue'];
    const handoffKeywords = ['speak to human', 'talk to agent', 'human support', 'escalate'];

    // Check intent
    if (handoffIntents.includes(intent)) {
      return true;
    }

    // Check keywords
    const lowerMessage = message.toLowerCase();
    if (handoffKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return true;
    }

    // Check conversation length (handoff after many exchanges without resolution)
    if (session.messages && session.messages.length > 20) {
      return true;
    }

    return false;
  }

  private generateSuggestedActions(intent: string, message: string): string[] {
    const actionMap: { [key: string]: string[] } = {
      product_inquiry: ['show_products', 'schedule_demo', 'send_brochure'],
      pricing_question: ['show_pricing', 'schedule_consultation', 'send_quote'],
      technical_support: ['check_documentation', 'schedule_support_call', 'create_ticket'],
      booking_request: ['show_calendar', 'book_appointment', 'send_availability'],
      complaint: ['escalate_to_manager', 'create_complaint_ticket', 'offer_compensation'],
      general_question: ['provide_faq', 'suggest_resources', 'offer_help'],
    };

    return actionMap[intent] || ['continue_conversation'];
  }

  private determineSessionType(context?: any): SessionType {
    if (context?.platform === 'facebook') {
      return SessionType.FACEBOOK_PAGE;
    } else if (context?.source === 'website') {
      return SessionType.WEBSITE_CHAT;
    } else if (context?.type === 'sales') {
      return SessionType.SALES_INQUIRY;
    } else if (context?.type === 'support') {
      return SessionType.CUSTOMER_SUPPORT;
    }

    return SessionType.GENERAL_CHAT;
  }

  private async updateSessionAnalytics(sessionId: string, response: any): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    const analytics = session.analytics || {};

    analytics.messageCount = (analytics.messageCount || 0) + 1;

    if (response.intent) {
      session.context = {
        ...session.context,
        intent: response.intent,
      };
    }

    session.analytics = analytics;
    await this.chatSessionRepository.save(session);
  }

  async endSession(sessionId: string, reason?: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.status = SessionStatus.COMPLETED;
    session.endedAt = new Date();

    if (reason) {
      session.context = {
        ...session.context,
        endReason: reason,
      } as any;
    }

    await this.chatSessionRepository.save(session);
  }

  async handoffToHuman(sessionId: string, reason: string, agentId?: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    session.handoffToHuman = true;
    session.handoffReason = reason;
    session.assignedAgent = agentId;

    await this.chatSessionRepository.save(session);

    // Add system message about handoff
    await this.addMessageToSession(
      sessionId,
      'system',
      `Session handed off to human agent. Reason: ${reason}`
    );
  }
}
