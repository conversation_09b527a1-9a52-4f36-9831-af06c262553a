import {
  Controller,
  Post,
  Body,
  HttpStatus,
  Req,
  UseGuards,
  Get,
  Query,
  Headers,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { Request } from 'express';
import { AuthService, AuthResponse } from './auth.service';
import { UserDeviceService } from '../users/services/user-device.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UpdateSecurityMethodDto, SecurityMethodResponse } from './dto/security-method.dto';
import { LoginVerificationDto, PartialLoginResponse, SendVerificationCodeDto } from './dto/login-verification.dto';
import { RefreshTokenDto, TokenResponse, RevokeTokenDto } from './dto/refresh-token.dto';
import { Public } from './decorators/public.decorator';
import { CurrentUser } from './decorators/current-user.decorator';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { User } from '../users/entities/user.entity';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userDeviceService: UserDeviceService,
  ) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiBody({
    type: LoginDto,
    description: 'User login credentials',
    examples: {
      emailLogin: {
        summary: 'Login with Email',
        description: 'Login using email address',
        value: {
          emailOrUsername: '<EMAIL>',
          password: 'Duogxaolin123!'
        }
      },
      usernameLogin: {
        summary: 'Login with Username',
        description: 'Login using username',
        value: {
          emailOrUsername: 'duogxaolin',
          password: 'Duogxaolin123!'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Login successful',
    schema: {
      type: 'object',
      properties: {
        user: { $ref: '#/components/schemas/User' },
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Invalid email/username or password' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: Request,
    @Headers('user-agent') userAgent: string = 'Unknown'
  ): Promise<AuthResponse | PartialLoginResponse> {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return this.authService.login(loginDto, {
      ip,
      userAgent,
      deviceName: this.extractDeviceName(userAgent),
    });
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiBody({
    type: RegisterDto,
    description: 'User registration data',
    examples: {
      example1: {
        summary: 'Personal Account Registration',
        description: 'Example of registering a personal account',
        value: {
          email: '<EMAIL>',
          username: 'duogxaolin',
          password: 'Duogxaolin123!',
          firstName: 'Nguyễn',
          lastName: 'Thái Dương',
          phone: '+***********',
          company: '',
          website: 'https://delify.vn',
          accountType: 'personal'
        }
      },
      example2: {
        summary: 'Business Account Registration',
        description: 'Example of registering a business account',
        value: {
          email: '<EMAIL>',
          username: 'duogxaolin',
          password: 'Duogxaolin123!',
          firstName: 'Nguyễn',
          lastName: 'Thái Dương',
          phone: '+***********',
          company: 'Delify Solutions',
          website: 'https://delify.vn',
          accountType: 'business'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Registration successful',
    schema: {
      type: 'object',
      properties: {
        user: { $ref: '#/components/schemas/User' },
        accessToken: { type: 'string' },
        refreshToken: { type: 'string' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'User already exists',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: { type: 'string', example: 'User with this email or username already exists' },
        error: { type: 'string', example: 'Conflict' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'array', items: { type: 'string' } },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  async register(
    @Body() registerDto: RegisterDto,
    @Req() req: Request,
    @Headers('user-agent') userAgent: string = 'Unknown'
  ): Promise<AuthResponse> {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return this.authService.register(registerDto, {
      ip,
      userAgent,
      deviceName: this.extractDeviceName(userAgent),
    });
  }

  @Public()
  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset email sent',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Public()
  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password reset successful',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Invalid or expired token' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change password (authenticated user)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Password changed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Current password is incorrect' })
  async changePassword(
    @CurrentUser() user: User,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    return this.authService.changePassword(user.id, changePasswordDto);
  }

  @Public()
  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email with token' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Email verified successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Invalid verification token' })
  async verifyEmail(@Query('token') token: string): Promise<{ message: string }> {
    return this.authService.verifyEmail(token);
  }



  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user info' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User info retrieved successfully',
    type: User
  })
  async getMe(@CurrentUser() user: User): Promise<User> {
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from current device' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Logged out successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  async logout(@Req() req: Request): Promise<{ message: string }> {
    const sessionToken = this.extractSessionToken(req);
    return this.authService.logout(sessionToken);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout-all')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Logged out from all devices successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        devicesLoggedOut: { type: 'number' }
      }
    }
  })
  async logoutAll(
    @CurrentUser() user: User,
    @Req() req: Request
  ): Promise<{ message: string; devicesLoggedOut: number }> {
    const currentSessionToken = this.extractSessionToken(req);
    return this.authService.logoutAllDevices(user.id, currentSessionToken);
  }

  @Public()
  @Post('verify-login')
  @ApiOperation({ summary: 'Verify login with verification code' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Login verification successful',
    schema: {
      type: 'object',
      properties: {
        user: { $ref: '#/components/schemas/User' },
        accessToken: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid verification code or session' })
  async verifyLogin(
    @Body() verificationDto: LoginVerificationDto,
    @Req() req: Request,
    @Headers('user-agent') userAgent: string = 'Unknown'
  ): Promise<AuthResponse> {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return this.authService.verifyLogin(verificationDto, {
      ip,
      userAgent,
      deviceName: this.extractDeviceName(userAgent),
    });
  }

  @Public()
  @Post('resend-verification-code')
  @ApiOperation({ summary: 'Resend verification code for login' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Verification code sent',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid session' })
  async resendVerificationCode(@Body() dto: SendVerificationCodeDto): Promise<{ message: string }> {
    return this.authService.resendVerificationCode(dto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('security-method')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current security method configuration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Security method retrieved successfully',
    type: SecurityMethodResponse
  })
  async getSecurityMethod(@CurrentUser() user: User): Promise<SecurityMethodResponse> {
    return this.authService.getSecurityMethod(user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('security-method')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update security method configuration' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Security method updated successfully',
    type: SecurityMethodResponse
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid verification code or configuration' })
  async updateSecurityMethod(
    @CurrentUser() user: User,
    @Body() dto: UpdateSecurityMethodDto
  ): Promise<SecurityMethodResponse> {
    return this.authService.updateSecurityMethod(user.id, dto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('send-password-change-code')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Send verification code for password change' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Verification code sent',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'No security method enabled' })
  async sendPasswordChangeVerificationCode(@CurrentUser() user: User): Promise<{ message: string }> {
    return this.authService.sendPasswordChangeVerificationCode(user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-fixed-code')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change fixed verification code' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fixed code changed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid fixed code format or current code' })
  async changeFixedCode(
    @CurrentUser() user: User,
    @Body() dto: { currentCode: string; newCode: string }
  ): Promise<{ message: string }> {
    return this.authService.changeFixedCode(user.id, dto.currentCode, dto.newCode);
  }

  @Public()
  @Post('refresh')
  @ApiOperation({ summary: 'Refresh access token using refresh token' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token refreshed successfully',
    type: TokenResponse
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Invalid or expired refresh token' })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<TokenResponse> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Public()
  @Post('revoke')
  @ApiOperation({ summary: 'Revoke refresh token (logout)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token revoked successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  async revokeToken(@Body() revokeTokenDto: RevokeTokenDto): Promise<{ message: string }> {
    return this.authService.revokeToken(revokeTokenDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('revoke-all')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke all refresh tokens for current user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All tokens revoked successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  async revokeAllTokens(@CurrentUser() user: User): Promise<{ message: string }> {
    return this.authService.revokeAllTokens(user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('devices')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user devices and sessions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User devices retrieved successfully'
  })
  async getUserDevices(@CurrentUser() user: User) {
    const devices = await this.userDeviceService.getUserDevices(user.id);
    const stats = await this.userDeviceService.getDeviceStats(user.id);

    return {
      devices,
      stats,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('devices/:deviceId/revoke')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke a specific device session' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Device revoked successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      }
    }
  })
  async revokeDevice(
    @CurrentUser() user: User,
    @Query('deviceId') deviceId: string
  ): Promise<{ message: string }> {
    await this.userDeviceService.revokeDevice(user.id, deviceId);
    return { message: 'Device revoked successfully' };
  }

  private extractDeviceName(userAgent: string): string {
    // Simple device name extraction
    if (userAgent.includes('Mobile')) return 'Mobile Device';
    if (userAgent.includes('Tablet')) return 'Tablet';
    if (userAgent.includes('Chrome')) return 'Chrome Browser';
    if (userAgent.includes('Firefox')) return 'Firefox Browser';
    if (userAgent.includes('Safari')) return 'Safari Browser';
    return 'Unknown Device';
  }

  private extractSessionToken(req: Request): string {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('No session token found');
    }

    // In a real implementation, you'd decode the JWT to get the session token
    // For now, we'll use the JWT token itself as session identifier
    return authHeader.substring(7);
  }
}
