import { DataSource } from 'typeorm';
import { Role } from '../../modules/rbac/entities/role.entity';
import { Permission, SystemModule, PermissionAction } from '../../modules/rbac/entities/permission.entity';
import { RolePermission } from '../../modules/rbac/entities/role-permission.entity';
import { RoleHierarchy, InheritanceType } from '../../modules/rbac/entities/role-hierarchy.entity';

/**
 * RBAC Seed Data - Dữ liệu khởi tạo cho hệ thống RBAC
 * RBAC Seed Data - Initial data for RBAC system
 */
export class RBACSeeder {
  constructor(private dataSource: DataSource) {}

  async seed(): Promise<void> {
    console.log('🌱 Seeding RBAC data...');

    await this.seedRoles();
    await this.seedPermissions();
    await this.seedRolePermissions();
    await this.seedRoleHierarchy();

    console.log('✅ RBAC seeding completed!');
  }

  /**
   * Tạo dữ liệu vai trò - Seed roles data
   */
  private async seedRoles(): Promise<void> {
    const roleRepository = this.dataSource.getRepository(Role);

    const roles = [
      {
        name: 'MASTER_ACCOUNT',
        displayName: 'Master Account',
        description: 'Chủ tài khoản chính với quyền tối cao',
        level: 0,
        isSystemRole: true,
      },
      {
        name: 'ADMIN',
        displayName: 'Administrator',
        description: 'Quản trị viên hệ thống',
        level: 1,
        isSystemRole: true,
      },
      {
        name: 'MANAGER',
        displayName: 'Manager',
        description: 'Quản lý cấp trung',
        level: 2,
        isSystemRole: true,
      },
      {
        name: 'MARKETING_LEAD',
        displayName: 'Marketing Lead',
        description: 'Trưởng nhóm Marketing',
        level: 3,
        isSystemRole: false,
      },
      {
        name: 'MARKETING_STAFF',
        displayName: 'Marketing Staff',
        description: 'Nhân viên Marketing',
        level: 4,
        isSystemRole: false,
      },
      {
        name: 'SALES_LEAD',
        displayName: 'Sales Lead',
        description: 'Trưởng nhóm Sales',
        level: 3,
        isSystemRole: false,
      },
      {
        name: 'SALES_STAFF',
        displayName: 'Sales Staff',
        description: 'Nhân viên Sales',
        level: 4,
        isSystemRole: false,
      },
      {
        name: 'CONTENT_LEAD',
        displayName: 'Content Lead',
        description: 'Trưởng nhóm Content',
        level: 3,
        isSystemRole: false,
      },
      {
        name: 'CONTENT_EDITOR',
        displayName: 'Content Editor',
        description: 'Biên tập viên nội dung',
        level: 4,
        isSystemRole: false,
      },
      {
        name: 'ANALYST',
        displayName: 'Analyst',
        description: 'Chuyên gia phân tích dữ liệu',
        level: 3,
        isSystemRole: false,
      },
      {
        name: 'VIEWER',
        displayName: 'Viewer',
        description: 'Người xem với quyền hạn chế',
        level: 5,
        isSystemRole: true,
      },
      {
        name: 'GUEST',
        displayName: 'Guest',
        description: 'Khách với quyền xem cơ bản',
        level: 6,
        isSystemRole: true,
      },
    ];

    for (const roleData of roles) {
      const existingRole = await roleRepository.findOne({ where: { name: roleData.name } });
      if (!existingRole) {
        const role = roleRepository.create(roleData);
        await roleRepository.save(role);
        console.log(`✓ Created role: ${roleData.name}`);
      }
    }
  }

  /**
   * Tạo dữ liệu quyền hạn - Seed permissions data
   */
  private async seedPermissions(): Promise<void> {
    const permissionRepository = this.dataSource.getRepository(Permission);

    const modules = Object.values(SystemModule);
    const actions = Object.values(PermissionAction);

    const permissions = [];

    // Tạo permissions cho từng module và action
    for (const module of modules) {
      for (const action of actions) {
        const code = Permission.generateCode(module, action);
        const description = this.getPermissionDescription(module, action);
        
        permissions.push({
          code,
          module,
          action,
          description,
        });
      }
    }

    for (const permissionData of permissions) {
      const existingPermission = await permissionRepository.findOne({ 
        where: { code: permissionData.code } 
      });
      
      if (!existingPermission) {
        const permission = permissionRepository.create(permissionData);
        await permissionRepository.save(permission);
        console.log(`✓ Created permission: ${permissionData.code}`);
      }
    }
  }

  /**
   * Tạo ánh xạ vai trò-quyền - Seed role-permission mappings
   */
  private async seedRolePermissions(): Promise<void> {
    const roleRepository = this.dataSource.getRepository(Role);
    const permissionRepository = this.dataSource.getRepository(Permission);
    const rolePermissionRepository = this.dataSource.getRepository(RolePermission);

    // MASTER_ACCOUNT có tất cả quyền
    const masterRole = await roleRepository.findOne({ where: { name: 'MASTER_ACCOUNT' } });
    const allPermissions = await permissionRepository.find();

    if (masterRole) {
      for (const permission of allPermissions) {
        const existing = await rolePermissionRepository.findOne({
          where: { roleId: masterRole.id, permissionId: permission.id }
        });

        if (!existing) {
          const rolePermission = rolePermissionRepository.create({
            roleId: masterRole.id,
            permissionId: permission.id,
          });
          await rolePermissionRepository.save(rolePermission);
        }
      }
      console.log(`✓ Assigned all permissions to MASTER_ACCOUNT`);
    }

    // ADMIN có hầu hết quyền (trừ một số quyền đặc biệt)
    const adminRole = await roleRepository.findOne({ where: { name: 'ADMIN' } });
    if (adminRole) {
      const adminPermissions = allPermissions.filter(p => 
        !p.code.includes('SYSTEM_SETTINGS_MANAGE') // Admin không thể thay đổi cài đặt hệ thống
      );

      for (const permission of adminPermissions) {
        const existing = await rolePermissionRepository.findOne({
          where: { roleId: adminRole.id, permissionId: permission.id }
        });

        if (!existing) {
          const rolePermission = rolePermissionRepository.create({
            roleId: adminRole.id,
            permissionId: permission.id,
          });
          await rolePermissionRepository.save(rolePermission);
        }
      }
      console.log(`✓ Assigned permissions to ADMIN`);
    }

    // Các vai trò khác có quyền hạn chế
    await this.assignSpecificRolePermissions();
  }

  /**
   * Gán quyền cụ thể cho từng vai trò - Assign specific permissions to roles
   */
  private async assignSpecificRolePermissions(): Promise<void> {
    const roleRepository = this.dataSource.getRepository(Role);
    const permissionRepository = this.dataSource.getRepository(Permission);
    const rolePermissionRepository = this.dataSource.getRepository(RolePermission);

    const rolePermissionMappings = {
      MANAGER: [
        'USER_MANAGEMENT_READ',
        'USER_MANAGEMENT_UPDATE',
        'CONTENT_MANAGEMENT_APPROVE',
        'MARKETING_CAMPAIGNS_APPROVE',
        'SALES_PIPELINE_APPROVE',
        'ANALYTICS_REPORTS_READ',
        'ANALYTICS_REPORTS_EXPORT',
        'AI_FEATURES_READ',
        'AI_FEATURES_CREATE',
      ],
      MARKETING_LEAD: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_CREATE',
        'CONTENT_MANAGEMENT_UPDATE',
        'MARKETING_CAMPAIGNS_MANAGE',
        'SALES_PIPELINE_READ',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_CREATE',
        'AI_FEATURES_UPDATE',
        'EXPORT_TOOLS_CREATE',
      ],
      MARKETING_STAFF: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_CREATE',
        'CONTENT_MANAGEMENT_UPDATE',
        'MARKETING_CAMPAIGNS_CREATE',
        'MARKETING_CAMPAIGNS_UPDATE',
        'SALES_PIPELINE_READ',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_CREATE',
      ],
      SALES_LEAD: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_READ',
        'MARKETING_CAMPAIGNS_READ',
        'SALES_PIPELINE_MANAGE',
        'ANALYTICS_REPORTS_READ',
        'FINANCIAL_DATA_READ',
        'AI_FEATURES_CREATE',
        'AI_FEATURES_UPDATE',
        'EXPORT_TOOLS_CREATE',
      ],
      SALES_STAFF: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_READ',
        'MARKETING_CAMPAIGNS_READ',
        'SALES_PIPELINE_CREATE',
        'SALES_PIPELINE_UPDATE',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_CREATE',
      ],
      CONTENT_LEAD: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_MANAGE',
        'MARKETING_CAMPAIGNS_READ',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_CREATE',
        'AI_FEATURES_UPDATE',
        'EXPORT_TOOLS_CREATE',
      ],
      CONTENT_EDITOR: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_CREATE',
        'CONTENT_MANAGEMENT_UPDATE',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_CREATE',
      ],
      ANALYST: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_READ',
        'MARKETING_CAMPAIGNS_READ',
        'SALES_PIPELINE_READ',
        'ANALYTICS_REPORTS_MANAGE',
        'FINANCIAL_DATA_READ',
        'AI_FEATURES_READ',
        'AI_FEATURES_CREATE',
        'EXPORT_TOOLS_MANAGE',
      ],
      VIEWER: [
        'USER_MANAGEMENT_READ',
        'CONTENT_MANAGEMENT_READ',
        'MARKETING_CAMPAIGNS_READ',
        'SALES_PIPELINE_READ',
        'ANALYTICS_REPORTS_READ',
        'AI_FEATURES_READ',
      ],
      GUEST: [
        'CONTENT_MANAGEMENT_READ',
      ],
    };

    for (const [roleName, permissionCodes] of Object.entries(rolePermissionMappings)) {
      const role = await roleRepository.findOne({ where: { name: roleName } });
      if (!role) continue;

      for (const permissionCode of permissionCodes) {
        const permission = await permissionRepository.findOne({ where: { code: permissionCode } });
        if (!permission) continue;

        const existing = await rolePermissionRepository.findOne({
          where: { roleId: role.id, permissionId: permission.id }
        });

        if (!existing) {
          const rolePermission = rolePermissionRepository.create({
            roleId: role.id,
            permissionId: permission.id,
          });
          await rolePermissionRepository.save(rolePermission);
        }
      }
      console.log(`✓ Assigned permissions to ${roleName}`);
    }
  }

  /**
   * Tạo phân cấp vai trò - Seed role hierarchy
   */
  private async seedRoleHierarchy(): Promise<void> {
    const roleRepository = this.dataSource.getRepository(Role);
    const roleHierarchyRepository = this.dataSource.getRepository(RoleHierarchy);

    const hierarchies = [
      { parent: 'ADMIN', child: 'MANAGER' },
      { parent: 'MANAGER', child: 'MARKETING_LEAD' },
      { parent: 'MANAGER', child: 'SALES_LEAD' },
      { parent: 'MANAGER', child: 'CONTENT_LEAD' },
      { parent: 'MANAGER', child: 'ANALYST' },
      { parent: 'MARKETING_LEAD', child: 'MARKETING_STAFF' },
      { parent: 'SALES_LEAD', child: 'SALES_STAFF' },
      { parent: 'CONTENT_LEAD', child: 'CONTENT_EDITOR' },
      { parent: 'ADMIN', child: 'VIEWER' },
      { parent: 'VIEWER', child: 'GUEST' },
    ];

    for (const hierarchy of hierarchies) {
      const parentRole = await roleRepository.findOne({ where: { name: hierarchy.parent } });
      const childRole = await roleRepository.findOne({ where: { name: hierarchy.child } });

      if (parentRole && childRole) {
        const existing = await roleHierarchyRepository.findOne({
          where: { parentRoleId: parentRole.id, childRoleId: childRole.id }
        });

        if (!existing) {
          const roleHierarchy = roleHierarchyRepository.create({
            parentRoleId: parentRole.id,
            childRoleId: childRole.id,
            inheritanceType: InheritanceType.FULL,
          });
          await roleHierarchyRepository.save(roleHierarchy);
          console.log(`✓ Created hierarchy: ${hierarchy.parent} -> ${hierarchy.child}`);
        }
      }
    }
  }

  /**
   * Lấy mô tả quyền hạn - Get permission description
   */
  private getPermissionDescription(module: SystemModule, action: PermissionAction): string {
    const moduleNames = {
      [SystemModule.USER_MANAGEMENT]: 'Quản lý người dùng',
      [SystemModule.ROLE_MANAGEMENT]: 'Quản lý vai trò',
      [SystemModule.CONTENT_MANAGEMENT]: 'Quản lý nội dung',
      [SystemModule.MARKETING_CAMPAIGNS]: 'Chiến dịch Marketing',
      [SystemModule.SALES_PIPELINE]: 'Quy trình Sales',
      [SystemModule.ANALYTICS_REPORTS]: 'Báo cáo phân tích',
      [SystemModule.FINANCIAL_DATA]: 'Dữ liệu tài chính',
      [SystemModule.SYSTEM_SETTINGS]: 'Cài đặt hệ thống',
      [SystemModule.AI_FEATURES]: 'Tính năng AI',
      [SystemModule.EXPORT_TOOLS]: 'Công cụ xuất dữ liệu',
    };

    const actionNames = {
      [PermissionAction.READ]: 'xem',
      [PermissionAction.CREATE]: 'tạo',
      [PermissionAction.UPDATE]: 'cập nhật',
      [PermissionAction.DELETE]: 'xóa',
      [PermissionAction.EXPORT]: 'xuất',
      [PermissionAction.APPROVE]: 'phê duyệt',
      [PermissionAction.MANAGE]: 'quản lý',
      [PermissionAction.ASSIGN]: 'gán quyền',
    };

    return `Quyền ${actionNames[action]} ${moduleNames[module]}`;
  }
}
