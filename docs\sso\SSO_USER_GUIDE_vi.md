# Hướng Dẫn Người Dùng SSO

## Tổng Quan

Hướng dẫn này cung cấp hướng dẫn toàn diện cho người dùng cuối về cách sử dụng hệ thống Single Sign-On (SSO) trên nhiều ứng dụng và subdomain.

## SSO là gì?

Single Sign-On (SSO) cho phép bạn đăng nhập một lần và truy cập nhiều ứng dụng mà không cần nhập lại thông tin đăng nhập. Hệ thống SSO của chúng tôi hoạt động trên các ứng dụng sau:

- **Ứng Dụng Chính** (`app.yourcompany.com`) - Ứng dụng kinh doanh chính
- **Dịch Vụ Email** (`mail.yourcompany.com`) - Nền tảng quản lý email
- **Dịch Vụ <PERSON>ố<PERSON> Lõi** (`core.yourcompany.com`) - Tiện ích kinh doanh cốt lõi
- **API Gateway** (`api.yourcompany.com`) - Công cụ phát triển và tích hợp

## Bắt Đầu

### Đăng Nhập Lần Đầu

1. **Điều Hướng Đến Trang Đăng Nhập**
   - Truy cập bất kỳ ứng dụng được hỗ trợ nào
   - Bạn sẽ được chuyển hướng đến trang đăng nhập nếu chưa xác thực

2. **Nhập Thông Tin Đăng Nhập**
   - Email hoặc tên người dùng
   - Mật khẩu
   - Hoàn thành xác minh bảo mật bổ sung nếu được yêu cầu

3. **Nhận Diện Thiết Bị**
   - Hệ thống sẽ ghi nhớ thiết bị của bạn cho các lần đăng nhập sau
   - Bạn có thể được yêu cầu cung cấp tên thiết bị (ví dụ: "Laptop Công Việc", "Điện Thoại Cá Nhân")

4. **Truy Cập Tự Động**
   - Sau khi đăng nhập, bạn có thể truy cập tất cả ứng dụng khác mà không cần nhập lại thông tin đăng nhập
   - Chỉ cần điều hướng đến bất kỳ subdomain được hỗ trợ nào

### Đăng Nhập Tiếp Theo

Sau lần đăng nhập đầu tiên, hệ thống SSO sẽ:
- Tự động đăng nhập khi bạn truy cập bất kỳ ứng dụng được hỗ trợ nào
- Duy trì phiên của bạn trên tất cả ứng dụng
- Ghi nhớ thiết bị của bạn để xác thực nhanh hơn

## Sử Dụng Tính Năng SSO

### Truy Cập Các Ứng Dụng Khác Nhau

**Từ Ứng Dụng Chính Đến Email:**
1. Nhấp vào liên kết email hoặc điều hướng trực tiếp đến `mail.yourcompany.com`
2. Bạn sẽ được tự động đăng nhập mà không cần nhập thông tin đăng nhập
3. Quyền của bạn sẽ được áp dụng dựa trên vai trò của bạn

**Điều Hướng Giữa Các Ứng Dụng:**
- Sử dụng bộ chuyển đổi ứng dụng (nếu có trong giao diện)
- Đánh dấu các ứng dụng khác nhau - bạn sẽ vẫn đăng nhập
- Mở nhiều ứng dụng trong các tab trình duyệt khác nhau

### Quản Lý Phiên

#### Xem Phiên Đang Hoạt Động

1. Vào **Cài Đặt Tài Khoản** hoặc phần **Bảo Mật**
2. Tìm **Phiên Đang Hoạt Động** hoặc **Quản Lý Thiết Bị**
3. Bạn sẽ thấy danh sách tất cả phiên đang hoạt động bao gồm:
   - Tên và loại thiết bị
   - Vị trí (ước tính)
   - Thời gian hoạt động cuối
   - Trạng thái phiên

#### Quản Lý Phiên

**Kết Thúc Phiên Cụ Thể:**
1. Trong danh sách Phiên Đang Hoạt Động
2. Nhấp **"Kết Thúc"** bên cạnh phiên bạn muốn kết thúc
3. Thiết bị đó sẽ được đăng xuất ngay lập tức

**Đăng Xuất Toàn Cục (Tất Cả Thiết Bị):**
1. Sử dụng tùy chọn **"Đăng xuất khỏi tất cả thiết bị"**
2. Điều này sẽ kết thúc tất cả phiên của bạn trên tất cả ứng dụng
3. Bạn sẽ cần đăng nhập lại trên tất cả thiết bị

### Tính Năng Bảo Mật

#### Nhận Diện Thiết Bị

Hệ thống theo dõi thiết bị của bạn để bảo mật:
- **Thiết Bị Tin Cậy**: Thiết bị bạn đã sử dụng trước đây
- **Thiết Bị Mới**: Thiết bị lần đầu có thể yêu cầu xác minh bổ sung
- **Hoạt Động Đáng Ngờ**: Các mẫu đăng nhập bất thường kích hoạt cảnh báo bảo mật

#### Hết Hạn Phiên

- **Hết Hạn Tự Động**: Phiên hết hạn sau 8 giờ không hoạt động
- **Gia Hạn**: Hoạt động trên bất kỳ ứng dụng nào sẽ gia hạn phiên của bạn trên tất cả ứng dụng
- **Cảnh Báo**: Bạn sẽ nhận được cảnh báo trước khi phiên hết hạn

## Khắc Phục Sự Cố

### Vấn Đề Thường Gặp

#### Thông Báo "Phiên Đã Hết Hạn"

**Nguyên Nhân**: Phiên của bạn đã hết hạn do không hoạt động
**Giải Pháp**: 
1. Nhấp "Đăng Nhập Lại" hoặc làm mới trang
2. Nhập thông tin đăng nhập để bắt đầu phiên mới

#### Không Thể Truy Cập Ứng Dụng

**Nguyên Nhân**: Không đủ quyền cho ứng dụng cụ thể
**Giải Pháp**:
1. Liên hệ quản trị viên để yêu cầu quyền truy cập
2. Xác minh bạn có vai trò chính xác được gán

#### Cảnh Báo "Thiết Bị Không Được Nhận Diện"

**Nguyên Nhân**: Đăng nhập từ thiết bị mới hoặc khác
**Giải Pháp**:
1. Hoàn thành quy trình xác minh thiết bị
2. Kiểm tra email để lấy mã xác minh nếu được yêu cầu
3. Liên hệ hỗ trợ IT nếu không thể xác minh thiết bị

#### Vấn Đề Đăng Xuất Tự Động

**Nguyên Nhân Có Thể**:
- Cookies trình duyệt bị vô hiệu hóa
- Chế độ duyệt riêng tư/ẩn danh
- Cài đặt bảo mật trình duyệt chặn cookies cross-domain

**Giải Pháp**:
1. Bật cookies trong cài đặt trình duyệt
2. Thêm `*.yourcompany.com` vào trang web tin cậy
3. Vô hiệu hóa trình chặn quảng cáo cho domain công ty
4. Sử dụng chế độ duyệt tiêu chuẩn (không riêng tư/ẩn danh)

### Tương Thích Trình Duyệt

#### Trình Duyệt Được Hỗ Trợ
- **Chrome** 80+ (Khuyến nghị)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

#### Cài Đặt Trình Duyệt
1. **Bật Cookies**: Bắt buộc cho chức năng SSO
2. **JavaScript**: Phải được bật
3. **Third-party Cookies**: Cho phép cho `*.yourcompany.com`

### Thiết Bị Di Động

#### Hỗ Trợ Trình Duyệt Di Động
- iOS Safari 13+
- Android Chrome 80+
- Samsung Internet 12+

#### Tích Hợp Ứng Dụng Di Động
- Ứng dụng di động gốc có thể có xác thực riêng biệt
- Liên hệ bộ phận IT để thiết lập ứng dụng di động

## Thực Hành Bảo Mật Tốt Nhất

### Bảo Mật Tài Khoản

1. **Mật Khẩu Mạnh**
   - Sử dụng mật khẩu độc đáo, phức tạp
   - Bật xác thực hai yếu tố nếu có
   - Thay đổi mật khẩu thường xuyên

2. **Quản Lý Thiết Bị**
   - Thường xuyên xem xét phiên đang hoạt động
   - Kết thúc phiên trên thiết bị bạn không còn sử dụng
   - Báo cáo hoạt động đáng ngờ ngay lập tức

3. **Duyệt Web An Toàn**
   - Luôn đăng xuất trên máy tính dùng chung
   - Sử dụng duyệt riêng tư trên máy tính công cộng
   - Xác minh URL trước khi nhập thông tin đăng nhập

### Cân Nhắc Về Quyền Riêng Tư

#### Thu Thập Dữ Liệu
Hệ thống SSO thu thập:
- Thông tin thiết bị cho mục đích bảo mật
- Thời gian và địa điểm đăng nhập
- Mẫu sử dụng ứng dụng

#### Sử Dụng Dữ Liệu
- Thông tin chỉ được sử dụng cho bảo mật và cải thiện hệ thống
- Dữ liệu không được chia sẻ với bên thứ ba
- Bạn có thể yêu cầu xóa dữ liệu bằng cách liên hệ hỗ trợ

## Tính Năng Nâng Cao

### Chuyển Đổi Ứng Dụng

**Bộ Chuyển Đổi Nhanh** (nếu có):
1. Tìm biểu tượng chuyển đổi ứng dụng trong thanh điều hướng trên cùng
2. Nhấp để xem tất cả ứng dụng có sẵn
3. Chọn ứng dụng bạn muốn truy cập

**Bookmarks**:
- Đánh dấu các ứng dụng thường sử dụng
- SSO sẽ tự động xác thực bạn khi truy cập liên kết đã đánh dấu

### Tích Hợp Với Công Cụ Bên Ngoài

#### Truy Cập API
- Nhà phát triển có thể sử dụng token SSO để xác thực API
- Liên hệ nhóm phát triển để biết chi tiết tích hợp API

#### Tích Hợp Bên Thứ Ba
- Một số công cụ bên ngoài có thể hỗ trợ tích hợp SSO
- Kiểm tra với quản trị viên về các tích hợp có sẵn

## Nhận Trợ Giúp

### Tùy Chọn Tự Phục Vụ

1. **Đặt Lại Mật Khẩu**
   - Sử dụng liên kết "Quên Mật Khẩu" trên trang đăng nhập
   - Làm theo hướng dẫn email để đặt lại mật khẩu

2. **Khôi Phục Tài Khoản**
   - Liên hệ quản trị viên nếu bị khóa
   - Cung cấp tên người dùng và ID nhân viên để xác minh

### Liên Hệ Hỗ Trợ

#### Bộ Phận Hỗ Trợ IT
- **Email**: <EMAIL>
- **Điện Thoại**: +84-XXX-XXX-XXXX
- **Giờ Làm Việc**: Thứ Hai-Thứ Sáu, 9 AM - 5 PM

#### Truy Cập Khẩn Cấp
- Cho các vấn đề truy cập khẩn cấp ngoài giờ làm việc
- Sử dụng thông tin liên hệ khẩn cấp do quản trị viên cung cấp

### Báo Cáo Vấn Đề

Khi báo cáo vấn đề SSO, vui lòng cung cấp:
1. **Thông Báo Lỗi**: Văn bản chính xác của bất kỳ thông báo lỗi nào
2. **Thông Tin Trình Duyệt**: Loại và phiên bản trình duyệt
3. **Thông Tin Thiết Bị**: Hệ điều hành và loại thiết bị
4. **Các Bước Tái Tạo**: Bạn đang làm gì khi vấn đề xảy ra
5. **Thời Gian Vấn Đề**: Khi nào vấn đề xảy ra

## Câu Hỏi Thường Gặp

### Câu Hỏi Chung

**H: Tôi có cần đăng nhập riêng biệt cho từng ứng dụng không?**
Đ: Không, SSO cho phép bạn đăng nhập một lần và truy cập tất cả ứng dụng tự động.

**H: Phiên kéo dài bao lâu?**
Đ: Phiên kéo dài 8 giờ không hoạt động. Hoạt động trên bất kỳ ứng dụng nào sẽ gia hạn phiên.

**H: Tôi có thể sử dụng SSO trên thiết bị di động không?**
Đ: Có, SSO hoạt động trên trình duyệt di động. Ứng dụng di động gốc có thể có xác thực riêng biệt.

### Câu Hỏi Kỹ Thuật

**H: Tại sao tôi được yêu cầu đăng nhập lại?**
Đ: Điều này có thể xảy ra do phiên hết hạn, chính sách bảo mật, hoặc cài đặt trình duyệt.

**H: Tôi có thể sử dụng SSO với nhiều profile trình duyệt không?**
Đ: Mỗi profile trình duyệt duy trì phiên riêng biệt. Bạn sẽ cần đăng nhập vào từng profile.

**H: Điều gì xảy ra nếu tôi xóa cookies trình duyệt?**
Đ: Xóa cookies sẽ đăng xuất bạn khỏi tất cả ứng dụng. Bạn sẽ cần đăng nhập lại.

### Câu Hỏi Bảo Mật

**H: SSO có an toàn không?**
Đ: Có, hệ thống SSO của chúng tôi sử dụng bảo mật cấp doanh nghiệp bao gồm theo dõi thiết bị và giám sát phiên.

**H: Điều gì xảy ra nếu người khác sử dụng máy tính của tôi?**
Đ: Luôn đăng xuất trên máy tính dùng chung. Sử dụng tùy chọn "Đăng xuất khỏi tất cả thiết bị" nếu cần.

**H: Làm sao tôi biết có ai khác truy cập tài khoản của tôi?**
Đ: Kiểm tra phiên đang hoạt động thường xuyên để tìm thiết bị hoặc vị trí không quen thuộc.

---

**Để biết tài liệu kỹ thuật, xem [Hướng Dẫn Triển Khai](./SSO_IMPLEMENTATION_GUIDE_vi.md) và [Tài Liệu API](./SSO_API_DOCUMENTATION_vi.md).**
