import { IsString, IsOptional, IsNumber, IsBoolean, IsUUID, IsArray, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>eng<PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO tạo vai trò mới - Create role DTO
 */
export class CreateRoleDto {
  @ApiProperty({
    description: 'Tên vai trò (unique) - Role name (unique)',
    example: 'MARKETING_MANAGER',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Tên hiển thị - Display name',
    example: 'Marketing Manager',
    minLength: 2,
    maxLength: 200,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(200)
  displayName: string;

  @ApiPropertyOptional({
    description: 'Mô tả vai trò - Role description',
    example: 'Qu<PERSON>n lý các hoạt động marketing',
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Cấp độ phân cấp (số càng nhỏ quyền càng cao) - Hierarchy level (lower number = higher authority)',
    example: 3,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  level: number;

  @ApiPropertyOptional({
    description: 'ID vai trò cha - Parent role ID',
    example: 'uuid-string',
  })
  @IsOptional()
  @IsUUID()
  parentRoleId?: string;

  @ApiPropertyOptional({
    description: 'Vai trò hệ thống (không thể xóa) - System role (cannot be deleted)',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isSystemRole?: boolean;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động - Active status',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO cập nhật vai trò - Update role DTO
 */
export class UpdateRoleDto {
  @ApiPropertyOptional({
    description: 'Tên vai trò (unique) - Role name (unique)',
    example: 'MARKETING_MANAGER',
    minLength: 2,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Tên hiển thị - Display name',
    example: 'Marketing Manager',
    minLength: 2,
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MinLength(2)
  @MaxLength(200)
  displayName?: string;

  @ApiPropertyOptional({
    description: 'Mô tả vai trò - Role description',
    example: 'Quản lý các hoạt động marketing',
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Cấp độ phân cấp - Hierarchy level',
    example: 3,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  level?: number;

  @ApiPropertyOptional({
    description: 'ID vai trò cha - Parent role ID',
    example: 'uuid-string',
  })
  @IsOptional()
  @IsUUID()
  parentRoleId?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động - Active status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO gán quyền cho vai trò - Assign permissions to role DTO
 */
export class AssignPermissionsDto {
  @ApiProperty({
    description: 'Danh sách ID quyền hạn - List of permission IDs',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  permissionIds: string[];

  @ApiPropertyOptional({
    description: 'Thay thế tất cả quyền hiện tại - Replace all existing permissions',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  replace?: boolean;
}

/**
 * DTO gán vai trò cho người dùng - Assign role to user DTO
 */
export class AssignRoleToUserDto {
  @ApiProperty({
    description: 'ID người dùng - User ID',
    example: 'uuid-string',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'ID vai trò - Role ID',
    example: 'uuid-string',
  })
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: 'Thời hạn vai trò (ISO string) - Role expiration (ISO string)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsString()
  expiresAt?: string;
}

/**
 * DTO thu hồi vai trò từ người dùng - Revoke role from user DTO
 */
export class RevokeRoleFromUserDto {
  @ApiProperty({
    description: 'ID người dùng - User ID',
    example: 'uuid-string',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'ID vai trò - Role ID',
    example: 'uuid-string',
  })
  @IsUUID()
  roleId: string;
}

/**
 * DTO phản hồi vai trò - Role response DTO
 */
export class RoleResponseDto {
  @ApiProperty({ description: 'ID vai trò - Role ID' })
  id: string;

  @ApiProperty({ description: 'Tên vai trò - Role name' })
  name: string;

  @ApiProperty({ description: 'Tên hiển thị - Display name' })
  displayName: string;

  @ApiPropertyOptional({ description: 'Mô tả - Description' })
  description?: string;

  @ApiProperty({ description: 'Cấp độ - Level' })
  level: number;

  @ApiPropertyOptional({ description: 'ID vai trò cha - Parent role ID' })
  parentRoleId?: string;

  @ApiProperty({ description: 'Vai trò hệ thống - System role' })
  isSystemRole: boolean;

  @ApiProperty({ description: 'Trạng thái hoạt động - Active status' })
  isActive: boolean;

  @ApiPropertyOptional({ description: 'ID người tạo - Creator ID' })
  createdBy?: string;

  @ApiProperty({ description: 'Ngày tạo - Created date' })
  createdAt: Date;

  @ApiProperty({ description: 'Ngày cập nhật - Updated date' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Vai trò cha - Parent role' })
  parentRole?: RoleResponseDto;

  @ApiPropertyOptional({ description: 'Vai trò con - Child roles', type: [RoleResponseDto] })
  childRoles?: RoleResponseDto[];

  @ApiPropertyOptional({ description: 'Số lượng quyền - Permission count' })
  permissionCount?: number;

  @ApiPropertyOptional({ description: 'Số lượng người dùng - User count' })
  userCount?: number;
}

/**
 * DTO danh sách vai trò - Role list DTO
 */
export class RoleListResponseDto {
  @ApiProperty({ description: 'Danh sách vai trò - Role list', type: [RoleResponseDto] })
  roles: RoleResponseDto[];

  @ApiProperty({ description: 'Tổng số - Total count' })
  total: number;

  @ApiPropertyOptional({ description: 'Trang hiện tại - Current page' })
  page?: number;

  @ApiPropertyOptional({ description: 'Số lượng mỗi trang - Items per page' })
  limit?: number;
}

/**
 * DTO truy vấn vai trò - Role query DTO
 */
export class RoleQueryDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên - Search by name',
    example: 'marketing',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo cấp độ - Filter by level',
    example: 3,
  })
  @IsOptional()
  @IsNumber()
  level?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái - Filter by active status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Lọc theo vai trò hệ thống - Filter by system role',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isSystemRole?: boolean;

  @ApiPropertyOptional({
    description: 'Số trang - Page number',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    description: 'Số lượng mỗi trang - Items per page',
    example: 10,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;
}
