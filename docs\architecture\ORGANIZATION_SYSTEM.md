# 🏢 Organization Management System

## 📋 Tổng quan

Delify Platform cung cấp **comprehensive organization management** với multi-tenant architecture, team collaboration, role-based permissions, và invitation system.

## 🏗️ Organization Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                Organization Hierarchy                      │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Organization                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │    Teams    │ │   Members   │ │   Settings  │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  │                                                         │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │    Roles    │ │ Permissions │ │ Invitations │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                     Teams                               │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │   Members   │ │   Projects  │ │ Permissions │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Core Entities

### 1. Organization Entity
```typescript
@Entity('organizations')
export class Organization {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OrganizationType,
    default: OrganizationType.COMPANY,
  })
  type: OrganizationType;

  @Column('json', { nullable: true })
  settings: {
    timezone?: string;
    language?: string;
    currency?: string;
    features?: string[];
  };

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => OrganizationMember, member => member.organization)
  members: OrganizationMember[];

  @OneToMany(() => Team, team => team.organization)
  teams: Team[];

  @OneToMany(() => Invitation, invitation => invitation.organization)
  invitations: Invitation[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### 2. Organization Member Entity
```typescript
@Entity('organization_members')
export class OrganizationMember {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, user => user.organizationMemberships)
  user: User;

  @ManyToOne(() => Organization, org => org.members)
  organization: Organization;

  @Column({
    type: 'enum',
    enum: MemberRole,
    default: MemberRole.MEMBER,
  })
  role: MemberRole;

  @ManyToMany(() => Permission)
  @JoinTable()
  permissions: Permission[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  joinedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### 3. Team Entity
```typescript
@Entity('teams')
export class Team {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @ManyToOne(() => Organization, org => org.teams)
  organization: Organization;

  @ManyToMany(() => OrganizationMember)
  @JoinTable()
  members: OrganizationMember[];

  @ManyToOne(() => OrganizationMember)
  lead: OrganizationMember;

  @Column('json', { nullable: true })
  settings: {
    isPrivate?: boolean;
    maxMembers?: number;
    features?: string[];
  };

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## 🎭 Role System

### Role Hierarchy
```typescript
export enum MemberRole {
  OWNER = 'owner',       // Level 5 - Full control
  ADMIN = 'admin',       // Level 4 - Administrative access
  MANAGER = 'manager',   // Level 3 - Team management
  MEMBER = 'member',     // Level 2 - Standard access
  VIEWER = 'viewer',     // Level 1 - Read-only access
}

// Role levels for comparison
export const ROLE_LEVELS = {
  [MemberRole.VIEWER]: 1,
  [MemberRole.MEMBER]: 2,
  [MemberRole.MANAGER]: 3,
  [MemberRole.ADMIN]: 4,
  [MemberRole.OWNER]: 5,
};
```

### Permission Matrix
```typescript
export const ROLE_PERMISSIONS = {
  [MemberRole.OWNER]: [
    'organization:delete',
    'organization:transfer',
    'billing:manage',
    'members:manage',
    'teams:manage',
    'settings:manage',
    'ai:use',
    'projects:manage',
    'reports:view',
  ],
  [MemberRole.ADMIN]: [
    'members:manage',
    'teams:manage', 
    'settings:manage',
    'ai:use',
    'projects:manage',
    'reports:view',
  ],
  [MemberRole.MANAGER]: [
    'teams:manage',
    'ai:use',
    'projects:manage',
    'reports:view',
  ],
  [MemberRole.MEMBER]: [
    'ai:use',
    'projects:access',
    'reports:view',
  ],
  [MemberRole.VIEWER]: [
    'reports:view',
  ],
};
```

## 🚀 API Endpoints

### Organization Management
```typescript
// Create organization
POST /api/v1/organizations
{
  "name": "Acme Corporation",
  "description": "Leading technology company",
  "type": "company",
  "settings": {
    "timezone": "UTC",
    "language": "en",
    "currency": "USD"
  }
}

// Get user's organizations
GET /api/v1/organizations

// Get organization details
GET /api/v1/organizations/:id

// Update organization
PUT /api/v1/organizations/:id
{
  "name": "Updated Name",
  "description": "Updated description",
  "settings": {
    "timezone": "America/New_York"
  }
}

// Delete organization (Owner only)
DELETE /api/v1/organizations/:id
```

### Member Management
```typescript
// Get organization members
GET /api/v1/organizations/:id/members

// Update member role (Admin+ only)
PUT /api/v1/organizations/:id/members/:memberId
{
  "role": "manager",
  "permissions": ["ai:use", "projects:manage"]
}

// Remove member (Admin+ only)
DELETE /api/v1/organizations/:id/members/:memberId
```

### Team Management
```typescript
// Create team (Manager+ only)
POST /api/v1/organizations/:id/teams
{
  "name": "Development Team",
  "description": "Frontend and backend developers",
  "settings": {
    "isPrivate": false,
    "maxMembers": 10
  }
}

// Get organization teams
GET /api/v1/organizations/:id/teams

// Add member to team
POST /api/v1/organizations/:id/teams/:teamId/members
{
  "memberIds": ["member-uuid-1", "member-uuid-2"]
}

// Set team lead
PUT /api/v1/organizations/:id/teams/:teamId/lead
{
  "leadId": "member-uuid"
}
```

### Invitation System
```typescript
// Send invitation (Admin+ only)
POST /api/v1/organizations/:id/invitations
{
  "email": "<EMAIL>",
  "role": "member",
  "message": "Welcome to our team!",
  "expiresIn": "7d"
}

// Get pending invitations
GET /api/v1/organizations/:id/invitations

// Accept invitation
POST /api/v1/invitations/:token/accept

// Decline invitation
POST /api/v1/invitations/:token/decline

// Revoke invitation (Admin+ only)
DELETE /api/v1/organizations/:id/invitations/:invitationId
```

## 🔧 Service Implementation

### Organization Service
```typescript
@Injectable()
export class OrganizationsService {
  async createOrganization(
    userId: string,
    createDto: CreateOrganizationDto
  ): Promise<Organization> {
    const organization = this.organizationRepository.create({
      ...createDto,
      createdBy: userId,
    });
    
    const savedOrg = await this.organizationRepository.save(organization);
    
    // Add creator as owner
    await this.addMember(savedOrg.id, userId, MemberRole.OWNER);
    
    return savedOrg;
  }
  
  async addMember(
    organizationId: string,
    userId: string,
    role: MemberRole
  ): Promise<OrganizationMember> {
    const member = this.memberRepository.create({
      organization: { id: organizationId },
      user: { id: userId },
      role,
      joinedAt: new Date(),
    });
    
    return this.memberRepository.save(member);
  }
  
  async updateMemberRole(
    organizationId: string,
    memberId: string,
    newRole: MemberRole,
    updatedBy: string
  ): Promise<OrganizationMember> {
    // Check permissions
    const updater = await this.getMember(organizationId, updatedBy);
    if (!this.canManageMembers(updater.role)) {
      throw new ForbiddenException('Insufficient permissions');
    }
    
    // Prevent role escalation above updater's level
    if (ROLE_LEVELS[newRole] >= ROLE_LEVELS[updater.role]) {
      throw new ForbiddenException('Cannot assign role equal or higher than your own');
    }
    
    await this.memberRepository.update(memberId, { role: newRole });
    return this.memberRepository.findOne(memberId);
  }
}
```

### Invitation Service
```typescript
@Injectable()
export class InvitationsService {
  async sendInvitation(
    organizationId: string,
    inviteDto: InviteUserDto,
    invitedBy: string
  ): Promise<Invitation> {
    // Check if user already exists
    const existingUser = await this.usersService.findByEmail(inviteDto.email);
    
    // Create invitation
    const invitation = this.invitationRepository.create({
      organization: { id: organizationId },
      email: inviteDto.email,
      role: inviteDto.role,
      message: inviteDto.message,
      invitedBy: { id: invitedBy },
      token: this.generateInvitationToken(),
      expiresAt: this.calculateExpiryDate(inviteDto.expiresIn),
      status: InvitationStatus.PENDING,
    });
    
    const savedInvitation = await this.invitationRepository.save(invitation);
    
    // Send email
    await this.emailService.sendInvitationEmail(savedInvitation);
    
    return savedInvitation;
  }
  
  async acceptInvitation(token: string, userId?: string): Promise<OrganizationMember> {
    const invitation = await this.findByToken(token);
    
    if (invitation.status !== InvitationStatus.PENDING) {
      throw new BadRequestException('Invitation already processed');
    }
    
    if (invitation.expiresAt < new Date()) {
      throw new BadRequestException('Invitation expired');
    }
    
    // If user doesn't exist, they need to register first
    if (!userId) {
      throw new BadRequestException('User must be registered to accept invitation');
    }
    
    // Add user to organization
    const member = await this.organizationsService.addMember(
      invitation.organization.id,
      userId,
      invitation.role
    );
    
    // Update invitation status
    await this.invitationRepository.update(invitation.id, {
      status: InvitationStatus.ACCEPTED,
      acceptedAt: new Date(),
    });
    
    return member;
  }
}
```

### Team Service
```typescript
@Injectable()
export class TeamsService {
  async createTeam(
    organizationId: string,
    createDto: CreateTeamDto,
    createdBy: string
  ): Promise<Team> {
    // Check permissions
    const member = await this.organizationsService.getMember(organizationId, createdBy);
    if (!this.canManageTeams(member.role)) {
      throw new ForbiddenException('Insufficient permissions to create teams');
    }
    
    const team = this.teamRepository.create({
      ...createDto,
      organization: { id: organizationId },
    });
    
    const savedTeam = await this.teamRepository.save(team);
    
    // Add creator to team
    await this.addMemberToTeam(savedTeam.id, member.id);
    
    return savedTeam;
  }
  
  async addMemberToTeam(teamId: string, memberId: string): Promise<void> {
    const team = await this.teamRepository.findOne(teamId, {
      relations: ['members'],
    });
    
    const member = await this.organizationMemberRepository.findOne(memberId);
    
    if (!team.members.find(m => m.id === memberId)) {
      team.members.push(member);
      await this.teamRepository.save(team);
    }
  }
  
  async setTeamLead(teamId: string, leadId: string): Promise<Team> {
    const team = await this.teamRepository.findOne(teamId);
    const lead = await this.organizationMemberRepository.findOne(leadId);
    
    // Verify lead is member of the team
    const isTeamMember = await this.isTeamMember(teamId, leadId);
    if (!isTeamMember) {
      throw new BadRequestException('Lead must be a member of the team');
    }
    
    team.lead = lead;
    return this.teamRepository.save(team);
  }
}
```

## 🔒 Permission System

### Permission Checking
```typescript
@Injectable()
export class PermissionsService {
  async hasPermission(
    userId: string,
    organizationId: string,
    permission: string
  ): Promise<boolean> {
    const member = await this.getMember(userId, organizationId);
    
    if (!member) {
      return false;
    }
    
    // Check role-based permissions
    const rolePermissions = ROLE_PERMISSIONS[member.role] || [];
    if (rolePermissions.includes(permission)) {
      return true;
    }
    
    // Check custom permissions
    const customPermissions = member.permissions.map(p => p.name);
    return customPermissions.includes(permission);
  }
  
  async checkPermission(
    userId: string,
    organizationId: string,
    permission: string
  ): Promise<void> {
    const hasPermission = await this.hasPermission(userId, organizationId, permission);
    
    if (!hasPermission) {
      throw new ForbiddenException(`Missing permission: ${permission}`);
    }
  }
  
  async getUserPermissions(
    userId: string,
    organizationId: string
  ): Promise<string[]> {
    const member = await this.getMember(userId, organizationId);
    
    if (!member) {
      return [];
    }
    
    const rolePermissions = ROLE_PERMISSIONS[member.role] || [];
    const customPermissions = member.permissions.map(p => p.name);
    
    return [...new Set([...rolePermissions, ...customPermissions])];
  }
}
```

### Guards Implementation
```typescript
@Injectable()
export class OrganizationGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = request.params.organizationId;
    
    // Check if user is member of organization
    return user.organizations.some(org => org.id === organizationId);
  }
}

@Injectable()
export class PermissionGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermission = this.reflector.get<string>(
      'permission',
      context.getHandler()
    );
    
    if (!requiredPermission) {
      return true;
    }
    
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = request.params.organizationId;
    
    return this.permissionsService.hasPermission(
      user.id,
      organizationId,
      requiredPermission
    );
  }
}
```

## 🧪 Testing Organization Features

### Unit Tests
```typescript
describe('OrganizationsService', () => {
  it('should create organization with owner role', async () => {
    const createDto = {
      name: 'Test Org',
      type: OrganizationType.COMPANY,
    };
    
    const organization = await service.createOrganization('user-id', createDto);
    
    expect(organization.name).toBe('Test Org');
    
    const member = await service.getMember(organization.id, 'user-id');
    expect(member.role).toBe(MemberRole.OWNER);
  });
  
  it('should prevent role escalation', async () => {
    await expect(
      service.updateMemberRole('org-id', 'member-id', MemberRole.OWNER, 'admin-user')
    ).rejects.toThrow('Cannot assign role equal or higher than your own');
  });
});
```

### Integration Tests
```typescript
describe('Organizations Controller (e2e)', () => {
  it('should create organization', () => {
    return request(app.getHttpServer())
      .post('/organizations')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Organization',
        type: 'company',
      })
      .expect(201)
      .expect((res) => {
        expect(res.body.name).toBe('Test Organization');
      });
  });
});
```

## 🎯 Best Practices

### 1. Role Management
- **Principle of least privilege** - Assign minimum required permissions
- **Role hierarchy enforcement** - Prevent privilege escalation
- **Regular permission audits** - Review and update permissions
- **Clear role definitions** - Document role responsibilities

### 2. Team Organization
- **Logical team structure** - Organize by function or project
- **Clear team leads** - Assign responsible team leaders
- **Team size limits** - Maintain manageable team sizes
- **Cross-functional teams** - Include diverse skill sets

### 3. Invitation Management
- **Secure invitation tokens** - Use cryptographically secure tokens
- **Expiration policies** - Set reasonable expiration times
- **Email verification** - Verify email addresses before sending
- **Audit trail** - Track invitation activities

**This organization system provides enterprise-grade multi-tenancy với comprehensive team management và security.** 🏢✨
