# 🔐 Luồng <PERSON> & <PERSON><PERSON> Quyền

## 📋 Tổng Quan

Delify Platform sử dụng **xác thực dựa trên JWT** kết hợp với **Kiểm Soát T<PERSON>y Cập Dựa Trên <PERSON> (RBAC)** và **quyền hạn cấp tổ chức** để đảm bảo bảo mật và tính linh hoạt.

## 🔑 Kiến Trúc Xác Thực

### Các Thành Phần Cốt Lõi
```
┌─────────────────────────────────────────────────────────────┐
│                    Hệ Thống Xác Thực                       │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │     JWT     │ │   Quản Lý   │ │   Refresh   │          │
│  │   Tokens    │ │   Phiên     │ │   Tokens    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Hệ Thống  │ │   Quyền     │ │   Guards    │          │
│  │    RBAC     │ │  Tổ Chức    │ │ & Filters   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Luồng Xác Thực

### 1. <PERSON><PERSON><PERSON>ờ<PERSON> Dùng
```typescript
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "accountType": "business", // hoặc "personal"
  "firstName": "John",
  "lastName": "Doe"
}

// Phản hồi
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "accountType": "business",
    "isActive": true,
    "emailVerified": false
  },
  "tokens": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 900 // 15 phút
  }
}
```

### 2. Đăng Nhập Người Dùng
```typescript
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********"
  }
}

// Phản hồi
{
  "user": { /* đối tượng người dùng */ },
  "tokens": { /* JWT tokens */ },
  "session": {
    "id": "session-uuid",
    "deviceInfo": { /* chi tiết thiết bị */ },
    "lastActivity": "2024-01-01T00:00:00Z"
  }
}
```

### 3. Làm Mới Token
```typescript
POST /api/v1/auth/refresh
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// Phản hồi
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "expiresIn": 900
}
```

## 🛡️ Hệ Thống Phân Quyền

### Phân Cấp Vai Trò
```
Chủ Sở Hữu (Cấp 5)
  ├── Kiểm soát tổ chức đầy đủ
  ├── Quản lý thanh toán
  ├── Xóa tổ chức
  └── Chuyển quyền sở hữu

Quản Trị Viên (Cấp 4)
  ├── Quản lý người dùng
  ├── Quản lý nhóm
  ├── Cấu hình cài đặt
  └── Tất cả quyền của Manager

Quản Lý (Cấp 3)
  ├── Quản lý dự án
  ├── Điều phối nhóm
  ├── Phân bổ tài nguyên
  └── Tất cả quyền của Member

Thành Viên (Cấp 2)
  ├── Truy cập dự án được giao
  ├── Cộng tác với nhóm
  ├── Sử dụng tính năng AI
  └── Tất cả quyền của Viewer

Người Xem (Cấp 1)
  ├── Truy cập chỉ đọc
  ├── Xem báo cáo
  └── Điều hướng cơ bản
```

### Ma Trận Quyền Hạn
| Hành Động | Chủ SH | Admin | Manager | Member | Viewer |
|-----------|--------|-------|---------|--------|--------|
| Xóa Tổ Chức | ✅ | ❌ | ❌ | ❌ | ❌ |
| Quản Lý Thanh Toán | ✅ | ❌ | ❌ | ❌ | ❌ |
| Mời Người Dùng | ✅ | ✅ | ✅ | ❌ | ❌ |
| Quản Lý Nhóm | ✅ | ✅ | ✅ | ❌ | ❌ |
| Sử Dụng AI | ✅ | ✅ | ✅ | ✅ | ❌ |
| Xem Báo Cáo | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🔒 Chi Tiết Triển Khai

### Cấu Trúc JWT Token
```typescript
// Payload Access Token
{
  "sub": "user-uuid",           // ID Người dùng
  "email": "<EMAIL>",
  "accountType": "business",
  "organizations": [
    {
      "id": "org-uuid",
      "role": "admin",
      "permissions": ["read", "write", "manage_users"]
    }
  ],
  "iat": **********,           // Thời gian phát hành
  "exp": **********,           // Hết hạn (15 phút)
  "type": "access"
}

// Payload Refresh Token
{
  "sub": "user-uuid",
  "sessionId": "session-uuid",
  "iat": **********,
  "exp": **********,           // Hết hạn (24 giờ)
  "type": "refresh"
}
```

### Triển Khai Guards

#### 1. JWT Auth Guard
```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // Bỏ qua xác thực cho các route công khai
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      IS_PUBLIC_KEY,
      [context.getHandler(), context.getClass()]
    );
    
    if (isPublic) {
      return true;
    }
    
    return super.canActivate(context);
  }
}
```

#### 2. Roles Guard
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<MemberRole[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    const organizationId = this.getOrganizationId(context);
    
    return this.hasRequiredRole(user, organizationId, requiredRoles);
  }
}
```

#### 3. Organization Guard
```typescript
@Injectable()
export class OrganizationGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = request.params.organizationId;
    
    // Kiểm tra xem người dùng có phải là thành viên của tổ chức không
    return user.organizations.some(org => org.id === organizationId);
  }
}
```

### Sử Dụng Decorators

#### 1. Routes Công Khai
```typescript
@Controller('auth')
export class AuthController {
  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }
  
  @Public()
  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }
}
```

#### 2. Truy Cập Dựa Trên Vai Trò
```typescript
@Controller('organizations')
@UseGuards(JwtAuthGuard, OrganizationGuard, RolesGuard)
export class OrganizationsController {
  @Roles(MemberRole.ADMIN, MemberRole.OWNER)
  @Post(':organizationId/users/invite')
  async inviteUser(
    @Param('organizationId') organizationId: string,
    @Body() inviteDto: InviteUserDto
  ) {
    return this.organizationsService.inviteUser(organizationId, inviteDto);
  }
  
  @Roles(MemberRole.MEMBER, MemberRole.MANAGER, MemberRole.ADMIN, MemberRole.OWNER)
  @Get(':organizationId/teams')
  async getTeams(@Param('organizationId') organizationId: string) {
    return this.teamsService.findByOrganization(organizationId);
  }
}
```

#### 3. Truy Cập Người Dùng Hiện Tại
```typescript
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  @Get('profile')
  async getProfile(@CurrentUser() user: User) {
    return this.usersService.getProfile(user.id);
  }

  @Put('profile')
  async updateProfile(
    @CurrentUser() user: User,
    @Body() updateDto: UpdateProfileDto
  ) {
    return this.usersService.updateProfile(user.id, updateDto);
  }
}
```

## 🔄 Quản Lý Phiên

### Session Entity
```typescript
@Entity('sessions')
export class Session {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, user => user.sessions)
  user: User;

  @Column()
  refreshToken: string;

  @Column('json')
  deviceInfo: {
    userAgent: string;
    ipAddress: string;
    platform?: string;
    browser?: string;
  };

  @Column()
  lastActivity: Date;

  @Column()
  expiresAt: Date;

  @Column({ default: true })
  isActive: boolean;
}
```

### Session Service
```typescript
@Injectable()
export class SessionService {
  async createSession(user: User, deviceInfo: any): Promise<Session> {
    const session = this.sessionRepository.create({
      user,
      deviceInfo,
      lastActivity: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 giờ
      isActive: true,
    });

    return this.sessionRepository.save(session);
  }

  async updateActivity(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastActivity: new Date(),
    });
  }

  async revokeSession(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      isActive: false,
    });
  }

  async getUserSessions(userId: string): Promise<Session[]> {
    return this.sessionRepository.find({
      where: { user: { id: userId }, isActive: true },
      order: { lastActivity: 'DESC' },
    });
  }
}
```

## 🚨 Tính Năng Bảo Mật

### 1. Bảo Mật Mật Khẩu
```typescript
// Mã hóa mật khẩu
async hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Xác thực mật khẩu
async validatePassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Yêu cầu độ mạnh mật khẩu
const passwordSchema = Joi.string()
  .min(8)
  .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
  .required()
  .messages({
    'string.pattern.base': 'Mật khẩu phải chứa ít nhất một chữ thường, một chữ hoa, một số và một ký tự đặc biệt',
  });
```

### 2. Giới Hạn Tốc Độ
```typescript
// Giới hạn tốc độ toàn cục
@UseGuards(ThrottlerGuard)
@Throttle(100, 60) // 100 yêu cầu mỗi phút
@Controller()
export class AppController {}

// Giới hạn tốc độ endpoint cụ thể
@Throttle(5, 60) // 5 lần đăng nhập mỗi phút
@Post('login')
async login(@Body() loginDto: LoginDto) {
  return this.authService.login(loginDto);
}
```

### 3. Xác Thực Đầu Vào

```typescript
// Xác thực DTO
export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @MinLength(8)
  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsObject()
  deviceInfo?: {
    userAgent?: string;
    ipAddress?: string;
  };
}
```

## 🔧 Cấu Hình

### Biến Môi Trường

```bash
# Cấu hình JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=24h

# Cấu hình Session
SESSION_SECRET=your-session-secret
SESSION_MAX_AGE=86400000

# Cấu hình Bảo mật
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Cấu hình CORS
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true
```

### Cấu Hình Auth Module

```typescript
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
    PassportModule,
    TypeOrmModule.forFeature([User, Session, RefreshToken]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtService,
    SessionService,
    JwtStrategy,
    LocalStrategy,
    JwtAuthGuard,
    RolesGuard,
    OrganizationGuard,
  ],
  exports: [AuthService, JwtAuthGuard, RolesGuard, OrganizationGuard],
})
export class AuthModule {}
```

## 🧪 Kiểm Thử Xác Thực

### Unit Tests

```typescript
describe('AuthService', () => {
  it('should authenticate user with valid credentials', async () => {
    const loginDto = { email: '<EMAIL>', password: 'password123' };
    const result = await authService.login(loginDto);

    expect(result).toHaveProperty('user');
    expect(result).toHaveProperty('tokens');
    expect(result.tokens).toHaveProperty('accessToken');
    expect(result.tokens).toHaveProperty('refreshToken');
  });

  it('should throw error for invalid credentials', async () => {
    const loginDto = { email: '<EMAIL>', password: 'wrongpassword' };

    await expect(authService.login(loginDto)).rejects.toThrow('Thông tin đăng nhập không hợp lệ');
  });
});
```

### Integration Tests

```typescript
describe('Auth Controller (e2e)', () => {
  it('/auth/login (POST)', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(201)
      .expect((res) => {
        expect(res.body).toHaveProperty('tokens');
        expect(res.body.tokens).toHaveProperty('accessToken');
      });
  });
});
```

## 🎯 Thực Hành Tốt Nhất

### 1. Quản Lý Token

- **Access tokens ngắn hạn** (15 phút)
- **Refresh tokens dài hạn** (24 giờ)
- **Xoay vòng token tự động**
- **Lưu trữ token an toàn**

### 2. Bảo Mật Phiên

- **Theo dõi thiết bị**
- **Timeout phiên**
- **Giới hạn phiên đồng thời**
- **Thu hồi phiên**

### 3. Xử Lý Lỗi

- **Thông báo lỗi chung** để ngăn chặn rò rỉ thông tin
- **Logging chi tiết** cho giám sát bảo mật
- **Giới hạn tốc độ** cho bảo vệ brute force

**Hệ thống xác thực này cung cấp bảo mật cấp doanh nghiệp với tính linh hoạt cho các cấu trúc tổ chức phức tạp.** 🔐✨
