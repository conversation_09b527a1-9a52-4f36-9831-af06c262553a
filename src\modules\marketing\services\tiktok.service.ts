import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class TikTokService {
  private readonly clientKey: string;
  private readonly clientSecret: string;
  private readonly baseUrl = 'https://open-api.tiktok.com';

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
  ) {
    this.clientKey = this.configService.get('TIKTOK_CLIENT_KEY');
    this.clientSecret = this.configService.get('TIKTOK_CLIENT_SECRET');
  }

  async getLoginUrl(redirectUri: string, scopes: string[] = []): Promise<string> {
    const defaultScopes = [
      'user.info.basic',
      'video.list',
      'video.upload',
    ];

    const allScopes = [...defaultScopes, ...scopes].join(',');
    
    const params = new URLSearchParams({
      client_key: this.clientKey,
      scope: allScopes,
      response_type: 'code',
      redirect_uri: redirectUri,
      state: this.generateState(),
    });

    return `${this.baseUrl}/platform/oauth/connect/?${params.toString()}`;
  }

  async exchangeCodeForToken(code: string, redirectUri: string): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    refreshExpiresIn: number;
    tokenType: string;
    scope: string;
  }> {
    const url = `${this.baseUrl}/oauth/access_token/`;
    
    const body = {
      client_key: this.clientKey,
      client_secret: this.clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: redirectUri,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error_description || data.error}`);
      }

      return {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        expiresIn: data.expires_in,
        refreshExpiresIn: data.refresh_expires_in,
        tokenType: data.token_type,
        scope: data.scope,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - exchangeCodeForToken');
      throw error;
    }
  }

  async refreshAccessToken(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    refreshExpiresIn: number;
  }> {
    const url = `${this.baseUrl}/oauth/refresh_token/`;
    
    const body = {
      client_key: this.clientKey,
      client_secret: this.clientSecret,
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error_description || data.error}`);
      }

      return {
        accessToken: data.access_token,
        refreshToken: data.refresh_token,
        expiresIn: data.expires_in,
        refreshExpiresIn: data.refresh_expires_in,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - refreshAccessToken');
      throw error;
    }
  }

  async getUserInfo(accessToken: string): Promise<{
    openId: string;
    unionId: string;
    avatarUrl: string;
    displayName: string;
  }> {
    const url = `${this.baseUrl}/user/info/`;
    const params = new URLSearchParams({
      access_token: accessToken,
      fields: 'open_id,union_id,avatar_url,display_name',
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error.message}`);
      }

      return {
        openId: data.data.open_id,
        unionId: data.data.union_id,
        avatarUrl: data.data.avatar_url,
        displayName: data.data.display_name,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - getUserInfo');
      throw error;
    }
  }

  async getUserVideos(accessToken: string, cursor: number = 0, maxCount: number = 20): Promise<{
    videos: Array<{
      id: string;
      title: string;
      coverImageUrl: string;
      videoUrl: string;
      duration: number;
      createTime: number;
    }>;
    cursor: number;
    hasMore: boolean;
  }> {
    const url = `${this.baseUrl}/video/list/`;
    const params = new URLSearchParams({
      access_token: accessToken,
      cursor: cursor.toString(),
      max_count: maxCount.toString(),
      fields: 'id,title,cover_image_url,video_url,duration,create_time',
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error.message}`);
      }

      return {
        videos: data.data.videos.map(video => ({
          id: video.id,
          title: video.title,
          coverImageUrl: video.cover_image_url,
          videoUrl: video.video_url,
          duration: video.duration,
          createTime: video.create_time,
        })),
        cursor: data.data.cursor,
        hasMore: data.data.has_more,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - getUserVideos');
      throw error;
    }
  }

  async uploadVideo(accessToken: string, videoData: {
    videoUrl: string;
    title: string;
    description?: string;
    privacy?: 'PUBLIC' | 'PRIVATE' | 'FRIENDS';
    allowComment?: boolean;
    allowDuet?: boolean;
    allowStitch?: boolean;
  }): Promise<{ videoId: string; shareUrl: string }> {
    const url = `${this.baseUrl}/share/video/upload/`;
    
    const body = {
      access_token: accessToken,
      video_url: videoData.videoUrl,
      title: videoData.title,
      description: videoData.description || '',
      privacy_level: videoData.privacy || 'PUBLIC',
      disable_comment: !(videoData.allowComment ?? true),
      disable_duet: !(videoData.allowDuet ?? true),
      disable_stitch: !(videoData.allowStitch ?? true),
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error.message}`);
      }

      return {
        videoId: data.data.video_id,
        shareUrl: data.data.share_url,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - uploadVideo');
      throw error;
    }
  }

  async getVideoComments(accessToken: string, videoId: string, cursor: number = 0, count: number = 50): Promise<{
    comments: Array<{
      id: string;
      text: string;
      user: {
        id: string;
        displayName: string;
        avatarUrl: string;
      };
      createTime: number;
      likeCount: number;
    }>;
    cursor: number;
    hasMore: boolean;
  }> {
    const url = `${this.baseUrl}/comment/list/`;
    const params = new URLSearchParams({
      access_token: accessToken,
      video_id: videoId,
      cursor: cursor.toString(),
      count: count.toString(),
    });

    try {
      const response = await fetch(`${url}?${params.toString()}`);
      const data = await response.json();

      if (data.error) {
        throw new Error(`TikTok API Error: ${data.error.message}`);
      }

      return {
        comments: data.data.comments.map(comment => ({
          id: comment.id,
          text: comment.text,
          user: {
            id: comment.user.id,
            displayName: comment.user.display_name,
            avatarUrl: comment.user.avatar_url,
          },
          createTime: comment.create_time,
          likeCount: comment.like_count,
        })),
        cursor: data.data.cursor,
        hasMore: data.data.has_more,
      };
    } catch (error) {
      this.logger.logError(error, 'TikTokService - getVideoComments');
      throw error;
    }
  }

  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
}
