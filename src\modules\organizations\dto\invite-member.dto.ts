import { IsEmail, IsString, IsOptional, IsUUID, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InviteMemberDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ example: 'role-uuid-here' })
  @IsOptional()
  @IsUUID()
  roleId?: string;

  @ApiPropertyOptional({ example: 'Welcome to our team! We are excited to have you join us.' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  message?: string;

  @ApiPropertyOptional({
    example: {
      department: 'Marketing',
      jobTitle: 'Marketing Specialist',
      phoneNumber: '******-0123'
    }
  })
  @IsOptional()
  metadata?: {
    department?: string;
    jobTitle?: string;
    phoneNumber?: string;
    notes?: string;
    customFields?: Record<string, any>;
  };

  @ApiPropertyOptional({
    example: {
      canInviteMembers: false,
      canManageRoles: false,
      canAccessBilling: false,
      canExportData: true
    }
  })
  @IsOptional()
  restrictions?: {
    canInviteMembers?: boolean;
    canManageRoles?: boolean;
    canAccessBilling?: boolean;
    canExportData?: boolean;
    ipWhitelist?: string[];
    timeRestrictions?: {
      allowedHours?: string;
      allowedDays?: number[];
      timezone?: string;
    };
  };
}
