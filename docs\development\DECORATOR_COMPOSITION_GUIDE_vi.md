# Hướng Dẫn Kết Hợp Decorator

## Tổng Quan

Hướng dẫn này gi<PERSON>i thích cách kết hợp decorators đúng cách trong NestJS, đặc biệt khi xử lý TypeScript strict mode và các vấn đề tương thích kiểu decorator.

## Lỗi TypeScript Decorator Thường Gặp

### TS2345: Decorator Type Mismatch

**Thông Báo Lỗi:**
```
Argument of type 'MethodDecorator' is not assignable to parameter of type 'MethodDecorator & ClassDecorator'.
Type 'MethodDecorator' is not assignable to type 'ClassDecorator'.
Target signature provides too few arguments. Expected 3 or more, but got 1.
```

**Nguyên Nhân:**
Lỗi này xảy ra khi sử dụng `applyDecorators()` với các decorators chỉ là kiểu `MethodDecorator`, nhưng TypeScript mong đợi decorators có thể hoạt động như cả `MethodDecorator & ClassDecorator`.

## Giải Pháp: Type Casting Đúng Cách

### Trước (Gây Lỗi TS2345)

```typescript
export const RBAC = (options: RBACOptions) => {
  const decorators = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  decorators.push(
    ApiOperation({ 
      summary: 'Protected endpoint',
      description: 'RBAC protected endpoint'
    }), // ❌ Lỗi TS2345: MethodDecorator không thể gán cho MethodDecorator & ClassDecorator
    ApiResponse({ 
      status: 200, 
      description: 'Success' 
    }), // ❌ Cùng lỗi
  );

  return applyDecorators(...decorators);
};
```

### Sau (Đã Sửa Lỗi)

```typescript
export const RBAC = (options: RBACOptions) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  decorators.push(
    ApiOperation({ 
      summary: 'Protected endpoint',
      description: 'RBAC protected endpoint'
    }) as MethodDecorator, // ✅ Type casting rõ ràng
    ApiResponse({ 
      status: 200, 
      description: 'Success' 
    }) as MethodDecorator, // ✅ Type casting rõ ràng
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Giải Thích Các Thay Đổi Chính

### 1. Typing Array Rõ Ràng

```typescript
// Trước
const decorators = [];

// Sau
const decorators: Array<MethodDecorator | ClassDecorator> = [];
```

**Tại Sao:** Typing array rõ ràng giúp TypeScript hiểu được các kiểu decorators được phép.

### 2. Type Casting Swagger Decorators

```typescript
// Trước
ApiOperation({ summary: 'Test' })

// Sau
ApiOperation({ summary: 'Test' }) as MethodDecorator
```

**Tại Sao:** Swagger decorators (`ApiOperation`, `ApiResponse`, `ApiForbiddenResponse`) là kiểu `MethodDecorator`. Casting rõ ràng báo cho TypeScript biết cách xử lý chúng.

### 3. Final Array Casting

```typescript
// Trước
return applyDecorators(...decorators);

// Sau
return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
```

**Tại Sao:** `applyDecorators` mong đợi decorators có thể hoạt động như cả method và class decorators. Cast cuối cùng đảm bảo tương thích.

## Các Kiểu Decorator Trong NestJS

### MethodDecorator
Áp dụng cho class methods:
```typescript
class Controller {
  @MethodDecorator()
  method() {}
}
```

### ClassDecorator
Áp dụng cho classes:
```typescript
@ClassDecorator()
class Controller {}
```

### MethodDecorator & ClassDecorator
Có thể áp dụng cho cả hai:
```typescript
@BothDecorator()
class Controller {
  @BothDecorator()
  method() {}
}
```

## NestJS Decorators Phổ Biến Theo Kiểu

### Chỉ MethodDecorator
- `@ApiOperation()`
- `@ApiResponse()`
- `@ApiForbiddenResponse()`
- `@Get()`, `@Post()`, `@Put()`, `@Delete()`
- `@Body()`, `@Param()`, `@Query()`

### Chỉ ClassDecorator
- `@Controller()`
- `@Injectable()`
- `@Module()`

### Cả Hai (MethodDecorator & ClassDecorator)
- `@UseGuards()`
- `@ApiBearerAuth()`
- `@SetMetadata()`
- Custom decorators sử dụng `applyDecorators()`

## Thực Hành Tốt Nhất

### 1. Sử Dụng Explicit Typing

```typescript
// ✅ Tốt
const decorators: Array<MethodDecorator | ClassDecorator> = [];

// ❌ Tránh
const decorators = [];
```

### 2. Cast Swagger Decorators

```typescript
// ✅ Tốt
decorators.push(
  ApiOperation({ summary: 'Test' }) as MethodDecorator,
  ApiResponse({ status: 200 }) as MethodDecorator,
);

// ❌ Tránh
decorators.push(
  ApiOperation({ summary: 'Test' }),
  ApiResponse({ status: 200 }),
);
```

### 3. Nhóm Decorators Tương Tự

```typescript
// ✅ Tốt - Nhóm theo kiểu
const guardDecorators = [
  UseGuards(JwtAuthGuard, RBACGuard),
  ApiBearerAuth(),
];

const swaggerDecorators = [
  ApiOperation({ summary: 'Test' }) as MethodDecorator,
  ApiResponse({ status: 200 }) as MethodDecorator,
];

return applyDecorators(
  ...guardDecorators,
  ...swaggerDecorators as Array<MethodDecorator & ClassDecorator>
);
```

### 4. Tạo Helper Functions An Toàn Kiểu

```typescript
// Helper function cho Swagger decorators
const createSwaggerDecorators = (summary: string, description: string) => [
  ApiOperation({ summary, description }) as MethodDecorator,
  ApiResponse({ status: 200, description: 'Thành công' }) as MethodDecorator,
  ApiForbiddenResponse({ description: 'Không có quyền truy cập' }) as MethodDecorator,
];

// Sử dụng
export const RBAC = (options: RBACOptions) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
    ...createSwaggerDecorators('Protected endpoint', options.description),
  ];

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Test Decorator Composition

### Ví Dụ Unit Test

```typescript
describe('RBAC Decorator', () => {
  it('should compose decorators without TypeScript errors', () => {
    const decorator = RBAC({
      permission: 'TEST_PERMISSION',
      description: 'Test endpoint'
    });

    expect(decorator).toBeDefined();
    expect(typeof decorator).toBe('function');
  });

  it('should apply to controller methods', () => {
    class TestController {
      @RBAC({ permission: 'TEST_PERMISSION' })
      testMethod() {
        return 'test';
      }
    }

    const controller = new TestController();
    expect(controller.testMethod()).toBe('test');
  });
});
```

## Khắc Phục Sự Cố

### Lỗi: "Cannot read property of undefined"

**Nguyên Nhân:** Decorator không được typed hoặc áp dụng đúng cách.

**Giải Pháp:** Đảm bảo tất cả decorators được cast đúng và array được typed chính xác.

### Lỗi: "Decorator function expected"

**Nguyên Nhân:** Kiểu trả về không đúng từ decorator factory.

**Giải Pháp:** Đảm bảo `applyDecorators()` được gọi với decorators được typed đúng.

### Lỗi: "Metadata key already exists"

**Nguyên Nhân:** Trùng lặp metadata keys trong decorator composition.

**Giải Pháp:** Kiểm tra các lệnh gọi `SetMetadata()` trùng lặp với cùng key.

## Patterns Nâng Cao

### Conditional Decorator Application

```typescript
export const ConditionalRBAC = (options: RBACOptions & { condition?: boolean }) => {
  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard),
    ApiBearerAuth(),
  ];

  if (options.condition !== false) {
    decorators.push(UseGuards(RBACGuard));
  }

  if (options.permission) {
    decorators.push(SetMetadata(PERMISSION_KEY, options.permission));
  }

  // Luôn thêm Swagger documentation
  decorators.push(
    ApiOperation({ 
      summary: options.condition ? 'Protected endpoint' : 'Authenticated endpoint' 
    }) as MethodDecorator
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

### Decorator Factory với Validation

```typescript
export const ValidatedRBAC = (options: RBACOptions) => {
  // Validate options
  if (!options.permission && !options.role && options.roleLevel === undefined) {
    throw new Error('Ít nhất một RBAC option phải được cung cấp');
  }

  const decorators: Array<MethodDecorator | ClassDecorator> = [
    UseGuards(JwtAuthGuard, RBACGuard),
    ApiBearerAuth(),
  ];

  // Thêm metadata dựa trên options
  if (options.permission) {
    decorators.push(SetMetadata(PERMISSION_KEY, options.permission));
  }

  // Thêm Swagger documentation
  decorators.push(
    ApiOperation({ 
      summary: `Yêu cầu: ${Object.keys(options).join(', ')}`,
      description: options.description || 'Endpoint được bảo vệ bởi RBAC'
    }) as MethodDecorator
  );

  return applyDecorators(...decorators as Array<MethodDecorator & ClassDecorator>);
};
```

## Kết Luận

Kết hợp decorator đúng cách trong NestJS yêu cầu:

1. **Explicit typing** của decorator arrays
2. **Type casting** của Swagger decorators
3. **Final casting** cho `applyDecorators()`
4. **Patterns nhất quán** trong toàn bộ codebase

Tuân theo các patterns này đảm bảo tương thích TypeScript trong khi duy trì chức năng tài liệu song ngữ và tính toàn vẹn của hệ thống RBAC.

---

Để biết thêm thông tin về NestJS decorators, xem [tài liệu chính thức NestJS](https://docs.nestjs.com/custom-decorators).
