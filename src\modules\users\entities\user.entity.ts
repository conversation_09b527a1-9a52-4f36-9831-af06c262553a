import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MANAGER = 'manager',
}

export enum AccountType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export enum SecurityMethod {
  DISABLED = 'disabled',
  EMAIL_VERIFICATION = 'email_verification',
  TWO_FACTOR_AUTH = 'two_factor_auth',
  FIXED_CODE = 'fixed_code',
}

@Entity('users')
@Index(['email'], { unique: true })
@Index(['username'], { unique: true })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column({ unique: true })
  username: string;

  @Column()
  @Exclude()
  password: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ nullable: true })
  avatar: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  company: string;

  @Column({ nullable: true })
  website: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: AccountType,
    default: AccountType.PERSONAL,
  })
  accountType: AccountType;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ nullable: true })
  @Exclude()
  emailVerificationToken: string;

  @Column({ nullable: true })
  @Exclude()
  passwordResetToken: string;

  @Column({ nullable: true })
  passwordResetExpires: Date;

  @Column({
    type: 'enum',
    enum: SecurityMethod,
    default: SecurityMethod.DISABLED,
  })
  securityMethod: SecurityMethod;

  @Column({ nullable: true })
  @Exclude()
  twoFactorSecret: string;

  @Column({ default: false })
  twoFactorEnabled: boolean;

  @Column({ nullable: true })
  @Exclude()
  verificationCode: string;

  @Column({ nullable: true })
  verificationCodeExpires: Date;

  @Column({ nullable: true })
  @Exclude()
  fixedCode: string;

  @Column({ nullable: true })
  @Exclude()
  refreshToken: string;

  @Column({ nullable: true })
  refreshTokenExpires: Date;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @Column({ nullable: true })
  lastLoginIp: string;

  @Column({ type: 'jsonb', nullable: true })
  preferences: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  socialAccounts: {
    facebook?: {
      id: string;
      accessToken: string;
      pages?: Array<{
        id: string;
        name: string;
        accessToken: string;
      }>;
    };
    tiktok?: {
      id: string;
      accessToken: string;
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  get isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get isBusiness(): boolean {
    return this.accountType === AccountType.BUSINESS;
  }

  get isPersonal(): boolean {
    return this.accountType === AccountType.PERSONAL;
  }

  // Helper methods
  hasFacebookAccess(): boolean {
    return !!(this.socialAccounts?.facebook?.accessToken);
  }

  hasTikTokAccess(): boolean {
    return !!(this.socialAccounts?.tiktok?.accessToken);
  }

  getFacebookPages(): Array<{ id: string; name: string; accessToken: string }> {
    return this.socialAccounts?.facebook?.pages || [];
  }

  // Security method helper methods
  hasSecurityMethodEnabled(): boolean {
    return this.securityMethod !== SecurityMethod.DISABLED;
  }

  requiresEmailVerification(): boolean {
    return this.securityMethod === SecurityMethod.EMAIL_VERIFICATION;
  }

  requiresTwoFactorAuth(): boolean {
    return this.securityMethod === SecurityMethod.TWO_FACTOR_AUTH;
  }

  requiresFixedCode(): boolean {
    return this.securityMethod === SecurityMethod.FIXED_CODE;
  }

  isVerificationCodeValid(code: string): boolean {
    if (!this.verificationCode || !this.verificationCodeExpires) {
      return false;
    }
    return this.verificationCode === code && new Date() < this.verificationCodeExpires;
  }

  isFixedCodeValid(code: string): boolean {
    if (!this.fixedCode) {
      return false;
    }
    return this.fixedCode === code;
  }

  hasFixedCodeSet(): boolean {
    return !!(this.fixedCode && this.fixedCode.length > 0);
  }

  isRefreshTokenValid(token: string): boolean {
    if (!this.refreshToken || !this.refreshTokenExpires) {
      return false;
    }
    return this.refreshToken === token && new Date() < this.refreshTokenExpires;
  }

  hasValidRefreshToken(): boolean {
    return !!(this.refreshToken && this.refreshTokenExpires && new Date() < this.refreshTokenExpires);
  }
}
