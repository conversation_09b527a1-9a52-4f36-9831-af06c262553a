# SSO API Documentation

## Overview

This document provides comprehensive API documentation for the Single Sign-On (SSO) system endpoints.

## Base URL

```
https://api.yourcompany.com/auth/sso
```

## Authentication

Most SSO endpoints require JWT authentication. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## SSO Authentication Endpoints

### SSO Login

Performs SSO login with cross-subdomain support.

**Endpoint:** `POST /auth/sso/login`

**Request Body:**
```json
{
  "userId": "uuid-user-id",
  "application": "app.yourcompany.com",
  "deviceName": "Chrome on Windows 11",
  "location": "New York, USA",
  "rememberDevice": true,
  "rbacInfo": {
    "email": "<EMAIL>",
    "username": "user123",
    "role": "MANAGER",
    "roles": ["MANAGER", "USER"],
    "permissions": ["USER_MANAGEMENT_READ", "CONTENT_MANAGEMENT_CREATE"],
    "roleLevel": 2,
    "isMasterAccount": false
  }
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "sessionId": "sso_1703000000_abc123def456",
  "expiresIn": 900,
  "tokenType": "Bearer",
  "ssoEnabled": true,
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ]
}
```

**Error Responses:**
- `400 Bad Request` - SSO not enabled or invalid request
- `403 Forbidden` - Application not allowed or device verification required
- `429 Too Many Requests` - Rate limit exceeded

### SSO Logout

Performs SSO logout with optional global logout.

**Endpoint:** `POST /auth/sso/logout`

**Required Headers:**
```
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "globalLogout": true
}
```

**Response:**
```json
{
  "message": "Global logout successful",
  "sessionsTerminated": 3
}
```

### Token Verification

Verifies JWT token validity for cross-domain authentication.

**Endpoint:** `POST /auth/sso/verify`

**Request Body:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "application": "mail.yourcompany.com"
}
```

**Response:**
```json
{
  "valid": true,
  "payload": {
    "sub": "uuid-user-id",
    "email": "<EMAIL>",
    "username": "user123",
    "sessionId": "sso_1703000000_abc123def456",
    "deviceId": "device_fingerprint_hash",
    "roles": ["MANAGER", "USER"],
    "permissions": ["MAIL_MANAGEMENT_READ", "MAIL_MANAGEMENT_CREATE"],
    "aud": ["app.yourcompany.com", "mail.yourcompany.com"],
    "iss": "auth.yourcompany.com",
    "exp": 1703001800,
    "iat": 1703000900
  },
  "sessionValid": true
}
```

**Error Response:**
```json
{
  "valid": false,
  "reason": "Token is blacklisted",
  "sessionValid": false
}
```

### Token Refresh

Refreshes access token using refresh token.

**Endpoint:** `POST /auth/sso/refresh`

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 900
}
```

**Error Responses:**
- `401 Unauthorized` - Invalid or expired refresh token
- `403 Forbidden` - Refresh token is blacklisted

## Session Management Endpoints

### Get Current Session

Retrieves current session information.

**Endpoint:** `GET /auth/sso/session`

**Required Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "sessionId": "sso_1703000000_abc123def456",
  "userId": "uuid-user-id",
  "deviceName": "Chrome on Windows 11",
  "deviceType": "DESKTOP",
  "ipAddress": "*************",
  "location": "New York, USA",
  "isActive": true,
  "lastActivityAt": "2024-01-01T12:30:00.000Z",
  "expiresAt": "2024-01-01T20:30:00.000Z",
  "remainingMinutes": 420
}
```

### Get All User Sessions

Retrieves all active sessions for the current user.

**Endpoint:** `GET /auth/sso/sessions`

**Required Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
[
  {
    "sessionId": "sso_1703000000_abc123def456",
    "userId": "uuid-user-id",
    "deviceName": "Chrome on Windows 11",
    "deviceType": "DESKTOP",
    "ipAddress": "*************",
    "location": "New York, USA",
    "isActive": true,
    "lastActivityAt": "2024-01-01T12:30:00.000Z",
    "expiresAt": "2024-01-01T20:30:00.000Z",
    "remainingMinutes": 420,
    "isCurrent": true
  },
  {
    "sessionId": "sso_1703000001_def456ghi789",
    "userId": "uuid-user-id",
    "deviceName": "Safari on iPhone",
    "deviceType": "MOBILE",
    "ipAddress": "*********",
    "location": "San Francisco, USA",
    "isActive": true,
    "lastActivityAt": "2024-01-01T11:45:00.000Z",
    "expiresAt": "2024-01-01T19:45:00.000Z",
    "remainingMinutes": 375,
    "isCurrent": false
  }
]
```

### Terminate Specific Session

Terminates a specific session.

**Endpoint:** `DELETE /auth/sso/sessions/{sessionId}`

**Required Headers:**
```
Authorization: Bearer <access_token>
```

**Path Parameters:**
- `sessionId` - Session ID to terminate

**Response:** `204 No Content`

**Error Responses:**
- `404 Not Found` - Session not found
- `403 Forbidden` - Cannot terminate session (not owned by user)

### Terminate All Other Sessions

Terminates all sessions except the current one.

**Endpoint:** `DELETE /auth/sso/sessions/all`

**Required Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "message": "All other sessions terminated successfully",
  "sessionsTerminated": 2
}
```

## Configuration Endpoints

### Get SSO Configuration

Retrieves public SSO configuration.

**Endpoint:** `GET /auth/sso/config`

**Response:**
```json
{
  "enabled": true,
  "baseDomain": "yourcompany.com",
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ],
  "sessionTimeout": 480,
  "maxConcurrentSessions": 5,
  "requireDeviceVerification": false
}
```

## Administrative Endpoints

### Get Blacklist Statistics

Retrieves JWT blacklist statistics.

**Endpoint:** `GET /auth/sso/blacklist/stats`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_READ`

**Response:**
```json
{
  "totalBlacklisted": 150,
  "byTokenType": {
    "ACCESS": 120,
    "REFRESH": 25,
    "SSO": 5
  },
  "byReason": {
    "USER_LOGOUT": 80,
    "GLOBAL_LOGOUT": 40,
    "SECURITY_BREACH": 15,
    "ADMIN_REVOKE": 10,
    "PASSWORD_CHANGE": 5
  },
  "recentRevocations": 25,
  "expiredTokens": 50
}
```

### Cleanup Expired Data

Cleans up expired sessions and blacklisted tokens.

**Endpoint:** `POST /auth/sso/cleanup/sessions`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_MANAGE`

**Response:**
```json
{
  "message": "Cleanup completed successfully",
  "cleanedSessions": 45,
  "cleanedTokens": 23
}
```

## SSO Application Management

### Create SSO Application

Creates a new SSO application.

**Endpoint:** `POST /sso/applications`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_CREATE`

**Request Body:**
```json
{
  "name": "ANALYTICS_APP",
  "subdomain": "analytics",
  "displayName": "Analytics Dashboard",
  "description": "Business intelligence and analytics platform",
  "baseUrl": "https://analytics.yourcompany.com",
  "allowedOrigins": [
    "https://analytics.yourcompany.com",
    "https://app.yourcompany.com"
  ]
}
```

**Response:**
```json
{
  "id": "uuid-app-id",
  "name": "ANALYTICS_APP",
  "subdomain": "analytics",
  "displayName": "Analytics Dashboard",
  "description": "Business intelligence and analytics platform",
  "baseUrl": "https://analytics.yourcompany.com",
  "allowedOrigins": [
    "https://analytics.yourcompany.com",
    "https://app.yourcompany.com"
  ],
  "isActive": true,
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z"
}
```

### Get SSO Applications

Retrieves list of SSO applications.

**Endpoint:** `GET /sso/applications`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_READ`

**Query Parameters:**
- `search` (optional) - Search by name, subdomain, or display name
- `isActive` (optional) - Filter by active status
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 10)

**Response:**
```json
{
  "applications": [
    {
      "id": "uuid-app-id",
      "name": "MAIN_APP",
      "subdomain": "app",
      "displayName": "Main Application",
      "description": "Primary application with full feature set",
      "baseUrl": "https://app.yourcompany.com",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 4,
  "page": 1,
  "limit": 10,
  "totalPages": 1
}
```

### Update SSO Application

Updates an existing SSO application.

**Endpoint:** `PUT /sso/applications/{id}`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_UPDATE`

**Path Parameters:**
- `id` - Application UUID

**Request Body:**
```json
{
  "displayName": "Updated Application Name",
  "description": "Updated description",
  "allowedOrigins": [
    "https://app.yourcompany.com",
    "https://new-domain.yourcompany.com"
  ],
  "isActive": true
}
```

### Delete SSO Application

Deletes an SSO application.

**Endpoint:** `DELETE /sso/applications/{id}`

**Required Headers:**
```
Authorization: Bearer <admin_token>
```

**Required Permission:** `SSO_MANAGEMENT_DELETE`

**Path Parameters:**
- `id` - Application UUID

**Response:** `204 No Content`

## Error Responses

### Standard Error Format

All error responses follow this format:

```json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Error type",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "path": "/auth/sso/login"
}
```

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Resource deleted successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource already exists
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## Rate Limiting

SSO endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 10 requests per minute per IP
- **Session management**: 60 requests per minute per user
- **Administrative endpoints**: 100 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 8
X-RateLimit-Reset: 1640995200
```

## CORS Configuration

The SSO system supports cross-origin requests with the following configuration:

```javascript
{
  origin: [
    'https://app.yourcompany.com',
    'https://mail.yourcompany.com',
    'https://core.yourcompany.com',
    'https://api.yourcompany.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Session-ID',
    'X-Device-ID',
    'X-Application'
  ],
  exposedHeaders: [
    'X-Session-ID',
    'X-Token-Expires',
    'X-Refresh-Token'
  ]
}
```

---

**For implementation details, refer to [Implementation Guide](./SSO_IMPLEMENTATION_GUIDE.md).**
