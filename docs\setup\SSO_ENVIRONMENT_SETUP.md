# SSO Environment Variables Setup Guide

## Overview

This guide explains how to configure environment variables for the Single Sign-On (SSO) system in the Delify Platform. The SSO system enables seamless authentication across multiple subdomains and applications.

## Required Environment Variables

### Core SSO Configuration

#### `SSO_ENABLED`
- **Type**: Boolean
- **Default**: `false`
- **Description**: Enables or disables the SSO functionality
- **Example**: `SSO_ENABLED=true`

#### `SSO_BASE_DOMAIN`
- **Type**: String
- **Description**: The base domain for your organization
- **Example**: `SSO_BASE_DOMAIN=yourcompany.com`
- **Note**: Do not include protocol (http/https) or subdomains

#### `SSO_COOKIE_DOMAIN`
- **Type**: String
- **Description**: Domain for cross-subdomain cookie sharing
- **Example**: `SSO_COOKIE_DOMAIN=.yourcompany.com`
- **Note**: Must start with a dot (.) for subdomain sharing

#### `SSO_ISSUER`
- **Type**: String
- **Description**: JWT issuer domain for token validation
- **Example**: `SSO_ISSUER=auth.yourcompany.com`

### Application Configuration

#### `SSO_ALLOWED_APPLICATIONS`
- **Type**: Comma-separated string
- **Description**: List of applications allowed for SSO
- **Example**: `SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com`
- **Note**: Include all subdomains that should participate in SSO

### Session Configuration

#### `SSO_SESSION_TIMEOUT`
- **Type**: Integer (minutes)
- **Default**: `480`
- **Description**: Session timeout in minutes (8 hours default)
- **Example**: `SSO_SESSION_TIMEOUT=480`

#### `SSO_MAX_CONCURRENT_SESSIONS`
- **Type**: Integer
- **Default**: `5`
- **Description**: Maximum concurrent sessions per user
- **Example**: `SSO_MAX_CONCURRENT_SESSIONS=5`

#### `SSO_REQUIRE_DEVICE_VERIFICATION`
- **Type**: Boolean
- **Default**: `false`
- **Description**: Require device verification for new devices
- **Example**: `SSO_REQUIRE_DEVICE_VERIFICATION=false`

### Security Configuration

#### `SSO_ENABLE_AUDIT_LOGGING`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable audit logging for SSO activities
- **Example**: `SSO_ENABLE_AUDIT_LOGGING=true`

#### `SSO_TOKEN_REVOCATION_ENABLED`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable token revocation capabilities
- **Example**: `SSO_TOKEN_REVOCATION_ENABLED=true`

#### `SSO_CROSS_DOMAIN_COOKIES`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Enable cross-domain cookie sharing
- **Example**: `SSO_CROSS_DOMAIN_COOKIES=true`

#### `SSO_SECURE_ONLY`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Require HTTPS for cookies (set to false for development)
- **Example**: `SSO_SECURE_ONLY=true`

#### `SSO_SAME_SITE`
- **Type**: String
- **Default**: `lax`
- **Options**: `strict`, `lax`, `none`
- **Description**: SameSite cookie attribute
- **Example**: `SSO_SAME_SITE=lax`

## JWT Configuration Updates

### `JWT_SECRET`
- **Type**: String
- **Description**: Secret key for JWT token signing
- **Minimum Length**: 32 characters (64+ recommended for production)
- **Example**: `JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters`

### `JWT_EXPIRES_IN`
- **Type**: String
- **Default**: `15m`
- **Description**: Access token expiration time
- **Example**: `JWT_EXPIRES_IN=15m`
- **Note**: Changed from `7d` to `15m` for SSO security

### `JWT_REFRESH_EXPIRES_IN`
- **Type**: String
- **Default**: `7d`
- **Description**: Refresh token expiration time
- **Example**: `JWT_REFRESH_EXPIRES_IN=7d`

## Optional Performance Variables

### `SSO_CACHE_TTL`
- **Type**: Integer (seconds)
- **Default**: `900`
- **Description**: Cache TTL for SSO data (15 minutes)

### `SSO_SESSION_CLEANUP_INTERVAL`
- **Type**: Integer (seconds)
- **Default**: `3600`
- **Description**: Interval for cleaning up expired sessions (1 hour)

### `SSO_TOKEN_CLEANUP_INTERVAL`
- **Type**: Integer (seconds)
- **Default**: `1800`
- **Description**: Interval for cleaning up expired tokens (30 minutes)

## Optional Logging Variables

### `SSO_LOG_LEVEL`
- **Type**: String
- **Default**: `info`
- **Options**: `debug`, `info`, `warn`, `error`
- **Description**: Logging level for SSO operations

### `SSO_AUDIT_LOG_RETENTION_DAYS`
- **Type**: Integer
- **Default**: `90`
- **Description**: Number of days to retain audit logs

### `SSO_ENABLE_PERFORMANCE_LOGGING`
- **Type**: Boolean
- **Default**: `false`
- **Description**: Enable performance logging for SSO operations

## Optional Rate Limiting Variables

### `SSO_RATE_LIMIT_AUTH`
- **Type**: Integer
- **Default**: `10`
- **Description**: Rate limit for authentication endpoints (requests per minute)

### `SSO_RATE_LIMIT_SESSION`
- **Type**: Integer
- **Default**: `60`
- **Description**: Rate limit for session management endpoints

### `SSO_RATE_LIMIT_ADMIN`
- **Type**: Integer
- **Default**: `100`
- **Description**: Rate limit for admin endpoints

## Environment-Specific Configurations

### Development Environment
```env
SSO_ENABLED=true
SSO_BASE_DOMAIN=localhost
SSO_COOKIE_DOMAIN=.localhost
SSO_SECURE_ONLY=false
SSO_REQUIRE_DEVICE_VERIFICATION=false
SSO_LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

### Production Environment
```env
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_SECURE_ONLY=true
SSO_REQUIRE_DEVICE_VERIFICATION=true
SSO_LOG_LEVEL=warn
CORS_ORIGIN=https://app.yourcompany.com,https://mail.yourcompany.com,https://core.yourcompany.com
```

## CORS Configuration Updates

Update your CORS configuration to include SSO domains:

```env
CORS_ORIGIN=http://localhost:3000,https://app.yourcompany.com,https://mail.yourcompany.com,https://core.yourcompany.com,https://api.yourcompany.com
CORS_CREDENTIALS=true
```

## Redis Configuration

Ensure Redis is properly configured for SSO session storage:

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
```

## Validation and Testing

After configuring the environment variables, test the SSO configuration:

### 1. Check SSO Configuration Endpoint
```bash
curl http://localhost:3000/auth/sso/config
```

Expected response:
```json
{
  "enabled": true,
  "baseDomain": "yourcompany.com",
  "allowedApplications": ["app.yourcompany.com", "mail.yourcompany.com"],
  "sessionTimeout": 480,
  "maxConcurrentSessions": 5
}
```

### 2. Verify Database Tables
Ensure SSO-related tables are created:
```bash
npm run migration:run
```

### 3. Test SSO Login Flow
Test the SSO login endpoint:
```bash
curl -X POST http://localhost:3000/auth/sso/login \
  -H "Content-Type: application/json" \
  -d '{"userId":"test-user-id","application":"app.yourcompany.com"}'
```

## Troubleshooting

### Common Issues

1. **SSO not enabled**: Check `SSO_ENABLED=true`
2. **Cross-domain cookies not working**: Verify `SSO_COOKIE_DOMAIN` starts with dot
3. **CORS errors**: Ensure all SSO domains are in `CORS_ORIGIN`
4. **Token verification fails**: Check `JWT_SECRET` consistency
5. **Session timeout issues**: Verify `SSO_SESSION_TIMEOUT` value

### Debug Mode
Enable debug logging for troubleshooting:
```env
SSO_LOG_LEVEL=debug
SSO_ENABLE_PERFORMANCE_LOGGING=true
```

## Security Best Practices

1. **Use strong JWT secrets** (64+ characters in production)
2. **Enable HTTPS-only cookies** in production (`SSO_SECURE_ONLY=true`)
3. **Set appropriate session timeouts** based on security requirements
4. **Enable audit logging** for compliance and security monitoring
5. **Use device verification** in production environments
6. **Regularly rotate JWT secrets** and update across all services

---

For more detailed information, refer to the [SSO Implementation Guide](../sso/SSO_IMPLEMENTATION_GUIDE.md).
