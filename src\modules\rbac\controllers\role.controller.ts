import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RoleService } from '../services/role.service';
import { RBACService } from '../services/rbac.service';
import {
  CreateRoleDto,
  UpdateRoleDto,
  AssignPermissionsDto,
  AssignRoleToUserDto,
  RevokeRoleFromUserDto,
  RoleResponseDto,
  RoleListResponseDto,
  RoleQueryDto,
} from '../dto/role.dto';
import { RoleManagement, RBAC } from '../decorators/rbac.decorators';

/**
 * Role Controller - Controller quản lý vai trò
 * Role Controller - Role management controller
 */
@ApiTags('Roles - Quản lý vai trò')
@Controller('roles')
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly rbacService: RBACService,
  ) {}

  /**
   * Tạo vai trò mới - Create new role
   */
  @Post()
  @RoleManagement.Create()
  @ApiOperation({
    summary: 'Tạo vai trò mới - Create new role',
    description: 'Tạo một vai trò mới trong hệ thống. Chỉ người có quyền ROLE_MANAGEMENT_CREATE mới có thể thực hiện.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Vai trò được tạo thành công - Role created successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Tên vai trò đã tồn tại - Role name already exists',
  })
  @HttpCode(HttpStatus.CREATED)
  async createRole(
    @Body() createRoleDto: CreateRoleDto,
    @Request() req: any,
  ): Promise<RoleResponseDto> {
    const role = await this.roleService.createRole(createRoleDto, req.user.id);
    return this.mapToResponseDto(role);
  }

  /**
   * Lấy danh sách vai trò - Get roles list
   */
  @Get()
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy danh sách vai trò - Get roles list',
    description: 'Lấy danh sách tất cả vai trò trong hệ thống với khả năng lọc và phân trang.',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo tên - Search by name' })
  @ApiQuery({ name: 'level', required: false, description: 'Lọc theo cấp độ - Filter by level' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Lọc theo trạng thái - Filter by active status' })
  @ApiQuery({ name: 'isSystemRole', required: false, description: 'Lọc theo vai trò hệ thống - Filter by system role' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang - Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang - Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách vai trò - Roles list',
    type: RoleListResponseDto,
  })
  async getRoles(@Query() query: RoleQueryDto): Promise<RoleListResponseDto> {
    const queryBuilder = this.buildRoleQuery(query);
    const roles = await this.roleService.getRoles(queryBuilder);
    
    return {
      roles: roles.map(role => this.mapToResponseDto(role)),
      total: roles.length,
      page: query.page,
      limit: query.limit,
    };
  }

  /**
   * Lấy vai trò theo ID - Get role by ID
   */
  @Get(':id')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy vai trò theo ID - Get role by ID',
    description: 'Lấy thông tin chi tiết của một vai trò cụ thể.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin vai trò - Role information',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy vai trò - Role not found',
  })
  async getRoleById(@Param('id', ParseUUIDPipe) id: string): Promise<RoleResponseDto> {
    const role = await this.roleService.getRoleById(id);
    return this.mapToResponseDto(role);
  }

  /**
   * Cập nhật vai trò - Update role
   */
  @Put(':id')
  @RoleManagement.Update()
  @ApiOperation({
    summary: 'Cập nhật vai trò - Update role',
    description: 'Cập nhật thông tin của một vai trò. Không thể cập nhật vai trò MASTER_ACCOUNT.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Vai trò được cập nhật thành công - Role updated successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy vai trò - Role not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền cập nhật vai trò này - No permission to update this role',
  })
  async updateRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Request() req: any,
  ): Promise<RoleResponseDto> {
    const role = await this.roleService.updateRole(id, updateRoleDto, req.user.id);
    return this.mapToResponseDto(role);
  }

  /**
   * Xóa vai trò - Delete role
   */
  @Delete(':id')
  @RoleManagement.Delete()
  @ApiOperation({
    summary: 'Xóa vai trò - Delete role',
    description: 'Xóa một vai trò khỏi hệ thống. Không thể xóa vai trò hệ thống hoặc vai trò đang được sử dụng.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Vai trò được xóa thành công - Role deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy vai trò - Role not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không thể xóa vai trò này - Cannot delete this role',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    await this.roleService.deleteRole(id, req.user.id);
  }

  /**
   * Gán quyền cho vai trò - Assign permissions to role
   */
  @Post(':id/permissions')
  @RoleManagement.Assign()
  @ApiOperation({
    summary: 'Gán quyền cho vai trò - Assign permissions to role',
    description: 'Gán một hoặc nhiều quyền cho vai trò cụ thể.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Quyền được gán thành công - Permissions assigned successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy vai trò hoặc quyền - Role or permissions not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền gán permission cho vai trò này - No permission to assign permissions to this role',
  })
  async assignPermissionsToRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignPermissionsDto: AssignPermissionsDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.roleService.assignPermissionsToRole(id, assignPermissionsDto, req.user.id);
    return { message: 'Permissions assigned successfully' };
  }

  /**
   * Thu hồi quyền từ vai trò - Revoke permissions from role
   */
  @Delete(':id/permissions')
  @RoleManagement.Assign()
  @ApiOperation({
    summary: 'Thu hồi quyền từ vai trò - Revoke permissions from role',
    description: 'Thu hồi một hoặc nhiều quyền từ vai trò cụ thể.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Quyền được thu hồi thành công - Permissions revoked successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy vai trò - Role not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không thể thu hồi quyền từ vai trò này - Cannot revoke permissions from this role',
  })
  async revokePermissionsFromRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { permissionIds: string[] },
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.roleService.revokePermissionsFromRole(id, body.permissionIds, req.user.id);
    return { message: 'Permissions revoked successfully' };
  }

  /**
   * Lấy quyền của vai trò - Get role permissions
   */
  @Get(':id/permissions')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy quyền của vai trò - Get role permissions',
    description: 'Lấy danh sách tất cả quyền được gán cho vai trò cụ thể.',
  })
  @ApiParam({ name: 'id', description: 'ID vai trò - Role ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách quyền của vai trò - Role permissions list',
  })
  async getRolePermissions(@Param('id', ParseUUIDPipe) id: string) {
    const permissions = await this.roleService.getRolePermissions(id);
    return { permissions };
  }

  /**
   * Gán vai trò cho người dùng - Assign role to user
   */
  @Post('assign')
  @RoleManagement.Assign()
  @ApiOperation({
    summary: 'Gán vai trò cho người dùng - Assign role to user',
    description: 'Gán một vai trò cho người dùng cụ thể.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Vai trò được gán thành công - Role assigned successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy người dùng hoặc vai trò - User or role not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền gán vai trò này - No permission to assign this role',
  })
  async assignRoleToUser(
    @Body() assignRoleDto: AssignRoleToUserDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    const expiresAt = assignRoleDto.expiresAt ? new Date(assignRoleDto.expiresAt) : undefined;
    
    await this.rbacService.assignRoleToUser(
      assignRoleDto.userId,
      assignRoleDto.roleId,
      req.user.id,
      expiresAt,
    );
    
    return { message: 'Role assigned successfully' };
  }

  /**
   * Thu hồi vai trò từ người dùng - Revoke role from user
   */
  @Post('revoke')
  @RoleManagement.Assign()
  @ApiOperation({
    summary: 'Thu hồi vai trò từ người dùng - Revoke role from user',
    description: 'Thu hồi một vai trò từ người dùng cụ thể.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Vai trò được thu hồi thành công - Role revoked successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy gán vai trò - Role assignment not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền thu hồi vai trò này - No permission to revoke this role',
  })
  async revokeRoleFromUser(
    @Body() revokeRoleDto: RevokeRoleFromUserDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    await this.rbacService.revokeRoleFromUser(
      revokeRoleDto.userId,
      revokeRoleDto.roleId,
      req.user.id,
    );
    
    return { message: 'Role revoked successfully' };
  }

  /**
   * Lấy phân cấp vai trò - Get role hierarchy
   */
  @Get('hierarchy/tree')
  @RoleManagement.Read()
  @ApiOperation({
    summary: 'Lấy phân cấp vai trò - Get role hierarchy',
    description: 'Lấy cây phân cấp của tất cả vai trò trong hệ thống.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cây phân cấp vai trò - Role hierarchy tree',
  })
  async getRoleHierarchy() {
    const hierarchy = await this.roleService.getRoleHierarchy();
    return { hierarchy };
  }

  /**
   * Chuyển đổi entity thành response DTO - Convert entity to response DTO
   */
  private mapToResponseDto(role: any): RoleResponseDto {
    return {
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description,
      level: role.level,
      parentRoleId: role.parentRoleId,
      isSystemRole: role.isSystemRole,
      isActive: role.isActive,
      createdBy: role.createdBy,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      parentRole: role.parentRole ? this.mapToResponseDto(role.parentRole) : undefined,
      childRoles: role.childRoles?.map((child: any) => this.mapToResponseDto(child)),
      permissionCount: role.rolePermissions?.length || 0,
      userCount: role.userRoles?.filter((ur: any) => ur.isActive)?.length || 0,
    };
  }

  /**
   * Xây dựng query cho vai trò - Build role query
   */
  private buildRoleQuery(query: RoleQueryDto): any {
    const where: any = {};
    
    if (query.search) {
      where.name = { $ilike: `%${query.search}%` };
    }
    
    if (query.level !== undefined) {
      where.level = query.level;
    }
    
    if (query.isActive !== undefined) {
      where.isActive = query.isActive;
    }
    
    if (query.isSystemRole !== undefined) {
      where.isSystemRole = query.isSystemRole;
    }

    const options: any = { where };
    
    if (query.page && query.limit) {
      options.skip = (query.page - 1) * query.limit;
      options.take = query.limit;
    }

    return options;
  }
}
