import { Injectable } from '@nestjs/common';
import { OpenAiService } from './openai.service';
import { LoggerService } from '../../../common/services/logger.service';
import { RecommendationAction } from '../entities/cv-analysis.entity';

@Injectable()
export class CvScoringService {
  constructor(
    private openAiService: OpenAiService,
    private logger: LoggerService,
  ) {}

  async analyzeCv(
    file: Express.Multer.File,
    jobDescription?: string,
    requirements?: string
  ): Promise<{
    overallScore: number;
    scores: {
      experience: number;
      skills: number;
      education: number;
      achievements: number;
      communication: number;
      cultural_fit: number;
    };
    extractedData: any;
    analysis: any;
    recommendation: RecommendationAction;
    recommendationReason: string;
    aiSummary: string;
    interviewQuestions: string;
  }> {
    try {
      // Extract text from CV file (simplified - in real implementation, use PDF parser)
      const cvText = await this.extractTextFromFile(file);
      
      // Analyze CV content
      const analysis = await this.performCvAnalysis(cvText, jobDescription, requirements);
      
      this.logger.logWithContext(`CV analysis completed for file: ${file.originalname}`, 'CvScoringService');
      
      return analysis;
    } catch (error) {
      this.logger.logError(error, 'CvScoringService - analyzeCv');
      throw error;
    }
  }

  private async extractTextFromFile(file: Express.Multer.File): Promise<string> {
    // Simplified text extraction - in real implementation, use libraries like pdf-parse, mammoth, etc.
    // For now, return placeholder text
    return `CV content extracted from ${file.originalname}`;
  }

  private async performCvAnalysis(
    cvText: string,
    jobDescription?: string,
    requirements?: string
  ): Promise<any> {
    const systemPrompt = `You are an expert HR AI assistant specializing in CV analysis and candidate evaluation. 

    Your task is to:
    1. Extract structured data from the CV
    2. Score different aspects (0-100 scale)
    3. Provide detailed analysis
    4. Make hiring recommendations
    5. Generate interview questions

    Consider:
    - Relevant experience and skills
    - Education background
    - Career progression
    - Achievements and accomplishments
    - Communication skills (based on CV quality)
    - Cultural fit indicators
    - Red flags or concerns

    Provide comprehensive analysis in JSON format with the specified structure.`;

    const prompt = `CV Content:
${cvText}

${jobDescription ? `Job Description:
${jobDescription}` : ''}

${requirements ? `Requirements:
${requirements}` : ''}

Please provide a comprehensive CV analysis with scores, recommendations, and interview questions.`;

    try {
      const response = await this.openAiService.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.3,
        maxTokens: 2000,
      });

      const result = JSON.parse(response);
      
      // Ensure all required fields are present with defaults
      return {
        overallScore: result.overallScore || 0,
        scores: {
          experience: result.scores?.experience || 0,
          skills: result.scores?.skills || 0,
          education: result.scores?.education || 0,
          achievements: result.scores?.achievements || 0,
          communication: result.scores?.communication || 0,
          cultural_fit: result.scores?.cultural_fit || 0,
        },
        extractedData: result.extractedData || {
          personalInfo: {},
          experience: [],
          education: [],
          skills: { technical: [], soft: [], languages: [], certifications: [] },
          achievements: [],
        },
        analysis: result.analysis || {
          strengths: [],
          weaknesses: [],
          recommendations: [],
          keywordMatch: { matched: [], missing: [], score: 0 },
          experienceAnalysis: {
            totalYears: 0,
            relevantYears: 0,
            careerProgression: '',
            industryFit: '',
          },
          redFlags: [],
          culturalFit: { score: 0, reasoning: '' },
        },
        recommendation: this.determineRecommendation(result.overallScore || 0),
        recommendationReason: result.recommendationReason || 'Automated analysis based on CV content',
        aiSummary: result.aiSummary || 'CV analysis completed',
        interviewQuestions: result.interviewQuestions || 'No specific questions generated',
      };
    } catch (error) {
      this.logger.logError(error, 'CvScoringService - performCvAnalysis');
      throw new Error('Failed to analyze CV');
    }
  }

  private determineRecommendation(overallScore: number): RecommendationAction {
    if (overallScore >= 80) {
      return RecommendationAction.ACCEPT;
    } else if (overallScore >= 60) {
      return RecommendationAction.INTERVIEW;
    } else if (overallScore >= 40) {
      return RecommendationAction.REVIEW;
    } else {
      return RecommendationAction.REJECT;
    }
  }

  async generateInterviewQuestions(
    cvAnalysis: any,
    jobDescription?: string,
    focusAreas?: string[]
  ): Promise<{
    technical: string[];
    behavioral: string[];
    situational: string[];
    cultural: string[];
    customized: string[];
  }> {
    const systemPrompt = `You are an expert interviewer. Generate relevant interview questions based on the candidate's CV analysis and job requirements.

    Create questions that:
    - Test technical competencies
    - Assess behavioral traits
    - Evaluate problem-solving skills
    - Determine cultural fit
    - Address specific CV highlights or concerns

    Provide categorized questions in JSON format.`;

    const prompt = `CV Analysis Summary:
${JSON.stringify(cvAnalysis, null, 2)}

${jobDescription ? `Job Description:
${jobDescription}` : ''}

${focusAreas ? `Focus Areas:
${focusAreas.join(', ')}` : ''}

Generate comprehensive interview questions tailored to this candidate.`;

    try {
      const response = await this.openAiService.generateCompletion(prompt, {
        systemPrompt,
        temperature: 0.7,
        maxTokens: 1000,
      });

      return JSON.parse(response);
    } catch (error) {
      this.logger.logError(error, 'CvScoringService - generateInterviewQuestions');
      return {
        technical: [],
        behavioral: [],
        situational: [],
        cultural: [],
        customized: [],
      };
    }
  }
}
