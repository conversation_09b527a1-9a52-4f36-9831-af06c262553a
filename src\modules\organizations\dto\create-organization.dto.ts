import { IsString, IsOptional, IsUrl, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, <PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateOrganizationDto {
  @ApiProperty({ example: 'Acme Corporation' })
  @IsString()
  @MinLength(2)
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({ example: 'Leading marketing agency specializing in digital transformation' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({ example: 'https://acme.com' })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  website?: string;

  @ApiPropertyOptional({ example: 'Marketing & Advertising' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  industry?: string;

  @ApiPropertyOptional({ 
    example: '11-50',
    description: 'Company size range',
    enum: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
  })
  @IsOptional()
  @IsString()
  @IsEnum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'])
  size?: string;

  @ApiPropertyOptional({
    example: {
      street: '123 Business St',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA'
    }
  })
  @IsOptional()
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };

  @ApiPropertyOptional({
    example: {
      email: '<EMAIL>',
      phone: '******-0123',
      supportEmail: '<EMAIL>'
    }
  })
  @IsOptional()
  contact?: {
    email?: string;
    phone?: string;
    supportEmail?: string;
  };
}
