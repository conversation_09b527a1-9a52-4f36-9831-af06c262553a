import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTag<PERSON>, Api<PERSON>earerAuth } from '@nestjs/swagger';
import { WorkflowsService } from './workflows.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Workflows')
@Controller('workflows')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WorkflowsController {
  constructor(private readonly workflowsService: WorkflowsService) {}

  @Get()
  async getWorkflows(@CurrentUser() user: User) {
    return this.workflowsService.getWorkflows(user.id);
  }

  @Post()
  async createWorkflow(@CurrentUser() user: User, @Body() data: any) {
    return this.workflowsService.createWorkflow(user.id, data);
  }

  @Get(':id')
  async getWorkflow(@CurrentUser() user: User, @Param('id') id: string) {
    return this.workflowsService.getWorkflow(user.id, id);
  }

  @Patch(':id')
  async updateWorkflow(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.workflowsService.updateWorkflow(user.id, id, data);
  }

  @Post(':id/execute')
  async executeWorkflow(@CurrentUser() user: User, @Param('id') id: string, @Body() data: any) {
    return this.workflowsService.executeWorkflow(user.id, id, data);
  }

  @Get(':id/executions')
  async getWorkflowExecutions(@CurrentUser() user: User, @Param('id') id: string) {
    return this.workflowsService.getWorkflowExecutions(user.id, id);
  }

  @Get('templates/available')
  async getAvailableTemplates(@CurrentUser() user: User) {
    return this.workflowsService.getAvailableTemplates();
  }
}
