import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { UtilsService } from './utils.service';

@Injectable()
export class FileUploadService {
  constructor(
    private configService: ConfigService,
    private utilsService: UtilsService,
  ) {}

  getMulterOptions(destination: string = 'uploads'): MulterOptions {
    return {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = join(process.cwd(), destination);
          if (!existsSync(uploadPath)) {
            mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = this.utilsService.generateRandomString(8);
          const ext = extname(file.originalname);
          const filename = `${Date.now()}-${uniqueSuffix}${ext}`;
          cb(null, filename);
        },
      }),
      limits: {
        fileSize: this.configService.get('MAX_FILE_SIZE', 10485760), // 10MB default
      },
      fileFilter: (req, file, cb) => {
        if (this.isValidFileType(file)) {
          cb(null, true);
        } else {
          cb(new BadRequestException('Invalid file type'), false);
        }
      },
    };
  }

  private isValidFileType(file: any): boolean {
    const allowedMimeTypes = [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // Text
      'text/plain',
      'text/csv',
      // Archives
      'application/zip',
      'application/x-rar-compressed',
    ];

    return allowedMimeTypes.includes(file.mimetype);
  }

  getImageMulterOptions(): MulterOptions {
    return {
      ...this.getMulterOptions('uploads/images'),
      fileFilter: (req, file, cb) => {
        const allowedImageTypes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
        ];

        if (allowedImageTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new BadRequestException('Only image files are allowed'), false);
        }
      },
    };
  }

  getDocumentMulterOptions(): MulterOptions {
    return {
      ...this.getMulterOptions('uploads/documents'),
      fileFilter: (req, file, cb) => {
        const allowedDocumentTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/plain',
          'text/csv',
        ];

        if (allowedDocumentTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new BadRequestException('Only document files are allowed'), false);
        }
      },
    };
  }

  formatFileSize(bytes: number): string {
    return this.utilsService.formatBytes(bytes);
  }

  getFileExtension(filename: string): string {
    return extname(filename).toLowerCase();
  }

  isImageFile(filename: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    return imageExtensions.includes(this.getFileExtension(filename));
  }

  isDocumentFile(filename: string): boolean {
    const documentExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv'];
    return documentExtensions.includes(this.getFileExtension(filename));
  }
}
