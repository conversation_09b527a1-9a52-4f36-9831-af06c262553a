import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Global prefix
  const apiPrefix = configService.get('API_PREFIX', 'api/v1');
  app.setGlobalPrefix(apiPrefix);

  // CORS configuration
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Swagger documentation - Enable in all environments for API testing
  const config = new DocumentBuilder()
    .setTitle('Delify Platform API')
    .setDescription('Comprehensive business platform for startups and small businesses')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('Authentication', 'User authentication and authorization')
    .addTag('Users', 'User management')
    .addTag('Organizations', 'Organization and team management')
    .addTag('Marketing', 'Social media automation')
    .addTag('AI', 'AI-powered features')
    .addTag('Email', 'Email management and marketing')
    .addTag('Documents', 'Document management and digital signatures')
    .addTag('Workflows', 'Drag-and-drop workflow builder')
    .addTag('Invoices', 'Electronic invoicing system')
    .addTag('Scheduling', 'Support scheduling system')
    .addTag('Test API', 'Test endpoints for API demonstration')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  });

  SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      filter: true,
      showRequestHeaders: true,
    },
    customSiteTitle: 'Delify API Documentation',
    customfavIcon: '/favicon.ico',
    customJs: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js',
    ],
    customCssUrl: [
      'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
    ],
  });

  const port = configService.get('PORT', 3000);
  const host = configService.get('NODE_ENV') === 'production' ? 'https://api.delify.vn' : `http://localhost:${port}`;
  logger.log(`Swagger documentation available at: ${host}/${apiPrefix}/docs`);

  await app.listen(port);

  logger.log(`Application is running on: ${host}/${apiPrefix}`);
}

bootstrap();
