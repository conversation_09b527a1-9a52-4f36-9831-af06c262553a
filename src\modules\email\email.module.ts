import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { EmailController } from './email.controller';
import { EmailService } from './email.service';
import { EmailTemplateService } from './services/email-template.service';
import { EmailCampaignService } from './services/email-campaign.service';
import { EmailAnalyticsService } from './services/email-analytics.service';
import { EmailTemplate } from './entities/email-template.entity';
import { EmailCampaign } from './entities/email-campaign.entity';
import { EmailLog } from './entities/email-log.entity';
import { EmailProcessor } from './processors/email.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EmailTemplate,
      EmailCampaign,
      EmailLog,
    ]),
    BullModule.registerQueue({
      name: 'email',
    }),
  ],
  controllers: [EmailController],
  providers: [
    EmailService,
    EmailTemplateService,
    EmailCampaignService,
    EmailAnalyticsService,
    EmailProcessor,
  ],
  exports: [
    EmailService,
    EmailTemplateService,
    EmailCampaignService,
    EmailAnalyticsService,
  ],
})
export class EmailModule {}
