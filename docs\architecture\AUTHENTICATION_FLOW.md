# 🔐 Authentication & Authorization Flow

## 📋 Tổng quan

Delify Platform sử dụng **JWT-based authentication** kết hợp với **Role-Based Access Control (RBAC)** và **Organization-level permissions** để đảm bảo security và flexibility.

## 🔑 Authentication Architecture

### Core Components
```
┌─────────────────────────────────────────────────────────────┐
│                Authentication System                       │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │     JWT     │ │   Session   │ │   Refresh   │          │
│  │   Tokens    │ │ Management  │ │   Tokens    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    RBAC     │ │ Organization│ │   Guards    │          │
│  │   System    │ │ Permissions │ │ & Filters   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Authentication Flow

### 1. User Registration
```typescript
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "accountType": "business", // or "personal"
  "firstName": "John",
  "lastName": "Doe"
}

// Response
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "accountType": "business",
    "isActive": true,
    "emailVerified": false
  },
  "tokens": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "expiresIn": 900 // 15 minutes
  }
}
```

### 2. User Login
```typescript
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********"
  }
}

// Response
{
  "user": { /* user object */ },
  "tokens": { /* JWT tokens */ },
  "session": {
    "id": "session-uuid",
    "deviceInfo": { /* device details */ },
    "lastActivity": "2024-01-01T00:00:00Z"
  }
}
```

### 3. Token Refresh
```typescript
POST /api/v1/auth/refresh
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}

// Response
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "expiresIn": 900
}
```

## 🛡️ Authorization System

### Role Hierarchy
```
Owner (Level 5)
  ├── Full organization control
  ├── Billing management
  ├── Delete organization
  └── Transfer ownership

Admin (Level 4)
  ├── User management
  ├── Team management
  ├── Settings configuration
  └── All Manager permissions

Manager (Level 3)
  ├── Project management
  ├── Team coordination
  ├── Resource allocation
  └── All Member permissions

Member (Level 2)
  ├── Access assigned projects
  ├── Collaborate with team
  ├── Use AI features
  └── All Viewer permissions

Viewer (Level 1)
  ├── Read-only access
  ├── View reports
  └── Basic navigation
```

### Permission Matrix
| Action | Owner | Admin | Manager | Member | Viewer |
|--------|-------|-------|---------|--------|--------|
| Delete Organization | ✅ | ❌ | ❌ | ❌ | ❌ |
| Manage Billing | ✅ | ❌ | ❌ | ❌ | ❌ |
| Invite Users | ✅ | ✅ | ✅ | ❌ | ❌ |
| Manage Teams | ✅ | ✅ | ✅ | ❌ | ❌ |
| Use AI Features | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Reports | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🔒 Implementation Details

### JWT Token Structure
```typescript
// Access Token Payload
{
  "sub": "user-uuid",           // User ID
  "email": "<EMAIL>",
  "accountType": "business",
  "organizations": [
    {
      "id": "org-uuid",
      "role": "admin",
      "permissions": ["read", "write", "manage_users"]
    }
  ],
  "iat": **********,           // Issued at
  "exp": **********,           // Expires at (15 minutes)
  "type": "access"
}

// Refresh Token Payload
{
  "sub": "user-uuid",
  "sessionId": "session-uuid",
  "iat": **********,
  "exp": **********,           // Expires at (24 hours)
  "type": "refresh"
}
```

### Guards Implementation

#### 1. JWT Auth Guard
```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // Skip authentication for public routes
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      IS_PUBLIC_KEY,
      [context.getHandler(), context.getClass()]
    );
    
    if (isPublic) {
      return true;
    }
    
    return super.canActivate(context);
  }
}
```

#### 2. Roles Guard
```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<MemberRole[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    const organizationId = this.getOrganizationId(context);
    
    return this.hasRequiredRole(user, organizationId, requiredRoles);
  }
}
```

#### 3. Organization Guard
```typescript
@Injectable()
export class OrganizationGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = request.params.organizationId;
    
    // Check if user is member of organization
    return user.organizations.some(org => org.id === organizationId);
  }
}
```

### Decorators Usage

#### 1. Public Routes
```typescript
@Controller('auth')
export class AuthController {
  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }
  
  @Public()
  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }
}
```

#### 2. Role-Based Access
```typescript
@Controller('organizations')
@UseGuards(JwtAuthGuard, OrganizationGuard, RolesGuard)
export class OrganizationsController {
  @Roles(MemberRole.ADMIN, MemberRole.OWNER)
  @Post(':organizationId/users/invite')
  async inviteUser(
    @Param('organizationId') organizationId: string,
    @Body() inviteDto: InviteUserDto
  ) {
    return this.organizationsService.inviteUser(organizationId, inviteDto);
  }
  
  @Roles(MemberRole.MEMBER, MemberRole.MANAGER, MemberRole.ADMIN, MemberRole.OWNER)
  @Get(':organizationId/teams')
  async getTeams(@Param('organizationId') organizationId: string) {
    return this.teamsService.findByOrganization(organizationId);
  }
}
```

#### 3. Current User Access
```typescript
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  @Get('profile')
  async getProfile(@CurrentUser() user: User) {
    return this.usersService.getProfile(user.id);
  }
  
  @Put('profile')
  async updateProfile(
    @CurrentUser() user: User,
    @Body() updateDto: UpdateProfileDto
  ) {
    return this.usersService.updateProfile(user.id, updateDto);
  }
}
```

## 🔄 Session Management

### Session Entity
```typescript
@Entity('sessions')
export class Session {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, user => user.sessions)
  user: User;

  @Column()
  refreshToken: string;

  @Column('json')
  deviceInfo: {
    userAgent: string;
    ipAddress: string;
    platform?: string;
    browser?: string;
  };

  @Column()
  lastActivity: Date;

  @Column()
  expiresAt: Date;

  @Column({ default: true })
  isActive: boolean;
}
```

### Session Service
```typescript
@Injectable()
export class SessionService {
  async createSession(user: User, deviceInfo: any): Promise<Session> {
    const session = this.sessionRepository.create({
      user,
      deviceInfo,
      lastActivity: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      isActive: true,
    });
    
    return this.sessionRepository.save(session);
  }
  
  async updateActivity(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastActivity: new Date(),
    });
  }
  
  async revokeSession(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      isActive: false,
    });
  }
  
  async getUserSessions(userId: string): Promise<Session[]> {
    return this.sessionRepository.find({
      where: { user: { id: userId }, isActive: true },
      order: { lastActivity: 'DESC' },
    });
  }
}
```

## 🚨 Security Features

### 1. Password Security
```typescript
// Password hashing
async hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Password validation
async validatePassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Password strength requirements
const passwordSchema = Joi.string()
  .min(8)
  .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
  .required()
  .messages({
    'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character',
  });
```

### 2. Rate Limiting
```typescript
// Global rate limiting
@UseGuards(ThrottlerGuard)
@Throttle(100, 60) // 100 requests per minute
@Controller()
export class AppController {}

// Specific endpoint rate limiting
@Throttle(5, 60) // 5 login attempts per minute
@Post('login')
async login(@Body() loginDto: LoginDto) {
  return this.authService.login(loginDto);
}
```

### 3. Input Validation
```typescript
// DTO validation
export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @MinLength(8)
  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsObject()
  deviceInfo?: {
    userAgent?: string;
    ipAddress?: string;
  };
}
```

## 🔧 Configuration

### Environment Variables
```bash
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=24h

# Session Configuration
SESSION_SECRET=your-session-secret
SESSION_MAX_AGE=86400000

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true
```

### Auth Module Configuration
```typescript
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
    PassportModule,
    TypeOrmModule.forFeature([User, Session, RefreshToken]),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtService,
    SessionService,
    JwtStrategy,
    LocalStrategy,
    JwtAuthGuard,
    RolesGuard,
    OrganizationGuard,
  ],
  exports: [AuthService, JwtAuthGuard, RolesGuard, OrganizationGuard],
})
export class AuthModule {}
```

## 🧪 Testing Authentication

### Unit Tests
```typescript
describe('AuthService', () => {
  it('should authenticate user with valid credentials', async () => {
    const loginDto = { email: '<EMAIL>', password: 'password123' };
    const result = await authService.login(loginDto);
    
    expect(result).toHaveProperty('user');
    expect(result).toHaveProperty('tokens');
    expect(result.tokens).toHaveProperty('accessToken');
    expect(result.tokens).toHaveProperty('refreshToken');
  });
  
  it('should throw error for invalid credentials', async () => {
    const loginDto = { email: '<EMAIL>', password: 'wrongpassword' };
    
    await expect(authService.login(loginDto)).rejects.toThrow('Invalid credentials');
  });
});
```

### Integration Tests
```typescript
describe('Auth Controller (e2e)', () => {
  it('/auth/login (POST)', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(201)
      .expect((res) => {
        expect(res.body).toHaveProperty('tokens');
        expect(res.body.tokens).toHaveProperty('accessToken');
      });
  });
});
```

## 🎯 Best Practices

### 1. Token Management
- **Short-lived access tokens** (15 minutes)
- **Longer refresh tokens** (24 hours)
- **Automatic token rotation**
- **Secure token storage**

### 2. Session Security
- **Device tracking**
- **Session timeout**
- **Concurrent session limits**
- **Session revocation**

### 3. Error Handling
- **Generic error messages** để prevent information leakage
- **Detailed logging** cho security monitoring
- **Rate limiting** cho brute force protection

**This authentication system provides enterprise-grade security với flexibility cho complex organizational structures.** 🔐✨
