# 🏗️ Delify Platform - Kiến Trúc Hệ Thống

## 📋 Tổng Quan

Delify Platform được thiết kế theo **kiến trúc monolithic modular** với khả năng mở rộng thành microservices. Hệ thống tập trung vào **tính năng cấp doanh nghiệp** với tích hợp AI và quản lý nhóm toàn diện.

## 🎯 Kiến Trúc Cấp Cao

```
┌─────────────────────────────────────────────────────────────┐
│                    Lớp Frontend                             │
│  React/Vue/Angular + TypeScript + State Management         │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/REST API
┌─────────────────────▼───────────────────────────────────────┐
│                  Lớp API Gateway                           │
│     NestJS Controllers + Guards + Interceptors             │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Lớp Logic Kinh Doanh                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Auth     │ │     AI      │ │    Org      │          │
│  │   Module    │ │   Module    │ │   Module    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Users     │ │   Common    │ │   Files     │          │
│  │   Module    │ │   Module    │ │   Module    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Lớp Truy Cập Dữ Liệu                     │
│              TypeORM + Repository Pattern                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Lớp Hạ Tầng                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ PostgreSQL  │ │    Redis    │ │   File      │          │
│  │  Database   │ │    Cache    │ │  Storage    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │    Grok     │ │   Gemini    │          │
│  │     API     │ │     API     │ │     API     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────┐                                          │
│  │   OLLAMA    │                                          │
│  │ Local AI    │                                          │
│  └─────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## 🏛️ Modules Cốt Lõi

### 1. Module Xác Thực (`src/modules/auth/`)
```typescript
auth/
├── controllers/
│   └── auth.controller.ts          # Endpoints đăng nhập, đăng ký, làm mới
├── services/
│   ├── auth.service.ts             # Logic xác thực
│   ├── jwt.service.ts              # Quản lý JWT token
│   └── session.service.ts          # Quản lý phiên
├── guards/
│   ├── jwt-auth.guard.ts           # Guard xác thực JWT
│   ├── roles.guard.ts              # Phân quyền dựa trên vai trò
│   └── organization.guard.ts       # Truy cập cấp tổ chức
├── strategies/
│   ├── jwt.strategy.ts             # Chiến lược JWT Passport
│   └── local.strategy.ts           # Xác thực cục bộ
├── decorators/
│   ├── current-user.decorator.ts   # Lấy user hiện tại
│   ├── roles.decorator.ts          # Yêu cầu vai trò
│   └── public.decorator.ts         # Endpoints công khai
└── entities/
    ├── session.entity.ts           # Phiên người dùng
    └── refresh-token.entity.ts     # Refresh tokens
```

**Tính Năng Chính:**
- Xác thực dựa trên JWT với refresh tokens
- Quản lý phiên với theo dõi thiết bị
- Kiểm soát truy cập dựa trên vai trò (RBAC)
- Quyền hạn cấp tổ chức
- Mã hóa mật khẩu với bcrypt
- Giới hạn tần suất cho bảo mật

### 2. Module Users (`src/modules/users/`)
```typescript
users/
├── controllers/
│   └── users.controller.ts         # Thao tác CRUD người dùng
├── services/
│   ├── users.service.ts            # Logic kinh doanh người dùng
│   └── profile.service.ts          # Quản lý hồ sơ
├── entities/
│   ├── user.entity.ts              # Entity người dùng
│   └── user-profile.entity.ts      # Thông tin hồ sơ mở rộng
├── dto/
│   ├── create-user.dto.ts          # Validation tạo người dùng
│   ├── update-user.dto.ts          # Validation cập nhật người dùng
│   └── change-password.dto.ts      # Validation đổi mật khẩu
└── enums/
    ├── account-type.enum.ts        # Tài khoản Doanh nghiệp/Cá nhân
    └── user-status.enum.ts         # Hoạt động/Không hoạt động/Tạm ngưng
```

**Tính Năng Chính:**
- Quản lý hồ sơ người dùng
- Loại tài khoản (Doanh nghiệp/Cá nhân)
- Xác minh email
- Quản lý mật khẩu
- Tùy chọn người dùng
- Theo dõi hoạt động

### 3. Module Organizations (`src/modules/organizations/`)
```typescript
organizations/
├── controllers/
│   ├── organizations.controller.ts  # CRUD tổ chức
│   ├── teams.controller.ts          # Quản lý nhóm
│   ├── invitations.controller.ts    # Hệ thống mời
│   └── roles.controller.ts          # Quản lý vai trò
├── services/
│   ├── organizations.service.ts     # Logic tổ chức
│   ├── teams.service.ts             # Thao tác nhóm
│   ├── invitations.service.ts       # Xử lý lời mời
│   ├── permissions.service.ts       # Quản lý quyền hạn
│   └── roles.service.ts             # Thao tác vai trò
├── entities/
│   ├── organization.entity.ts       # Entity tổ chức
│   ├── team.entity.ts               # Entity nhóm
│   ├── organization-member.entity.ts # Thành viên
│   ├── invitation.entity.ts         # Lời mời
│   ├── role.entity.ts               # Vai trò
│   └── permission.entity.ts         # Quyền hạn
└── enums/
    ├── organization-type.enum.ts    # Công ty/Đại lý/Freelancer
    ├── member-role.enum.ts          # Chủ sở hữu/Admin/Quản lý/Thành viên/Người xem
    └── invitation-status.enum.ts    # Đang chờ/Chấp nhận/Từ chối
```

**Tính Năng Chính:**
- Hỗ trợ tổ chức đa thuê bao
- Cộng tác dựa trên nhóm
- Hệ thống mời với thông báo email
- Hệ thống vai trò 5 cấp (Chủ sở hữu → Người xem)
- Quyền hạn tùy chỉnh
- Quản lý cài đặt tổ chức

### 4. Module AI (`src/modules/ai/`)
```typescript
ai/
├── controllers/
│   ├── ai.controller.ts             # Endpoints AI chính
│   └── ollama.controller.ts         # Endpoints đặc biệt cho OLLAMA
├── services/
│   ├── ai.service.ts                # Điều phối AI
│   ├── openai.service.ts            # Tích hợp OpenAI
│   ├── content-optimization.service.ts
│   ├── cv-scoring.service.ts
│   └── chatbot.service.ts
├── providers/
│   ├── base-ai.provider.ts          # Provider trừu tượng
│   ├── openai.provider.ts           # Triển khai OpenAI
│   ├── grok.provider.ts             # Triển khai Grok
│   ├── gemini.provider.ts           # Triển khai Gemini
│   ├── ollama.provider.ts           # Triển khai OLLAMA
│   └── ai-provider.factory.ts       # Factory provider
├── entities/
│   ├── ai-model.entity.ts           # Cấu hình mô hình AI
│   ├── chat-session.entity.ts       # Phiên chat
│   └── cv-analysis.entity.ts        # Kết quả phân tích CV
└── dto/
    ├── generate-text.dto.ts         # Yêu cầu tạo văn bản
    └── ollama.dto.ts                # DTOs đặc biệt cho OLLAMA
```

**Tính Năng Chính:**
- 4 nhà cung cấp AI (OpenAI, Grok, Gemini, OLLAMA)
- Lựa chọn provider thông minh
- AI cục bộ với OLLAMA
- Phản hồi streaming
- Quản lý mô hình
- Theo dõi sử dụng

### 5. Module Common (`src/common/`)
```typescript
common/
├── services/
│   ├── logger.service.ts            # Logging tập trung
│   ├── email.service.ts             # Thông báo email
│   └── file.service.ts              # Thao tác file
├── guards/
│   ├── throttler.guard.ts           # Giới hạn tần suất
│   └── validation.guard.ts          # Validation đầu vào
├── interceptors/
│   ├── logging.interceptor.ts       # Logging request/response
│   ├── transform.interceptor.ts     # Chuyển đổi response
│   └── timeout.interceptor.ts       # Timeout request
├── filters/
│   ├── http-exception.filter.ts     # Xử lý lỗi
│   └── validation.filter.ts         # Lỗi validation
├── decorators/
│   └── api-paginated-response.decorator.ts
└── utils/
    ├── pagination.util.ts           # Helpers phân trang
    ├── validation.util.ts           # Tiện ích validation
    └── crypto.util.ts               # Hàm mã hóa
```

## 🔄 Luồng Request

### 1. Luồng Xác Thực
```
Client Request
     ↓
API Gateway (NestJS)
     ↓
JWT Auth Guard
     ↓
Role Guard (nếu cần)
     ↓
Organization Guard (nếu cần)
     ↓
Controller Method
     ↓
Service Layer
     ↓
Repository/Database
     ↓
Response Transformation
     ↓
Client Response
```

### 2. Luồng Xử Lý AI
```
AI Request
     ↓
AI Controller
     ↓
AI Service (Lựa chọn Provider thông minh)
     ↓
Provider Factory
     ↓
Provider cụ thể (OpenAI/Grok/Gemini/OLLAMA)
     ↓
External API / Xử lý cục bộ
     ↓
Xử lý Response
     ↓
Theo dõi sử dụng
     ↓
Client Response
```

## 🗄️ Kiến Trúc Database

### Mối Quan Hệ Entity
```
User ──────────────── UserProfile
  │
  ├── OrganizationMember ──── Organization
  │                              │
  │                              ├── Team
  │                              └── Invitation
  │
  ├── Session
  ├── RefreshToken
  └── ChatSession ──── AIModel
```

### Mẫu Thiết Kế Chính
1. **Repository Pattern** - Trừu tượng hóa truy cập dữ liệu
2. **Factory Pattern** - Tạo AI provider
3. **Strategy Pattern** - Các AI provider khác nhau
4. **Decorator Pattern** - Xác thực và phân quyền
5. **Observer Pattern** - Thông báo theo sự kiện

## 🔒 Kiến Trúc Bảo Mật

### Lớp Xác Thực
1. **JWT Tokens** - Xác thực không trạng thái
2. **Refresh Tokens** - Gia hạn token an toàn
3. **Quản lý Phiên** - Theo dõi thiết bị
4. **Giới hạn Tần suất** - Bảo vệ DDoS
5. **Validation Đầu vào** - Làm sạch dữ liệu

### Cấp Độ Phân Quyền
1. **Endpoints Công khai** - Không cần xác thực
2. **Endpoints Đã xác thực** - Cần JWT hợp lệ
3. **Endpoints Dựa trên Vai trò** - Cần vai trò cụ thể
4. **Endpoints Tổ chức** - Cần thành viên tổ chức
5. **Endpoints Chỉ Chủ sở hữu** - Chỉ chủ sở hữu tổ chức

## 🚀 Cân Nhắc Hiệu Suất

### Chiến Lược Caching
```typescript
// Redis caching cho dữ liệu truy cập thường xuyên
@Cacheable('user-profile', 300) // 5 phút
async getUserProfile(userId: string) {
  return this.userRepository.findOne(userId);
}

// Caching phản hồi AI provider
@Cacheable('ai-response', 3600) // 1 giờ
async generateText(prompt: string, provider: string) {
  return this.aiService.generate(prompt, provider);
}
```

### Tối Ưu Database
- **Indexing** trên các cột truy vấn thường xuyên
- **Connection Pooling** cho kết nối database
- **Tối ưu Query** với TypeORM query builder
- **Phân trang** cho datasets lớn
- **Lazy Loading** cho relationships

### Hiệu Suất AI
- **Lựa chọn Provider** dựa trên loại tác vụ và hiệu suất
- **Xử lý Cục bộ** với OLLAMA cho quyền riêng tư
- **Caching Response** cho requests lặp lại
- **Streaming** cho phản hồi thời gian thực
- **Load Balancing** trên nhiều providers

## 🔧 Quản Lý Cấu Hình

### Cấu Hình Dựa trên Môi trường
```typescript
// config/configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
  },
  ai: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    },
    ollama: {
      baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    },
  },
});
```

## 📊 Giám Sát & Logging

### Chiến Lược Logging
```typescript
// Structured logging với context
this.logger.logWithContext(
  'User authenticated successfully',
  'AuthService',
  { userId, organizationId, timestamp }
);

// Error logging với stack traces
this.logger.logError(
  'AI provider request failed',
  error,
  { provider, model, requestId }
);
```

### Thu Thập Metrics
- **Thời gian Request/Response**
- **Tỷ lệ lỗi theo endpoint**
- **Hiệu suất AI provider**
- **Hiệu suất truy vấn Database**
- **Sử dụng Memory và CPU**

## 🔮 Cân Nhắc Khả Năng Mở Rộng

### Mở Rộng Theo Chiều Ngang
- **Thiết kế Stateless** cho việc mở rộng dễ dàng
- **Database connection pooling**
- **Redis session storage**
- **Load balancer ready**
- **Container orchestration** với Kubernetes

### Lộ Trình Migration Microservice
```
Monolith Hiện tại
     ↓
Tách AI Service
     ↓
Tách Auth Service
     ↓
Tách Organization Service
     ↓
Kiến trúc Microservices Đầy đủ
```

## 🎯 Bước Tiếp Theo

### Cải Tiến Ngay Lập Tức
1. **API Rate Limiting** theo user/organization
2. **Advanced Caching** với Redis
3. **Tính năng Real-time** với WebSockets
4. **Xử lý File Upload**
5. **Giám sát Nâng cao** với metrics

### Kiến Trúc Tương Lai
1. **Event-Driven Architecture** với message queues
2. **CQRS Pattern** cho truy vấn phức tạp
3. **Tách Microservices**
4. **GraphQL** API layer
5. **Tính năng AI Nâng cao** với RAG

**Kiến trúc này cung cấp nền tảng vững chắc cho ứng dụng cấp doanh nghiệp với không gian cho tăng trưởng và tối ưu hóa.** 🏗️✨
