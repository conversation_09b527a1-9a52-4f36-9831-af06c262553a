import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { SecurityMethod } from '../entities/user.entity';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiPropertyOptional({
    enum: SecurityMethod,
    example: SecurityMethod.EMAIL_VERIFICATION,
    description: 'Security verification method'
  })
  @IsOptional()
  @IsEnum(SecurityMethod)
  securityMethod?: SecurityMethod;

  @ApiPropertyOptional({
    example: 'JBSWY3DPEHPK3PXP',
    description: 'Two-factor authentication secret'
  })
  @IsOptional()
  @IsString()
  twoFactorSecret?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether two-factor authentication is enabled'
  })
  @IsOptional()
  @IsBoolean()
  twoFactorEnabled?: boolean;

  @ApiPropertyOptional({
    example: '123456',
    description: 'Verification code'
  })
  @IsOptional()
  @IsString()
  verificationCode?: string;

  @ApiPropertyOptional({
    description: 'Verification code expiration date'
  })
  @IsOptional()
  verificationCodeExpires?: Date;

  @ApiPropertyOptional({
    example: '123456',
    description: 'Fixed verification code (6-8 digits)'
  })
  @IsOptional()
  @IsString()
  fixedCode?: string;
}
