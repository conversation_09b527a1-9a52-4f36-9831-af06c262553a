# 🤖 AI Multi-Provider System Guide

## 📋 Tổng quan

Delify Platform tích hợp **4 AI providers** để cung cấp comprehensive AI capabilities với smart provider selection, cost optimization, và privacy-first options.

## 🎯 AI Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AI System Architecture                  │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Client    │ │     AI      │ │  Provider   │          │
│  │  Request    │ │ Controller  │ │  Factory    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Smart       │ │     AI      │ │  Provider   │          │
│  │ Selection   │ │  Service    │ │ Selection   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │    Grok     │ │   Gemini    │          │
│  │  Provider   │ │  Provider   │ │  Provider   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐                                          │
│  │   OLLAMA    │                                          │
│  │  Provider   │                                          │
│  │ (Local AI)  │                                          │
│  └─────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Supported AI Providers

### 1. OpenAI (Cloud)
```typescript
// Models supported
enum OpenAIModels {
  GPT_3_5_TURBO = 'gpt-3.5-turbo',      // Fast, cost-effective
  GPT_4 = 'gpt-4',                      // High quality reasoning
  GPT_4_TURBO = 'gpt-4-turbo',          // Latest with vision
  GPT_4O = 'gpt-4o',                    // Optimized version
}

// Capabilities
- ✅ Text generation
- ✅ Code generation  
- ✅ Image analysis (GPT-4 Turbo, GPT-4o)
- ✅ Function calling
- ✅ Streaming responses
- ✅ Content moderation
```

### 2. Grok (xAI - Cloud)
```typescript
// Models supported
enum GrokModels {
  GROK_1 = 'grok-1',                    // First generation
  GROK_1_5 = 'grok-1.5',               // Improved reasoning
  GROK_2 = 'grok-2',                   // Latest with vision
}

// Capabilities
- ✅ Text generation
- ✅ Code generation
- ✅ Real-time data access
- ✅ Streaming responses
- ⚠️ Image analysis (Grok-2 only)
- ❌ Embeddings (not yet available)
```

### 3. Gemini (Google - Cloud)
```typescript
// Models supported
enum GeminiModels {
  GEMINI_PRO = 'gemini-pro',                    // Text generation
  GEMINI_PRO_VISION = 'gemini-pro-vision',      // Text + Image
  GEMINI_ULTRA = 'gemini-ultra',                // Most capable
  GEMINI_1_5_PRO = 'gemini-1.5-pro',           // 1M token context
}

// Capabilities
- ✅ Text generation
- ✅ Code generation
- ✅ Image analysis (Pro Vision, Ultra, 1.5 Pro)
- ✅ Text embeddings
- ✅ Streaming responses
- ✅ Safety filtering
- ✅ Long context (1M tokens for 1.5 Pro)
```

### 4. OLLAMA (Local)
```typescript
// Models supported
enum OllamaModels {
  LLAMA_2_7B = 'llama2:7b',             // General purpose
  LLAMA_2_13B = 'llama2:13b',           // Better performance
  CODE_LLAMA_7B = 'codellama:7b',       // Code specialized
  CODE_LLAMA_13B = 'codellama:13b',     // Advanced coding
  MISTRAL_7B = 'mistral:7b',            // Fast and efficient
  PHI_3_MINI = 'phi3:mini',             // Compact model
  NEURAL_CHAT = 'neural-chat:7b',       // Conversational
  STARCODE = 'starcoder:7b',            // Multi-language code
}

// Capabilities
- ✅ Text generation
- ✅ Code generation
- ✅ Streaming responses
- ✅ Complete privacy (local processing)
- ✅ No API costs
- ✅ Offline capability
- ⚠️ Image analysis (with LLaVA models)
- ⚠️ Embeddings (with specific models)
```

## 🎯 Smart Provider Selection

### Selection Logic
```typescript
const providerPreferences = {
  text: ['grok', 'ollama', 'openai', 'gemini'],      // Grok for general text
  code: ['ollama', 'openai', 'grok', 'gemini'],      // Code Llama preferred
  image: ['openai', 'gemini', 'grok'],               // Vision capabilities
  creative: ['grok', 'ollama', 'openai', 'gemini'],  // Creative content
  embedding: ['openai', 'gemini', 'ollama'],         // Embedding support
  privacy: ['ollama'],                                // Local processing only
};
```

### Cost Optimization
```typescript
// Cost per 1K tokens (approximate)
const providerCosts = {
  openai: {
    'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    'gpt-4-turbo': { input: 0.01, output: 0.03 },
  },
  grok: {
    'grok-1.5': { input: 0.002, output: 0.004 },
  },
  gemini: {
    'gemini-pro': { input: 0.0005, output: 0.0015 },
  },
  ollama: {
    'all-models': { input: 0, output: 0 }, // Local processing
  },
};
```

## 🔧 Implementation Details

### Provider Factory Pattern
```typescript
@Injectable()
export class AIProviderFactory {
  createProvider(config: ProviderConfig): BaseAIProvider {
    switch (config.provider) {
      case AIProvider.OPENAI:
        return new OpenAIProvider(config);
      case AIProvider.GROK:
        return new GrokProvider(config);
      case AIProvider.GEMINI:
        return new GeminiProvider(config);
      case AIProvider.OLLAMA:
        return new OllamaProvider(config);
      default:
        throw new Error(`Unsupported provider: ${config.provider}`);
    }
  }
  
  createProviderFromEnv(provider: AIProvider, model?: AIModelName): BaseAIProvider {
    const config = this.getProviderConfigs()[provider];
    return this.createProvider({
      provider,
      model: model || this.getDefaultModel(provider),
      ...config,
    });
  }
}
```

### Base Provider Interface
```typescript
export abstract class BaseAIProvider {
  protected config: AIProviderConfig;
  
  constructor(config: AIProviderConfig) {
    this.config = config;
  }
  
  abstract generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown>;
  
  abstract analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse>;
  
  abstract validateConfig(): Promise<boolean>;
}
```

## 🚀 API Usage Examples

### 1. Smart Text Generation
```typescript
POST /api/v1/ai/smart/generate
{
  "messages": [
    {
      "role": "user",
      "content": "Explain quantum computing in simple terms"
    }
  ],
  "task": "text",
  "options": {
    "temperature": 0.7,
    "maxTokens": 500
  }
}

// Response
{
  "content": "Quantum computing is like...",
  "usage": {
    "promptTokens": 10,
    "completionTokens": 150,
    "totalTokens": 160
  },
  "model": "grok-1.5",
  "providerUsed": {
    "provider": "grok",
    "model": "grok-1.5",
    "reason": "Grok is optimized for text generation"
  }
}
```

### 2. Specific Provider Usage
```typescript
POST /api/v1/ai/generate/text
{
  "provider": "ollama",
  "model": "llama2:7b",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful coding assistant."
    },
    {
      "role": "user", 
      "content": "Write a Python function to calculate fibonacci"
    }
  ],
  "options": {
    "temperature": 0.2,
    "maxTokens": 300
  }
}
```

### 3. Provider Comparison
```typescript
POST /api/v1/ai/compare/providers
{
  "messages": [
    {
      "role": "user",
      "content": "Write a creative story about AI"
    }
  ],
  "providers": [
    { "provider": "openai", "model": "gpt-4-turbo" },
    { "provider": "grok", "model": "grok-1.5" },
    { "provider": "ollama", "model": "llama2:7b" }
  ],
  "options": {
    "temperature": 0.8,
    "maxTokens": 500
  }
}

// Response
{
  "results": [
    {
      "provider": "openai",
      "model": "gpt-4-turbo",
      "response": { /* AI response */ },
      "responseTime": 1200,
      "success": true
    },
    {
      "provider": "grok", 
      "model": "grok-1.5",
      "response": { /* AI response */ },
      "responseTime": 800,
      "success": true
    },
    {
      "provider": "ollama",
      "model": "llama2:7b", 
      "response": { /* AI response */ },
      "responseTime": 2000,
      "success": true
    }
  ],
  "comparison": {
    "fastest": { "provider": "grok", "responseTime": 800 },
    "mostTokens": { "provider": "openai", "totalTokens": 520 }
  }
}
```

### 4. OLLAMA Model Management
```typescript
// List available models
GET /api/v1/ai/ollama/models

// Pull new model
POST /api/v1/ai/ollama/models/pull
{
  "modelName": "mistral:7b"
}

// Delete model
DELETE /api/v1/ai/ollama/models/llama2:7b

// Get model recommendations
GET /api/v1/ai/ollama/models/recommended?task=code

// Generate code with OLLAMA
POST /api/v1/ai/ollama/code/generate
{
  "model": "codellama:7b",
  "prompt": "Create a REST API endpoint for user authentication",
  "language": "typescript"
}
```

## 🔄 Provider Selection Strategies

### 1. Task-Based Selection
```typescript
async getBestProviderForTask(task: string): Promise<ProviderRecommendation> {
  switch (task) {
    case 'text':
      return this.selectProvider(['grok', 'ollama', 'openai', 'gemini']);
    case 'code':
      return this.selectProvider(['ollama', 'openai', 'grok', 'gemini']);
    case 'image':
      return this.selectProvider(['openai', 'gemini', 'grok']);
    case 'privacy':
      return this.selectProvider(['ollama']);
    default:
      return this.selectProvider(['grok', 'openai', 'gemini', 'ollama']);
  }
}
```

### 2. Cost-Based Selection
```typescript
async getCheapestProvider(task: string): Promise<ProviderRecommendation> {
  const providers = await this.getAvailableProviders();
  const costs = providers.map(p => ({
    provider: p,
    cost: this.calculateCost(p, estimatedTokens),
  }));
  
  return costs.sort((a, b) => a.cost - b.cost)[0];
}
```

### 3. Performance-Based Selection
```typescript
async getFastestProvider(task: string): Promise<ProviderRecommendation> {
  const providers = await this.getAvailableProviders();
  const performance = await this.getProviderPerformanceMetrics();
  
  return providers.sort((a, b) => 
    performance[a.provider].avgResponseTime - performance[b.provider].avgResponseTime
  )[0];
}
```

## 📊 Usage Tracking & Analytics

### Request Logging
```typescript
@Injectable()
export class AIUsageService {
  async logRequest(request: AIRequest): Promise<void> {
    await this.usageRepository.save({
      userId: request.userId,
      organizationId: request.organizationId,
      provider: request.provider,
      model: request.model,
      promptTokens: request.usage.promptTokens,
      completionTokens: request.usage.completionTokens,
      totalTokens: request.usage.totalTokens,
      responseTime: request.responseTime,
      cost: this.calculateCost(request),
      timestamp: new Date(),
    });
  }
  
  async getUsageStats(organizationId: string, period: string): Promise<UsageStats> {
    return this.usageRepository
      .createQueryBuilder('usage')
      .select([
        'provider',
        'COUNT(*) as requests',
        'SUM(totalTokens) as totalTokens',
        'SUM(cost) as totalCost',
        'AVG(responseTime) as avgResponseTime',
      ])
      .where('organizationId = :organizationId', { organizationId })
      .andWhere('timestamp >= :startDate', { startDate: this.getStartDate(period) })
      .groupBy('provider')
      .getRawMany();
  }
}
```

### Cost Calculation
```typescript
calculateCost(request: AIRequest): number {
  if (request.provider === AIProvider.OLLAMA) {
    return 0; // Local processing
  }
  
  const pricing = this.getPricing(request.provider, request.model);
  const inputCost = (request.usage.promptTokens / 1000) * pricing.input;
  const outputCost = (request.usage.completionTokens / 1000) * pricing.output;
  
  return inputCost + outputCost;
}
```

## 🔒 Security & Privacy

### Data Privacy Levels
```typescript
enum PrivacyLevel {
  PUBLIC = 'public',           // Can use any provider
  INTERNAL = 'internal',       // Prefer local or trusted providers
  CONFIDENTIAL = 'confidential', // Local processing only
  RESTRICTED = 'restricted',   // No AI processing allowed
}

async selectProviderByPrivacy(
  privacyLevel: PrivacyLevel,
  task: string
): Promise<ProviderRecommendation> {
  switch (privacyLevel) {
    case PrivacyLevel.CONFIDENTIAL:
    case PrivacyLevel.RESTRICTED:
      return this.selectProvider(['ollama']);
    case PrivacyLevel.INTERNAL:
      return this.selectProvider(['ollama', 'openai']);
    default:
      return this.getBestProviderForTask(task);
  }
}
```

### API Key Management
```typescript
// Environment-based configuration
const providerConfigs = {
  [AIProvider.OPENAI]: {
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
  },
  [AIProvider.GROK]: {
    apiKey: process.env.GROK_API_KEY,
    baseURL: process.env.GROK_BASE_URL,
  },
  [AIProvider.GEMINI]: {
    apiKey: process.env.GEMINI_API_KEY,
    baseURL: process.env.GEMINI_BASE_URL,
  },
  [AIProvider.OLLAMA]: {
    baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    // No API key required for OLLAMA
  },
};
```

## 🧪 Testing AI Features

### Unit Tests
```typescript
describe('AIProviderFactory', () => {
  it('should create correct provider instance', () => {
    const provider = factory.createProvider({
      provider: AIProvider.OLLAMA,
      model: AIModelName.LLAMA_2_7B,
    });
    
    expect(provider).toBeInstanceOf(OllamaProvider);
  });
});
```

### Integration Tests
```typescript
describe('AI Controller (Integration)', () => {
  it('should generate text using smart selection', async () => {
    const response = await request(app)
      .post('/ai/smart/generate')
      .send({
        messages: [{ role: 'user', content: 'Hello' }],
        task: 'text',
      })
      .expect(201);
      
    expect(response.body).toHaveProperty('content');
    expect(response.body).toHaveProperty('providerUsed');
  });
});
```

## 🎯 Best Practices

### 1. Provider Selection
- **Use smart selection** cho optimal performance
- **Consider privacy requirements** khi chọn provider
- **Monitor costs** và optimize usage
- **Implement fallbacks** cho provider failures

### 2. Error Handling
- **Graceful degradation** khi provider unavailable
- **Retry logic** với exponential backoff
- **Clear error messages** cho users
- **Comprehensive logging** cho debugging

### 3. Performance Optimization
- **Cache responses** cho repeated requests
- **Use streaming** cho real-time applications
- **Monitor response times** và optimize
- **Load balance** across providers

**This AI system provides enterprise-grade AI capabilities với flexibility, cost optimization, và privacy protection.** 🤖✨
