# Enhanced Authentication System

This document describes the enhanced authentication system with configurable security verification methods implemented in the Delify platform.

## Overview

The enhanced authentication system provides users with configurable security verification methods to add an extra layer of security to their accounts. Users can choose from four security methods:

1. **DISABLED** - No additional verification required (default)
2. **EMAIL_VERIFICATION** - Send verification code to user's email
3. **TWO_FACTOR_AUTH** - Use TOTP/authenticator app-based 2FA
4. **FIXED_CODE** - User-generated fixed verification code that can be reused

## Features

### 1. User Security Method Configuration

- Users can view their current security method configuration
- Users can update their security method preference
- Support for email verification, TOTP-based 2FA, and fixed codes
- QR code generation for easy 2FA setup
- Fixed code validation with security requirements (6-8 digits, no weak patterns)

### 2. Enhanced Login Flow
- Password verification followed by security method check
- Partial login response for additional verification
- Verification code submission endpoints
- Session-based verification tracking

### 3. Enhanced Password Change Flow
- Verification code requirement for users with security methods enabled
- Double entry validation (confirm new password)
- Email notifications for password changes

## API Endpoints

### Authentication Endpoints

#### Login
```http
POST /auth/login
```
Returns either complete login response or partial login requiring verification.

#### Verify Login
```http
POST /auth/verify-login
```
Complete login after verification code submission.

#### Resend Verification Code
```http
POST /auth/resend-verification-code
```
Resend verification code for login.

### Security Method Management

#### Get Security Method
```http
GET /auth/security-method
Authorization: Bearer <token>
```

#### Update Security Method
```http
POST /auth/security-method
Authorization: Bearer <token>
```

### Password Management

#### Send Password Change Verification Code
```http
POST /auth/send-password-change-code
Authorization: Bearer <token>
```

#### Change Password (Enhanced)
```http
POST /auth/change-password
Authorization: Bearer <token>
```

#### Change Fixed Code
```http
POST /auth/change-fixed-code
Authorization: Bearer <token>
```

### Token Management

#### Refresh Token
```http
POST /auth/refresh
```

#### Revoke Token (Logout)
```http
POST /auth/revoke
```

#### Revoke All Tokens
```http
POST /auth/revoke-all
Authorization: Bearer <token>
```

## Usage Examples

### 1. Enable Email Verification

```javascript
// Step 1: Update security method
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'email_verification'
  })
});
```

### 2. Enable Two-Factor Authentication

```javascript
// Step 1: Initiate 2FA setup
const setupResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth'
  })
});

const { qrCodeUrl, manualEntryKey } = await setupResponse.json();

// Step 2: User scans QR code or enters manual key in authenticator app

// Step 3: Verify and enable 2FA
const verifyResponse = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'two_factor_auth',
    verificationCode: '123456' // Code from authenticator app
  })
});
```

### 3. Login with Security Verification

```javascript
// Step 1: Initial login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.sessionId) {
  // Additional verification required
  const verificationCode = prompt('Enter verification code:');

  // Step 2: Submit verification code
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: verificationCode
    })
  });

  const { user, accessToken } = await verifyResponse.json();
  // Login complete
} else {
  // Direct login (no security method enabled)
  const { user, accessToken } = loginData;
}
```

### 4. Enable Fixed Code Verification

```javascript
// Enable fixed code verification
const response = await fetch('/auth/security-method', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    securityMethod: 'fixed_code',
    fixedCode: '789123' // User's chosen 6-8 digit code
  })
});

const result = await response.json();
// { "securityMethod": "fixed_code", "twoFactorEnabled": false, "hasFixedCode": true }
```

### 5. Change Fixed Code

```javascript
// Change existing fixed code
const response = await fetch('/auth/change-fixed-code', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    currentCode: '789123', // Current fixed code
    newCode: '456789'      // New fixed code
  })
});

const result = await response.json();
// { "message": "Fixed code changed successfully" }
```

### 6. Login with Fixed Code

```javascript
// Step 1: Initial login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();

if (loginData.sessionId && loginData.requiredVerification === 'fixed_code') {
  // Fixed code verification required
  const fixedCode = prompt('Enter your fixed verification code:');

  // Step 2: Submit fixed code
  const verifyResponse = await fetch('/auth/verify-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      sessionId: loginData.sessionId,
      verificationCode: fixedCode
    })
  });

  const { user, accessToken } = await verifyResponse.json();
  // Login complete
}
```

### 7. Refresh Token Usage

```javascript
// Store tokens after login
const loginResponse = await fetch('/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

const { accessToken, refreshToken, expiresIn } = await loginResponse.json();

// Store tokens securely
localStorage.setItem('accessToken', accessToken);
localStorage.setItem('refreshToken', refreshToken);

// Refresh access token when it expires
const refreshResponse = await fetch('/auth/refresh', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    refreshToken: localStorage.getItem('refreshToken')
  })
});

const newTokens = await refreshResponse.json();
localStorage.setItem('accessToken', newTokens.accessToken);
localStorage.setItem('refreshToken', newTokens.refreshToken);
```

### 8. Logout (Revoke Tokens)

```javascript
// Logout - revoke refresh token
const logoutResponse = await fetch('/auth/revoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    refreshToken: localStorage.getItem('refreshToken')
  })
});

// Clear stored tokens
localStorage.removeItem('accessToken');
localStorage.removeItem('refreshToken');

// Revoke all tokens (logout from all devices)
const revokeAllResponse = await fetch('/auth/revoke-all', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + accessToken }
});
```

### 9. Automatic Token Refresh

```javascript
// Axios interceptor for automatic token refresh
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await fetch('/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken })
        });

        const { accessToken, refreshToken: newRefreshToken } = await response.json();

        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', newRefreshToken);

        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        return axios(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);
```

### 10. Change Password with Verification

```javascript
// Step 1: Request verification code (if security method enabled)
await fetch('/auth/send-password-change-code', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + token }
});

// Step 2: Change password with verification
const response = await fetch('/auth/change-password', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    currentPassword: 'oldPassword123',
    newPassword: 'newPassword123',
    confirmNewPassword: 'newPassword123',
    verificationCode: '123456' // Required if security method enabled
  })
});
```

## Database Schema Changes

The following fields have been added to the `users` table:

- `securityMethod` (enum): The selected security verification method
- `twoFactorSecret` (varchar): TOTP secret for 2FA (encrypted)
- `twoFactorEnabled` (boolean): Whether 2FA is enabled
- `verificationCode` (varchar): Temporary verification code
- `verificationCodeExpires` (timestamp): Verification code expiration
- `fixedCode` (varchar): Hashed fixed verification code
- `refreshToken` (text): JWT refresh token for token renewal
- `refreshTokenExpires` (timestamp): Refresh token expiration

## Security Considerations

1. **Access tokens have short lifespan (15 minutes)**
2. **Refresh tokens have longer lifespan (7 days)**
3. **Verification codes expire after 5 minutes**
4. **2FA secrets are excluded from API responses**
5. **Verification codes are excluded from API responses**
6. **Fixed codes are hashed and excluded from API responses**
7. **Refresh tokens are stored securely in database**
8. **Fixed code validation prevents weak patterns (sequential, repeated digits)**
9. **Email notifications for security changes**
10. **Session-based verification tracking**
11. **Token rotation on refresh (new refresh token issued)**
12. **Backward compatibility maintained**

## Dependencies

The following packages were added:
- `speakeasy`: For TOTP generation and verification
- `qrcode`: For QR code generation
- `@types/speakeasy`: TypeScript definitions
- `@types/qrcode`: TypeScript definitions

## Migration

Run the database migrations to add the new security fields:

```bash
npm run migration:run
```

The migration files will add the necessary columns and indexes to the users table:
- `003-add-security-fields-to-users.ts` - Initial security fields
- `004-add-fixed-code-to-users.ts` - Fixed code support
- `005-add-refresh-token-to-users.ts` - Refresh token support

## Troubleshooting

### Common Issues and Solutions

#### 1. Email Verification Issues

**Problem**: Verification code not received
- **Solution**: Check email spam folder, verify email service configuration
- **Debug**: Check server logs for email sending errors

**Problem**: "Invalid verification code" error
- **Solution**: Ensure code is entered within 5 minutes of generation
- **Debug**: Check if verification code has expired

#### 2. Two-Factor Authentication Issues

**Problem**: QR code not displaying
- **Solution**: Ensure `qrcode` package is installed and working
- **Debug**: Check browser console for JavaScript errors

**Problem**: "Invalid verification code" for 2FA
- **Solution**: Ensure device time is synchronized (TOTP is time-based)
- **Debug**: Check if 2FA secret is properly stored in database

**Problem**: Cannot scan QR code
- **Solution**: Use manual entry key provided in the response
- **Alternative**: Generate new QR code by re-initiating 2FA setup

#### 3. Fixed Code Issues

**Problem**: "Fixed code is too weak" error
- **Solution**: Use a code that doesn't contain:
  - Sequential numbers (123456, 654321)
  - Repeated digits (111111, 222222)
  - Common patterns (000000, 123123)
- **Recommendation**: Use a random 6-8 digit combination

**Problem**: "Current fixed code is incorrect"
- **Solution**: Verify you're entering the correct current code
- **Debug**: If forgotten, disable and re-enable fixed code method

**Problem**: Cannot change fixed code
- **Solution**: Ensure fixed code method is currently enabled
- **Debug**: Check user's security method in database

#### 4. Login Flow Issues

**Problem**: Stuck in partial login state
- **Solution**: Complete verification within session timeout (5 minutes)
- **Alternative**: Start login process again

**Problem**: Session expired during verification
- **Solution**: Restart login process from the beginning
- **Prevention**: Complete verification promptly

#### 5. Password Change Issues

**Problem**: Verification required but no code sent
- **Solution**: For fixed code users, no email is sent - use your fixed code
- **For 2FA users**: Use authenticator app code
- **For email users**: Check spam folder or request new code

#### 6. Database Migration Issues

**Problem**: Migration fails with enum error
- **Solution**: Ensure PostgreSQL version supports adding enum values
- **Alternative**: Manually add enum value: `ALTER TYPE security_method_enum ADD VALUE 'fixed_code'`

**Problem**: Column already exists error
- **Solution**: Check if migration was already run
- **Debug**: Query database to see existing columns

### API Error Codes

| Error Code | Description | Solution |
|------------|-------------|----------|
| 400 | Invalid verification code | Check code format and expiration |
| 400 | Fixed code is too weak | Use stronger code pattern |
| 400 | Security method not enabled | Enable security method first |
| 401 | Invalid or expired session | Restart login process |
| 401 | Current fixed code incorrect | Verify current code |

### Testing Commands

```bash
# Test build
npm run build

# Run tests
npm test -- auth.service.spec.ts

# Check database connection
npm run migration:show

# Verify dependencies
npm list speakeasy qrcode
```
