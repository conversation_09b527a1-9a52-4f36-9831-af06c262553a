# Hướng Dẫn Cấu Hình SSO

## Tổng Quan

Hướng dẫn này cung cấp hướng dẫn cấu hình toàn diện cho hệ thống Single Sign-On (SSO), bao gồm biến môi trường, thi<PERSON><PERSON> lập database, cài đặt bả<PERSON> mậ<PERSON>, và cấu hình triển khai.

## Cấu Hình Môi Trường

### Biến Môi Trường Bắt Buộc

```env
# Cấu Hình SSO Cốt Lõi
SSO_ENABLED=true
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ISSUER=auth.yourcompany.com

# Cấu Hình Ứng Dụng
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com

# Cấu Hình Session
SSO_SESSION_TIMEOUT=480
SSO_MAX_CONCURRENT_SESSIONS=5
SSO_REQUIRE_DEVICE_VERIFICATION=false

# Cấu Hình Bảo Mật
SSO_ENABLE_AUDIT_LOGGING=true
SSO_TOKEN_REVOCATION_ENABLED=true
SSO_CROSS_DOMAIN_COOKIES=true
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax

# Cấu Hình JWT
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=15m

# Cấu Hình Database
DATABASE_URL=postgresql://username:password@localhost:5432/your_database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_db_user
DATABASE_PASSWORD=your_db_password
DATABASE_NAME=your_database

# Cấu Hình Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
```

### Biến Môi Trường Tùy Chọn

```env
# Bảo Mật Nâng Cao
SSO_DEVICE_FINGERPRINT_ENABLED=true
SSO_IP_VALIDATION_ENABLED=false
SSO_GEOLOCATION_TRACKING=true
SSO_SUSPICIOUS_ACTIVITY_THRESHOLD=3

# Điều Chỉnh Hiệu Suất
SSO_CACHE_TTL=900
SSO_SESSION_CLEANUP_INTERVAL=3600
SSO_TOKEN_CLEANUP_INTERVAL=1800

# Cấu Hình Logging
SSO_LOG_LEVEL=info
SSO_AUDIT_LOG_RETENTION_DAYS=90
SSO_ENABLE_PERFORMANCE_LOGGING=false

# Giới Hạn Tần Suất
SSO_RATE_LIMIT_AUTH=10
SSO_RATE_LIMIT_SESSION=60
SSO_RATE_LIMIT_ADMIN=100

# Cài Đặt Development
NODE_ENV=production
NODE_TLS_REJECT_UNAUTHORIZED=1
```

## Cấu Hình Domain

### Thiết Lập Domain Đơn

Cho triển khai domain đơn:

```env
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,admin.yourcompany.com
```

### Thiết Lập Nhiều Domain

Cho triển khai nhiều domain:

```env
SSO_BASE_DOMAIN=yourcompany.com
SSO_COOKIE_DOMAIN=.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com,api.yourcompany.com,analytics.yourcompany.com
```

### Cấu Hình Domain Tùy Chỉnh

Cho tên domain tùy chỉnh:

```env
SSO_BASE_DOMAIN=mycompany.io
SSO_COOKIE_DOMAIN=.mycompany.io
SSO_ISSUER=auth.mycompany.io
SSO_ALLOWED_APPLICATIONS=dashboard.mycompany.io,mail.mycompany.io,api.mycompany.io
```

## Cấu Hình Database

### Thiết Lập PostgreSQL

1. **Tạo Database:**
```sql
CREATE DATABASE sso_production;
CREATE USER sso_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE sso_production TO sso_user;
```

2. **Cấu Hình Kết Nối:**
```env
DATABASE_URL=postgresql://sso_user:secure_password@localhost:5432/sso_production
DATABASE_SSL=true
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=60000
```

3. **Cấu Hình SSL:**
```env
DATABASE_SSL_CA=/path/to/ca-certificate.crt
DATABASE_SSL_CERT=/path/to/client-certificate.crt
DATABASE_SSL_KEY=/path/to/client-key.key
DATABASE_SSL_REJECT_UNAUTHORIZED=true
```

### Migration và Seeding

```bash
# Chạy migrations
npm run migration:run

# Seed dữ liệu ban đầu
npm run seed:sso

# Xác minh thiết lập
npm run migration:show
```

## Cấu Hình Redis

### Thiết Lập Redis Cơ Bản

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
```

### Cấu Hình Redis Cluster

```env
REDIS_CLUSTER_ENABLED=true
REDIS_CLUSTER_NODES=redis1.yourcompany.com:6379,redis2.yourcompany.com:6379,redis3.yourcompany.com:6379
REDIS_CLUSTER_PASSWORD=cluster_password
```

### Cấu Hình Redis Sentinel

```env
REDIS_SENTINEL_ENABLED=true
REDIS_SENTINEL_MASTER_NAME=mymaster
REDIS_SENTINEL_NODES=sentinel1:26379,sentinel2:26379,sentinel3:26379
REDIS_SENTINEL_PASSWORD=sentinel_password
```

## Cấu Hình Bảo Mật

### Cài Đặt Bảo Mật Cookie

```env
# Cài Đặt Production
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax
SSO_CROSS_DOMAIN_COOKIES=true

# Cài Đặt Development
SSO_SECURE_ONLY=false
SSO_SAME_SITE=none
```

### Cấu Hình Bảo Mật JWT

```env
# JWT Secret mạnh (tối thiểu 32 ký tự)
JWT_SECRET=your_super_secure_jwt_secret_key_with_at_least_32_characters

# Hết Hạn Token
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Cấu Hình Algorithm
JWT_ALGORITHM=HS256
JWT_ISSUER=auth.yourcompany.com
JWT_AUDIENCE=yourcompany.com
```

### Cài Đặt Bảo Mật Thiết Bị

```env
# Device Fingerprinting
SSO_DEVICE_FINGERPRINT_ENABLED=true
SSO_REQUIRE_DEVICE_VERIFICATION=false
SSO_DEVICE_TRUST_THRESHOLD=80

# Phát Hiện Hoạt Động Đáng Ngờ
SSO_SUSPICIOUS_ACTIVITY_THRESHOLD=3
SSO_IP_CHANGE_DETECTION=true
SSO_USER_AGENT_VALIDATION=true
```

## Đăng Ký Ứng Dụng

### Đăng Ký Tự Động

Ứng dụng có thể được đăng ký tự động qua biến môi trường:

```env
SSO_AUTO_REGISTER_APPS=true
SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com
```

### Đăng Ký Thủ Công

Đăng ký ứng dụng qua API hoặc database:

```sql
INSERT INTO sso_applications (name, subdomain, display_name, base_url, allowed_origins, is_active)
VALUES 
  ('MAIN_APP', 'app', 'Ứng Dụng Chính', 'https://app.yourcompany.com', ARRAY['https://app.yourcompany.com'], true),
  ('MAIL_APP', 'mail', 'Dịch Vụ Email', 'https://mail.yourcompany.com', ARRAY['https://mail.yourcompany.com'], true);
```

### Cấu Hình Ứng Dụng Cụ Thể

```typescript
// Object cấu hình ứng dụng
const applicationConfig = {
  name: 'ANALYTICS_APP',
  subdomain: 'analytics',
  displayName: 'Bảng Điều Khiển Phân Tích',
  description: 'Nền tảng business intelligence',
  baseUrl: 'https://analytics.yourcompany.com',
  allowedOrigins: [
    'https://analytics.yourcompany.com',
    'https://app.yourcompany.com'
  ],
  corsSettings: {
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    maxAge: 86400
  }
};
```

## Cấu Hình CORS

### Thiết Lập CORS Cơ Bản

```typescript
// Cấu hình CORS
const corsConfig = {
  origin: [
    'https://app.yourcompany.com',
    'https://mail.yourcompany.com',
    'https://core.yourcompany.com',
    'https://api.yourcompany.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Session-ID',
    'X-Device-ID',
    'X-Application'
  ],
  exposedHeaders: [
    'X-Session-ID',
    'X-Token-Expires',
    'X-Refresh-Token'
  ],
  maxAge: 86400
};
```

### Cấu Hình CORS Động

```env
# CORS dựa trên môi trường
CORS_ORIGINS=https://app.yourcompany.com,https://mail.yourcompany.com
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400
```

## Cấu Hình Load Balancer

### Cấu Hình Nginx

```nginx
# /etc/nginx/sites-available/sso
upstream sso_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    listen 443 ssl http2;
    server_name auth.yourcompany.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://sso_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Headers đặc biệt cho SSO
        proxy_set_header X-Session-ID $http_x_session_id;
        proxy_set_header X-Device-ID $http_x_device_id;
        
        # Xử lý Cookie
        proxy_cookie_domain localhost yourcompany.com;
        proxy_cookie_path / /;
    }
}

# Cấu hình subdomain
server {
    listen 443 ssl http2;
    server_name *.yourcompany.com;

    ssl_certificate /path/to/ssl/wildcard.crt;
    ssl_certificate_key /path/to/ssl/wildcard.key;

    location /auth/ {
        proxy_pass http://sso_backend/auth/;
        proxy_set_header Host auth.yourcompany.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Cấu Hình Apache

```apache
# /etc/apache2/sites-available/sso.conf
<VirtualHost *:443>
    ServerName auth.yourcompany.com
    
    SSLEngine on
    SSLCertificateFile /path/to/ssl/certificate.crt
    SSLCertificateKeyFile /path/to/ssl/private.key
    
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # Headers đặc biệt cho SSO
    ProxyPassReverse / http://localhost:3000/
    ProxyPassReverseMatch ^(/.*) http://localhost:3000$1
    
    # Cấu hình Cookie
    Header edit Set-Cookie "^(.*; )Domain=localhost(.*)$" "$1Domain=.yourcompany.com$2"
</VirtualHost>
```

## Cấu Hình SSL/TLS

### Thiết Lập SSL Certificate

```bash
# Tạo SSL certificate (Let's Encrypt)
certbot certonly --nginx -d auth.yourcompany.com -d *.yourcompany.com

# Hoặc sử dụng certificates có sẵn
cp /path/to/certificate.crt /etc/ssl/certs/yourcompany.crt
cp /path/to/private.key /etc/ssl/private/yourcompany.key
```

### Cấu Hình SSL trong Application

```env
# Cài Đặt SSL
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/yourcompany.crt
SSL_KEY_PATH=/etc/ssl/private/yourcompany.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# HTTPS Redirect
FORCE_HTTPS=true
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
```

## Cấu Hình Monitoring

### Health Check Endpoints

```typescript
// Cấu hình health check
const healthConfig = {
  endpoints: {
    '/health': 'basic',
    '/health/detailed': 'detailed',
    '/health/sso': 'sso-specific'
  },
  checks: [
    'database',
    'redis',
    'jwt-service',
    'sso-config'
  ]
};
```

### Cấu Hình Logging

```env
# Cài Đặt Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/sso/application.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_PATH=/var/log/sso/audit.log
AUDIT_LOG_RETENTION_DAYS=90
```

### Cấu Hình Metrics

```env
# Thu Thập Metrics
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Giám Sát Hiệu Suất
PERFORMANCE_MONITORING=true
RESPONSE_TIME_THRESHOLD=1000
ERROR_RATE_THRESHOLD=5
```

## Cấu Hình Triển Khai

### Cấu Hình Docker

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  sso-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - SSO_ENABLED=true
      - SSO_BASE_DOMAIN=yourcompany.com
      - DATABASE_URL=******************************/sso
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: sso
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass your_redis_password

volumes:
  postgres_data:
```

### Cấu Hình Kubernetes

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sso-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: sso-app
  template:
    metadata:
      labels:
        app: sso-app
    spec:
      containers:
      - name: sso-app
        image: yourcompany/sso-app:latest
        ports:
        - containerPort: 3000
        env:
        - name: SSO_ENABLED
          value: "true"
        - name: SSO_BASE_DOMAIN
          value: "yourcompany.com"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sso-secrets
              key: database-url
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## Cấu Hình Theo Môi Trường

### Môi Trường Development

```env
# Development overrides
NODE_ENV=development
SSO_SECURE_ONLY=false
SSO_SAME_SITE=none
LOG_LEVEL=debug
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### Môi Trường Staging

```env
# Cấu hình Staging
NODE_ENV=staging
SSO_BASE_DOMAIN=staging.yourcompany.com
SSO_COOKIE_DOMAIN=.staging.yourcompany.com
SSO_ALLOWED_APPLICATIONS=app.staging.yourcompany.com,mail.staging.yourcompany.com
```

### Môi Trường Production

```env
# Cấu hình Production
NODE_ENV=production
SSO_SECURE_ONLY=true
SSO_SAME_SITE=lax
LOG_LEVEL=warn
METRICS_ENABLED=true
AUDIT_LOG_ENABLED=true
```

## Xác Thực Cấu Hình

### Xác Thực Khi Khởi Động

```typescript
// Xác thực cấu hình khi khởi động
const validateConfig = () => {
  const required = [
    'SSO_BASE_DOMAIN',
    'SSO_COOKIE_DOMAIN',
    'JWT_SECRET',
    'DATABASE_URL',
    'REDIS_HOST'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Thiếu biến môi trường bắt buộc: ${missing.join(', ')}`);
  }
};
```

### Kiểm Tra Cấu Hình Runtime

```bash
# Script kiểm tra cấu hình
#!/bin/bash

echo "Đang kiểm tra cấu hình SSO..."

# Kiểm tra biến môi trường
if [ -z "$SSO_ENABLED" ]; then
  echo "LỖI: SSO_ENABLED chưa được thiết lập"
  exit 1
fi

# Kiểm tra kết nối database
npm run migration:show > /dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "LỖI: Kết nối database thất bại"
  exit 1
fi

# Kiểm tra kết nối Redis
redis-cli -h $REDIS_HOST ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "LỖI: Kết nối Redis thất bại"
  exit 1
fi

echo "Kiểm tra cấu hình thành công!"
```

---

**Để biết chi tiết triển khai, tham khảo [Hướng Dẫn Triển Khai](./SSO_IMPLEMENTATION_GUIDE_vi.md) và [Tài Liệu API](./SSO_API_DOCUMENTATION_vi.md).**
