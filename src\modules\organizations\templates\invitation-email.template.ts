export interface InvitationEmailData {
  organizationName: string;
  inviterName: string;
  inviterEmail: string;
  roleName: string;
  invitationMessage?: string;
  inviteUrl: string;
  expiryDate: string;
  organizationLogo?: string;
}

export function generateInvitationEmailHtml(data: InvitationEmailData): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation to join ${data.organizationName}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 120px;
            height: auto;
            margin-bottom: 20px;
        }
        .title {
            color: #1f2937;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        .subtitle {
            color: #6b7280;
            font-size: 16px;
            margin: 0;
        }
        .content {
            margin: 30px 0;
        }
        .invitation-details {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: 600;
            color: #374151;
        }
        .detail-value {
            color: #6b7280;
        }
        .message-box {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 16px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .message-text {
            font-style: italic;
            color: #1e40af;
            margin: 0;
        }
        .cta-section {
            text-align: center;
            margin: 40px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .alternative-link {
            margin-top: 20px;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
        }
        .alternative-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        .link-text {
            word-break: break-all;
            color: #3b82f6;
            font-size: 12px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
        }
        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 5px 0;
        }
        .expiry-warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 12px;
            margin: 20px 0;
            text-align: center;
        }
        .expiry-text {
            color: #92400e;
            font-size: 14px;
            font-weight: 500;
            margin: 0;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .title {
                font-size: 24px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            ${data.organizationLogo ? `<img src="${data.organizationLogo}" alt="${data.organizationName}" class="logo">` : ''}
            <h1 class="title">You're Invited!</h1>
            <p class="subtitle">Join ${data.organizationName} and start collaborating</p>
        </div>

        <div class="content">
            <p>Hello,</p>
            
            <p><strong>${data.inviterName}</strong> (${data.inviterEmail}) has invited you to join <strong>${data.organizationName}</strong> as a <strong>${data.roleName}</strong>.</p>

            <div class="invitation-details">
                <div class="detail-row">
                    <span class="detail-label">Organization:</span>
                    <span class="detail-value">${data.organizationName}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Role:</span>
                    <span class="detail-value">${data.roleName}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Invited by:</span>
                    <span class="detail-value">${data.inviterName}</span>
                </div>
            </div>

            ${data.invitationMessage ? `
            <div class="message-box">
                <p class="message-text">"${data.invitationMessage}"</p>
            </div>
            ` : ''}

            <div class="cta-section">
                <a href="${data.inviteUrl}" class="cta-button">Accept Invitation</a>
            </div>

            <div class="alternative-link">
                <p class="alternative-text">Or copy and paste this link into your browser:</p>
                <p class="link-text">${data.inviteUrl}</p>
            </div>

            <div class="expiry-warning">
                <p class="expiry-text">⏰ This invitation expires on ${data.expiryDate}</p>
            </div>
        </div>

        <div class="footer">
            <p class="footer-text">If you don't want to join this organization, you can safely ignore this email.</p>
            <p class="footer-text">This invitation was sent by ${data.organizationName} via Delify Platform.</p>
        </div>
    </div>
</body>
</html>
  `;
}

export function generateInvitationEmailText(data: InvitationEmailData): string {
  return `
You're invited to join ${data.organizationName}!

${data.inviterName} (${data.inviterEmail}) has invited you to join ${data.organizationName} as a ${data.roleName}.

${data.invitationMessage ? `Personal message: "${data.invitationMessage}"` : ''}

To accept this invitation, click the link below:
${data.inviteUrl}

This invitation will expire on ${data.expiryDate}.

If you don't want to join this organization, you can safely ignore this email.

---
This invitation was sent by ${data.organizationName} via Delify Platform.
  `;
}
