import { Injectable, Logger, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class LoggerService extends Logger {
  private logLevels: LogLevel[];

  constructor(private configService: ConfigService) {
    super('DelifyLogger');
    this.setLogLevels();
  }

  private setLogLevels() {
    const logLevel = this.configService.get('LOG_LEVEL', 'debug');
    
    switch (logLevel) {
      case 'error':
        this.logLevels = ['error'];
        break;
      case 'warn':
        this.logLevels = ['error', 'warn'];
        break;
      case 'log':
        this.logLevels = ['error', 'warn', 'log'];
        break;
      case 'debug':
        this.logLevels = ['error', 'warn', 'log', 'debug'];
        break;
      case 'verbose':
        this.logLevels = ['error', 'warn', 'log', 'debug', 'verbose'];
        break;
      default:
        this.logLevels = ['error', 'warn', 'log'];
    }
  }

  logWithContext(message: string, context?: string, level: LogLevel = 'log') {
    if (this.logLevels.includes(level)) {
      const timestamp = new Date().toISOString();
      const contextStr = context ? `[${context}]` : '';
      const formattedMessage = `${timestamp} ${contextStr} ${message}`;
      
      switch (level) {
        case 'error':
          super.error(formattedMessage);
          break;
        case 'warn':
          super.warn(formattedMessage);
          break;
        case 'debug':
          super.debug(formattedMessage);
          break;
        case 'verbose':
          super.verbose(formattedMessage);
          break;
        default:
          super.log(formattedMessage);
      }
    }
  }

  logApiRequest(method: string, url: string, userId?: string) {
    const context = userId ? `User:${userId}` : 'Anonymous';
    this.logWithContext(`${method} ${url}`, context, 'log');
  }

  logApiResponse(method: string, url: string, statusCode: number, duration: number) {
    this.logWithContext(
      `${method} ${url} - ${statusCode} (${duration}ms)`,
      'API',
      'log'
    );
  }

  logError(error: Error, context?: string) {
    const errorMessage = `${error.message}\nStack: ${error.stack}`;
    this.logWithContext(errorMessage, context, 'error');
  }
}
