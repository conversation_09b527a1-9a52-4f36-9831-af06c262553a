import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

/**
 * Enum định nghĩa loại token
 * Enum defining token types
 */
export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH',
  SSO = 'SSO',
}

/**
 * Enum định nghĩa lý do thu hồi token
 * Enum defining token revocation reasons
 */
export enum RevocationReason {
  USER_LOGOUT = 'USER_LOGOUT',
  ADMIN_REVOKE = 'ADMIN_REVOKE',
  SECURITY_BREACH = 'SECURITY_BREACH',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  DEVICE_CHANGE = 'DEVICE_CHANGE',
  GLOBAL_LOGOUT = 'GLOBAL_LOGOUT',
}

/**
 * JWTBlacklist Entity - Quản lý danh sách đen token JWT
 * JWTBlacklist Entity - Manages JWT token blacklist
 */
@Entity('jwt_blacklist')
@Index(['jti'])
@Index(['userId'])
@Index(['expiresAt'])
export class JWTBlacklist {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * JWT ID (jti claim) - JWT ID (jti claim)
   */
  @Column({ type: 'varchar', length: 255, unique: true })
  jti: string;

  /**
   * ID người dùng - User ID
   */
  @Column({ type: 'uuid', name: 'user_id' })
  userId: string;

  /**
   * ID phiên - Session ID
   */
  @Column({ type: 'varchar', length: 255, nullable: true, name: 'session_id' })
  sessionId?: string;

  /**
   * Loại token - Token type
   */
  @Column({ 
    type: 'varchar', 
    length: 20, 
    name: 'token_type',
    enum: TokenType 
  })
  tokenType: TokenType;

  /**
   * Thời gian hết hạn token - Token expiration time
   */
  @Column({ type: 'timestamp', name: 'expires_at' })
  expiresAt: Date;

  /**
   * Thời gian thu hồi - Revocation time
   */
  @CreateDateColumn({ name: 'revoked_at' })
  revokedAt: Date;

  /**
   * ID người thu hồi - Revoker ID
   */
  @Column({ type: 'uuid', nullable: true, name: 'revoked_by' })
  revokedBy?: string;

  /**
   * Lý do thu hồi - Revocation reason
   */
  @Column({ 
    type: 'varchar', 
    length: 200, 
    nullable: true,
    enum: RevocationReason 
  })
  reason?: RevocationReason;

  // Relations

  /**
   * Người dùng - User
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  /**
   * Người thu hồi - Revoker
   */
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'revoked_by' })
  revoker?: User;

  // Virtual properties

  /**
   * Kiểm tra token có hết hạn không - Check if token is expired
   */
  get isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  /**
   * Lấy số phút còn lại trước khi hết hạn - Get remaining minutes before expiration
   */
  get remainingMinutes(): number {
    if (this.isExpired) return 0;
    const diffMs = this.expiresAt.getTime() - new Date().getTime();
    return Math.floor(diffMs / (1000 * 60));
  }

  /**
   * Kiểm tra có phải thu hồi bởi admin không - Check if revoked by admin
   */
  get isAdminRevoked(): boolean {
    return this.reason === RevocationReason.ADMIN_REVOKE;
  }

  /**
   * Kiểm tra có phải thu hồi vì bảo mật không - Check if security revocation
   */
  get isSecurityRevocation(): boolean {
    return [
      RevocationReason.SECURITY_BREACH,
      RevocationReason.SUSPICIOUS_ACTIVITY,
      RevocationReason.PASSWORD_CHANGE,
      RevocationReason.ACCOUNT_DISABLED,
    ].includes(this.reason as RevocationReason);
  }

  /**
   * Lấy mô tả lý do thu hồi - Get revocation reason description
   */
  get reasonDescription(): string {
    const descriptions = {
      [RevocationReason.USER_LOGOUT]: 'User logged out',
      [RevocationReason.ADMIN_REVOKE]: 'Revoked by administrator',
      [RevocationReason.SECURITY_BREACH]: 'Security breach detected',
      [RevocationReason.PASSWORD_CHANGE]: 'Password changed',
      [RevocationReason.ACCOUNT_DISABLED]: 'Account disabled',
      [RevocationReason.TOKEN_EXPIRED]: 'Token expired',
      [RevocationReason.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected',
      [RevocationReason.DEVICE_CHANGE]: 'Device changed',
      [RevocationReason.GLOBAL_LOGOUT]: 'Global logout',
    };

    return descriptions[this.reason as RevocationReason] || 'Unknown reason';
  }

  /**
   * Tạo JTI mới - Generate new JTI
   */
  static generateJTI(): string {
    const crypto = require('crypto');
    const timestamp = Date.now().toString(36);
    const randomBytes = crypto.randomBytes(16).toString('hex');
    return `${timestamp}_${randomBytes}`;
  }

  /**
   * Kiểm tra JTI có trong blacklist không - Check if JTI is blacklisted
   */
  static async isBlacklisted(jti: string, repository: any): Promise<boolean> {
    const blacklistedToken = await repository.findOne({
      where: { jti },
    });

    return !!blacklistedToken;
  }

  /**
   * Thu hồi token - Revoke token
   */
  static create(
    jti: string,
    userId: string,
    tokenType: TokenType,
    expiresAt: Date,
    reason?: RevocationReason,
    revokedBy?: string,
    sessionId?: string,
  ): JWTBlacklist {
    const blacklistEntry = new JWTBlacklist();
    blacklistEntry.jti = jti;
    blacklistEntry.userId = userId;
    blacklistEntry.tokenType = tokenType;
    blacklistEntry.expiresAt = expiresAt;
    blacklistEntry.reason = reason;
    blacklistEntry.revokedBy = revokedBy;
    blacklistEntry.sessionId = sessionId;
    
    return blacklistEntry;
  }

  /**
   * Cleanup expired tokens - Cleanup expired tokens
   */
  static async cleanupExpired(repository: any): Promise<number> {
    const result = await repository
      .createQueryBuilder()
      .delete()
      .from(JWTBlacklist)
      .where('expires_at < :now', { now: new Date() })
      .execute();

    return result.affected || 0;
  }

  /**
   * Thu hồi tất cả token của user - Revoke all user tokens
   */
  static async revokeAllUserTokens(
    userId: string,
    reason: RevocationReason,
    revokedBy: string,
    repository: any,
    tokenRepository: any,
  ): Promise<number> {
    // This would typically involve getting all active tokens for the user
    // and adding them to the blacklist. Implementation depends on how tokens are tracked.
    
    // For now, we'll create a placeholder entry
    const globalRevocation = JWTBlacklist.create(
      `global_revoke_${userId}_${Date.now()}`,
      userId,
      TokenType.ACCESS,
      new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      reason,
      revokedBy,
    );

    await repository.save(globalRevocation);
    return 1;
  }

  /**
   * Lấy thống kê thu hồi token - Get revocation statistics
   */
  static async getRevocationStats(
    repository: any,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const query = repository
      .createQueryBuilder('blacklist')
      .select('blacklist.reason', 'reason')
      .addSelect('COUNT(*)', 'count')
      .groupBy('blacklist.reason');

    if (startDate) {
      query.andWhere('blacklist.revoked_at >= :startDate', { startDate });
    }

    if (endDate) {
      query.andWhere('blacklist.revoked_at <= :endDate', { endDate });
    }

    return query.getRawMany();
  }
}
