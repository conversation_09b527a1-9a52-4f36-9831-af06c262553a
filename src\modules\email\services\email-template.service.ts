import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailTemplate, TemplateType } from '../entities/email-template.entity';
import { LoggerService } from '../../../common/services/logger.service';

@Injectable()
export class EmailTemplateService {
  constructor(
    @InjectRepository(EmailTemplate)
    private emailTemplateRepository: Repository<EmailTemplate>,
    private logger: LoggerService,
  ) {}

  async createTemplate(userId: string, data: {
    name: string;
    description?: string;
    templateType: TemplateType;
    subject: string;
    htmlContent: string;
    textContent?: string;
    variables?: string[];
    settings?: any;
  }): Promise<EmailTemplate> {
    const template = this.emailTemplateRepository.create({
      userId,
      ...data,
    });

    const savedTemplate = await this.emailTemplateRepository.save(template);
    
    this.logger.logWithContext(`Email template created: ${savedTemplate.id}`, 'EmailTemplateService');
    
    return savedTemplate;
  }

  async getTemplates(userId: string, templateType?: TemplateType): Promise<EmailTemplate[]> {
    const where: any = { userId };
    if (templateType) {
      where.templateType = templateType;
    }

    return this.emailTemplateRepository.find({
      where,
      order: { createdAt: 'DESC' },
    });
  }

  async getTemplate(userId: string, templateId: string): Promise<EmailTemplate> {
    return this.emailTemplateRepository.findOne({
      where: { id: templateId, userId },
    });
  }

  async updateTemplate(userId: string, templateId: string, data: Partial<EmailTemplate>): Promise<EmailTemplate> {
    await this.emailTemplateRepository.update(
      { id: templateId, userId },
      data
    );

    const updatedTemplate = await this.getTemplate(userId, templateId);
    
    this.logger.logWithContext(`Email template updated: ${templateId}`, 'EmailTemplateService');
    
    return updatedTemplate;
  }

  async deleteTemplate(userId: string, templateId: string): Promise<void> {
    await this.emailTemplateRepository.delete({
      id: templateId,
      userId,
    });

    this.logger.logWithContext(`Email template deleted: ${templateId}`, 'EmailTemplateService');
  }

  async incrementUsage(templateId: string): Promise<void> {
    await this.emailTemplateRepository.update(templateId, {
      usageCount: () => 'usage_count + 1',
      lastUsedAt: new Date(),
    });
  }

  async getPopularTemplates(userId: string, limit: number = 10): Promise<EmailTemplate[]> {
    return this.emailTemplateRepository.find({
      where: { userId, isActive: true },
      order: { usageCount: 'DESC' },
      take: limit,
    });
  }

  async duplicateTemplate(userId: string, templateId: string, newName: string): Promise<EmailTemplate> {
    const originalTemplate = await this.getTemplate(userId, templateId);
    
    if (!originalTemplate) {
      throw new Error('Template not found');
    }

    const duplicatedTemplate = this.emailTemplateRepository.create({
      ...originalTemplate,
      id: undefined, // Let TypeORM generate new ID
      name: newName,
      usageCount: 0,
      lastUsedAt: null,
      createdAt: undefined,
      updatedAt: undefined,
    });

    const savedTemplate = await this.emailTemplateRepository.save(duplicatedTemplate);
    
    this.logger.logWithContext(`Email template duplicated: ${templateId} -> ${savedTemplate.id}`, 'EmailTemplateService');
    
    return savedTemplate;
  }

  async getTemplatesByType(userId: string): Promise<Record<TemplateType, EmailTemplate[]>> {
    const templates = await this.getTemplates(userId);
    
    const groupedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.templateType]) {
        acc[template.templateType] = [];
      }
      acc[template.templateType].push(template);
      return acc;
    }, {} as Record<TemplateType, EmailTemplate[]>);

    return groupedTemplates;
  }

  async searchTemplates(userId: string, query: string): Promise<EmailTemplate[]> {
    return this.emailTemplateRepository
      .createQueryBuilder('template')
      .where('template.userId = :userId', { userId })
      .andWhere('(template.name ILIKE :query OR template.description ILIKE :query)', {
        query: `%${query}%`,
      })
      .orderBy('template.createdAt', 'DESC')
      .getMany();
  }

  async getTemplateVariables(templateId: string): Promise<string[]> {
    const template = await this.emailTemplateRepository.findOne({
      where: { id: templateId },
      select: ['htmlContent', 'textContent', 'subject'],
    });

    if (!template) {
      return [];
    }

    const variableRegex = /\{\{(\w+)\}\}/g;
    const variables = new Set<string>();

    // Extract variables from subject
    let match;
    while ((match = variableRegex.exec(template.subject)) !== null) {
      variables.add(match[1]);
    }

    // Extract variables from HTML content
    variableRegex.lastIndex = 0;
    while ((match = variableRegex.exec(template.htmlContent)) !== null) {
      variables.add(match[1]);
    }

    // Extract variables from text content
    if (template.textContent) {
      variableRegex.lastIndex = 0;
      while ((match = variableRegex.exec(template.textContent)) !== null) {
        variables.add(match[1]);
      }
    }

    return Array.from(variables);
  }

  async renderTemplate(templateId: string, variables: Record<string, any>): Promise<{
    subject: string;
    htmlContent: string;
    textContent?: string;
  }> {
    const template = await this.emailTemplateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('Template not found');
    }

    let subject = template.subject;
    let htmlContent = template.htmlContent;
    let textContent = template.textContent;

    // Replace variables
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      subject = subject.replace(placeholder, String(value));
      htmlContent = htmlContent.replace(placeholder, String(value));
      if (textContent) {
        textContent = textContent.replace(placeholder, String(value));
      }
    }

    return {
      subject,
      htmlContent,
      textContent,
    };
  }
}
