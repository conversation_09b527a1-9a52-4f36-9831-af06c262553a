import { SetMetadata } from '@nestjs/common';
import { CacheDecoratorOptions } from '../interfaces/cache.interface';

/**
 * Metadata keys cho cache decorators
 * Metadata keys for cache decorators
 */
export const CACHE_KEY_METADATA = 'cache:key';
export const CACHE_TTL_METADATA = 'cache:ttl';
export const CACHE_PREFIX_METADATA = 'cache:prefix';
export const CACHE_CONDITION_METADATA = 'cache:condition';
export const CACHE_EVICT_METADATA = 'cache:evict';

/**
 * Decorator để cache kết quả method
 * Decorator to cache method results
 * 
 * @param keyOrOptions - Cache key hoặc options object
 * @param ttl - Time to live in seconds (optional nếu dùng options object)
 * 
 * @example
 * ```typescript
 * @Cacheable('user-profile', 300)
 * async getUserProfile(userId: string) {
 *   return this.userRepository.findOne(userId);
 * }
 * 
 * @Cacheable({ key: 'user-profile', ttl: 300, prefix: 'api:' })
 * async getUserProfile(userId: string) {
 *   return this.userRepository.findOne(userId);
 * }
 * ```
 */
export function Cacheable(
  keyOrOptions: string | CacheDecoratorOptions,
  ttl?: number
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    let options: CacheDecoratorOptions;
    
    if (typeof keyOrOptions === 'string') {
      options = { key: keyOrOptions, ttl };
    } else {
      options = keyOrOptions;
    }

    // Set metadata for the interceptor to use
    SetMetadata(CACHE_KEY_METADATA, options.key)(target, propertyName, descriptor);
    SetMetadata(CACHE_TTL_METADATA, options.ttl)(target, propertyName, descriptor);
    SetMetadata(CACHE_PREFIX_METADATA, options.prefix)(target, propertyName, descriptor);
    SetMetadata(CACHE_CONDITION_METADATA, options.condition)(target, propertyName, descriptor);

    return descriptor;
  };
}

/**
 * Decorator để xóa cache
 * Decorator to evict cache
 * 
 * @param keyOrKeys - Cache key hoặc array of keys để xóa
 * @param prefix - Cache prefix (optional)
 * 
 * @example
 * ```typescript
 * @CacheEvict('user-profile')
 * async updateUserProfile(userId: string, data: UpdateUserDto) {
 *   return this.userRepository.update(userId, data);
 * }
 * 
 * @CacheEvict(['user-profile', 'user-permissions'])
 * async deleteUser(userId: string) {
 *   return this.userRepository.delete(userId);
 * }
 * ```
 */
export function CacheEvict(
  keyOrKeys: string | string[],
  prefix?: string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const keys = Array.isArray(keyOrKeys) ? keyOrKeys : [keyOrKeys];
    
    SetMetadata(CACHE_EVICT_METADATA, { keys, prefix })(target, propertyName, descriptor);

    return descriptor;
  };
}

/**
 * Decorator để cache với key động dựa trên parameters
 * Decorator to cache with dynamic key based on parameters
 * 
 * @param keyTemplate - Template cho cache key với placeholders
 * @param ttl - Time to live in seconds
 * @param prefix - Cache prefix (optional)
 * 
 * @example
 * ```typescript
 * @CacheKey('user-profile-{0}', 300) // {0} sẽ được thay thế bằng parameter đầu tiên
 * async getUserProfile(userId: string) {
 *   return this.userRepository.findOne(userId);
 * }
 * 
 * @CacheKey('user-{0}-org-{1}', 300)
 * async getUserInOrganization(userId: string, orgId: string) {
 *   return this.userRepository.findUserInOrg(userId, orgId);
 * }
 * ```
 */
export function CacheKey(
  keyTemplate: string,
  ttl?: number,
  prefix?: string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const options: CacheDecoratorOptions = {
      key: keyTemplate,
      ttl,
      prefix,
    };

    SetMetadata(CACHE_KEY_METADATA, options.key)(target, propertyName, descriptor);
    SetMetadata(CACHE_TTL_METADATA, options.ttl)(target, propertyName, descriptor);
    SetMetadata(CACHE_PREFIX_METADATA, options.prefix)(target, propertyName, descriptor);

    return descriptor;
  };
}

/**
 * Decorator để cache với điều kiện
 * Decorator to cache with condition
 * 
 * @param condition - Function để kiểm tra có nên cache không
 * @param key - Cache key
 * @param ttl - Time to live in seconds
 * 
 * @example
 * ```typescript
 * @CacheWhen(
 *   (userId: string) => userId !== 'admin',
 *   'user-profile',
 *   300
 * )
 * async getUserProfile(userId: string) {
 *   return this.userRepository.findOne(userId);
 * }
 * ```
 */
export function CacheWhen(
  condition: (...args: any[]) => boolean,
  key: string,
  ttl?: number,
  prefix?: string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const options: CacheDecoratorOptions = {
      key,
      ttl,
      prefix,
      condition,
    };

    SetMetadata(CACHE_KEY_METADATA, options.key)(target, propertyName, descriptor);
    SetMetadata(CACHE_TTL_METADATA, options.ttl)(target, propertyName, descriptor);
    SetMetadata(CACHE_PREFIX_METADATA, options.prefix)(target, propertyName, descriptor);
    SetMetadata(CACHE_CONDITION_METADATA, options.condition)(target, propertyName, descriptor);

    return descriptor;
  };
}

/**
 * Decorator để cache với TTL động
 * Decorator to cache with dynamic TTL
 * 
 * @param key - Cache key
 * @param ttlFunction - Function để tính TTL dựa trên parameters
 * @param prefix - Cache prefix (optional)
 * 
 * @example
 * ```typescript
 * @CacheTTL(
 *   'user-session',
 *   (userId: string, sessionType: string) => sessionType === 'admin' ? 300 : 3600
 * )
 * async getUserSession(userId: string, sessionType: string) {
 *   return this.sessionRepository.findUserSession(userId, sessionType);
 * }
 * ```
 */
export function CacheTTL(
  key: string,
  ttlFunction: (...args: any[]) => number,
  prefix?: string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const options: CacheDecoratorOptions = {
      key,
      prefix,
    };

    SetMetadata(CACHE_KEY_METADATA, options.key)(target, propertyName, descriptor);
    SetMetadata(CACHE_PREFIX_METADATA, options.prefix)(target, propertyName, descriptor);
    
    // Store TTL function for interceptor to use
    SetMetadata('cache:ttl_function', ttlFunction)(target, propertyName, descriptor);

    return descriptor;
  };
}

/**
 * Helper function để tạo cache key từ template và parameters
 * Helper function to create cache key from template and parameters
 */
export function buildCacheKey(template: string, args: any[]): string {
  let key = template;
  
  // Replace placeholders {0}, {1}, etc. with actual arguments
  args.forEach((arg, index) => {
    const placeholder = `{${index}}`;
    const value = typeof arg === 'object' ? JSON.stringify(arg) : String(arg);
    key = key.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
  });
  
  return key;
}

/**
 * Helper function để validate cache key
 * Helper function to validate cache key
 */
export function validateCacheKey(key: string): boolean {
  // Cache key should not be empty and should not contain special characters
  return key && key.length > 0 && !/[{}[\]()\/\\#?@!$&'*+,;=\s]/.test(key);
}

/**
 * Helper function để tạo cache key hash cho keys dài
 * Helper function to create cache key hash for long keys
 */
export function hashCacheKey(key: string): string {
  // Simple hash function for cache keys
  let hash = 0;
  if (key.length === 0) return hash.toString();
  
  for (let i = 0; i < key.length; i++) {
    const char = key.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}
