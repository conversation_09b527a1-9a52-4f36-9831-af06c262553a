import { Controller, Get, Post, Body, Param, Put, Delete, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsEnum, MinLength, MaxLength } from 'class-validator';

// DTOs for testing
export class CreateTestUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'username123',
    description: 'Username (3-30 characters)',
    minLength: 3,
    maxLength: 30
  })
  @IsString()
  @MinLength(3)
  @MaxLength(30)
  username: string;

  @ApiProperty({
    example: 'SecurePassword123!',
    description: 'Password (minimum 8 characters)',
    minLength: 8
  })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'First name'
  })
  @IsString()
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name'
  })
  @IsString()
  lastName: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number (optional)'
  })
  @IsOptional()
  @IsString()
  phone?: string;
}

export class UpdateTestUserDto {
  @ApiPropertyOptional({
    example: 'John',
    description: 'First name'
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    example: 'Doe',
    description: 'Last name'
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number'
  })
  @IsOptional()
  @IsString()
  phone?: string;
}

export class TestUserResponse {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: 'username123' })
  username: string;

  @ApiProperty({ example: 'John' })
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  lastName: string;

  @ApiPropertyOptional({ example: '+1234567890' })
  phone?: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  createdAt: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z' })
  updatedAt: string;
}

@ApiTags('Test API')
@Controller('test')
export class TestController {
  
  @Get()
  @ApiOperation({ summary: 'Get all test users' })
  @ApiResponse({
    status: 200,
    description: 'List of test users retrieved successfully',
    type: [TestUserResponse]
  })
  async getAllUsers(): Promise<TestUserResponse[]> {
    return [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        username: 'john123',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      },
      {
        id: '123e4567-e89b-12d3-a456-426614174001',
        email: '<EMAIL>',
        username: 'jane123',
        firstName: 'Jane',
        lastName: 'Smith',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      }
    ];
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get test user by ID' })
  @ApiResponse({
    status: 200,
    description: 'Test user retrieved successfully',
    type: TestUserResponse
  })
  @ApiResponse({ status: 404, description: 'Test user not found' })
  async getUserById(@Param('id') id: string): Promise<TestUserResponse> {
    return {
      id,
      email: '<EMAIL>',
      username: 'john123',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+1234567890',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z'
    };
  }

  @Post()
  @ApiOperation({ summary: 'Create a new test user' })
  @ApiResponse({
    status: 201,
    description: 'Test user created successfully',
    type: TestUserResponse
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  async createUser(@Body() createUserDto: CreateTestUserDto): Promise<TestUserResponse> {
    return {
      id: '123e4567-e89b-12d3-a456-426614174002',
      email: createUserDto.email,
      username: createUserDto.username,
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
      phone: createUserDto.phone,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update test user by ID' })
  @ApiResponse({
    status: 200,
    description: 'Test user updated successfully',
    type: TestUserResponse
  })
  @ApiResponse({ status: 404, description: 'Test user not found' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateTestUserDto
  ): Promise<TestUserResponse> {
    return {
      id,
      email: '<EMAIL>',
      username: 'john123',
      firstName: updateUserDto.firstName || 'John',
      lastName: updateUserDto.lastName || 'Doe',
      phone: updateUserDto.phone || '+1234567890',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete test user by ID' })
  @ApiResponse({
    status: 200,
    description: 'Test user deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'User deleted successfully' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Test user not found' })
  async deleteUser(@Param('id') id: string): Promise<{ message: string }> {
    return { message: 'User deleted successfully' };
  }

  @Get('search/users')
  @ApiOperation({ summary: 'Search test users' })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
    type: [TestUserResponse]
  })
  async searchUsers(
    @Query('q') query?: string,
    @Query('limit') limit?: number
  ): Promise<TestUserResponse[]> {
    return [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        username: 'john123',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      }
    ];
  }
}
