import {
  Entity,
  PrimaryColumn,
  <PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { Role } from './role.entity';

/**
 * Enum định nghĩa loại kế thừa
 * Enum defining inheritance types
 */
export enum InheritanceType {
  FULL = 'FULL',     // Kế thừa đầy đủ - Full inheritance
  PARTIAL = 'PARTIAL', // Kế thừa một phần - Partial inheritance
  NONE = 'NONE',     // Không kế thừa - No inheritance
}

/**
 * RoleHierarchy Entity - Phân cấp vai trò
 * RoleHierarchy Entity - Role hierarchy
 */
@Entity('role_hierarchy')
export class RoleHierarchy {
  /**
   * ID vai trò cha - Parent role ID
   */
  @PrimaryColumn({ type: 'uuid', name: 'parent_role_id' })
  parentRoleId: string;

  /**
   * ID vai trò con - Child role ID
   */
  @PrimaryColumn({ type: 'uuid', name: 'child_role_id' })
  childRoleId: string;

  /**
   * <PERSON><PERSON><PERSON> kế thừa - Inheritance type
   */
  @Column({
    type: 'varchar',
    length: 20,
    default: InheritanceType.FULL,
    name: 'inheritance_type',
  })
  inheritanceType: InheritanceType;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations

  /**
   * Vai trò cha - Parent role
   */
  @ManyToOne(() => Role, (role) => role.parentHierarchies, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'parent_role_id' })
  parentRole: Role;

  /**
   * Vai trò con - Child role
   */
  @ManyToOne(() => Role, (role) => role.childHierarchies, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'child_role_id' })
  childRole: Role;

  // Virtual properties

  /**
   * Kiểm tra có kế thừa đầy đủ không - Check if full inheritance
   */
  get isFullInheritance(): boolean {
    return this.inheritanceType === InheritanceType.FULL;
  }

  /**
   * Kiểm tra có kế thừa một phần không - Check if partial inheritance
   */
  get isPartialInheritance(): boolean {
    return this.inheritanceType === InheritanceType.PARTIAL;
  }

  /**
   * Kiểm tra có kế thừa không - Check if has inheritance
   */
  get hasInheritance(): boolean {
    return this.inheritanceType !== InheritanceType.NONE;
  }

  /**
   * Lấy thông tin tóm tắt - Get summary info
   */
  get summary(): string {
    return `${this.parentRole?.name} -> ${this.childRole?.name} (${this.inheritanceType})`;
  }
}
