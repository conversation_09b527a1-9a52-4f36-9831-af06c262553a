import { config } from 'dotenv';
import { join } from 'path';
import axios from 'axios';

// Global setup for all tests
export default async function globalSetup() {
  console.log('🚀 Starting global test setup...');
  
  // Load test environment variables
  config({ path: join(__dirname, '.env.test') });
  
  // Set test environment
  process.env.NODE_ENV = 'test';
  
  // Check if OLLAMA server is running for integration tests
  await checkOllamaServer();
  
  // Setup test database if needed
  await setupTestDatabase();
  
  // Setup test Redis if needed
  await setupTestRedis();
  
  console.log('✅ Global test setup completed');
}

async function checkOllamaServer() {
  const ollamaUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
  
  try {
    const response = await axios.get(`${ollamaUrl}/api/tags`, {
      timeout: 5000,
    });
    
    console.log(`✅ OLLAMA server is running at ${ollamaUrl}`);
    console.log(`📦 Available models: ${response.data.models?.length || 0}`);
    
    // Set flag for tests to know OLLAMA is available
    process.env.OLLAMA_AVAILABLE = 'true';
    
    // Check for recommended test models
    const models = response.data.models || [];
    const modelNames = models.map((m: any) => m.name);
    
    const recommendedModels = ['llama2:7b', 'codellama:7b', 'phi3:mini'];
    const availableRecommended = recommendedModels.filter(model => 
      modelNames.some((name: string) => name.includes(model.split(':')[0]))
    );
    
    if (availableRecommended.length > 0) {
      console.log(`📚 Recommended models available: ${availableRecommended.join(', ')}`);
      process.env.OLLAMA_TEST_MODELS = availableRecommended.join(',');
    } else {
      console.log('⚠️  No recommended test models found. Some integration tests may be skipped.');
      console.log('💡 To install test models, run:');
      console.log('   ollama pull llama2:7b');
      console.log('   ollama pull codellama:7b');
      console.log('   ollama pull phi3:mini');
    }
    
  } catch (error) {
    console.log(`⚠️  OLLAMA server not available at ${ollamaUrl}`);
    console.log('💡 To start OLLAMA server: ollama serve');
    console.log('🧪 OLLAMA integration tests will be skipped');
    process.env.OLLAMA_AVAILABLE = 'false';
  }
}

async function setupTestDatabase() {
  // For SQLite in-memory database, no setup needed
  // For other databases, you might want to create test database here
  console.log('📊 Using SQLite in-memory database for tests');
}

async function setupTestRedis() {
  // Redis is mocked in tests, but you could setup a test Redis instance here
  console.log('🔴 Using mocked Redis for tests');
}

// Helper function to check external API availability
async function checkExternalAPIs() {
  const apis = [
    {
      name: 'OpenAI',
      url: 'https://api.openai.com/v1/models',
      key: process.env.OPENAI_API_KEY,
      headers: { Authorization: `Bearer ${process.env.OPENAI_API_KEY}` },
    },
    {
      name: 'Grok',
      url: 'https://api.x.ai/v1/models',
      key: process.env.GROK_API_KEY,
      headers: { Authorization: `Bearer ${process.env.GROK_API_KEY}` },
    },
    {
      name: 'Gemini',
      url: `https://generativelanguage.googleapis.com/v1beta/models?key=${process.env.GEMINI_API_KEY}`,
      key: process.env.GEMINI_API_KEY,
    },
  ];
  
  for (const api of apis) {
    if (!api.key || api.key.startsWith('test-')) {
      console.log(`⚠️  ${api.name} API key not configured for integration tests`);
      continue;
    }
    
    try {
      await axios.get(api.url, {
        headers: api.headers,
        timeout: 10000,
      });
      console.log(`✅ ${api.name} API is accessible`);
      process.env[`${api.name.toUpperCase()}_AVAILABLE`] = 'true';
    } catch (error) {
      console.log(`⚠️  ${api.name} API not accessible: ${error.message}`);
      process.env[`${api.name.toUpperCase()}_AVAILABLE`] = 'false';
    }
  }
}

// Only check external APIs if not mocking them
if (process.env.MOCK_EXTERNAL_APIS !== 'true') {
  checkExternalAPIs();
}
