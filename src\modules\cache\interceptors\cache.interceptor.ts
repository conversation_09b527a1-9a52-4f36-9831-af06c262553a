import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from '../services/cache.service';
import {
  CACHE_KEY_METADATA,
  CACHE_TTL_METADATA,
  CACHE_PREFIX_METADATA,
  CACHE_CONDITION_METADATA,
  CACHE_EVICT_METADATA,
  buildCacheKey,
} from '../decorators/cache.decorator';

/**
 * Cache Interceptor - Xử lý cache decorators
 * Cache Interceptor - Handles cache decorators
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON>,
  ): Promise<Observable<any>> {
    const handler = context.getHandler();
    const className = context.getClass().name;
    const methodName = handler.name;

    // Check for cache eviction first
    const evictMetadata = this.reflector.get(CACHE_EVICT_METADATA, handler);
    if (evictMetadata) {
      return this.handleCacheEviction(evictMetadata, next, className, methodName);
    }

    // Check for cacheable metadata
    const cacheKey = this.reflector.get(CACHE_KEY_METADATA, handler);
    if (!cacheKey) {
      return next.handle();
    }

    const cacheTTL = this.reflector.get(CACHE_TTL_METADATA, handler);
    const cachePrefix = this.reflector.get(CACHE_PREFIX_METADATA, handler);
    const cacheCondition = this.reflector.get(CACHE_CONDITION_METADATA, handler);

    // Get method arguments
    const args = context.getArgs();
    const methodArgs = this.extractMethodArguments(context);

    // Check condition if provided
    if (cacheCondition && !cacheCondition(...methodArgs)) {
      this.logger.debug(`Cache condition not met for ${className}.${methodName}`);
      return next.handle();
    }

    // Build final cache key
    const finalCacheKey = buildCacheKey(cacheKey, methodArgs);
    
    try {
      // Try to get from cache
      const cachedResult = await this.cacheService.get(finalCacheKey, cachePrefix);
      
      if (cachedResult !== undefined) {
        this.logger.debug(`Cache HIT for ${className}.${methodName}: ${finalCacheKey}`);
        return of(cachedResult);
      }

      this.logger.debug(`Cache MISS for ${className}.${methodName}: ${finalCacheKey}`);

      // Execute method and cache result
      return next.handle().pipe(
        tap(async (result) => {
          if (result !== undefined && result !== null) {
            await this.cacheService.set(finalCacheKey, result, {
              ttl: cacheTTL,
              prefix: cachePrefix,
            });
            
            this.logger.debug(`Cache SET for ${className}.${methodName}: ${finalCacheKey}`);
          }
        }),
      );
    } catch (error) {
      this.logger.error(`Cache error for ${className}.${methodName}:`, error);
      // Continue with method execution if cache fails
      return next.handle();
    }
  }

  /**
   * Xử lý cache eviction
   * Handle cache eviction
   */
  private handleCacheEviction(
    evictMetadata: { keys: string[]; prefix?: string },
    next: CallHandler,
    className: string,
    methodName: string,
  ): Observable<any> {
    return next.handle().pipe(
      tap(async () => {
        try {
          for (const key of evictMetadata.keys) {
            await this.cacheService.del(key, evictMetadata.prefix);
            this.logger.debug(`Cache EVICT for ${className}.${methodName}: ${key}`);
          }
        } catch (error) {
          this.logger.error(`Cache eviction error for ${className}.${methodName}:`, error);
        }
      }),
    );
  }

  /**
   * Trích xuất arguments của method
   * Extract method arguments
   */
  private extractMethodArguments(context: ExecutionContext): any[] {
    const contextType = context.getType();
    
    if (contextType === 'http') {
      // For HTTP requests, we might want to extract specific parameters
      const request = context.switchToHttp().getRequest();
      return [
        request.params,
        request.query,
        request.body,
        request.user,
      ].filter(arg => arg !== undefined);
    }
    
    if (contextType === 'rpc') {
      // For RPC calls
      const rpcContext = context.switchToRpc();
      return [rpcContext.getData(), rpcContext.getContext()];
    }
    
    // Default: return all arguments
    return context.getArgs();
  }
}

/**
 * Cache Eviction Interceptor - Chỉ xử lý cache eviction
 * Cache Eviction Interceptor - Only handles cache eviction
 */
@Injectable()
export class CacheEvictInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheEvictInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const handler = context.getHandler();
    const className = context.getClass().name;
    const methodName = handler.name;

    const evictMetadata = this.reflector.get(CACHE_EVICT_METADATA, handler);
    
    if (!evictMetadata) {
      return next.handle();
    }

    return next.handle().pipe(
      tap(async () => {
        try {
          const methodArgs = this.extractMethodArguments(context);
          
          for (const keyTemplate of evictMetadata.keys) {
            const finalKey = buildCacheKey(keyTemplate, methodArgs);
            await this.cacheService.del(finalKey, evictMetadata.prefix);
            
            this.logger.debug(`Cache EVICT for ${className}.${methodName}: ${finalKey}`);
          }
        } catch (error) {
          this.logger.error(`Cache eviction error for ${className}.${methodName}:`, error);
        }
      }),
    );
  }

  private extractMethodArguments(context: ExecutionContext): any[] {
    const contextType = context.getType();
    
    if (contextType === 'http') {
      const request = context.switchToHttp().getRequest();
      return [
        request.params,
        request.query,
        request.body,
        request.user,
      ].filter(arg => arg !== undefined);
    }
    
    return context.getArgs();
  }
}

/**
 * Cache Manager Interceptor - Quản lý cache tự động
 * Cache Manager Interceptor - Automatic cache management
 */
@Injectable()
export class CacheManagerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheManagerInterceptor.name);

  constructor(private readonly cacheService: CacheService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const className = context.getClass().name;
    const methodName = context.getHandler().name;

    return next.handle().pipe(
      tap({
        next: () => {
          const duration = Date.now() - startTime;
          this.logger.debug(`Method ${className}.${methodName} executed in ${duration}ms`);
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          this.logger.error(
            `Method ${className}.${methodName} failed after ${duration}ms:`,
            error,
          );
        },
      }),
    );
  }
}
