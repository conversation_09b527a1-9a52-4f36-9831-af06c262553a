# Tài Liệu API SSO

## Tổng Quan

Tài liệu này cung cấp hướng dẫn API toàn diện cho các endpoint c<PERSON><PERSON> hệ thống Single Sign-On (SSO).

## URL Gốc

```
https://api.yourcompany.com/auth/sso
```

## X<PERSON><PERSON>h<PERSON>c

Hầu hết các endpoint SSO yêu cầu xác thực JWT. Bao gồm access token trong header Authorization:

```
Authorization: Bearer <access_token>
```

## Endpoints Xác Thực SSO

### Đăng Nhập SSO

Thực hiện đăng nhập SSO với hỗ trợ cross-subdomain.

**Endpoint:** `POST /auth/sso/login`

**Request Body:**
```json
{
  "userId": "uuid-user-id",
  "application": "app.yourcompany.com",
  "deviceName": "Chrome trên Windows 11",
  "location": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Nam",
  "rememberDevice": true,
  "rbacInfo": {
    "email": "<EMAIL>",
    "username": "user123",
    "role": "MANAGER",
    "roles": ["MANAGER", "USER"],
    "permissions": ["USER_MANAGEMENT_READ", "CONTENT_MANAGEMENT_CREATE"],
    "roleLevel": 2,
    "isMasterAccount": false
  }
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "sessionId": "sso_1703000000_abc123def456",
  "expiresIn": 900,
  "tokenType": "Bearer",
  "ssoEnabled": true,
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ]
}
```

**Phản Hồi Lỗi:**
- `400 Bad Request` - SSO không được bật hoặc request không hợp lệ
- `403 Forbidden` - Ứng dụng không được phép hoặc cần xác minh thiết bị
- `429 Too Many Requests` - Vượt quá giới hạn tần suất

### Đăng Xuất SSO

Thực hiện đăng xuất SSO với tùy chọn đăng xuất toàn cục.

**Endpoint:** `POST /auth/sso/logout`

**Headers Bắt Buộc:**
```
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "globalLogout": true
}
```

**Response:**
```json
{
  "message": "Đăng xuất toàn cục thành công",
  "sessionsTerminated": 3
}
```

### Xác Minh Token

Xác minh tính hợp lệ của JWT token cho xác thực cross-domain.

**Endpoint:** `POST /auth/sso/verify`

**Request Body:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "application": "mail.yourcompany.com"
}
```

**Response:**
```json
{
  "valid": true,
  "payload": {
    "sub": "uuid-user-id",
    "email": "<EMAIL>",
    "username": "user123",
    "sessionId": "sso_1703000000_abc123def456",
    "deviceId": "device_fingerprint_hash",
    "roles": ["MANAGER", "USER"],
    "permissions": ["MAIL_MANAGEMENT_READ", "MAIL_MANAGEMENT_CREATE"],
    "aud": ["app.yourcompany.com", "mail.yourcompany.com"],
    "iss": "auth.yourcompany.com",
    "exp": 1703001800,
    "iat": 1703000900
  },
  "sessionValid": true
}
```

**Phản Hồi Lỗi:**
```json
{
  "valid": false,
  "reason": "Token đã bị blacklist",
  "sessionValid": false
}
```

### Làm Mới Token

Làm mới access token bằng refresh token.

**Endpoint:** `POST /auth/sso/refresh`

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 900
}
```

**Phản Hồi Lỗi:**
- `401 Unauthorized` - Refresh token không hợp lệ hoặc hết hạn
- `403 Forbidden` - Refresh token đã bị blacklist

## Endpoints Quản Lý Session

### Lấy Session Hiện Tại

Lấy thông tin session hiện tại.

**Endpoint:** `GET /auth/sso/session`

**Headers Bắt Buộc:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "sessionId": "sso_1703000000_abc123def456",
  "userId": "uuid-user-id",
  "deviceName": "Chrome trên Windows 11",
  "deviceType": "DESKTOP",
  "ipAddress": "*************",
  "location": "Hồ Chí Minh, Việt Nam",
  "isActive": true,
  "lastActivityAt": "2024-01-01T12:30:00.000Z",
  "expiresAt": "2024-01-01T20:30:00.000Z",
  "remainingMinutes": 420
}
```

### Lấy Tất Cả Sessions Của User

Lấy tất cả sessions đang hoạt động của user hiện tại.

**Endpoint:** `GET /auth/sso/sessions`

**Headers Bắt Buộc:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
[
  {
    "sessionId": "sso_1703000000_abc123def456",
    "userId": "uuid-user-id",
    "deviceName": "Chrome trên Windows 11",
    "deviceType": "DESKTOP",
    "ipAddress": "*************",
    "location": "Hồ Chí Minh, Việt Nam",
    "isActive": true,
    "lastActivityAt": "2024-01-01T12:30:00.000Z",
    "expiresAt": "2024-01-01T20:30:00.000Z",
    "remainingMinutes": 420,
    "isCurrent": true
  },
  {
    "sessionId": "sso_1703000001_def456ghi789",
    "userId": "uuid-user-id",
    "deviceName": "Safari trên iPhone",
    "deviceType": "MOBILE",
    "ipAddress": "*********",
    "location": "Hà Nội, Việt Nam",
    "isActive": true,
    "lastActivityAt": "2024-01-01T11:45:00.000Z",
    "expiresAt": "2024-01-01T19:45:00.000Z",
    "remainingMinutes": 375,
    "isCurrent": false
  }
]
```

### Kết Thúc Session Cụ Thể

Kết thúc một session cụ thể.

**Endpoint:** `DELETE /auth/sso/sessions/{sessionId}`

**Headers Bắt Buộc:**
```
Authorization: Bearer <access_token>
```

**Path Parameters:**
- `sessionId` - ID của session cần kết thúc

**Response:** `204 No Content`

**Phản Hồi Lỗi:**
- `404 Not Found` - Không tìm thấy session
- `403 Forbidden` - Không thể kết thúc session (không thuộc sở hữu của user)

### Kết Thúc Tất Cả Sessions Khác

Kết thúc tất cả sessions trừ session hiện tại.

**Endpoint:** `DELETE /auth/sso/sessions/all`

**Headers Bắt Buộc:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "message": "Tất cả sessions khác đã được kết thúc thành công",
  "sessionsTerminated": 2
}
```

## Endpoints Cấu Hình

### Lấy Cấu Hình SSO

Lấy cấu hình SSO công khai.

**Endpoint:** `GET /auth/sso/config`

**Response:**
```json
{
  "enabled": true,
  "baseDomain": "yourcompany.com",
  "allowedApplications": [
    "app.yourcompany.com",
    "mail.yourcompany.com",
    "core.yourcompany.com",
    "api.yourcompany.com"
  ],
  "sessionTimeout": 480,
  "maxConcurrentSessions": 5,
  "requireDeviceVerification": false
}
```

## Endpoints Quản Trị

### Lấy Thống Kê Blacklist

Lấy thống kê blacklist JWT.

**Endpoint:** `GET /auth/sso/blacklist/stats`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_READ`

**Response:**
```json
{
  "totalBlacklisted": 150,
  "byTokenType": {
    "ACCESS": 120,
    "REFRESH": 25,
    "SSO": 5
  },
  "byReason": {
    "USER_LOGOUT": 80,
    "GLOBAL_LOGOUT": 40,
    "SECURITY_BREACH": 15,
    "ADMIN_REVOKE": 10,
    "PASSWORD_CHANGE": 5
  },
  "recentRevocations": 25,
  "expiredTokens": 50
}
```

### Dọn Dẹp Dữ Liệu Hết Hạn

Dọn dẹp sessions và tokens hết hạn.

**Endpoint:** `POST /auth/sso/cleanup/sessions`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_MANAGE`

**Response:**
```json
{
  "message": "Dọn dẹp hoàn thành thành công",
  "cleanedSessions": 45,
  "cleanedTokens": 23
}
```

## Quản Lý Ứng Dụng SSO

### Tạo Ứng Dụng SSO

Tạo một ứng dụng SSO mới.

**Endpoint:** `POST /sso/applications`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_CREATE`

**Request Body:**
```json
{
  "name": "ANALYTICS_APP",
  "subdomain": "analytics",
  "displayName": "Bảng Điều Khiển Phân Tích",
  "description": "Nền tảng business intelligence và phân tích",
  "baseUrl": "https://analytics.yourcompany.com",
  "allowedOrigins": [
    "https://analytics.yourcompany.com",
    "https://app.yourcompany.com"
  ]
}
```

**Response:**
```json
{
  "id": "uuid-app-id",
  "name": "ANALYTICS_APP",
  "subdomain": "analytics",
  "displayName": "Bảng Điều Khiển Phân Tích",
  "description": "Nền tảng business intelligence và phân tích",
  "baseUrl": "https://analytics.yourcompany.com",
  "allowedOrigins": [
    "https://analytics.yourcompany.com",
    "https://app.yourcompany.com"
  ],
  "isActive": true,
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z"
}
```

### Lấy Danh Sách Ứng Dụng SSO

Lấy danh sách các ứng dụng SSO.

**Endpoint:** `GET /sso/applications`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_READ`

**Query Parameters:**
- `search` (tùy chọn) - Tìm kiếm theo tên, subdomain, hoặc tên hiển thị
- `isActive` (tùy chọn) - Lọc theo trạng thái hoạt động
- `page` (tùy chọn) - Số trang (mặc định: 1)
- `limit` (tùy chọn) - Số items mỗi trang (mặc định: 10)

**Response:**
```json
{
  "applications": [
    {
      "id": "uuid-app-id",
      "name": "MAIN_APP",
      "subdomain": "app",
      "displayName": "Ứng Dụng Chính",
      "description": "Ứng dụng chính với đầy đủ tính năng",
      "baseUrl": "https://app.yourcompany.com",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 4,
  "page": 1,
  "limit": 10,
  "totalPages": 1
}
```

### Cập Nhật Ứng Dụng SSO

Cập nhật một ứng dụng SSO hiện có.

**Endpoint:** `PUT /sso/applications/{id}`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_UPDATE`

**Path Parameters:**
- `id` - UUID của ứng dụng

**Request Body:**
```json
{
  "displayName": "Tên Ứng Dụng Đã Cập Nhật",
  "description": "Mô tả đã cập nhật",
  "allowedOrigins": [
    "https://app.yourcompany.com",
    "https://new-domain.yourcompany.com"
  ],
  "isActive": true
}
```

### Xóa Ứng Dụng SSO

Xóa một ứng dụng SSO.

**Endpoint:** `DELETE /sso/applications/{id}`

**Headers Bắt Buộc:**
```
Authorization: Bearer <admin_token>
```

**Quyền Yêu Cầu:** `SSO_MANAGEMENT_DELETE`

**Path Parameters:**
- `id` - UUID của ứng dụng

**Response:** `204 No Content`

## Phản Hồi Lỗi

### Định Dạng Lỗi Chuẩn

Tất cả phản hồi lỗi tuân theo định dạng này:

```json
{
  "statusCode": 400,
  "message": "Mô tả lỗi",
  "error": "Loại lỗi",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "path": "/auth/sso/login"
}
```

### Mã Trạng Thái HTTP Thường Gặp

- `200 OK` - Request thành công
- `201 Created` - Tài nguyên được tạo thành công
- `204 No Content` - Tài nguyên được xóa thành công
- `400 Bad Request` - Dữ liệu request không hợp lệ
- `401 Unauthorized` - Cần xác thực
- `403 Forbidden` - Không đủ quyền
- `404 Not Found` - Không tìm thấy tài nguyên
- `409 Conflict` - Tài nguyên đã tồn tại
- `429 Too Many Requests` - Vượt quá giới hạn tần suất
- `500 Internal Server Error` - Lỗi server

## Giới Hạn Tần Suất

Các endpoints SSO có giới hạn tần suất để ngăn chặn lạm dụng:

- **Endpoints xác thực**: 10 requests mỗi phút mỗi IP
- **Quản lý session**: 60 requests mỗi phút mỗi user
- **Endpoints quản trị**: 100 requests mỗi phút mỗi user

Headers giới hạn tần suất được bao gồm trong phản hồi:

```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 8
X-RateLimit-Reset: 1640995200
```

## Cấu Hình CORS

Hệ thống SSO hỗ trợ cross-origin requests với cấu hình sau:

```javascript
{
  origin: [
    'https://app.yourcompany.com',
    'https://mail.yourcompany.com',
    'https://core.yourcompany.com',
    'https://api.yourcompany.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Session-ID',
    'X-Device-ID',
    'X-Application'
  ],
  exposedHeaders: [
    'X-Session-ID',
    'X-Token-Expires',
    'X-Refresh-Token'
  ]
}
```

---

**Để biết chi tiết triển khai, tham khảo [Hướng Dẫn Triển Khai](./SSO_IMPLEMENTATION_GUIDE_vi.md).**
