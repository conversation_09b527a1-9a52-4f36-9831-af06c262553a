import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
}

export enum CampaignType {
  ONE_TIME = 'one_time',
  RECURRING = 'recurring',
  DRIP = 'drip',
  AUTOMATED = 'automated',
}

@Entity('email_campaigns')
@Index(['userId', 'status'])
@Index(['scheduledAt', 'status'])
export class EmailCampaign {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: CampaignType,
    default: CampaignType.ONE_TIME,
  })
  campaignType: CampaignType;

  @Column({
    type: 'enum',
    enum: CampaignStatus,
    default: CampaignStatus.DRAFT,
  })
  status: CampaignStatus;

  @Column()
  subject: string;

  @Column({ type: 'text' })
  htmlContent: string;

  @Column({ type: 'text', nullable: true })
  textContent: string;

  @Column({ type: 'jsonb' })
  recipients: Array<{
    email: string;
    name?: string;
    variables?: Record<string, any>;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  segmentation: {
    criteria?: any;
    tags?: string[];
    excludeList?: string[];
  };

  @Column({ nullable: true })
  scheduledAt: Date;

  @Column({ nullable: true })
  sentAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    trackOpens?: boolean;
    trackClicks?: boolean;
    unsubscribeLink?: boolean;
    replyTo?: string;
    fromName?: string;
    fromEmail?: string;
    timezone?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    totalSent?: number;
    delivered?: number;
    bounced?: number;
    opened?: number;
    clicked?: number;
    unsubscribed?: number;
    complained?: number;
    openRate?: number;
    clickRate?: number;
    bounceRate?: number;
  };

  @Column({ default: 0 })
  totalRecipients: number;

  @Column({ default: 0 })
  sentCount: number;

  @Column({ default: 0 })
  failedCount: number;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isScheduled(): boolean {
    return this.status === CampaignStatus.SCHEDULED && !!this.scheduledAt;
  }

  get isSent(): boolean {
    return this.status === CampaignStatus.SENT;
  }

  get canBeSent(): boolean {
    return [CampaignStatus.DRAFT, CampaignStatus.SCHEDULED].includes(this.status);
  }
}
