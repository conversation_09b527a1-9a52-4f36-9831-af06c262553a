# 🔧 Troubleshooting Guide

## 📋 Tổng quan

Comprehensive troubleshooting guide cho Delify Platform với **common issues**, **solutions**, và **debugging techniques** để resolve problems quickly và efficiently.

## 🚨 Common Issues & Solutions

### 1. Application Startup Issues

#### Issue: Port Already in Use
```bash
Error: listen EADDRINUSE :::3000
```

**Solutions:**
```bash
# Find process using port 3000
lsof -ti:3000

# Kill process
kill -9 $(lsof -ti:3000)

# Or use different port
PORT=3001 npm run start:dev

# Check all Node processes
ps aux | grep node
```

#### Issue: Module Not Found
```bash
Error: Cannot find module '@nestjs/core'
```

**Solutions:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear npm cache
npm cache clean --force

# Check Node.js version
node --version  # Should be >= 18.0.0
```

#### Issue: TypeScript Compilation Errors
```bash
Error: Cannot find name 'process'
```

**Solutions:**
```bash
# Install missing types
npm install --save-dev @types/node

# Check tsconfig.json
{
  "compilerOptions": {
    "types": ["node"]
  }
}

# Rebuild project
npm run build
```

### 2. Database Connection Issues

#### Issue: Database Connection Refused
```bash
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Solutions:**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Start PostgreSQL
sudo systemctl start postgresql

# Check connection manually
psql -h localhost -U delify_user -d delify_platform

# Verify environment variables
echo $DATABASE_HOST
echo $DATABASE_PORT
echo $DATABASE_USERNAME
```

#### Issue: Authentication Failed
```bash
Error: password authentication failed for user "delify_user"
```

**Solutions:**
```bash
# Reset user password
sudo -u postgres psql
ALTER USER delify_user WITH PASSWORD 'new_password';

# Check .env file
DATABASE_PASSWORD=your_actual_password

# Test connection
psql postgresql://delify_user:password@localhost:5432/delify_platform
```

#### Issue: Database Does Not Exist
```bash
Error: database "delify_platform" does not exist
```

**Solutions:**
```bash
# Create database
sudo -u postgres psql
CREATE DATABASE delify_platform;
GRANT ALL PRIVILEGES ON DATABASE delify_platform TO delify_user;

# Run migrations
npm run migration:run
```

### 3. Migration Issues

#### Issue: Migration Already Exists
```bash
Error: Migration "CreateUsersTable1640995200000" has already been executed
```

**Solutions:**
```bash
# Check migration status
npm run migration:show

# Revert last migration
npm run migration:revert

# Generate new migration with different name
npm run migration:generate -- --name CreateUsersTableV2
```

#### Issue: Migration Fails
```bash
Error: relation "users" already exists
```

**Solutions:**
```bash
# Check current database state
psql -d delify_platform -c "\dt"

# Drop problematic table (CAUTION: Data loss)
psql -d delify_platform -c "DROP TABLE IF EXISTS users CASCADE;"

# Or create migration to handle existing table
npm run migration:create -- --name HandleExistingUsers
```

### 4. Authentication Issues

#### Issue: JWT Token Invalid
```bash
Error: JsonWebTokenError: invalid token
```

**Solutions:**
```bash
# Check JWT_SECRET in .env
JWT_SECRET=your-super-secret-jwt-key-min-32-characters

# Verify token format
echo "eyJhbGciOiJIUzI1NiIs..." | base64 -d

# Clear browser storage and re-login
localStorage.clear()
```

#### Issue: Token Expired
```bash
Error: TokenExpiredError: jwt expired
```

**Solutions:**
```bash
# Implement token refresh logic
POST /api/v1/auth/refresh
{
  "refreshToken": "your-refresh-token"
}

# Check token expiration settings
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
```

### 5. AI Provider Issues

#### Issue: OLLAMA Server Not Running
```bash
Error: OLLAMA: Server not running at http://localhost:11434
```

**Solutions:**
```bash
# Start OLLAMA server
ollama serve

# Check OLLAMA status
curl http://localhost:11434/api/tags

# Verify OLLAMA_BASE_URL
echo $OLLAMA_BASE_URL

# Install OLLAMA if not installed
curl -fsSL https://ollama.ai/install.sh | sh
```

#### Issue: OpenAI API Key Invalid
```bash
Error: OpenAI API key is invalid
```

**Solutions:**
```bash
# Check API key format
echo $OPENAI_API_KEY  # Should start with sk-

# Test API key
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"

# Update .env file
OPENAI_API_KEY=sk-your-actual-api-key
```

#### Issue: Model Not Found
```bash
Error: Model 'llama2:7b' not found
```

**Solutions:**
```bash
# List available models
ollama list

# Pull missing model
ollama pull llama2:7b

# Check model status
curl http://localhost:11434/api/tags
```

### 6. Performance Issues

#### Issue: Slow Database Queries
```bash
Query took 5000ms to execute
```

**Solutions:**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Add missing indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_org_members_org_id ON organization_members(organization_id);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';
```

#### Issue: High Memory Usage
```bash
JavaScript heap out of memory
```

**Solutions:**
```bash
# Increase memory limit
node --max-old-space-size=4096 dist/main.js

# Check memory leaks
npm install -g clinic
clinic doctor -- node dist/main.js

# Monitor memory usage
docker stats delify-app
```

#### Issue: High CPU Usage
```bash
CPU usage consistently above 80%
```

**Solutions:**
```bash
# Profile application
node --prof dist/main.js

# Check for infinite loops
npm install -g clinic
clinic flame -- node dist/main.js

# Optimize database queries
# Add caching layer
# Implement rate limiting
```

## 🔍 Debugging Techniques

### 1. Application Debugging

#### Enable Debug Logging
```typescript
// main.ts
const app = await NestFactory.create(AppModule, {
  logger: ['log', 'debug', 'error', 'verbose', 'warn'],
});

// Or use environment variable
DEBUG=* npm run start:dev
```

#### VS Code Debugging
```json
// .vscode/launch.json
{
  "name": "Debug NestJS",
  "type": "node",
  "request": "launch",
  "program": "${workspaceFolder}/src/main.ts",
  "runtimeArgs": ["-r", "ts-node/register"],
  "env": {
    "NODE_ENV": "development"
  },
  "console": "integratedTerminal"
}
```

#### Add Debug Breakpoints
```typescript
// Add debugger statement
async createUser(userData: CreateUserDto): Promise<User> {
  debugger; // Execution will pause here
  console.log('Creating user:', userData);
  
  // Your code here
}
```

### 2. Database Debugging

#### Enable Query Logging
```typescript
// ormconfig.ts
export default {
  type: 'postgres',
  logging: true, // Enable SQL logging
  logger: 'advanced-console',
  // ... other config
};
```

#### Check Database Connections
```sql
-- Check active connections
SELECT * FROM pg_stat_activity WHERE datname = 'delify_platform';

-- Check connection limits
SHOW max_connections;

-- Check current connection count
SELECT count(*) FROM pg_stat_activity;
```

#### Analyze Query Performance
```sql
-- Enable query statistics
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### 3. API Debugging

#### Request/Response Logging
```typescript
// Add logging interceptor
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();
    
    console.log(`[${request.method}] ${request.url}`);
    console.log('Headers:', request.headers);
    console.log('Body:', request.body);
    
    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        console.log(`Request completed in ${duration}ms`);
      }),
    );
  }
}
```

#### Test API Endpoints
```bash
# Test with curl
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' \
  -v  # Verbose output

# Test with httpie
http POST localhost:3000/api/v1/auth/login \
  email=<EMAIL> \
  password=password

# Check API health
curl http://localhost:3000/health
```

## 📊 Monitoring & Diagnostics

### 1. Health Checks

#### Application Health
```bash
# Check application health
curl http://localhost:3000/health

# Check specific services
curl http://localhost:3000/health/database
curl http://localhost:3000/health/redis
```

#### System Health
```bash
# Check system resources
top
htop
free -h
df -h

# Check Docker containers
docker ps
docker stats
docker logs delify-app
```

### 2. Log Analysis

#### Application Logs
```bash
# View real-time logs
tail -f logs/application.log

# Search for errors
grep -i error logs/application.log

# Filter by timestamp
grep "2024-01-01" logs/application.log
```

#### Database Logs
```bash
# PostgreSQL logs location
tail -f /var/log/postgresql/postgresql-15-main.log

# Check for connection errors
grep "connection" /var/log/postgresql/postgresql-15-main.log
```

### 3. Performance Monitoring

#### Application Metrics
```typescript
// Add metrics collection
import { register, Counter, Histogram } from 'prom-client';

const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status'],
});

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route'],
});
```

#### Database Performance
```sql
-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
  indexrelname,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;
```

## 🚨 Emergency Procedures

### 1. Application Recovery

#### Quick Restart
```bash
# Docker restart
docker restart delify-app

# PM2 restart
pm2 restart delify-platform

# Kubernetes restart
kubectl rollout restart deployment/delify-platform
```

#### Rollback Deployment
```bash
# Docker rollback
docker service rollback delify-platform

# Kubernetes rollback
kubectl rollout undo deployment/delify-platform

# Git rollback
git revert HEAD
git push origin main
```

### 2. Database Recovery

#### Backup and Restore
```bash
# Create backup
pg_dump -h localhost -U delify_user delify_platform > backup.sql

# Restore from backup
psql -h localhost -U delify_user -d delify_platform < backup.sql

# Point-in-time recovery (if WAL enabled)
pg_basebackup -h localhost -U delify_user -D /backup/base
```

#### Migration Rollback
```bash
# Revert last migration
npm run migration:revert

# Check migration status
npm run migration:show

# Manual rollback (if needed)
psql -d delify_platform -c "DELETE FROM migrations WHERE name = 'MigrationName';"
```

## 📞 Getting Help

### 1. Internal Resources
- **Documentation**: Check relevant guide files
- **API Docs**: Visit `/api/v1/docs` for Swagger UI
- **Logs**: Check application and system logs
- **Health Checks**: Use `/health` endpoint

### 2. External Resources
- **NestJS Docs**: https://docs.nestjs.com/
- **TypeORM Docs**: https://typeorm.io/
- **PostgreSQL Docs**: https://www.postgresql.org/docs/
- **Stack Overflow**: Search for specific error messages

### 3. Escalation Process
1. **Check Documentation** - Review relevant guides
2. **Search Logs** - Look for error patterns
3. **Test Isolation** - Reproduce in minimal environment
4. **Team Consultation** - Discuss with team members
5. **External Support** - Contact vendor support if needed

## 🎯 Prevention Best Practices

### 1. Monitoring
- Set up comprehensive logging
- Implement health checks
- Monitor key metrics
- Set up alerts for critical issues

### 2. Testing
- Maintain high test coverage
- Run tests before deployment
- Test in staging environment
- Perform load testing

### 3. Documentation
- Keep troubleshooting guide updated
- Document known issues
- Maintain runbooks
- Share knowledge with team

**This troubleshooting guide helps quickly identify và resolve common issues trong Delify Platform development và production environments.** 🔧✨
