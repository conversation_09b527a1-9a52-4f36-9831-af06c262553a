import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { DataSource } from 'typeorm';

// Modules
import { RBACModule } from '../rbac.module';
import { AuthModule } from '../../auth/auth.module';
import { UsersModule } from '../../users/users.module';
import { CacheModule } from '../../cache/cache.module';

// Services
import { RBACService } from '../services/rbac.service';
import { RoleService } from '../services/role.service';
import { PermissionService } from '../services/permission.service';
import { AuthService } from '../../auth/auth.service';
import { UsersService } from '../../users/users.service';

// Entities
import { User, AccountType } from '../../users/entities/user.entity';
import { Role } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import { UserRole } from '../entities/user-role.entity';
import { RolePermission } from '../entities/role-permission.entity';

// DTOs
import { CreateRoleDto } from '../dto/role.dto';
import { CreatePermissionDto } from '../dto/permission.dto';
import { SystemModule, PermissionAction } from '../entities/permission.entity';

/**
 * RBAC Integration Tests - Kiểm thử tích hợp hệ thống RBAC
 * RBAC Integration Tests - Integration tests for RBAC system
 */
describe('RBAC Integration Tests', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let rbacService: RBACService;
  let roleService: RoleService;
  let permissionService: PermissionService;
  let authService: AuthService;
  let usersService: UsersService;

  // Test data
  let masterUser: User;
  let adminUser: User;
  let managerUser: User;
  let regularUser: User;
  let masterToken: string;
  let adminToken: string;
  let managerToken: string;
  let regularToken: string;

  let adminRole: Role;
  let managerRole: Role;
  let userRole: Role;
  let testPermission: Permission;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [__dirname + '/../**/*.entity{.ts,.js}'],
          synchronize: true,
          logging: false,
        }),
        RBACModule,
        AuthModule,
        UsersModule,
        CacheModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get services
    dataSource = moduleFixture.get<DataSource>(DataSource);
    rbacService = moduleFixture.get<RBACService>(RBACService);
    roleService = moduleFixture.get<RoleService>(RoleService);
    permissionService = moduleFixture.get<PermissionService>(PermissionService);
    authService = moduleFixture.get<AuthService>(AuthService);
    usersService = moduleFixture.get<UsersService>(UsersService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await dataSource.destroy();
    await app.close();
  });

  beforeEach(async () => {
    // Clear cache before each test
    await rbacService.invalidateAllCaches();
  });

  /**
   * Setup test data - Thiết lập dữ liệu test
   */
  async function setupTestData() {
    // Create test roles
    adminRole = await roleService.createRole({
      name: 'TEST_ADMIN',
      displayName: 'Test Admin',
      level: 1,
      isSystemRole: false,
    } as CreateRoleDto, 'system');

    managerRole = await roleService.createRole({
      name: 'TEST_MANAGER',
      displayName: 'Test Manager',
      level: 2,
      parentRoleId: adminRole.id,
      isSystemRole: false,
    } as CreateRoleDto, 'system');

    userRole = await roleService.createRole({
      name: 'TEST_USER',
      displayName: 'Test User',
      level: 3,
      parentRoleId: managerRole.id,
      isSystemRole: false,
    } as CreateRoleDto, 'system');

    // Create test permission
    testPermission = await permissionService.createPermission({
      module: SystemModule.USER_MANAGEMENT,
      action: PermissionAction.READ,
      description: 'Test permission for reading users',
    } as CreatePermissionDto, 'system');

    // Assign permission to roles
    await roleService.assignPermissionsToRole(
      adminRole.id,
      { permissionIds: [testPermission.id], replace: false },
      'system'
    );

    // Create test users
    masterUser = await usersService.create({
      email: '<EMAIL>',
      username: 'master',
      password: 'Password123!',
      firstName: 'Master',
      lastName: 'User',
      accountType: AccountType.PERSONAL,
    });

    adminUser = await usersService.create({
      email: '<EMAIL>',
      username: 'admin',
      password: 'Password123!',
      firstName: 'Admin',
      lastName: 'User',
      accountType: AccountType.PERSONAL,
    });

    managerUser = await usersService.create({
      email: '<EMAIL>',
      username: 'manager',
      password: 'Password123!',
      firstName: 'Manager',
      lastName: 'User',
      accountType: AccountType.PERSONAL,
    });

    regularUser = await usersService.create({
      email: '<EMAIL>',
      username: 'user',
      password: 'Password123!',
      firstName: 'Regular',
      lastName: 'User',
      accountType: AccountType.PERSONAL,
    });

    // Assign roles to users
    await rbacService.assignRoleToUser(adminUser.id, adminRole.id, 'system');
    await rbacService.assignRoleToUser(managerUser.id, managerRole.id, 'system');
    await rbacService.assignRoleToUser(regularUser.id, userRole.id, 'system');

    // Generate tokens
    const testDeviceInfo = {
      ip: '127.0.0.1',
      userAgent: 'Test-Agent/1.0',
      deviceName: 'Test Device',
      location: 'Test Location',
    };

    const masterTokens = await authService.login({
      emailOrUsername: '<EMAIL>',
      password: 'Password123!',
    }, testDeviceInfo);
    masterToken = (masterTokens as any).accessToken;

    const adminTokens = await authService.login({
      emailOrUsername: '<EMAIL>',
      password: 'Password123!',
    }, testDeviceInfo);
    adminToken = (adminTokens as any).accessToken;

    const managerTokens = await authService.login({
      emailOrUsername: '<EMAIL>',
      password: 'Password123!',
    }, testDeviceInfo);
    managerToken = (managerTokens as any).accessToken;

    const regularTokens = await authService.login({
      emailOrUsername: '<EMAIL>',
      password: 'Password123!',
    }, testDeviceInfo);
    regularToken = (regularTokens as any).accessToken;
  }

  describe('Role Management', () => {
    it('should create a new role with admin permissions', async () => {
      const createRoleDto: CreateRoleDto = {
        name: 'NEW_TEST_ROLE',
        displayName: 'New Test Role',
        description: 'A new test role',
        level: 4,
        parentRoleId: userRole.id,
      };

      const response = await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createRoleDto)
        .expect(201);

      expect(response.body.name).toBe(createRoleDto.name);
      expect(response.body.displayName).toBe(createRoleDto.displayName);
      expect(response.body.level).toBe(createRoleDto.level);
    });

    it('should not allow regular user to create roles', async () => {
      const createRoleDto: CreateRoleDto = {
        name: 'UNAUTHORIZED_ROLE',
        displayName: 'Unauthorized Role',
        level: 5,
      };

      await request(app.getHttpServer())
        .post('/roles')
        .set('Authorization', `Bearer ${regularToken}`)
        .send(createRoleDto)
        .expect(403);
    });

    it('should get roles list with pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.roles).toBeInstanceOf(Array);
      expect(response.body.total).toBeGreaterThan(0);
      expect(response.body.page).toBe(1);
      expect(response.body.limit).toBe(10);
    });

    it('should update role information', async () => {
      const updateData = {
        displayName: 'Updated Test Role',
        description: 'Updated description',
      };

      const response = await request(app.getHttpServer())
        .put(`/roles/${userRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.displayName).toBe(updateData.displayName);
      expect(response.body.description).toBe(updateData.description);
    });

    it('should assign permissions to role', async () => {
      const assignData = {
        permissionIds: [testPermission.id],
        replace: false,
      };

      await request(app.getHttpServer())
        .post(`/roles/${userRole.id}/permissions`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignData)
        .expect(200);

      // Verify permission was assigned
      const rolePermissions = await roleService.getRolePermissions(userRole.id);
      expect(rolePermissions.some(p => p.id === testPermission.id)).toBe(true);
    });
  });

  describe('Permission Management', () => {
    it('should create a new permission', async () => {
      const createPermissionDto: CreatePermissionDto = {
        module: SystemModule.CONTENT_MANAGEMENT,
        action: PermissionAction.CREATE,
        description: 'Test permission for creating content',
      };

      const response = await request(app.getHttpServer())
        .post('/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createPermissionDto)
        .expect(201);

      expect(response.body.module).toBe(createPermissionDto.module);
      expect(response.body.action).toBe(createPermissionDto.action);
      expect(response.body.code).toBe('CONTENT_MANAGEMENT_CREATE');
    });

    it('should check user permission correctly', async () => {
      const checkData = {
        userId: adminUser.id,
        permission: testPermission.code,
      };

      const response = await request(app.getHttpServer())
        .post('/permissions/check')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(checkData)
        .expect(200);

      expect(response.body.allowed).toBe(true);
      expect(response.body.reason).toContain('User has required permission');
    });

    it('should get permissions by module', async () => {
      const response = await request(app.getHttpServer())
        .get(`/permissions/module/${SystemModule.USER_MANAGEMENT}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.module).toBe(SystemModule.USER_MANAGEMENT);
      expect(response.body.permissions).toBeInstanceOf(Array);
    });

    it('should get system modules', async () => {
      const response = await request(app.getHttpServer())
        .get('/permissions/meta/modules')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.modules).toBeInstanceOf(Array);
      expect(response.body.modules).toContain(SystemModule.USER_MANAGEMENT);
    });
  });

  describe('Role Assignment', () => {
    it('should assign role to user', async () => {
      const assignData = {
        userId: regularUser.id,
        roleId: managerRole.id,
      };

      await request(app.getHttpServer())
        .post('/roles/assign')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(assignData)
        .expect(200);

      // Verify role was assigned
      const userRoles = await rbacService.getUserRoles(regularUser.id);
      expect(userRoles.some(r => r.id === managerRole.id)).toBe(true);
    });

    it('should revoke role from user', async () => {
      const revokeData = {
        userId: regularUser.id,
        roleId: userRole.id,
      };

      await request(app.getHttpServer())
        .post('/roles/revoke')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(revokeData)
        .expect(200);

      // Verify role was revoked
      const userRoles = await rbacService.getUserRoles(regularUser.id);
      expect(userRoles.some(r => r.id === userRole.id)).toBe(false);
    });

    it('should not allow lower level user to assign higher level role', async () => {
      const assignData = {
        userId: regularUser.id,
        roleId: adminRole.id,
      };

      await request(app.getHttpServer())
        .post('/roles/assign')
        .set('Authorization', `Bearer ${managerToken}`)
        .send(assignData)
        .expect(403);
    });
  });

  describe('Permission Checking', () => {
    it('should allow access with correct permission', async () => {
      const result = await rbacService.checkPermission(
        adminUser.id,
        testPermission.code
      );

      expect(result.allowed).toBe(true);
      expect(result.reason).toContain('User has required permission');
    });

    it('should deny access without permission', async () => {
      const result = await rbacService.checkPermission(
        regularUser.id,
        testPermission.code
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('User lacks required permission');
    });

    it('should cache permission results', async () => {
      // First call
      const start1 = Date.now();
      await rbacService.checkPermission(adminUser.id, testPermission.code);
      const time1 = Date.now() - start1;

      // Second call (should be faster due to cache)
      const start2 = Date.now();
      await rbacService.checkPermission(adminUser.id, testPermission.code);
      const time2 = Date.now() - start2;

      expect(time2).toBeLessThan(time1);
    });
  });

  describe('Role Hierarchy', () => {
    it('should inherit permissions from parent roles', async () => {
      // Manager should inherit permissions from admin role
      const result = await rbacService.checkPermission(
        managerUser.id,
        testPermission.code
      );

      expect(result.allowed).toBe(true);
    });

    it('should get role hierarchy tree', async () => {
      const response = await request(app.getHttpServer())
        .get('/roles/hierarchy/tree')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.hierarchy).toBeInstanceOf(Array);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid role ID', async () => {
      await request(app.getHttpServer())
        .get('/roles/invalid-uuid')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);
    });

    it('should handle non-existent role', async () => {
      const fakeUuid = '00000000-0000-0000-0000-000000000000';
      
      await request(app.getHttpServer())
        .get(`/roles/${fakeUuid}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should handle unauthorized access', async () => {
      await request(app.getHttpServer())
        .post('/roles')
        .send({ name: 'TEST' })
        .expect(401);
    });
  });

  describe('Cache Management', () => {
    it('should invalidate cache when role changes', async () => {
      // Get initial permissions
      const initialPermissions = await rbacService.getUserPermissions(regularUser.id);
      
      // Assign new role
      await rbacService.assignRoleToUser(regularUser.id, adminRole.id, 'system');
      
      // Get updated permissions
      const updatedPermissions = await rbacService.getUserPermissions(regularUser.id);
      
      expect(updatedPermissions.permissions.length).toBeGreaterThan(
        initialPermissions.permissions.length
      );
    });

    it('should handle cache invalidation', async () => {
      await rbacService.invalidateUserCache(adminUser.id);
      
      // Should still work after cache invalidation
      const result = await rbacService.checkPermission(
        adminUser.id,
        testPermission.code
      );
      
      expect(result.allowed).toBe(true);
    });
  });
});
