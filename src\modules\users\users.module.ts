import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { UserDeviceService } from './services/user-device.service';
import { User } from './entities/user.entity';
import { UserDevice } from './entities/user-device.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserDevice])
  ],
  controllers: [UsersController],
  providers: [UsersService, UserDeviceService],
  exports: [UsersService, UserDeviceService],
})
export class UsersModule {}
