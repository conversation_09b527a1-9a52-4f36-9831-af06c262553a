import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheModule } from './cache.module';
import { CacheService } from './services/cache.service';

describe('CacheModule', () => {
  let module: TestingModule;
  let cacheService: CacheService;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        CacheModule.forRoot(),
      ],
    }).compile();

    cacheService = module.get<CacheService>(CacheService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(cacheService).toBeDefined();
  });

  it('should provide CacheService', () => {
    expect(cacheService).toBeInstanceOf(CacheService);
  });

  describe('CacheService', () => {
    const testKey = 'test:cache:key';
    const testValue = { data: 'test', timestamp: Date.now() };

    afterEach(async () => {
      // Clean up test data
      try {
        await cacheService.del(testKey);
      } catch (error) {
        // Ignore cleanup errors
      }
    });

    it('should set and get cache values', async () => {
      await cacheService.set(testKey, testValue, { ttl: 60 });
      const retrieved = await cacheService.get(testKey);
      
      expect(retrieved).toEqual(testValue);
    });

    it('should return undefined for non-existent keys', async () => {
      const retrieved = await cacheService.get('non-existent-key');
      expect(retrieved).toBeUndefined();
    });

    it('should check if key exists', async () => {
      await cacheService.set(testKey, testValue, { ttl: 60 });
      
      const exists = await cacheService.exists(testKey);
      expect(exists).toBe(true);
      
      const notExists = await cacheService.exists('non-existent-key');
      expect(notExists).toBe(false);
    });

    it('should delete cache values', async () => {
      await cacheService.set(testKey, testValue, { ttl: 60 });
      await cacheService.del(testKey);
      
      const retrieved = await cacheService.get(testKey);
      expect(retrieved).toBeUndefined();
    });

    it('should use getOrSet pattern', async () => {
      const factory = jest.fn().mockResolvedValue(testValue);
      
      // First call should execute factory
      const result1 = await cacheService.getOrSet(testKey, factory, { ttl: 60 });
      expect(result1).toEqual(testValue);
      expect(factory).toHaveBeenCalledTimes(1);
      
      // Second call should use cache
      const result2 = await cacheService.getOrSet(testKey, factory, { ttl: 60 });
      expect(result2).toEqual(testValue);
      expect(factory).toHaveBeenCalledTimes(1); // Should not be called again
    });

    it('should perform health check', async () => {
      const isHealthy = await cacheService.healthCheck();
      expect(typeof isHealthy).toBe('boolean');
    });

    it('should get statistics', () => {
      const stats = cacheService.getStats();
      expect(stats).toHaveProperty('hits');
      expect(stats).toHaveProperty('misses');
      expect(typeof stats.hits).toBe('number');
      expect(typeof stats.misses).toBe('number');
    });

    it('should reset statistics', () => {
      cacheService.resetStats();
      const stats = cacheService.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
    });
  });
});
