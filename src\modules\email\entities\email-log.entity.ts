import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum EmailStatus {
  QUEUED = 'queued',
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  BOUNCED = 'bounced',
  FAILED = 'failed',
  OPENED = 'opened',
  CLICKED = 'clicked',
  UNSUBSCRIBED = 'unsubscribed',
  COMPLAINED = 'complained',
}

@Entity('email_logs')
@Index(['userId', 'status'])
@Index(['recipientEmail', 'status'])
@Index(['campaignId', 'status'])
export class EmailLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ nullable: true })
  campaignId: string;

  @Column({ nullable: true })
  templateId: string;

  @Column()
  recipientEmail: string;

  @Column({ nullable: true })
  recipientName: string;

  @Column()
  subject: string;

  @Column({
    type: 'enum',
    enum: EmailStatus,
    default: EmailStatus.QUEUED,
  })
  status: EmailStatus;

  @Column({ nullable: true })
  messageId: string; // Provider message ID

  @Column({ nullable: true })
  providerResponse: string;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ nullable: true })
  sentAt: Date;

  @Column({ nullable: true })
  deliveredAt: Date;

  @Column({ nullable: true })
  openedAt: Date;

  @Column({ nullable: true })
  clickedAt: Date;

  @Column({ nullable: true })
  bouncedAt: Date;

  @Column({ nullable: true })
  unsubscribedAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  events: Array<{
    type: string;
    timestamp: string;
    data?: any;
  }>;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    location?: string;
    device?: string;
    clickedLinks?: string[];
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get wasOpened(): boolean {
    return this.status === EmailStatus.OPENED || !!this.openedAt;
  }

  get wasClicked(): boolean {
    return this.status === EmailStatus.CLICKED || !!this.clickedAt;
  }

  get wasDelivered(): boolean {
    return [EmailStatus.DELIVERED, EmailStatus.OPENED, EmailStatus.CLICKED].includes(this.status);
  }

  get hasBounced(): boolean {
    return this.status === EmailStatus.BOUNCED;
  }
}
