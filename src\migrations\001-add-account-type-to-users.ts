import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddAccountTypeToUsers1700000001 implements MigrationInterface {
  name = 'AddAccountTypeToUsers1700000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add accountType column to users table
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'accountType',
        type: 'enum',
        enum: ['personal', 'business'],
        default: "'personal'",
        isNullable: false,
      })
    );

    // Update existing users to have personal account type by default
    await queryRunner.query(`
      UPDATE users 
      SET "accountType" = 'personal' 
      WHERE "accountType" IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('users', 'accountType');
  }
}
