# Hướng Dẫn Toàn Diện Về Luồng <PERSON>ác <PERSON> (Authentication Flow)

## Tổng Quan

Hệ thống xác thực của chúng ta hỗ trợ 4 phương thức bảo mật khác nhau và sử dụng refresh token để tăng cường bảo mật. Tài liệu này mô tả chi tiết tất cả các luồng xác thực và quản lý token.

## Mục Lục

1. [<PERSON><PERSON> Đồ Luồng Xác Thực](#sơ-đồ-luồng-xác-thực)
2. [Quy Trình Từng Bước](#quy-trình-từng-bước)
3. [Chuỗi API Endpoints](#chuỗi-api-endpoints)
4. [Vòng Đời Quản Lý Token](#vòng-đời-quản-lý-token)
5. [Luồng Phương Thức Bả<PERSON>](#luồng-phương-thức-bả<PERSON>-mật)
6. [<PERSON><PERSON> Lý Lỗi](#xử-lý-lỗi)
7. [Thay Đổi Trạng Thái Database](#thay-đổi-trạng-thái-database)

## Sơ Đồ Luồng Xác Thực

### 1. Luồng Đăng Ký (Registration Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant Device as Device Service

    C->>API: POST /auth/register
    API->>DB: Tạo user mới
    API->>Device: Tạo device session
    API->>API: Tạo access & refresh tokens
    API->>DB: Lưu refresh token
    API->>Email: Gửi email chào mừng (optional)
    API->>C: Trả về tokens + user info
```

### 2. Luồng Đăng Nhập Cơ Bản (Basic Login Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant VS as Verification Service

    C->>API: POST /auth/login
    API->>DB: Kiểm tra credentials
    
    alt Security Method = DISABLED
        API->>API: Tạo tokens ngay lập tức
        API->>C: Trả về complete login response
    else Security Method Enabled
        API->>VS: Tạo verification session
        API->>C: Trả về partial login response
        Note over C: Client cần verification code
    end
```

### 3. Luồng Xác Minh Đăng Nhập (Login Verification Flow)

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database
    participant Email as Email Service
    participant VS as Verification Service

    C->>API: POST /auth/verify-login
    API->>VS: Kiểm tra verification session
    API->>DB: Lấy thông tin user
    
    alt EMAIL_VERIFICATION
        API->>DB: Kiểm tra verification code
    else TWO_FACTOR_AUTH
        API->>VS: Verify TOTP code
    else FIXED_CODE
        API->>VS: Verify fixed code
    end
    
    API->>API: Tạo access & refresh tokens
    API->>DB: Lưu refresh token
    API->>C: Trả về complete login response
```

### 4. Luồng Refresh Token

```mermaid
sequenceDiagram
    participant C as Client
    participant API as Auth API
    participant DB as Database

    C->>API: POST /auth/refresh
    API->>API: Verify refresh token JWT
    API->>DB: Tìm user bằng refresh token
    API->>DB: Kiểm tra token expiration
    API->>API: Tạo tokens mới
    API->>DB: Cập nhật refresh token mới
    API->>C: Trả về tokens mới
```

## Quy Trình Từng Bước

### Phương Thức 1: DISABLED (Không Bảo Mật)

**Đặc điểm:**
- Đăng nhập trực tiếp bằng email/username + password
- Không cần verification code
- Phù hợp cho môi trường development hoặc ứng dụng nội bộ

**Quy trình:**
1. Client gửi credentials
2. Server kiểm tra password
3. Tạo tokens ngay lập tức
4. Trả về complete login response

```javascript
// Request
POST /auth/login
{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}

// Response (Success)
{
  "user": { ... },
  "accessToken": "eyJ...",
  "refreshToken": "eyJ...",
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

### Phương Thức 2: EMAIL_VERIFICATION (Xác Minh Email)

**Đặc điểm:**
- Gửi mã xác minh 6 chữ số qua email
- Mã có hiệu lực 5 phút
- Có thể gửi lại mã nếu cần

**Quy trình:**
1. Client gửi credentials
2. Server kiểm tra password
3. Tạo verification code và gửi email
4. Trả về partial login response với sessionId
5. Client gửi verification code
6. Server xác minh và tạo tokens

```javascript
// Bước 1: Login
POST /auth/login
{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}

// Response (Partial)
{
  "requiresVerification": true,
  "sessionId": "session-123",
  "message": "Verification code sent to your email"
}

// Bước 2: Verify
POST /auth/verify-login
{
  "sessionId": "session-123",
  "verificationCode": "123456"
}

// Response (Complete)
{
  "user": { ... },
  "accessToken": "eyJ...",
  "refreshToken": "eyJ...",
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

### Phương Thức 3: TWO_FACTOR_AUTH (Xác Thực 2 Yếu Tố)

**Đặc điểm:**
- Sử dụng TOTP (Time-based One-Time Password)
- Tương thích với Google Authenticator, Authy
- Mã thay đổi mỗi 30 giây

**Quy trình Setup:**
1. Client yêu cầu enable 2FA
2. Server tạo secret và QR code
3. Client scan QR code vào authenticator app
4. Client gửi verification code để confirm
5. Server enable 2FA cho user

```javascript
// Bước 1: Yêu cầu setup 2FA
POST /auth/security-method
{
  "securityMethod": "two_factor_auth"
}

// Response
{
  "securityMethod": "disabled", // Chưa enable
  "twoFactorEnabled": false,
  "qrCodeUrl": "data:image/png;base64,...",
  "manualEntryKey": "JBSWY3DPEHPK3PXP"
}

// Bước 2: Confirm setup
POST /auth/security-method
{
  "securityMethod": "two_factor_auth",
  "verificationCode": "123456"
}

// Response
{
  "securityMethod": "two_factor_auth",
  "twoFactorEnabled": true
}
```

**Quy trình Login:**
1. Client gửi credentials
2. Server trả về partial login
3. Client nhập mã từ authenticator app
4. Server verify TOTP và tạo tokens

### Phương Thức 4: FIXED_CODE (Mã Cố Định)

**Đặc điểm:**
- Mã 6-8 chữ số do user tự đặt
- Có thể tái sử dụng nhiều lần
- Validation chống weak patterns

**Quy trình Setup:**
```javascript
POST /auth/security-method
{
  "securityMethod": "fixed_code",
  "fixedCode": "789123"
}
```

**Validation Rules:**
- Độ dài: 6-8 chữ số
- Không được là số tuần tự (123456, 654321)
- Không được lặp lại (111111, 222222)
- Không được là pattern phổ biến (000000, 123123)

## Chuỗi API Endpoints

### Đăng Ký Hoàn Chỉnh
```
1. POST /auth/register
   → Tạo user + tokens ngay lập tức
```

### Đăng Nhập với EMAIL_VERIFICATION
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login
   → Complete response + tokens
```

### Đăng Nhập với TWO_FACTOR_AUTH
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login (với TOTP code)
   → Complete response + tokens
```

### Đăng Nhập với FIXED_CODE
```
1. POST /auth/login
   → Partial response + sessionId
2. POST /auth/verify-login (với fixed code)
   → Complete response + tokens
```

### Refresh Token Flow
```
1. POST /auth/refresh
   → New access token + new refresh token
```

### Logout Flow
```
1. POST /auth/revoke (với refresh token)
   → Revoke specific token
   
HOẶC

1. POST /auth/revoke-all (với access token)
   → Revoke tất cả tokens của user
```

---

**Tài liệu này cung cấp hướng dẫn toàn diện về hệ thống xác thực. Để biết thêm chi tiết về implementation, tham khảo [Enhanced Authentication Documentation](../features/ENHANCED_AUTHENTICATION.md).**
