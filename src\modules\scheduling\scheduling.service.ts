import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Appointment } from './entities/appointment.entity';
import { TimeSlot } from './entities/time-slot.entity';
import { ServiceType } from './entities/service-type.entity';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class SchedulingService {
  constructor(
    @InjectRepository(Appointment)
    private appointmentRepository: Repository<Appointment>,
    @InjectRepository(TimeSlot)
    private timeSlotRepository: Repository<TimeSlot>,
    @InjectRepository(ServiceType)
    private serviceTypeRepository: Repository<ServiceType>,
    private logger: LoggerService,
  ) {}

  async getAppointments(userId: string) {
    return this.appointmentRepository.find({
      where: { userId },
      relations: ['serviceType'],
      order: { scheduledAt: 'ASC' },
    });
  }

  async createAppointment(userId: string, data: any) {
    this.logger.logWithContext(`Appointment creation requested for user: ${userId}`, 'SchedulingService');
    return { message: 'Appointment created successfully' };
  }

  async getAvailableTimeSlots(userId: string) {
    return this.timeSlotRepository.find({
      where: { userId, isAvailable: true },
      order: { startTime: 'ASC' },
    });
  }

  async createTimeSlot(userId: string, data: any) {
    this.logger.logWithContext(`Time slot creation requested for user: ${userId}`, 'SchedulingService');
    return { message: 'Time slot created successfully' };
  }

  async getServices(userId: string) {
    return this.serviceTypeRepository.find({
      where: { userId },
      order: { name: 'ASC' },
    });
  }

  async createService(userId: string, data: any) {
    this.logger.logWithContext(`Service creation requested for user: ${userId}`, 'SchedulingService');
    return { message: 'Service created successfully' };
  }
}
