# Hướng Dẫn Khắc Phục Sự Cố SSO

## Tổng Quan

Hướng dẫn này cung cấp các bước khắc phục sự cố toàn diện cho các vấn đề SSO thường gặp, quy trình chẩn đo<PERSON>, và chiến lư<PERSON>c giải quyết cho quản trị viên và nhà phát triển.

## Danh Sách Kiểm Tra Chẩn Đoán <PERSON>hanh

Trước khi đi vào các vấn đề cụ thể, hãy kiểm tra danh sách này:

- [ ] SSO đã được bật (`SSO_ENABLED=true`)
- [ ] Biến môi trường được cấu hình đúng
- [ ] Migration database đã được chạy
- [ ] Redis đang chạy và có thể truy cập
- [ ] JWT secrets nhất quán trên tất cả services
- [ ] CORS được cấu hình đúng cho tất cả domains
- [ ] Cookies trình duyệt đã được bật
- [ ] User có quyền phù hợp

## Vấn Đề Thường Gặp và Giải Pháp

### 1. Vấn Đề Xác Thực

#### Vấn Đề: Lỗi "SSO not enabled"

**Triệu Chứng:**
- Users nhận thông báo "SSO is not enabled"
- API trả về 400 Bad Request với thông báo SSO disabled

**Chẩn Đoán:**
```bash
# Kiểm tra cấu hình SSO
curl -X GET http://localhost:3000/auth/sso/config

# Phản hồi mong đợi nên hiển thị "enabled": true
```

**Giải Pháp:**
1. **Cấu Hình Môi Trường:**
   ```env
   SSO_ENABLED=true
   ```

2. **Khởi Động Lại Ứng Dụng:**
   ```bash
   npm run start:dev
   ```

3. **Xác Minh Configuration Service:**
   ```typescript
   // Kiểm tra SSOConfigService.isEnabled()
   const config = ssoConfigService.getConfig();
   console.log('SSO Enabled:', config.enabled);
   ```

#### Vấn Đề: Xác Minh Token Thất Bại

**Triệu Chứng:**
- Lỗi "Token is invalid"
- Xác thực cross-domain không hoạt động
- Users bị chuyển hướng đến login liên tục

**Chẩn Đoán:**
```bash
# Test xác minh token
curl -X POST http://localhost:3000/auth/sso/verify \
  -H "Content-Type: application/json" \
  -d '{"token":"your-jwt-token","application":"app.yourcompany.com"}'
```

**Giải Pháp:**
1. **Kiểm Tra Tính Nhất Quán JWT Secret:**
   ```env
   # Đảm bảo cùng JWT_SECRET trên tất cả services
   JWT_SECRET=your_consistent_secret_key
   ```

2. **Xác Minh Định Dạng Token:**
   ```typescript
   // Decode token để kiểm tra cấu trúc
   const decoded = jwt.decode(token);
   console.log('Token payload:', decoded);
   ```

3. **Kiểm Tra Token Blacklist:**
   ```bash
   # Kiểm tra token có bị blacklist không
   curl -X GET http://localhost:3000/auth/sso/blacklist/stats \
     -H "Authorization: Bearer admin-token"
   ```

#### Vấn Đề: Session Hết Hạn Ngay Lập Tức

**Triệu Chứng:**
- Sessions hết hạn ngay sau khi login
- Thông báo "Session expired" xuất hiện ngay lập tức
- Users không thể duy trì đăng nhập

**Chẩn Đoán:**
```bash
# Kiểm tra thông tin session
curl -X GET http://localhost:3000/auth/sso/session \
  -H "Authorization: Bearer access-token"
```

**Giải Pháp:**
1. **Kiểm Tra Cấu Hình Session Timeout:**
   ```env
   SSO_SESSION_TIMEOUT=480  # 8 giờ tính bằng phút
   ```

2. **Xác Minh Đồng Hồ Hệ Thống:**
   ```bash
   # Đảm bảo thời gian server đúng
   date
   timedatectl status
   ```

3. **Kiểm Tra Kết Nối Redis:**
   ```bash
   # Test kết nối Redis
   redis-cli ping
   # Nên trả về PONG
   ```

### 2. Vấn Đề Cross-Domain

#### Vấn Đề: Cookies Không Được Chia Sẻ Giữa Subdomains

**Triệu Chứng:**
- Login hoạt động trên domain chính nhưng không trên subdomains
- Users cần login riêng biệt cho từng subdomain
- Cross-domain API calls thất bại

**Chẩn Đoán:**
```javascript
// Kiểm tra cookies trong browser developer tools
document.cookie
// Nên hiển thị cookies với domain=.yourcompany.com
```

**Giải Pháp:**
1. **Cấu Hình Cookie Domain:**
   ```env
   SSO_COOKIE_DOMAIN=.yourcompany.com
   ```

2. **Xác Minh Cấu Hình CORS:**
   ```typescript
   // Kiểm tra cài đặt CORS
   const corsConfig = ssoConfigService.getCORSConfig();
   console.log('CORS Origins:', corsConfig.origin);
   ```

3. **Cài Đặt Trình Duyệt:**
   - Bật third-party cookies
   - Thêm `*.yourcompany.com` vào trusted sites
   - Vô hiệu hóa ad blockers cho company domains

#### Vấn Đề: Lỗi CORS

**Triệu Chứng:**
- Lỗi "CORS policy" trong browser console
- Cross-domain requests bị chặn
- Preflight requests thất bại

**Chẩn Đoán:**
```bash
# Test CORS với curl
curl -X OPTIONS http://localhost:3000/auth/sso/login \
  -H "Origin: https://app.yourcompany.com" \
  -H "Access-Control-Request-Method: POST" \
  -v
```

**Giải Pháp:**
1. **Cập Nhật Cấu Hình CORS:**
   ```env
   SSO_ALLOWED_APPLICATIONS=app.yourcompany.com,mail.yourcompany.com,core.yourcompany.com
   ```

2. **Kiểm Tra Đăng Ký Application:**
   ```bash
   # Xác minh applications đã được đăng ký
   curl -X GET http://localhost:3000/sso/applications \
     -H "Authorization: Bearer admin-token"
   ```

### 3. Vấn Đề Thiết Bị và Bảo Mật

#### Vấn Đề: Thiết Bị Không Được Nhận Diện

**Triệu Chứng:**
- Thông báo "Device verification required"
- Users bị chặn trên thiết bị quen thuộc
- Cảnh báo bảo mật quá mức

**Chẩn Đoán:**
```typescript
// Kiểm tra tạo device fingerprint
const deviceInfo = extractDeviceInfo(request);
const fingerprint = deviceFingerprintService.generateFingerprint(deviceInfo);
console.log('Device fingerprint:', fingerprint);
```

**Giải Pháp:**
1. **Điều Chỉnh Cài Đặt Device Verification:**
   ```env
   SSO_REQUIRE_DEVICE_VERIFICATION=false
   ```

2. **Xóa Device Cache:**
   ```bash
   # Xóa Redis device cache
   redis-cli FLUSHDB
   ```

3. **Xem Xét Logic Device Fingerprinting:**
   ```typescript
   // Kiểm tra phát hiện thiết bị đáng ngờ
   const analysis = deviceFingerprintService.analyzeDevice(deviceInfo);
   console.log('Device analysis:', analysis);
   ```

#### Vấn Đề: False Positives Hoạt Động Đáng Ngờ

**Triệu Chứng:**
- Users hợp pháp bị đánh dấu đáng ngờ
- Yêu cầu xác minh thiết bị thường xuyên
- Sessions bị kết thúc bất ngờ

**Chẩn Đoán:**
```bash
# Kiểm tra audit logs cho hoạt động đáng ngờ
curl -X GET http://localhost:3000/auth/sso/audit/security \
  -H "Authorization: Bearer admin-token"
```

**Giải Pháp:**
1. **Điều Chỉnh Ngưỡng Bảo Mật:**
   ```typescript
   // Điều chỉnh ngưỡng trust score
   const trustThreshold = 70; // Thấp hơn để kiểm tra ít nghiêm ngặt hơn
   ```

2. **Whitelist Dải IP Đã Biết:**
   ```typescript
   // Thêm dải IP công ty vào whitelist
   const companyIpRanges = ['***********/24', '10.0.0.0/8'];
   ```

### 4. Vấn Đề Hiệu Suất

#### Vấn Đề: Xác Thực Chậm

**Triệu Chứng:**
- Login mất hơn 5 giây
- Token verification timeouts
- Trải nghiệm người dùng kém

**Chẩn Đoán:**
```bash
# Kiểm tra thời gian phản hồi
time curl -X POST http://localhost:3000/auth/sso/login \
  -H "Content-Type: application/json" \
  -d '{"userId":"test","deviceInfo":{}}'
```

**Giải Pháp:**
1. **Tối Ưu Database Queries:**
   ```sql
   -- Kiểm tra indexes bị thiếu
   EXPLAIN ANALYZE SELECT * FROM user_sessions WHERE user_id = 'uuid';
   ```

2. **Tối Ưu Redis Cache:**
   ```bash
   # Kiểm tra hiệu suất Redis
   redis-cli --latency-history -i 1
   ```

3. **Điều Chỉnh Connection Pool:**
   ```typescript
   // Tăng database connection pool
   {
     type: 'postgres',
     poolSize: 20,
     extra: {
       connectionLimit: 20
     }
   }
   ```

#### Vấn Đề: Sử Dụng Memory Cao

**Triệu Chứng:**
- Tiêu thụ memory server tăng theo thời gian
- Lỗi out of memory
- Hiệu suất giảm

**Chẩn Đoán:**
```bash
# Giám sát sử dụng memory
top -p $(pgrep node)
# Kiểm tra Redis memory usage
redis-cli info memory
```

**Giải Pháp:**
1. **Triển Khai Cache Cleanup:**
   ```typescript
   // Lên lịch cleanup thường xuyên
   @Cron('0 */6 * * *') // Mỗi 6 giờ
   async cleanupExpiredData() {
     await sessionService.cleanupExpiredSessions();
     await blacklistService.cleanupExpiredEntries();
   }
   ```

2. **Tối Ưu Cache TTL:**
   ```env
   # Giảm cache TTL nếu cần
   CACHE_TTL=900  # 15 phút
   ```

### 5. Vấn Đề Database

#### Vấn Đề: Migration Thất Bại

**Triệu Chứng:**
- Bảng SSO không được tạo
- Lỗi foreign key constraint
- Migration rollback thất bại

**Chẩn Đoán:**
```bash
# Kiểm tra trạng thái migration
npm run migration:show

# Kiểm tra kết nối database
npm run migration:run -- --dry-run
```

**Giải Pháp:**
1. **Migration Thủ Công:**
   ```bash
   # Chạy migration cụ thể
   npm run migration:run -- --transaction=each
   ```

2. **Kiểm Tra Quyền Database:**
   ```sql
   -- Xác minh user có quyền cần thiết
   GRANT CREATE, ALTER, DROP ON DATABASE your_db TO your_user;
   ```

3. **Rollback và Thử Lại:**
   ```bash
   # Rollback migration thất bại
   npm run migration:revert
   # Thử lại migration
   npm run migration:run
   ```

#### Vấn Đề: Seed Data

**Triệu Chứng:**
- Applications mặc định không được tạo
- Permissions không được gán
- Role-permission mappings bị thiếu

**Chẩn Đoán:**
```bash
# Kiểm tra seed data
npm run seed:sso -- --dry-run
```

**Giải Pháp:**
1. **Thực Thi Seed Thủ Công:**
   ```bash
   # Chạy seed với verbose output
   npm run seed:sso -- --verbose
   ```

2. **Xác Minh Tính Toàn Vẹn Dữ Liệu:**
   ```sql
   -- Kiểm tra dữ liệu đã tạo
   SELECT * FROM sso_applications;
   SELECT * FROM permissions WHERE application != 'MAIN_APP';
   ```

## Công Cụ và Lệnh Chẩn Đoán

### Lệnh Kiểm Tra Sức Khỏe

```bash
# Kiểm Tra Sức Khỏe Hệ Thống SSO
curl -X GET http://localhost:3000/auth/sso/config

# Kết Nối Database
npm run migration:show

# Kết Nối Redis
redis-cli ping

# Trạng Thái Application
curl -X GET http://localhost:3000/health
```

### Phân Tích Log

```bash
# Application Logs
tail -f logs/application.log | grep SSO

# Audit Logs
curl -X GET http://localhost:3000/auth/sso/audit/stats \
  -H "Authorization: Bearer admin-token"

# Error Logs
grep -i error logs/application.log | grep SSO
```

### Giám Sát Hiệu Suất

```bash
# Test Thời Gian Phản Hồi
ab -n 100 -c 10 http://localhost:3000/auth/sso/config

# Sử Dụng Memory
ps aux | grep node
free -h

# Hiệu Suất Database
EXPLAIN ANALYZE SELECT * FROM user_sessions WHERE is_active = true;
```

## Vấn Đề Theo Môi Trường

### Môi Trường Development

**Vấn Đề Thường Gặp:**
- Yêu cầu HTTPS trong development
- Vấn đề self-signed certificate
- Cấu hình local domain

**Giải Pháp:**
```env
# Development overrides
SSO_SECURE_ONLY=false
NODE_TLS_REJECT_UNAUTHORIZED=0
```

### Môi Trường Production

**Vấn Đề Thường Gặp:**
- Cấu hình load balancer
- Vấn đề SSL termination
- Đồng bộ hóa environment variables

**Giải Pháp:**
1. **Cấu Hình Load Balancer:**
   ```nginx
   # Cấu hình Nginx cho SSO
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   ```

2. **Cấu Hình SSL:**
   ```env
   SSO_SECURE_ONLY=true
   SSO_SAME_SITE=lax
   ```

## Quy Trình Khẩn Cấp

### Reset SSO Hoàn Toàn

```bash
# 1. Dừng application
pm2 stop all

# 2. Xóa Redis cache
redis-cli FLUSHALL

# 3. Reset database (CẢNH BÁO: Mất dữ liệu)
npm run migration:revert
npm run migration:run
npm run seed:sso

# 4. Khởi động lại application
pm2 start all
```

### Khôi Phục Tài Khoản User

```sql
-- Reset user sessions
DELETE FROM user_sessions WHERE user_id = 'user-uuid';

-- Xóa tokens bị blacklist cho user
DELETE FROM jwt_blacklist WHERE user_id = 'user-uuid';

-- Reset device verification
UPDATE users SET require_device_verification = false WHERE id = 'user-uuid';
```

### Phản Ứng Sự Cố Bảo Mật

```bash
# 1. Thu hồi tất cả tokens cho user bị xâm phạm
curl -X POST http://localhost:3000/auth/sso/revoke-all \
  -H "Authorization: Bearer admin-token" \
  -d '{"userId":"compromised-user-id"}'

# 2. Kiểm tra audit logs
curl -X GET http://localhost:3000/auth/sso/audit/security \
  -H "Authorization: Bearer admin-token"

# 3. Vô hiệu hóa tài khoản user nếu cần
curl -X PUT http://localhost:3000/users/compromised-user-id \
  -H "Authorization: Bearer admin-token" \
  -d '{"isActive":false}'
```

## Nhận Trợ Giúp Bổ Sung

### Thu Thập Log Cho Support

```bash
# Thu thập logs liên quan
mkdir sso-debug-$(date +%Y%m%d)
cp logs/application.log sso-debug-$(date +%Y%m%d)/
curl -X GET http://localhost:3000/auth/sso/config > sso-debug-$(date +%Y%m%d)/config.json
curl -X GET http://localhost:3000/auth/sso/blacklist/stats \
  -H "Authorization: Bearer admin-token" > sso-debug-$(date +%Y%m%d)/stats.json
tar -czf sso-debug-$(date +%Y%m%d).tar.gz sso-debug-$(date +%Y%m%d)/
```

### Template Thông Tin Support

Khi liên hệ support, bao gồm:

1. **Chi Tiết Môi Trường:**
   - Phiên bản Node.js
   - Phiên bản Database
   - Phiên bản Redis
   - Hệ điều hành

2. **Thông Tin Lỗi:**
   - Thông báo lỗi chính xác
   - Stack traces
   - Ví dụ request/response

3. **Cấu Hình:**
   - Environment variables liên quan (đã ẩn thông tin nhạy cảm)
   - Cài đặt CORS
   - Cấu hình domain

4. **Các Bước Tái Tạo:**
   - Các bước tái tạo chi tiết
   - Hành vi mong đợi vs thực tế
   - Tần suất xảy ra

---

**Để được hỗ trợ thêm, tham khảo [Hướng Dẫn Triển Khai](./SSO_IMPLEMENTATION_GUIDE_vi.md) và [Tài Liệu API](./SSO_API_DOCUMENTATION_vi.md).**
