# Cache Module Integration Guide

## Overview

This guide explains how to properly integrate the Cache Module with other modules in the Delify Platform, specifically focusing on the RBAC module integration that was recently fixed.

## Problem Resolution: TS2307 Module Resolution Error

### Original Error
```
TS2307: Cannot find module '../../cache/cache.service' or its corresponding type declarations.
File: src/modules/rbac/services/rbac.service.ts
Line: 9, columns 30-57
Import Statement: import { CacheService } from '../../cache/cache.service';
```

### Root Cause
The RBAC service was trying to import `CacheService` from an incorrect path that didn't match the actual cache module structure.

### Solution Implemented

#### 1. Corrected Import Path
**Before** (causing TS2307 error):
```typescript
import { CacheService } from '../../cache/cache.service';
```

**After** (error resolved):
```typescript
import { CacheService, CACHE_PREFIXES, CACHE_TTL } from '../../cache';
```

#### 2. Updated Cache Constants Usage
**Before**:
```typescript
private readonly CACHE_TTL = 15 * 60; // 15 minutes
private readonly CACHE_PREFIX = 'user_permissions';
```

**After**:
```typescript
private readonly CACHE_TTL = CACHE_TTL.MEDIUM; // 15 minutes
private readonly CACHE_PREFIX = CACHE_PREFIXES.RBAC;
```

#### 3. Fixed Cache Service Method Calls
**Before**:
```typescript
await this.cacheService.set(cacheKey, cacheData, this.CACHE_TTL);
```

**After**:
```typescript
await this.cacheService.set(cacheKey, cacheData, { 
  ttl: this.CACHE_TTL, 
  prefix: this.CACHE_PREFIX 
});
```

## Cache Module Structure

### Directory Structure
```
src/modules/cache/
├── cache.module.ts          # Main module file
├── index.ts                 # Export file (use this for imports)
├── services/
│   └── cache.service.ts     # Cache service implementation
├── interfaces/
│   └── cache.interface.ts   # TypeScript interfaces
├── decorators/
│   └── cache.decorator.ts   # Caching decorators
├── interceptors/
│   └── cache.interceptor.ts # Cache interceptors
└── controllers/
    └── cache.controller.ts  # Cache management endpoints
```

### Correct Import Patterns

#### ✅ Recommended Import (from index.ts)
```typescript
import { CacheService, CACHE_PREFIXES, CACHE_TTL } from '../../cache';
```

#### ✅ Alternative Import (specific service)
```typescript
import { CacheService } from '../../cache/services/cache.service';
import { CACHE_PREFIXES, CACHE_TTL } from '../../cache';
```

#### ❌ Incorrect Import (causes TS2307)
```typescript
import { CacheService } from '../../cache/cache.service'; // File doesn't exist
```

## Integration Best Practices

### 1. Module Import in Module File

```typescript
// In your-module.module.ts
import { CacheModule } from '../cache/cache.module';

@Module({
  imports: [
    CacheModule, // Import the cache module
    // ... other imports
  ],
  // ... rest of module configuration
})
export class YourModule {}
```

### 2. Service Import in Service File

```typescript
// In your-service.service.ts
import { CacheService, CACHE_PREFIXES, CACHE_TTL } from '../../cache';

@Injectable()
export class YourService {
  private readonly CACHE_TTL = CACHE_TTL.MEDIUM;
  private readonly CACHE_PREFIX = CACHE_PREFIXES.YOUR_MODULE;

  constructor(
    private readonly cacheService: CacheService,
  ) {}

  async someMethod() {
    // Use cache service with proper options
    await this.cacheService.set('key', data, {
      ttl: this.CACHE_TTL,
      prefix: this.CACHE_PREFIX
    });
  }
}
```

### 3. Using Cache Constants

```typescript
// Available cache prefixes
CACHE_PREFIXES = {
  SSO: 'sso:',
  RBAC: 'rbac:',
  USER: 'user:',
  AI: 'ai:',
  ORGANIZATION: 'org:',
  SESSION: 'session:',
  AUTH: 'auth:',
}

// Available TTL constants
CACHE_TTL = {
  SHORT: 300,    // 5 minutes
  MEDIUM: 900,   // 15 minutes
  LONG: 3600,    // 1 hour
  VERY_LONG: 86400, // 24 hours
}
```

## Cache Service API Reference

### Basic Operations

#### `set<T>(key: string, value: T, options?: CacheOptions): Promise<void>`
```typescript
await cacheService.set('user:123', userData, {
  ttl: CACHE_TTL.MEDIUM,
  prefix: CACHE_PREFIXES.USER
});
```

#### `get<T>(key: string, prefix?: string): Promise<T | undefined>`
```typescript
const userData = await cacheService.get<User>('user:123', CACHE_PREFIXES.USER);
```

#### `del(key: string, prefix?: string): Promise<void>`
```typescript
await cacheService.del('user:123', CACHE_PREFIXES.USER);
```

#### `exists(key: string, prefix?: string): Promise<boolean>`
```typescript
const exists = await cacheService.exists('user:123', CACHE_PREFIXES.USER);
```

### Advanced Operations

#### `getOrSet<T>(key: string, factory: () => Promise<T>, options?: CacheOptions): Promise<T>`
```typescript
const userData = await cacheService.getOrSet(
  'user:123',
  () => this.userRepository.findOne('123'),
  { ttl: CACHE_TTL.MEDIUM, prefix: CACHE_PREFIXES.USER }
);
```

#### `delPattern(pattern: string): Promise<void>`
```typescript
await cacheService.delPattern(`${CACHE_PREFIXES.USER}*`);
```

## RBAC Module Integration Example

### Complete RBAC Service Integration

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { CacheService, CACHE_PREFIXES, CACHE_TTL } from '../../cache';

@Injectable()
export class RBACService {
  private readonly logger = new Logger(RBACService.name);
  private readonly CACHE_TTL = CACHE_TTL.MEDIUM; // 15 minutes
  private readonly CACHE_PREFIX = CACHE_PREFIXES.RBAC;

  constructor(
    private readonly cacheService: CacheService,
  ) {}

  async getUserPermissions(userId: string): Promise<UserPermissionCache> {
    // Check cache first
    const cacheKey = `${this.CACHE_PREFIX}:${userId}`;
    const cached = await this.cacheService.get<UserPermissionCache>(
      cacheKey, 
      this.CACHE_PREFIX
    );

    if (cached && new Date() < cached.expiresAt) {
      return cached;
    }

    // Get from database
    const userPermissions = await this.calculateUserPermissions(userId);

    // Save to cache
    const cacheData: UserPermissionCache = {
      ...userPermissions,
      cachedAt: new Date(),
      expiresAt: new Date(Date.now() + this.CACHE_TTL * 1000),
    };

    await this.cacheService.set(cacheKey, cacheData, {
      ttl: this.CACHE_TTL,
      prefix: this.CACHE_PREFIX
    });

    return cacheData;
  }

  async invalidateUserCache(userId: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}:${userId}`;
    await this.cacheService.del(cacheKey, this.CACHE_PREFIX);
  }

  async invalidateAllCaches(): Promise<void> {
    await this.cacheService.delPattern(`${this.CACHE_PREFIX}:*`);
  }
}
```

## Troubleshooting

### Common Import Errors

#### Error: TS2307 - Cannot find module
**Cause**: Incorrect import path
**Solution**: Use the correct import path from the cache module's index.ts

```typescript
// ❌ Wrong
import { CacheService } from '../../cache/cache.service';

// ✅ Correct
import { CacheService } from '../../cache';
```

#### Error: TS2345 - Argument type not assignable
**Cause**: Incorrect cache service method parameters
**Solution**: Use proper CacheOptions object

```typescript
// ❌ Wrong
await cacheService.set('key', value, 300);

// ✅ Correct
await cacheService.set('key', value, { ttl: 300 });
```

### Module Import Issues

#### Error: Module not found in DI container
**Cause**: Cache module not imported in your module
**Solution**: Add CacheModule to your module imports

```typescript
@Module({
  imports: [
    CacheModule, // Add this
    // ... other imports
  ],
})
export class YourModule {}
```

## Testing Cache Integration

### Unit Test Example

```typescript
describe('YourService', () => {
  let service: YourService;
  let cacheService: CacheService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.forRoot()],
      providers: [YourService],
    }).compile();

    service = module.get<YourService>(YourService);
    cacheService = module.get<CacheService>(CacheService);
  });

  it('should cache data correctly', async () => {
    const testData = { id: '1', name: 'test' };
    
    await service.cacheData('test-key', testData);
    const cached = await cacheService.get('test-key');
    
    expect(cached).toEqual(testData);
  });
});
```

## Performance Considerations

### Cache Key Design
- Use consistent prefixes for different modules
- Include relevant identifiers in keys
- Avoid overly long cache keys

### TTL Strategy
- Use appropriate TTL values based on data volatility
- Consider cache warming for frequently accessed data
- Implement cache invalidation strategies

### Memory Management
- Monitor Redis memory usage
- Set appropriate max cache size
- Implement cache eviction policies

---

For more information about the cache module, see [Cache Module Documentation](../modules/CACHE_MODULE.md).
