import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum DeviceType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  UNKNOWN = 'unknown',
}

export enum DeviceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  REVOKED = 'revoked',
}

@Entity('user_devices')
@Index(['userId', 'status'])
@Index(['sessionToken'], { unique: true })
@Index(['lastActiveAt', 'status'])
export class UserDevice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ unique: true })
  sessionToken: string;

  @Column()
  deviceName: string; // e.g., "Chrome on Windows", "Safari on iPhone"

  @Column({
    type: 'enum',
    enum: DeviceType,
    default: DeviceType.UNKNOWN,
  })
  deviceType: DeviceType;

  @Column({ nullable: true })
  deviceId: string; // Unique device identifier if available

  @Column()
  userAgent: string;

  @Column()
  ipAddress: string;

  @Column({ nullable: true })
  location: string; // City, Country based on IP

  @Column({
    type: 'enum',
    enum: DeviceStatus,
    default: DeviceStatus.ACTIVE,
  })
  status: DeviceStatus;

  @Column({ type: 'jsonb', nullable: true })
  deviceInfo: {
    browser?: string;
    browserVersion?: string;
    os?: string;
    osVersion?: string;
    platform?: string;
    isMobile?: boolean;
    isTablet?: boolean;
    isDesktop?: boolean;
  };

  @Column()
  loginAt: Date;

  @Column()
  lastActiveAt: Date;

  @Column({ nullable: true })
  logoutAt: Date;

  @Column({ nullable: true })
  expiresAt: Date;

  @Column({ default: false })
  isCurrentDevice: boolean; // Mark the current session device

  @Column({ type: 'jsonb', nullable: true })
  securityInfo: {
    isTrusted?: boolean;
    riskScore?: number;
    isNewLocation?: boolean;
    isNewDevice?: boolean;
    requiresVerification?: boolean;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isActive(): boolean {
    return this.status === DeviceStatus.ACTIVE;
  }

  get isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  get isRevoked(): boolean {
    return this.status === DeviceStatus.REVOKED;
  }

  get isValid(): boolean {
    return this.isActive && !this.isExpired && !this.isRevoked;
  }

  get sessionDuration(): number {
    const endTime = this.logoutAt || new Date();
    return Math.floor((endTime.getTime() - this.loginAt.getTime()) / 1000);
  }

  get lastActiveAgo(): string {
    const now = new Date();
    const diff = now.getTime() - this.lastActiveAt.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  }

  get shortDeviceName(): string {
    // Extract browser and OS for short display
    const info = this.deviceInfo;
    if (info?.browser && info?.os) {
      return `${info.browser} on ${info.os}`;
    }
    return this.deviceName;
  }

  get isHighRisk(): boolean {
    return this.securityInfo?.riskScore > 70;
  }

  get needsVerification(): boolean {
    return this.securityInfo?.requiresVerification || false;
  }
}
