# Delify Platform Makefile

.PHONY: help install dev build start stop clean logs test

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Start development environment"
	@echo "  build       - Build the application"
	@echo "  start       - Start production environment"
	@echo "  stop        - Stop all containers"
	@echo "  clean       - Clean up containers and volumes"
	@echo "  logs        - Show application logs"
	@echo "  test        - Run tests"
	@echo "  migration   - Run database migrations"
	@echo "  seed        - Seed database with sample data"

# Install dependencies
install:
	npm install

# Start development environment
dev:
	docker-compose up -d
	@echo "Development environment started!"
	@echo "API: http://localhost:3000/api/v1"
	@echo "Docs: http://localhost:3000/api/v1/docs"

# Build application
build:
	npm run build

# Start production environment
start:
	docker-compose --profile production up -d
	@echo "Production environment started!"
	@echo "API: http://localhost:3001/api/v1"

# Stop all containers
stop:
	docker-compose down

# Clean up containers and volumes
clean:
	docker-compose down -v
	docker system prune -f

# Show logs
logs:
	docker-compose logs -f app

# Run tests
test:
	npm run test

# Run database migrations
migration:
	npm run migration:run

# Generate new migration
migration-generate:
	@read -p "Enter migration name: " name; \
	npm run migration:generate -- src/migrations/$$name

# Seed database
seed:
	npm run seed

# Setup development environment
setup: install
	cp .env.example .env
	@echo "Please edit .env file with your configuration"
	@echo "Then run 'make dev' to start the development environment"

# Database commands
db-reset:
	docker-compose down postgres
	docker volume rm delify_postgres_data
	docker-compose up -d postgres
	sleep 5
	npm run migration:run

# Backup database
db-backup:
	docker-compose exec postgres pg_dump -U delify_user delify_db > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore database
db-restore:
	@read -p "Enter backup file path: " file; \
	docker-compose exec -T postgres psql -U delify_user delify_db < $$file
