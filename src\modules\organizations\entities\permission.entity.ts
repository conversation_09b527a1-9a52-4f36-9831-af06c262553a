import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  Index,
} from 'typeorm';
import { Role } from './role.entity';

export enum PermissionCategory {
  ORGANIZATION = 'organization',
  USERS = 'users',
  MARKETING = 'marketing',
  AI = 'ai',
  EMAIL = 'email',
  DOCUMENTS = 'documents',
  WORKFLOWS = 'workflows',
  INVOICES = 'invoices',
  SCHEDULING = 'scheduling',
  ANALYTICS = 'analytics',
  SETTINGS = 'settings',
}

export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  EXPORT = 'export',
  IMPORT = 'import',
}

@Entity('permissions')
@Index(['name'], { unique: true })
@Index(['category'])
export class Permission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string; // e.g., "marketing:campaigns:create"

  @Column()
  displayName: string; // e.g., "Create Marketing Campaigns"

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PermissionCategory,
  })
  category: PermissionCategory;

  @Column({
    type: 'enum',
    enum: PermissionAction,
  })
  action: PermissionAction;

  @Column({ nullable: true })
  resource: string; // Specific resource if applicable

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isSystemPermission: boolean; // Cannot be deleted

  @ManyToMany(() => Role, role => role.permissions)
  roles: Role[];

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    requiresApproval?: boolean;
    riskLevel?: 'low' | 'medium' | 'high';
    dependencies?: string[]; // Other permissions required
    conditions?: {
      planRequired?: string; // Minimum plan required
      featureFlag?: string; // Feature flag required
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get fullName(): string {
    return `${this.category}:${this.resource || 'all'}:${this.action}`;
  }

  get isHighRisk(): boolean {
    return this.metadata?.riskLevel === 'high';
  }

  get requiresApproval(): boolean {
    return this.metadata?.requiresApproval || false;
  }

  get categoryDisplayName(): string {
    const categoryNames = {
      [PermissionCategory.ORGANIZATION]: 'Organization',
      [PermissionCategory.USERS]: 'User Management',
      [PermissionCategory.MARKETING]: 'Marketing',
      [PermissionCategory.AI]: 'AI Features',
      [PermissionCategory.EMAIL]: 'Email Marketing',
      [PermissionCategory.DOCUMENTS]: 'Documents',
      [PermissionCategory.WORKFLOWS]: 'Workflows',
      [PermissionCategory.INVOICES]: 'Invoices',
      [PermissionCategory.SCHEDULING]: 'Scheduling',
      [PermissionCategory.ANALYTICS]: 'Analytics',
      [PermissionCategory.SETTINGS]: 'Settings',
    };
    return categoryNames[this.category] || this.category;
  }

  get actionDisplayName(): string {
    const actionNames = {
      [PermissionAction.CREATE]: 'Create',
      [PermissionAction.READ]: 'View',
      [PermissionAction.UPDATE]: 'Edit',
      [PermissionAction.DELETE]: 'Delete',
      [PermissionAction.MANAGE]: 'Manage',
      [PermissionAction.EXECUTE]: 'Execute',
      [PermissionAction.APPROVE]: 'Approve',
      [PermissionAction.EXPORT]: 'Export',
      [PermissionAction.IMPORT]: 'Import',
    };
    return actionNames[this.action] || this.action;
  }

  static createPermissionName(category: PermissionCategory, resource: string, action: PermissionAction): string {
    return `${category}:${resource}:${action}`;
  }

  static parsePermissionName(permissionName: string): { category: string; resource: string; action: string } {
    const parts = permissionName.split(':');
    return {
      category: parts[0] || '',
      resource: parts[1] || '',
      action: parts[2] || '',
    };
  }
}
