import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Document } from './entities/document.entity';
import { DocumentSignature } from './entities/document-signature.entity';
import { DocumentWorkflow } from './entities/document-workflow.entity';
import { LoggerService } from '../../common/services/logger.service';

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private documentRepository: Repository<Document>,
    @InjectRepository(DocumentSignature)
    private documentSignatureRepository: Repository<DocumentSignature>,
    @InjectRepository(DocumentWorkflow)
    private documentWorkflowRepository: Repository<DocumentWorkflow>,
    private logger: LoggerService,
  ) {}

  async getDocuments(userId: string) {
    return this.documentRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async uploadDocument(userId: string, file: Express.Multer.File, data: any) {
    this.logger.logWithContext(`Document upload requested for user: ${userId}`, 'DocumentsService');
    return { message: 'Document uploaded successfully' };
  }

  async signDocument(userId: string, documentId: string, data: any) {
    this.logger.logWithContext(`Document signing requested for user: ${userId}, document: ${documentId}`, 'DocumentsService');
    return { message: 'Document signed successfully' };
  }

  async getDocumentSignatures(userId: string, documentId: string) {
    return this.documentSignatureRepository.find({
      where: { documentId },
      order: { createdAt: 'DESC' },
    });
  }

  async createWorkflow(userId: string, documentId: string, data: any) {
    this.logger.logWithContext(`Document workflow creation requested for user: ${userId}, document: ${documentId}`, 'DocumentsService');
    return { message: 'Document workflow created successfully' };
  }

  async getWorkflows(userId: string) {
    return this.documentWorkflowRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }
}
