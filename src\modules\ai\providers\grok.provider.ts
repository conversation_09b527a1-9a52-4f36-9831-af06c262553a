import axios, { AxiosInstance } from 'axios';
import { BaseAIProvider, AIMessage, AIResponse, AIProviderConfig } from './base-ai.provider';

export class GrokProvider extends BaseAIProvider {
  private client: AxiosInstance;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = axios.create({
      baseURL: config.baseURL || 'https://api.x.ai/v1',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.model || 'grok-1',
        messages: messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 1000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
        stream: false,
      });

      const responseTime = Date.now() - startTime;
      const data = response.data;

      return {
        content: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || this.calculateTokens(messages.map(m => m.content).join(' ')),
          completionTokens: data.usage?.completion_tokens || this.calculateTokens(data.choices[0].message.content),
          totalTokens: data.usage?.total_tokens || 0,
        },
        model: data.model || config.model,
        finishReason: data.choices[0].finish_reason || 'stop',
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'Grok');
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    const config = this.mergeConfig(options);

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.model || 'grok-1',
        messages: messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 1000,
        stream: true,
      }, {
        responseType: 'stream',
      });

      for await (const chunk of response.data) {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'Grok');
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    // Grok doesn't support image analysis yet, but we'll prepare for future support
    throw new Error('Grok: Image analysis not yet supported');
  }

  async embedText(text: string): Promise<number[]> {
    // Grok doesn't have embedding API yet, fallback to OpenAI or return empty
    throw new Error('Grok: Text embedding not yet supported');
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test with a simple completion
      await this.generateText([
        { role: 'user', content: 'Hello' }
      ], { maxTokens: 5 });
      return true;
    } catch (error) {
      return false;
    }
  }

  // Grok specific methods
  async getModelInfo(): Promise<{
    name: string;
    description: string;
    contextLength: number;
    capabilities: string[];
  }> {
    // Return static info since Grok API might not have model info endpoint
    return {
      name: this.config.model || 'grok-1',
      description: 'Grok AI model by xAI',
      contextLength: 8192,
      capabilities: ['text-generation', 'conversation', 'reasoning'],
    };
  }

  async getRealTimeData(query: string): Promise<{
    content: string;
    sources: string[];
    timestamp: string;
  }> {
    // Grok's real-time data access feature
    try {
      const response = await this.generateText([
        {
          role: 'system',
          content: 'You have access to real-time information. Please provide current, accurate data with sources when possible.'
        },
        {
          role: 'user',
          content: query
        }
      ]);

      return {
        content: response.content,
        sources: [], // Extract sources from response if available
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.handleError(error, 'Grok');
    }
  }

  async analyzeCode(
    code: string,
    language: string,
    analysisType: 'review' | 'debug' | 'optimize' | 'explain' = 'review'
  ): Promise<AIResponse> {
    const prompts = {
      review: `Please review this ${language} code and provide feedback on code quality, best practices, and potential improvements:`,
      debug: `Please help debug this ${language} code and identify potential issues or bugs:`,
      optimize: `Please suggest optimizations for this ${language} code to improve performance and efficiency:`,
      explain: `Please explain what this ${language} code does in detail:`,
    };

    return this.generateText([
      {
        role: 'system',
        content: `You are an expert ${language} developer. Provide detailed, actionable feedback.`
      },
      {
        role: 'user',
        content: `${prompts[analysisType]}\n\n\`\`\`${language}\n${code}\n\`\`\``
      }
    ]);
  }
}
