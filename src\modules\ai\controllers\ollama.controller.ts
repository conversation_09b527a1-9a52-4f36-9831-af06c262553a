import { Controller, Get, Post, Delete, Body, Param, UseGuards, Query } from '@nestjs/common';
import { Api<PERSON><PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { User } from '../../users/entities/user.entity';
import { AIProviderFactory } from '../providers/ai-provider.factory';
import { OllamaProvider } from '../providers/ollama.provider';
import { AIProvider, AIModelName } from '../entities/ai-model.entity';
import {
  PullModelDto,
  DeleteModelDto,
  OllamaGenerateDto,
  OllamaCodeGenerateDto,
  OllamaCodeExplainDto,
  OllamaEmbeddingDto,
  OllamaImageAnalyzeDto,
  ModelInfoDto,
  OllamaModelResponse,
  OllamaHealthResponse,
  OllamaGenerateResponse,
} from '../dto/ollama.dto';

@ApiTags('AI - OLLAMA')
@Controller('ai/ollama')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class OllamaController {
  constructor(private readonly aiProviderFactory: AIProviderFactory) {}

  private getOllamaProvider(): OllamaProvider {
    return this.aiProviderFactory.createProviderFromEnv(AIProvider.OLLAMA) as OllamaProvider;
  }

  @Get('status')
  @ApiOperation({ summary: 'Check OLLAMA server health and status' })
  @ApiResponse({ status: 200, description: 'Server status retrieved', type: OllamaHealthResponse })
  async getServerStatus(): Promise<OllamaHealthResponse> {
    const provider = this.getOllamaProvider();
    return provider.checkServerHealth();
  }

  @Get('models')
  @ApiOperation({ summary: 'List all available OLLAMA models' })
  @ApiResponse({ status: 200, description: 'Models retrieved successfully', type: [OllamaModelResponse] })
  async listModels(@CurrentUser() user: User): Promise<OllamaModelResponse[]> {
    const provider = this.getOllamaProvider();
    return provider.listModels();
  }

  @Post('models/pull')
  @ApiOperation({ summary: 'Download a new model from OLLAMA registry' })
  @ApiResponse({ status: 200, description: 'Model pull initiated' })
  async pullModel(
    @CurrentUser() user: User,
    @Body() pullModelDto: PullModelDto
  ): Promise<{ success: boolean; status: string; progress?: number }> {
    const provider = this.getOllamaProvider();
    return provider.pullModel(pullModelDto.modelName);
  }

  @Delete('models/:modelName')
  @ApiOperation({ summary: 'Delete a model from local storage' })
  @ApiResponse({ status: 200, description: 'Model deleted successfully' })
  async deleteModel(
    @CurrentUser() user: User,
    @Param('modelName') modelName: string
  ): Promise<{ success: boolean; message: string }> {
    const provider = this.getOllamaProvider();
    return provider.deleteModel(modelName);
  }

  @Get('models/:modelName/info')
  @ApiOperation({ summary: 'Get detailed information about a specific model' })
  @ApiResponse({ status: 200, description: 'Model info retrieved' })
  async getModelInfo(
    @CurrentUser() user: User,
    @Param('modelName') modelName: string
  ): Promise<any> {
    const provider = this.getOllamaProvider();
    return provider.getModelInfo(modelName);
  }

  @Post('generate')
  @ApiOperation({ summary: 'Generate text using OLLAMA model' })
  @ApiResponse({ status: 200, description: 'Text generated successfully', type: OllamaGenerateResponse })
  async generateText(
    @CurrentUser() user: User,
    @Body() generateDto: OllamaGenerateDto
  ): Promise<any> {
    const provider = this.getOllamaProvider();
    
    const messages = [
      ...(generateDto.system ? [{ role: 'system' as const, content: generateDto.system }] : []),
      { role: 'user' as const, content: generateDto.prompt },
    ];

    return provider.generateText(messages, {
      model: generateDto.model,
      temperature: generateDto.options?.temperature,
      maxTokens: generateDto.options?.num_predict,
      topP: generateDto.options?.top_p,
    });
  }

  @Post('generate/stream')
  @ApiOperation({ summary: 'Generate streaming text using OLLAMA model' })
  @ApiResponse({ status: 200, description: 'Streaming text generation started' })
  async generateStreamingText(
    @CurrentUser() user: User,
    @Body() generateDto: OllamaGenerateDto
  ): Promise<{ content: string }> {
    const provider = this.getOllamaProvider();
    
    const messages = [
      ...(generateDto.system ? [{ role: 'system' as const, content: generateDto.system }] : []),
      { role: 'user' as const, content: generateDto.prompt },
    ];

    const generator = await provider.generateStream(messages, {
      model: generateDto.model,
      temperature: generateDto.options?.temperature,
      maxTokens: generateDto.options?.num_predict,
      topP: generateDto.options?.top_p,
    });

    // Collect all chunks (in real implementation, use SSE)
    let fullResponse = '';
    for await (const chunk of generator) {
      fullResponse += chunk;
    }

    return { content: fullResponse };
  }

  @Post('code/generate')
  @ApiOperation({ summary: 'Generate code using OLLAMA code models' })
  @ApiResponse({ status: 200, description: 'Code generated successfully' })
  async generateCode(
    @CurrentUser() user: User,
    @Body() codeDto: OllamaCodeGenerateDto
  ): Promise<any> {
    const provider = this.getOllamaProvider();
    return provider.generateCode(codeDto.prompt, codeDto.language, {
      model: codeDto.model,
      temperature: codeDto.options?.temperature,
      maxTokens: codeDto.options?.num_predict,
    });
  }

  @Post('code/explain')
  @ApiOperation({ summary: 'Explain code using OLLAMA code models' })
  @ApiResponse({ status: 200, description: 'Code explanation generated' })
  async explainCode(
    @CurrentUser() user: User,
    @Body() explainDto: OllamaCodeExplainDto
  ): Promise<any> {
    const provider = this.getOllamaProvider();
    return provider.explainCode(
      explainDto.code,
      explainDto.language || 'auto-detect',
      {
        model: explainDto.model,
        temperature: explainDto.options?.temperature,
        maxTokens: explainDto.options?.num_predict,
      }
    );
  }

  @Post('embeddings')
  @ApiOperation({ summary: 'Generate text embeddings using OLLAMA embedding models' })
  @ApiResponse({ status: 200, description: 'Embeddings generated successfully' })
  async generateEmbeddings(
    @CurrentUser() user: User,
    @Body() embeddingDto: OllamaEmbeddingDto
  ): Promise<{ embeddings: number[] }> {
    const provider = this.getOllamaProvider();
    const embeddings = await provider.embedText(embeddingDto.text);
    return { embeddings };
  }

  @Post('image/analyze')
  @ApiOperation({ summary: 'Analyze images using OLLAMA vision models (e.g., LLaVA)' })
  @ApiResponse({ status: 200, description: 'Image analyzed successfully' })
  async analyzeImage(
    @CurrentUser() user: User,
    @Body() imageDto: OllamaImageAnalyzeDto
  ): Promise<any> {
    const provider = this.getOllamaProvider();
    return provider.analyzeImage(imageDto.imageUrl, imageDto.prompt, {
      model: imageDto.model,
      temperature: imageDto.options?.temperature,
      maxTokens: imageDto.options?.num_predict,
    });
  }

  @Get('models/recommended')
  @ApiOperation({ summary: 'Get recommended OLLAMA models for different tasks' })
  @ApiResponse({ status: 200, description: 'Recommended models retrieved' })
  async getRecommendedModels(
    @Query('task') task?: 'text' | 'code' | 'embedding' | 'vision'
  ): Promise<{
    task: string;
    recommended: Array<{
      model: string;
      description: string;
      size: string;
      use_case: string;
    }>;
  }> {
    const recommendations = {
      text: [
        {
          model: 'llama2:7b',
          description: 'Llama 2 7B - Good balance of performance and speed',
          size: '3.8GB',
          use_case: 'General text generation, chat, Q&A',
        },
        {
          model: 'mistral:7b',
          description: 'Mistral 7B - Fast and efficient',
          size: '4.1GB',
          use_case: 'Quick responses, lightweight applications',
        },
        {
          model: 'phi3:mini',
          description: 'Phi-3 Mini - Microsoft\'s compact model',
          size: '2.3GB',
          use_case: 'Resource-constrained environments',
        },
      ],
      code: [
        {
          model: 'codellama:7b',
          description: 'Code Llama 7B - Specialized for code generation',
          size: '3.8GB',
          use_case: 'Code generation, completion, debugging',
        },
        {
          model: 'codellama:13b',
          description: 'Code Llama 13B - Better code understanding',
          size: '7.3GB',
          use_case: 'Complex code tasks, refactoring',
        },
        {
          model: 'starcoder:7b',
          description: 'StarCoder - Multi-language code model',
          size: '4.3GB',
          use_case: 'Multiple programming languages',
        },
      ],
      embedding: [
        {
          model: 'nomic-embed-text:latest',
          description: 'Nomic Embed Text - High-quality embeddings',
          size: '274MB',
          use_case: 'Text similarity, search, clustering',
        },
      ],
      vision: [
        {
          model: 'llava:latest',
          description: 'LLaVA - Large Language and Vision Assistant',
          size: '4.7GB',
          use_case: 'Image description, visual Q&A',
        },
      ],
    };

    const selectedTask = task || 'text';
    return {
      task: selectedTask,
      recommended: recommendations[selectedTask] || recommendations.text,
    };
  }

  @Post('models/install-recommended')
  @ApiOperation({ summary: 'Install recommended models for a specific task' })
  @ApiResponse({ status: 200, description: 'Model installation initiated' })
  async installRecommendedModels(
    @CurrentUser() user: User,
    @Body() body: { task: 'text' | 'code' | 'embedding' | 'vision'; modelSize?: 'small' | 'medium' | 'large' }
  ): Promise<{ message: string; models: string[] }> {
    const { task, modelSize = 'small' } = body;
    
    const modelsByTask = {
      text: {
        small: ['phi3:mini'],
        medium: ['llama2:7b', 'mistral:7b'],
        large: ['llama2:13b'],
      },
      code: {
        small: ['codellama:7b'],
        medium: ['codellama:13b'],
        large: ['codellama:34b'],
      },
      embedding: {
        small: ['nomic-embed-text:latest'],
        medium: ['nomic-embed-text:latest'],
        large: ['nomic-embed-text:latest'],
      },
      vision: {
        small: ['llava:latest'],
        medium: ['llava:latest'],
        large: ['llava:latest'],
      },
    };

    const modelsToInstall = modelsByTask[task]?.[modelSize] || [];
    
    // Note: In a real implementation, you might want to queue these installations
    // and provide progress updates via WebSocket or polling endpoint
    
    return {
      message: `Installation queued for ${modelsToInstall.length} model(s)`,
      models: modelsToInstall,
    };
  }
}
