# Hướng Dẫn Triển Khai Hệ Thống RBAC

## Tổng Quan

Hệ thống RBAC (Role-Based Access Control) cung cấp khả năng kiểm soát truy cập dựa trên vai trò với các tính năng:

- **<PERSON><PERSON> cấp vai trò**: <PERSON><PERSON> thống phân cấp từ MASTER_ACCOUNT đến GUEST
- **Quyền hạn chi tiết**: 8 loại quyền (READ, CREATE, UPDATE, DELETE, EXPORT, APPROVE, MANAGE, ASSIGN)
- **10 modules hệ thống**: Từ quản lý người dùng đến tính năng AI
- **<PERSON><PERSON> thừa quyền**: <PERSON>ai trò cao hơn kế thừa quyền từ vai trò thấp hơn
- **Cache hiệu suất**: Redis cache cho permissions với TTL 15 phút
- **Tích hợp JWT**: <PERSON>hông tin RBAC được nhúng trong access token

## Cấu Trúc Database

### Bảng Chính

```sql
-- Bảng vai trò
CREATE TABLE roles (
  id UUID PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  display_name VARCHAR(200) NOT NULL,
  description TEXT,
  level INTEGER NOT NULL,
  parent_role_id UUID REFERENCES roles(id),
  is_system_role BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Bảng quyền hạn
CREATE TABLE permissions (
  id UUID PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  module VARCHAR(50) NOT NULL,
  action VARCHAR(20) NOT NULL,
  resource VARCHAR(100),
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Bảng ánh xạ vai trò-quyền
CREATE TABLE role_permissions (
  id UUID PRIMARY KEY,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  granted_by UUID REFERENCES users(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);

-- Bảng gán vai trò cho người dùng
CREATE TABLE user_roles (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(user_id, role_id)
);
```

### Dữ Liệu Mặc Định

Hệ thống được khởi tạo với 12 vai trò và 80 quyền hạn (8 actions × 10 modules):

**Vai trò hệ thống:**
- MASTER_ACCOUNT (Level 0) - Quyền tối cao
- ADMIN (Level 1) - Quản trị viên
- MANAGER (Level 2) - Quản lý
- VIEWER (Level 5) - Người xem
- GUEST (Level 6) - Khách

**Vai trò chuyên môn:**
- MARKETING_LEAD/STAFF (Level 3-4)
- SALES_LEAD/STAFF (Level 3-4)
- CONTENT_LEAD/EDITOR (Level 3-4)
- ANALYST (Level 3)

## API Endpoints

### Quản Lý Vai Trò

```typescript
// Tạo vai trò mới
POST /api/roles
{
  "name": "CUSTOM_ROLE",
  "displayName": "Custom Role",
  "description": "Vai trò tùy chỉnh",
  "level": 4,
  "parentRoleId": "uuid-parent-role"
}

// Lấy danh sách vai trò
GET /api/roles?search=marketing&level=3&page=1&limit=10

// Cập nhật vai trò
PUT /api/roles/:id
{
  "displayName": "Updated Role Name",
  "description": "Mô tả mới"
}

// Xóa vai trò
DELETE /api/roles/:id

// Gán quyền cho vai trò
POST /api/roles/:id/permissions
{
  "permissionIds": ["uuid1", "uuid2"],
  "replace": false
}

// Thu hồi quyền từ vai trò
DELETE /api/roles/:id/permissions
{
  "permissionIds": ["uuid1", "uuid2"]
}
```

### Quản Lý Quyền Hạn

```typescript
// Tạo quyền mới
POST /api/permissions
{
  "module": "USER_MANAGEMENT",
  "action": "READ",
  "resource": "profile",
  "description": "Quyền xem profile người dùng"
}

// Lấy quyền theo module
GET /api/permissions/module/USER_MANAGEMENT

// Kiểm tra quyền người dùng
POST /api/permissions/check
{
  "userId": "uuid-user",
  "permission": "USER_MANAGEMENT_READ",
  "resource": "user:123"
}

// Tạo permissions mặc định cho module
POST /api/permissions/module/CONTENT_MANAGEMENT/defaults
```

### Gán Vai Trò

```typescript
// Gán vai trò cho người dùng
POST /api/roles/assign
{
  "userId": "uuid-user",
  "roleId": "uuid-role",
  "expiresAt": "2024-12-31T23:59:59.999Z"
}

// Thu hồi vai trò từ người dùng
POST /api/roles/revoke
{
  "userId": "uuid-user",
  "roleId": "uuid-role"
}
```

## Sử Dụng Decorators

### Decorators Cơ Bản

```typescript
import { 
  RequirePermission, 
  RequireRole, 
  RequireRoleLevel,
  RBAC,
  AdminOnly,
  ManagerOnly 
} from '../rbac/decorators/rbac.decorators';

// Yêu cầu quyền cụ thể
@RequirePermission('USER_MANAGEMENT_READ')
@Get('/users')
async getUsers() {
  return this.usersService.findAll();
}

// Yêu cầu vai trò cụ thể
@RequireRole('ADMIN')
@Delete('/users/:id')
async deleteUser(@Param('id') id: string) {
  return this.usersService.delete(id);
}

// Yêu cầu cấp độ vai trò
@RequireRoleLevel(2) // Manager trở lên
@Post('/approve')
async approveContent() {
  // Logic phê duyệt
}

// Decorator kết hợp
@RBAC({
  permission: 'CONTENT_MANAGEMENT_APPROVE',
  roleLevel: 2,
  description: 'Phê duyệt nội dung'
})
@Post('/content/approve')
async approveContent() {
  // Logic phê duyệt
}
```

### Decorators Chuyên Dụng

```typescript
// Chỉ Admin
@AdminOnly()
@Get('/system/settings')
async getSystemSettings() {
  return this.settingsService.getAll();
}

// Chỉ Manager trở lên
@ManagerOnly()
@Get('/reports/financial')
async getFinancialReports() {
  return this.reportsService.getFinancial();
}

// Decorators theo module
@UserManagement.Create()
@Post('/users')
async createUser(@Body() createUserDto: CreateUserDto) {
  return this.usersService.create(createUserDto);
}

@ContentManagement.Approve()
@Post('/content/:id/approve')
async approveContent(@Param('id') id: string) {
  return this.contentService.approve(id);
}
```

## Tích Hợp với Authentication

### JWT Payload Mở Rộng

```typescript
interface JwtPayload {
  sub: string;
  email: string;
  username: string;
  role: string;
  sessionToken?: string;
  // RBAC fields
  roles?: string[];
  permissions?: string[];
  roleLevel?: number;
  isMasterAccount?: boolean;
  permissionVersion?: number;
}
```

### Middleware Integration

```typescript
// Trong controller
@UseGuards(JwtAuthGuard, RBACGuard)
@RequirePermission('USER_MANAGEMENT_READ')
@Get('/users')
async getUsers(@Request() req) {
  // req.user chứa thông tin RBAC
  const userLevel = req.user.roleLevel;
  const permissions = req.user.permissions;
  
  return this.usersService.findAll();
}
```

## Cache Strategy

### Redis Cache Configuration

```typescript
// Cache key pattern
const CACHE_KEY = `user_permissions:${userId}`;

// Cache structure
interface UserPermissionCache {
  userId: string;
  permissions: string[];
  roles: string[];
  roleLevel: number;
  isMasterAccount: boolean;
  cachedAt: Date;
  expiresAt: Date;
}

// TTL: 15 phút
const CACHE_TTL = 15 * 60; // seconds
```

### Cache Invalidation

```typescript
// Invalidate khi thay đổi role/permission
await this.rbacService.invalidateUserCache(userId);

// Invalidate tất cả cache
await this.rbacService.invalidateAllCaches();
```

## Migration và Seeding

### Chạy Migration

```bash
# Tạo bảng RBAC
npm run migration:run

# Seed dữ liệu mặc định
npm run seed:rbac
```

### Custom Seeding

```typescript
// Tạo vai trò tùy chỉnh
const customRole = await this.roleService.createRole({
  name: 'CUSTOM_MANAGER',
  displayName: 'Custom Manager',
  level: 3,
  parentRoleId: managerRole.id
}, createdBy);

// Gán quyền cho vai trò
await this.roleService.assignPermissionsToRole(
  customRole.id,
  {
    permissionIds: [permission1.id, permission2.id],
    replace: false
  },
  createdBy
);
```

## Error Handling

### Common Errors

```typescript
// 403 Forbidden - Không có quyền
{
  "statusCode": 403,
  "message": "User lacks required permission: USER_MANAGEMENT_DELETE",
  "error": "Forbidden"
}

// 404 Not Found - Không tìm thấy role/permission
{
  "statusCode": 404,
  "message": "Role not found",
  "error": "Not Found"
}

// 409 Conflict - Tên role đã tồn tại
{
  "statusCode": 409,
  "message": "Role name already exists",
  "error": "Conflict"
}
```

### Error Handling Best Practices

```typescript
try {
  await this.rbacService.assignRoleToUser(userId, roleId, assignedBy);
} catch (error) {
  if (error instanceof ForbiddenException) {
    // Xử lý lỗi quyền hạn
    return { error: 'Insufficient permissions' };
  }
  if (error instanceof NotFoundException) {
    // Xử lý lỗi không tìm thấy
    return { error: 'Resource not found' };
  }
  // Xử lý lỗi khác
  throw error;
}
```

## Performance Optimization

### Database Indexing

```sql
-- Indexes quan trọng
CREATE INDEX idx_roles_level ON roles(level);
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_active ON user_roles(is_active);
CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);
```

### Query Optimization

```typescript
// Sử dụng eager loading
const roles = await this.roleRepository.find({
  relations: ['rolePermissions', 'rolePermissions.permission'],
  where: { isActive: true }
});

// Batch operations
const userRoles = await this.userRoleRepository.find({
  where: { userId: In(userIds), isActive: true },
  relations: ['role']
});
```

## Security Considerations

### Best Practices

1. **Principle of Least Privilege**: Chỉ cấp quyền tối thiểu cần thiết
2. **Regular Audit**: Định kỳ review và cleanup permissions
3. **Role Expiration**: Sử dụng expires_at cho vai trò tạm thời
4. **Monitoring**: Log tất cả thay đổi permissions
5. **Rate Limiting**: Giới hạn API calls cho role management

### Audit Logging

```typescript
// Log thay đổi permissions
this.logger.log(`Role ${roleName} assigned to user ${userId} by ${assignedBy}`, {
  action: 'ROLE_ASSIGNED',
  userId,
  roleId,
  assignedBy,
  timestamp: new Date(),
  ipAddress: req.ip
});
```

## Testing

### Unit Tests

```typescript
describe('RBACService', () => {
  it('should check user permission correctly', async () => {
    const result = await rbacService.checkPermission(
      userId, 
      'USER_MANAGEMENT_READ'
    );
    
    expect(result.allowed).toBe(true);
    expect(result.reason).toContain('User has required permission');
  });
});
```

### Integration Tests

```typescript
describe('Role Assignment', () => {
  it('should assign role to user successfully', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/roles/assign')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        userId: testUser.id,
        roleId: managerRole.id
      })
      .expect(200);
      
    expect(response.body.message).toBe('Role assigned successfully');
  });
});
```

---

**Để biết thêm chi tiết, tham khảo [API Documentation](./RBAC_API_DOCUMENTATION_vi.md) và [User Guide](./RBAC_USER_GUIDE_vi.md).**
