import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';

// Configuration
import { databaseConfig } from './config/database.config';
import { redisConfig } from './config/redis.config';

// Cache Module (Custom)
import { CacheModule } from './modules/cache/cache.module';

// Modules
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { RBACModule } from './modules/rbac/rbac.module';
import { SSOModule } from './modules/sso/sso.module';
import { OrganizationsModule } from './modules/organizations/organizations.module';
import { MarketingModule } from './modules/marketing/marketing.module';
import { AiModule } from './modules/ai/ai.module';
import { EmailModule } from './modules/email/email.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { WorkflowsModule } from './modules/workflows/workflows.module';
import { InvoicesModule } from './modules/invoices/invoices.module';
import { SchedulingModule } from './modules/scheduling/scheduling.module';
import { TestModule } from './modules/test/test.module';
import { AuthDemoModule } from './modules/auth-demo/auth-demo.module';

// Common
import { CommonModule } from './common/common.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database - Remote PostgreSQL
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: databaseConfig,
      inject: [ConfigService],
    }),

    // Cache Module (Custom Redis Integration for SSO)
    CacheModule.forRoot(),

    // Bull Queue (Redis) - Temporarily disabled for Swagger demo
    // BullModule.forRootAsync({
    //   imports: [ConfigModule],
    //   useFactory: redisConfig,
    //   inject: [ConfigService],
    // }),

    // Task Scheduling
    ScheduleModule.forRoot(),

    // Rate Limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ([{
        ttl: configService.get('RATE_LIMIT_TTL', 60000),
        limit: configService.get('RATE_LIMIT_LIMIT', 100),
      }]),
      inject: [ConfigService],
    }),

    // Application Modules
    CommonModule,
    TestModule, // Keep test module for Swagger demonstration
    // AuthDemoModule, // Demo auth module - disabled for production
    AuthModule, // Real auth module with remote PostgreSQL
    UsersModule, // Enable for remote PostgreSQL
    RBACModule, // RBAC system for role-based access control
    SSOModule, // SSO system for cross-subdomain authentication
    // OrganizationsModule, // Temporarily disabled - requires database
    // MarketingModule, // Temporarily disabled - requires database
    // AiModule, // Temporarily disabled - requires database
    // EmailModule, // Temporarily disabled - requires database
    // DocumentsModule, // Temporarily disabled - requires database
    // WorkflowsModule, // Temporarily disabled - requires database
    // InvoicesModule, // Temporarily disabled - requires database
    // SchedulingModule, // Temporarily disabled - requires database
  ],
})
export class AppModule {}
