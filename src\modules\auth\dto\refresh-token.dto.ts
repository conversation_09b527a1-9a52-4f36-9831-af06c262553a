import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RefreshTokenDto {
  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token to exchange for new access token'
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class TokenResponse {
  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token'
  })
  accessToken: string;

  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT refresh token'
  })
  refreshToken: string;

  @ApiProperty({ 
    example: 900,
    description: 'Access token expiration time in seconds'
  })
  expiresIn: number;

  @ApiProperty({ 
    example: 'Bearer',
    description: 'Token type'
  })
  tokenType: string;
}

export class RevokeTokenDto {
  @ApiProperty({ 
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token to revoke'
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}
