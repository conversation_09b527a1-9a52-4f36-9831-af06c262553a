# 🛠️ Feature Development Guide

## 📋 Tổng quan

Hướng dẫn chi tiết để **thêm features mới** vào Delify Platform theo best practices và maintain consistency với existing architecture.

## 🎯 Development Process

### 1. Feature Planning Phase
```
📋 Requirements Analysis
     ↓
🎨 Design & Architecture
     ↓
📊 Database Schema Design
     ↓
🔧 API Design
     ↓
🧪 Test Planning
     ↓
💻 Implementation
     ↓
🧪 Testing & QA
     ↓
📚 Documentation
     ↓
🚀 Deployment
```

## 🏗️ Creating New Module

### Step 1: Generate Module Structure
```bash
# Generate new module
nest generate module features/notifications

# Generate controller
nest generate controller features/notifications

# Generate service
nest generate service features/notifications

# Generate DTOs
mkdir src/modules/notifications/dto
touch src/modules/notifications/dto/create-notification.dto.ts
touch src/modules/notifications/dto/update-notification.dto.ts

# Generate entities
mkdir src/modules/notifications/entities
touch src/modules/notifications/entities/notification.entity.ts

# Generate enums (if needed)
mkdir src/modules/notifications/enums
touch src/modules/notifications/enums/notification-type.enum.ts
```

### Step 2: Module Structure Template
```typescript
// src/modules/notifications/notifications.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { Notification } from './entities/notification.entity';
import { UsersModule } from '../users/users.module';
import { OrganizationsModule } from '../organizations/organizations.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Notification]),
    UsersModule,
    OrganizationsModule,
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
```

## 🗄️ Database Entity Design

### Step 1: Create Entity
```typescript
// src/modules/notifications/entities/notification.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { NotificationType } from '../enums/notification-type.enum';

@Entity('notifications')
@Index(['userId', 'isRead'])
@Index(['organizationId', 'createdAt'])
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @ManyToOne(() => User, user => user.notifications)
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => Organization, { nullable: true })
  organization: Organization;

  @Column({ nullable: true })
  organizationId: string;

  @Column('json', { nullable: true })
  metadata: {
    actionUrl?: string;
    actionText?: string;
    data?: any;
  };

  @Column({ default: false })
  isRead: boolean;

  @Column({ nullable: true })
  readAt: Date;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### Step 2: Create Enum
```typescript
// src/modules/notifications/enums/notification-type.enum.ts
export enum NotificationType {
  SYSTEM = 'system',
  INVITATION = 'invitation',
  TEAM_UPDATE = 'team_update',
  AI_COMPLETION = 'ai_completion',
  BILLING = 'billing',
  SECURITY = 'security',
}
```

### Step 3: Create Migration
```bash
# Generate migration
npm run migration:generate -- --name CreateNotificationsTable

# Run migration
npm run migration:run
```

## 📝 DTO Design

### Step 1: Create DTOs
```typescript
// src/modules/notifications/dto/create-notification.dto.ts
import { IsString, IsEnum, IsOptional, IsUUID, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType } from '../enums/notification-type.enum';

export class CreateNotificationDto {
  @ApiProperty({ example: 'New team invitation' })
  @IsString()
  title: string;

  @ApiProperty({ example: 'You have been invited to join the Development team' })
  @IsString()
  message: string;

  @ApiProperty({ enum: NotificationType })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ example: 'user-uuid' })
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({ example: 'organization-uuid' })
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiPropertyOptional({
    example: {
      actionUrl: '/teams/join/invitation-token',
      actionText: 'Join Team',
      data: { teamId: 'team-uuid' }
    }
  })
  @IsOptional()
  @IsObject()
  metadata?: {
    actionUrl?: string;
    actionText?: string;
    data?: any;
  };
}
```

```typescript
// src/modules/notifications/dto/update-notification.dto.ts
import { PartialType } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';
import { CreateNotificationDto } from './create-notification.dto';

export class UpdateNotificationDto extends PartialType(CreateNotificationDto) {
  @IsOptional()
  @IsBoolean()
  isRead?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
```

```typescript
// src/modules/notifications/dto/query-notifications.dto.ts
import { IsOptional, IsEnum, IsBoolean, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType } from '../enums/notification-type.enum';

export class QueryNotificationsDto {
  @ApiPropertyOptional({ enum: NotificationType })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isRead?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  organizationId?: string;

  @ApiPropertyOptional({ default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiPropertyOptional({ default: 20 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 20;
}
```

## 🔧 Service Implementation

### Step 1: Create Service
```typescript
// src/modules/notifications/notifications.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationsDto } from './dto/query-notifications.dto';
import { LoggerService } from '../common/services/logger.service';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    private logger: LoggerService,
  ) {}

  async create(createDto: CreateNotificationDto): Promise<Notification> {
    try {
      const notification = this.notificationRepository.create(createDto);
      const saved = await this.notificationRepository.save(notification);
      
      this.logger.logInfo(
        `Notification created for user ${createDto.userId}`,
        'NotificationsService',
        { notificationId: saved.id, type: createDto.type }
      );
      
      return saved;
    } catch (error) {
      this.logger.logError(error, 'Failed to create notification');
      throw error;
    }
  }

  async findByUser(
    userId: string,
    query: QueryNotificationsDto
  ): Promise<{ notifications: Notification[]; total: number }> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.organization', 'organization')
      .where('notification.userId = :userId', { userId })
      .andWhere('notification.isActive = :isActive', { isActive: true });

    // Apply filters
    if (query.type) {
      queryBuilder.andWhere('notification.type = :type', { type: query.type });
    }

    if (query.isRead !== undefined) {
      queryBuilder.andWhere('notification.isRead = :isRead', { isRead: query.isRead });
    }

    if (query.organizationId) {
      queryBuilder.andWhere('notification.organizationId = :organizationId', {
        organizationId: query.organizationId,
      });
    }

    // Pagination
    const total = await queryBuilder.getCount();
    const notifications = await queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .getMany();

    return { notifications, total };
  }

  async markAsRead(id: string, userId: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id, userId },
    });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    notification.isRead = true;
    notification.readAt = new Date();

    return this.notificationRepository.save(notification);
  }

  async markAllAsRead(userId: string, organizationId?: string): Promise<void> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder()
      .update(Notification)
      .set({ isRead: true, readAt: new Date() })
      .where('userId = :userId', { userId })
      .andWhere('isRead = :isRead', { isRead: false });

    if (organizationId) {
      queryBuilder.andWhere('organizationId = :organizationId', { organizationId });
    }

    await queryBuilder.execute();
  }

  async getUnreadCount(userId: string, organizationId?: string): Promise<number> {
    const queryBuilder = this.notificationRepository
      .createQueryBuilder('notification')
      .where('notification.userId = :userId', { userId })
      .andWhere('notification.isRead = :isRead', { isRead: false })
      .andWhere('notification.isActive = :isActive', { isActive: true });

    if (organizationId) {
      queryBuilder.andWhere('notification.organizationId = :organizationId', {
        organizationId,
      });
    }

    return queryBuilder.getCount();
  }

  async delete(id: string, userId: string): Promise<void> {
    const result = await this.notificationRepository.update(
      { id, userId },
      { isActive: false }
    );

    if (result.affected === 0) {
      throw new NotFoundException('Notification not found');
    }
  }

  // Bulk notification methods
  async createBulk(notifications: CreateNotificationDto[]): Promise<Notification[]> {
    const entities = this.notificationRepository.create(notifications);
    return this.notificationRepository.save(entities);
  }

  async notifyTeamMembers(
    teamId: string,
    notification: Omit<CreateNotificationDto, 'userId'>
  ): Promise<void> {
    // Implementation would get team members and create notifications
    // This is a placeholder for the actual implementation
  }
}
```

## 🎮 Controller Implementation

### Step 1: Create Controller
```typescript
// src/modules/notifications/notifications.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { QueryNotificationsDto } from './dto/query-notifications.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create notification (Admin only)' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  async create(@Body() createDto: CreateNotificationDto) {
    return this.notificationsService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  async findAll(
    @CurrentUser() user: User,
    @Query() query: QueryNotificationsDto
  ) {
    return this.notificationsService.findByUser(user.id, query);
  }

  @Get('unread-count')
  @ApiOperation({ summary: 'Get unread notifications count' })
  @ApiResponse({ status: 200, description: 'Unread count retrieved' })
  @ApiQuery({ name: 'organizationId', required: false })
  async getUnreadCount(
    @CurrentUser() user: User,
    @Query('organizationId') organizationId?: string
  ) {
    const count = await this.notificationsService.getUnreadCount(
      user.id,
      organizationId
    );
    return { count };
  }

  @Put(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  async markAsRead(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    return this.notificationsService.markAsRead(id, user.id);
  }

  @Put('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  @ApiQuery({ name: 'organizationId', required: false })
  async markAllAsRead(
    @CurrentUser() user: User,
    @Query('organizationId') organizationId?: string
  ) {
    await this.notificationsService.markAllAsRead(user.id, organizationId);
    return { message: 'All notifications marked as read' };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete notification' })
  @ApiResponse({ status: 200, description: 'Notification deleted successfully' })
  async delete(
    @Param('id') id: string,
    @CurrentUser() user: User
  ) {
    await this.notificationsService.delete(id, user.id);
    return { message: 'Notification deleted successfully' };
  }
}
```

## 🔐 Authentication & Authorization

### Step 1: Add Guards
```typescript
// For organization-specific notifications
@UseGuards(JwtAuthGuard, OrganizationGuard)
@Get('organizations/:organizationId/notifications')
async getOrganizationNotifications(
  @Param('organizationId') organizationId: string,
  @CurrentUser() user: User
) {
  return this.notificationsService.findByUser(user.id, { organizationId });
}

// For admin-only endpoints
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(MemberRole.ADMIN, MemberRole.OWNER)
@Post('bulk')
async createBulkNotifications(@Body() notifications: CreateNotificationDto[]) {
  return this.notificationsService.createBulk(notifications);
}
```

### Step 2: Permission Checking
```typescript
// In service method
async createTeamNotification(
  teamId: string,
  notification: CreateNotificationDto,
  createdBy: string
) {
  // Check if user has permission to send notifications to team
  const hasPermission = await this.permissionsService.hasPermission(
    createdBy,
    notification.organizationId,
    'notifications:send'
  );
  
  if (!hasPermission) {
    throw new ForbiddenException('Insufficient permissions');
  }
  
  return this.create(notification);
}
```

## 🧪 Testing Implementation

### Step 1: Unit Tests
```typescript
// src/modules/notifications/notifications.service.spec.ts
describe('NotificationsService', () => {
  let service: NotificationsService;
  let repository: Repository<Notification>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: getRepositoryToken(Notification),
          useClass: Repository,
        },
        {
          provide: LoggerService,
          useValue: { logInfo: jest.fn(), logError: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    repository = module.get<Repository<Notification>>(getRepositoryToken(Notification));
  });

  it('should create notification', async () => {
    const createDto: CreateNotificationDto = {
      title: 'Test Notification',
      message: 'Test message',
      type: NotificationType.SYSTEM,
      userId: 'user-id',
    };

    const savedNotification = { id: 'notification-id', ...createDto };
    jest.spyOn(repository, 'create').mockReturnValue(savedNotification as any);
    jest.spyOn(repository, 'save').mockResolvedValue(savedNotification as any);

    const result = await service.create(createDto);

    expect(result).toEqual(savedNotification);
    expect(repository.create).toHaveBeenCalledWith(createDto);
    expect(repository.save).toHaveBeenCalledWith(savedNotification);
  });
});
```

### Step 2: Integration Tests
```typescript
// src/modules/notifications/notifications.controller.integration.spec.ts
describe('NotificationsController (Integration)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [NotificationsModule, AuthModule, UsersModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Setup auth token
    authToken = await getAuthToken(app);
  });

  it('/notifications (GET)', () => {
    return request(app.getHttpServer())
      .get('/notifications')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('notifications');
        expect(res.body).toHaveProperty('total');
      });
  });
});
```

## 📚 Documentation

### Step 1: Update API Documentation
```typescript
// Add to main module
@Module({
  imports: [
    // ... other modules
    NotificationsModule,
  ],
})
export class AppModule {}
```

### Step 2: Add to Swagger
```typescript
// In main.ts, the module will automatically be included in Swagger
// due to @ApiTags and other decorators
```

## 🎯 Best Practices Checklist

### ✅ Code Quality
- [ ] Follow TypeScript strict mode
- [ ] Use proper DTOs with validation
- [ ] Implement proper error handling
- [ ] Add comprehensive logging
- [ ] Follow naming conventions

### ✅ Security
- [ ] Add authentication guards
- [ ] Implement authorization checks
- [ ] Validate all inputs
- [ ] Sanitize outputs
- [ ] Add rate limiting if needed

### ✅ Performance
- [ ] Add database indexes
- [ ] Implement pagination
- [ ] Use efficient queries
- [ ] Add caching if appropriate
- [ ] Monitor performance

### ✅ Testing
- [ ] Write unit tests (>80% coverage)
- [ ] Add integration tests
- [ ] Test error scenarios
- [ ] Test edge cases
- [ ] Add E2E tests for critical paths

### ✅ Documentation
- [ ] Add API documentation
- [ ] Update README if needed
- [ ] Document complex business logic
- [ ] Add code comments
- [ ] Update architecture docs

**Following this guide ensures consistent, maintainable, và scalable feature development trong Delify Platform.** 🛠️✨
