import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AIProviderFactory } from '../ai-provider.factory';
import { AIProvider, AIModelName } from '../../entities/ai-model.entity';
import { OpenAIProvider } from '../openai.provider';
import { GrokProvider } from '../grok.provider';
import { GeminiProvider } from '../gemini.provider';
import { OllamaProvider } from '../ollama.provider';

describe('AIProviderFactory', () => {
  let factory: AIProviderFactory;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AIProviderFactory,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    factory = module.get<AIProviderFactory>(AIProviderFactory);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createProvider', () => {
    it('should create OpenAI provider', () => {
      const config = {
        provider: AIProvider.OPENAI,
        model: AIModelName.GPT_4_TURBO,
        apiKey: 'test-key',
        baseURL: 'https://api.openai.com/v1',
      };

      const provider = factory.createProvider(config);

      expect(provider).toBeInstanceOf(OpenAIProvider);
    });

    it('should create Grok provider', () => {
      const config = {
        provider: AIProvider.GROK,
        model: AIModelName.GROK_1_5,
        apiKey: 'test-key',
        baseURL: 'https://api.x.ai/v1',
      };

      const provider = factory.createProvider(config);

      expect(provider).toBeInstanceOf(GrokProvider);
    });

    it('should create Gemini provider', () => {
      const config = {
        provider: AIProvider.GEMINI,
        model: AIModelName.GEMINI_PRO,
        apiKey: 'test-key',
        baseURL: 'https://generativelanguage.googleapis.com/v1beta',
      };

      const provider = factory.createProvider(config);

      expect(provider).toBeInstanceOf(GeminiProvider);
    });

    it('should create OLLAMA provider', () => {
      const config = {
        provider: AIProvider.OLLAMA,
        model: AIModelName.LLAMA_2_7B,
        apiKey: 'not-required',
        baseURL: 'http://localhost:11434',
      };

      const provider = factory.createProvider(config);

      expect(provider).toBeInstanceOf(OllamaProvider);
    });

    it('should throw error for unsupported provider', () => {
      const config = {
        provider: 'unsupported' as AIProvider,
        model: AIModelName.GPT_4_TURBO,
        apiKey: 'test-key',
      };

      expect(() => factory.createProvider(config)).toThrow(
        'Unsupported AI provider: unsupported'
      );
    });
  });

  describe('createProviderFromEnv', () => {
    it('should create provider from environment variables', () => {
      configService.get
        .mockReturnValueOnce('test-openai-key') // OPENAI_API_KEY
        .mockReturnValueOnce('https://api.openai.com/v1'); // OPENAI_BASE_URL

      const provider = factory.createProviderFromEnv(AIProvider.OPENAI);

      expect(provider).toBeInstanceOf(OpenAIProvider);
      expect(configService.get).toHaveBeenCalledWith('OPENAI_API_KEY');
      expect(configService.get).toHaveBeenCalledWith('OPENAI_BASE_URL');
    });

    it('should throw error when API key is missing', () => {
      configService.get.mockReturnValue(undefined);

      expect(() => factory.createProviderFromEnv(AIProvider.OPENAI)).toThrow(
        'No configuration found for provider: openai'
      );
    });

    it('should create OLLAMA provider without API key', () => {
      configService.get
        .mockReturnValueOnce('not-required') // Not used for OLLAMA
        .mockReturnValueOnce('http://localhost:11434'); // OLLAMA_BASE_URL

      const provider = factory.createProviderFromEnv(AIProvider.OLLAMA);

      expect(provider).toBeInstanceOf(OllamaProvider);
    });
  });

  describe('getAvailableProviders', () => {
    it('should return providers with valid API keys', () => {
      configService.get.mockImplementation((key: string) => {
        const configs = {
          OPENAI_API_KEY: 'test-openai-key',
          GROK_API_KEY: 'test-grok-key',
          GEMINI_API_KEY: undefined,
          OLLAMA_BASE_URL: 'http://localhost:11434',
        };
        return configs[key];
      });

      const providers = factory.getAvailableProviders();

      expect(providers).toContain(AIProvider.OPENAI);
      expect(providers).toContain(AIProvider.GROK);
      expect(providers).toContain(AIProvider.OLLAMA);
      expect(providers).not.toContain(AIProvider.GEMINI);
    });
  });

  describe('getProviderModels', () => {
    it('should return OpenAI models', () => {
      const models = factory.getProviderModels(AIProvider.OPENAI);

      expect(models).toContain(AIModelName.GPT_3_5_TURBO);
      expect(models).toContain(AIModelName.GPT_4);
      expect(models).toContain(AIModelName.GPT_4_TURBO);
      expect(models).toContain(AIModelName.GPT_4O);
    });

    it('should return OLLAMA models', () => {
      const models = factory.getProviderModels(AIProvider.OLLAMA);

      expect(models).toContain(AIModelName.LLAMA_2_7B);
      expect(models).toContain(AIModelName.CODE_LLAMA_7B);
      expect(models).toContain(AIModelName.MISTRAL_7B);
      expect(models).toContain(AIModelName.PHI_3_MINI);
    });

    it('should return empty array for unknown provider', () => {
      const models = factory.getProviderModels('unknown' as AIProvider);

      expect(models).toEqual([]);
    });
  });

  describe('getDefaultModel', () => {
    it('should return default model for each provider', () => {
      expect(factory.getDefaultModel(AIProvider.OPENAI)).toBe(AIModelName.GPT_4_TURBO);
      expect(factory.getDefaultModel(AIProvider.GROK)).toBe(AIModelName.GROK_1_5);
      expect(factory.getDefaultModel(AIProvider.GEMINI)).toBe(AIModelName.GEMINI_PRO);
      expect(factory.getDefaultModel(AIProvider.OLLAMA)).toBe(AIModelName.LLAMA_2_7B);
    });
  });

  describe('getModelCapabilities', () => {
    it('should return capabilities for OpenAI models', () => {
      const capabilities = factory.getModelCapabilities(AIModelName.GPT_4_TURBO);

      expect(capabilities).toEqual({
        textGeneration: true,
        imageAnalysis: true,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 128000,
      });
    });

    it('should return capabilities for OLLAMA models', () => {
      const capabilities = factory.getModelCapabilities(AIModelName.LLAMA_2_7B);

      expect(capabilities).toEqual({
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: true,
        embedding: false,
        streaming: true,
        maxTokens: 4096,
        contextLength: 4096,
      });
    });

    it('should return default capabilities for unknown model', () => {
      const capabilities = factory.getModelCapabilities('unknown' as AIModelName);

      expect(capabilities).toEqual({
        textGeneration: true,
        imageAnalysis: false,
        codeGeneration: false,
        embedding: false,
        streaming: false,
        maxTokens: 2048,
        contextLength: 4096,
      });
    });
  });

  describe('validateProvider', () => {
    it('should validate provider successfully', async () => {
      configService.get
        .mockReturnValueOnce('test-key')
        .mockReturnValueOnce('http://localhost:11434');

      // Mock the provider's validateConfig method
      const mockProvider = {
        validateConfig: jest.fn().mockResolvedValue(true),
        listModels: jest.fn().mockResolvedValue(['model1', 'model2']),
      };

      jest.spyOn(factory, 'createProviderFromEnv').mockReturnValue(mockProvider as any);

      const result = await factory.validateProvider(AIProvider.OLLAMA);

      expect(result).toEqual({
        isValid: true,
        models: [],
      });
    });

    it('should handle validation errors', async () => {
      configService.get.mockReturnValue(undefined);

      const result = await factory.validateProvider(AIProvider.OPENAI);

      expect(result).toEqual({
        isValid: false,
        error: 'No configuration found for provider: openai',
      });
    });
  });
});
