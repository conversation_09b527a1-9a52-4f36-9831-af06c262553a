import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum TemplateType {
  MARKETING = 'marketing',
  TRANSACTIONAL = 'transactional',
  NEWSLETTER = 'newsletter',
  WELCOME = 'welcome',
  FOLLOW_UP = 'follow_up',
  REMINDER = 'reminder',
}

@Entity('email_templates')
@Index(['userId', 'templateType'])
export class EmailTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TemplateType,
  })
  templateType: TemplateType;

  @Column()
  subject: string;

  @Column({ type: 'text' })
  htmlContent: string;

  @Column({ type: 'text', nullable: true })
  textContent: string;

  @Column({ type: 'jsonb', nullable: true })
  variables: string[]; // List of template variables like {{name}}, {{company}}

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    trackOpens?: boolean;
    trackClicks?: boolean;
    unsubscribeLink?: boolean;
    replyTo?: string;
    fromName?: string;
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  usageCount: number;

  @Column({ nullable: true })
  lastUsedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
