import { IsString, Min<PERSON>ength, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ChangePasswordDto {
  @ApiProperty({
    example: 'CurrentPassword123!',
    description: 'Current password'
  })
  @IsString()
  currentPassword: string;

  @ApiProperty({
    example: 'NewSecurePassword123!',
    description: 'New password'
  })
  @IsString()
  @MinLength(8)
  newPassword: string;

  @ApiProperty({
    example: 'NewSecurePassword123!',
    description: 'Confirm new password'
  })
  @IsString()
  @MinLength(8)
  confirmNewPassword: string;

  @ApiProperty({
    example: '123456',
    description: 'Verification code (required if security method is enabled)',
    required: false
  })
  @IsOptional()
  @IsString()
  verificationCode?: string;
}
