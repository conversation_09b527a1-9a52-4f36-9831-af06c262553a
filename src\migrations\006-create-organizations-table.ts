import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOrganizationsTable1700000006 implements MigrationInterface {
  name = 'CreateOrganizationsTable1700000006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'organizations',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'slug',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'website',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'industry',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'size',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'logo',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'ownerId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['active', 'suspended', 'inactive'],
            default: "'active'",
          },
          {
            name: 'plan',
            type: 'enum',
            enum: ['free', 'basic', 'professional', 'enterprise'],
            default: "'free'",
          },
          {
            name: 'address',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'contact',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'settings',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'billing',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'limits',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'usage',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'memberCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'lastActivityAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_organizations_ownerId" ON "organizations" ("ownerId")
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_organizations_slug" ON "organizations" ("slug")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_organizations_status" ON "organizations" ("status")
    `);

    // Create foreign key
    await queryRunner.query(`
      ALTER TABLE "organizations" ADD CONSTRAINT "FK_organizations_ownerId"
      FOREIGN KEY ("ownerId") REFERENCES "users"("id") ON DELETE RESTRICT
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('organizations');
  }
}
