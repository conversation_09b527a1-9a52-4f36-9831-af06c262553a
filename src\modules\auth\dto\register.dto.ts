import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { AccountType } from '../../users/entities/user.entity';

export class RegisterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address (must be unique)'
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @Transform(({ value }) => value.toLowerCase().trim())
  email: string;

  @ApiProperty({
    example: 'username123',
    description: 'Username (3-30 characters, must be unique)',
    minLength: 3,
    maxLength: 30
  })
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(30, { message: 'Username cannot exceed 30 characters' })
  @Transform(({ value }) => value.toLowerCase().trim())
  username: string;

  @ApiProperty({
    example: 'SecurePassword123!',
    description: 'Password (minimum 8 characters, must contain uppercase, lowercase, number, and special character)',
    minLength: 8,
    maxLength: 100
  })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(100, { message: 'Password cannot exceed 100 characters' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    { message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character' }
  )
  password: string;

  @ApiProperty({
    example: 'John',
    description: 'First name',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1, { message: 'First name is required' })
  @MaxLength(50, { message: 'First name cannot exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name',
    minLength: 1,
    maxLength: 50
  })
  @IsString()
  @MinLength(1, { message: 'Last name is required' })
  @MaxLength(50, { message: 'Last name cannot exceed 50 characters' })
  @Transform(({ value }) => value.trim())
  lastName: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number (optional)'
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  phone?: string;

  @ApiPropertyOptional({
    example: 'Acme Corp',
    description: 'Company name (optional)',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Company name cannot exceed 100 characters' })
  @Transform(({ value }) => value?.trim())
  company?: string;

  @ApiPropertyOptional({
    example: 'https://example.com',
    description: 'Website URL (optional)',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MaxLength(255, { message: 'Website URL cannot exceed 255 characters' })
  @Transform(({ value }) => value?.trim())
  website?: string;

  @ApiPropertyOptional({
    enum: AccountType,
    example: AccountType.PERSONAL,
    description: 'Account type (personal or business)',
    enumName: 'AccountType'
  })
  @IsOptional()
  @IsEnum(AccountType, { message: 'Account type must be either personal or business' })
  accountType?: AccountType;
}
