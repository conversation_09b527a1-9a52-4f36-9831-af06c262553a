# 💻 Development Setup Guide

## 📋 Tổng quan

Hướng dẫn chi tiết để **setup môi trường development** cho Delify Platform, bao gồm prerequisites, installation, configuration, và development workflow.

## 🔧 Prerequisites

### System Requirements
```bash
# Required software versions
Node.js >= 18.0.0
npm >= 9.0.0
PostgreSQL >= 13.0
Redis >= 6.0 (optional, for caching)
Git >= 2.30.0

# Optional (for AI features)
Docker >= 20.10.0 (for OLLAMA)
Python >= 3.8 (for AI model management)
```

### Development Tools
```bash
# Recommended IDE
Visual Studio Code with extensions:
- TypeScript and JavaScript Language Features
- ESLint
- Prettier
- REST Client
- GitLens
- Thunder Client (for API testing)

# Alternative IDEs
WebStorm
Neovim with TypeScript support
```

## 🚀 Installation Process

### Step 1: Clone Repository
```bash
# Clone the repository
git clone <repository-url>
cd delify-platform

# Check Node.js version
node --version  # Should be >= 18.0.0
npm --version   # Should be >= 9.0.0
```

### Step 2: Install Dependencies
```bash
# Install all dependencies
npm install

# Install global tools (optional)
npm install -g @nestjs/cli
npm install -g typeorm
npm install -g nodemon
```

### Step 3: Database Setup
```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Install PostgreSQL (macOS with Homebrew)
brew install postgresql
brew services start postgresql

# Install PostgreSQL (Windows)
# Download from https://www.postgresql.org/download/windows/

# Create database and user
sudo -u postgres psql
CREATE DATABASE delify_platform;
CREATE USER delify_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE delify_platform TO delify_user;
\q
```

### Step 4: Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### Environment Variables Setup
```bash
# .env file configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=delify_user
DATABASE_PASSWORD=your_secure_password
DATABASE_NAME=delify_platform
DATABASE_SSL=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-min-32-characters
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-key
SESSION_MAX_AGE=86400000

# Email Configuration (for development)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_password
FROM_EMAIL=<EMAIL>

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AI Provider Configuration (optional)
OPENAI_API_KEY=sk-your-openai-api-key
GROK_API_KEY=xai-your-grok-api-key
GEMINI_API_KEY=your-gemini-api-key
OLLAMA_BASE_URL=http://localhost:11434

# Frontend URL
FRONTEND_URL=http://localhost:3000

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Logging
LOG_LEVEL=debug
LOG_FILE=true

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
```

## 🗄️ Database Initialization

### Step 1: Run Migrations
```bash
# Run database migrations
npm run migration:run

# Verify migration status
npm run migration:show
```

### Step 2: Seed Database
```bash
# Run database seeds
npm run seed:run

# This will create:
# - System roles (owner, admin, manager, member, viewer)
# - System permissions
# - Default admin user
# - Sample organization (optional)
```

### Step 3: Verify Database Setup
```bash
# Connect to database and verify
psql -h localhost -U delify_user -d delify_platform

# Check tables
\dt

# Check sample data
SELECT * FROM users LIMIT 5;
SELECT * FROM organizations LIMIT 5;
\q
```

## 🚀 Starting Development Server

### Basic Development
```bash
# Start development server
npm run start:dev

# Server will start on http://localhost:3000
# API documentation available at http://localhost:3000/api/v1/docs
```

### Advanced Development Setup
```bash
# Terminal 1: Start backend with watch mode
npm run start:dev

# Terminal 2: Start Redis (if using caching)
redis-server

# Terminal 3: Start OLLAMA (if using local AI)
ollama serve

# Terminal 4: Monitor logs
tail -f logs/application.log
```

## 🧪 Testing Setup

### Test Database Setup
```bash
# Create test database
sudo -u postgres psql
CREATE DATABASE delify_test;
GRANT ALL PRIVILEGES ON DATABASE delify_test TO delify_user;
\q

# Copy test environment
cp .env.example .env.test

# Edit test environment
nano .env.test
```

### Test Environment Variables
```bash
# .env.test
NODE_ENV=test
DATABASE_NAME=delify_test
DATABASE_SYNCHRONIZE=true
DATABASE_LOGGING=false

# Disable external services in tests
MOCK_EXTERNAL_APIS=true
SILENT_TESTS=true
TEST_TIMEOUT=30000
```

### Run Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:cov

# Run specific test suites
npm run test:unit
npm run test:e2e
npm run test:ai

# Run tests in watch mode
npm run test:watch
```

## 🔧 Development Tools Setup

### VS Code Configuration
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true
  }
}
```

### VS Code Extensions
```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "humao.rest-client",
    "eamodio.gitlens",
    "rangav.vscode-thunder-client",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss"
  ]
}
```

### Debug Configuration
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug NestJS",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/main.ts",
      "args": [],
      "runtimeArgs": [
        "-r",
        "ts-node/register",
        "-r",
        "tsconfig-paths/register"
      ],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": [
        "--runInBand",
        "--config",
        "test/jest.config.js"
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "env": {
        "NODE_ENV": "test"
      }
    }
  ]
}
```

## 🐳 Docker Development (Optional)

### Docker Compose Setup
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: delify-postgres-dev
    environment:
      POSTGRES_DB: delify_platform
      POSTGRES_USER: delify_user
      POSTGRES_PASSWORD: your_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

  redis:
    image: redis:7-alpine
    container_name: delify-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  ollama:
    image: ollama/ollama:latest
    container_name: delify-ollama-dev
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*

volumes:
  postgres_data:
  redis_data:
  ollama_data:
```

### Docker Commands
```bash
# Start development services
docker-compose -f docker-compose.dev.yml up -d

# Stop services
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Reset all data
docker-compose -f docker-compose.dev.yml down -v
```

## 🔧 Development Workflow

### Daily Development
```bash
# 1. Start development environment
npm run dev:start

# 2. Pull latest changes
git pull origin main

# 3. Install new dependencies (if any)
npm install

# 4. Run migrations (if any)
npm run migration:run

# 5. Start development server
npm run start:dev

# 6. Run tests before committing
npm run test:unit
npm run lint
```

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push branch
git push origin feature/new-feature-name

# Create pull request
# (Use GitHub/GitLab interface)
```

### Code Quality Checks
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Type checking
npm run build

# Run all quality checks
npm run quality:check
```

## 🧪 API Testing

### Using Thunder Client (VS Code)
```json
// thunder-tests/auth.json
{
  "name": "Login",
  "method": "POST",
  "url": "{{baseUrl}}/auth/login",
  "headers": [
    {
      "name": "Content-Type",
      "value": "application/json"
    }
  ],
  "body": {
    "type": "json",
    "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"
  }
}
```

### Using cURL
```bash
# Login
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Get profile (with token)
curl -X GET http://localhost:3000/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Using Postman
```json
// Import this collection
{
  "info": {
    "name": "Delify Platform API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3000/api/v1"
    }
  ]
}
```

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Error
```bash
# Error: ECONNREFUSED
# Solution: Check PostgreSQL service
sudo systemctl status postgresql
sudo systemctl start postgresql

# Check connection
psql -h localhost -U delify_user -d delify_platform
```

#### Port Already in Use
```bash
# Error: EADDRINUSE :::3000
# Solution: Kill process using port
lsof -ti:3000 | xargs kill -9

# Or use different port
PORT=3001 npm run start:dev
```

#### Migration Errors
```bash
# Error: relation already exists
# Solution: Check migration status
npm run migration:show

# Revert and re-run
npm run migration:revert
npm run migration:run
```

#### TypeScript Errors
```bash
# Error: Cannot find module
# Solution: Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Rebuild TypeScript
npm run build
```

### Performance Issues
```bash
# Check memory usage
node --max-old-space-size=4096 dist/main.js

# Enable debug logging
DEBUG=* npm run start:dev

# Profile application
node --inspect dist/main.js
```

## 📚 Additional Resources

### Documentation Links
- [NestJS Documentation](https://docs.nestjs.com/)
- [TypeORM Documentation](https://typeorm.io/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

### Development Scripts
```json
// package.json scripts reference
{
  "start:dev": "nest start --watch",
  "start:debug": "nest start --debug --watch",
  "build": "nest build",
  "test": "jest",
  "test:watch": "jest --watch",
  "test:cov": "jest --coverage",
  "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"",
  "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
  "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
  "migration:generate": "typeorm-ts-node-commonjs migration:generate",
  "migration:run": "typeorm-ts-node-commonjs migration:run",
  "seed:run": "ts-node src/database/seeds/run-seeds.ts"
}
```

## 🎯 Next Steps

### After Setup
1. **Explore API Documentation** - Visit http://localhost:3000/api/v1/docs
2. **Run Test Suite** - Ensure all tests pass
3. **Create Sample Data** - Use seeds or manual creation
4. **Test AI Features** - Setup AI providers and test endpoints
5. **Review Code Structure** - Understand module organization

### Development Best Practices
1. **Follow Git Flow** - Use feature branches
2. **Write Tests** - Maintain test coverage
3. **Code Reviews** - Review all changes
4. **Documentation** - Update docs with changes
5. **Performance** - Monitor and optimize

**Following this guide ensures a smooth development experience với Delify Platform!** 💻✨
