import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddFixedCodeToUsers1700000004 implements MigrationInterface {
  name = 'AddFixedCodeToUsers1700000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update security method enum to include fixed_code
    await queryRunner.query(`
      ALTER TYPE "security_method_enum" ADD VALUE 'fixed_code'
    `);

    // Add fixed code column
    await queryRunner.addColumn(
      'users',
      new TableColumn({
        name: 'fixedCode',
        type: 'varchar',
        length: '255',
        isNullable: true,
      })
    );

    // Create index for fixed code (for potential future queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_fixed_code ON users("fixedCode")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_fixed_code`);

    // Drop column
    await queryRunner.dropColumn('users', 'fixedCode');

    // Note: Cannot remove enum value in PostgreSQL easily, so we leave it
    // In production, you might want to handle this differently
  }
}
