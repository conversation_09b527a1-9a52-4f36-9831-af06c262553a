# 📖 Tài Liệu Tham Khảo

## 📋 Tổng Quan

Tài liệu tham khảo bao gồm **tài liệu API hoàn chỉnh** và **hướng dẫn khắc phục sự cố** cho Delify Platform.

## 📚 Hướng Dẫn Tham Khảo

### 🔗 **Tài Liệu API**
- **[Tham Khảo API](API_REFERENCE_vi.md)** - Tài liệu API hoàn chỉnh với ví dụ

### 🔧 **Hỗ Trợ & Khắc Phục Sự Cố**
- **[Khắc Phục Sự Cố](TROUBLESHOOTING_vi.md)** - Vấn đề thường gặp và giải pháp

## 🎯 Tham Khảo Nhanh

### API Endpoints
- **Xác <PERSON>h<PERSON>c**: `/api/v1/auth/*`
- **Người Dùng**: `/api/v1/users/*`
- **T<PERSON>ức**: `/api/v1/organizations/*`
- **Dịch <PERSON>**: `/api/v1/ai/*`
- **<PERSON><PERSON>m <PERSON>ra Sứ<PERSON> Khỏe**: `/health`

### Lệnh Thường Dùng
```bash
# Phát triển
npm run start:dev
npm test
npm run lint

# Cơ sở dữ liệu
npm run migration:run
npm run migration:generate
npm run seed:run

# Production
npm run build
npm run start:prod
```

### URLs Môi Trường
- **Phát Triển**: http://localhost:3000
- **API Docs**: http://localhost:3000/api/v1/docs
- **Kiểm Tra Sức Khỏe**: http://localhost:3000/health

**Những tài liệu tham khảo này cung cấp truy cập nhanh đến thông tin thiết yếu cho phát triển và khắc phục sự cố.** 📖✨
