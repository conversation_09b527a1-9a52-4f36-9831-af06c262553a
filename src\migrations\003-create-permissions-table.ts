import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePermissionsTable1700000003 implements MigrationInterface {
  name = 'CreatePermissionsTable1700000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'permissions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'displayName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'category',
            type: 'enum',
            enum: ['organization', 'users', 'marketing', 'ai', 'email', 'documents', 'workflows', 'invoices', 'scheduling', 'analytics', 'settings'],
            isNullable: false,
          },
          {
            name: 'action',
            type: 'enum',
            enum: ['create', 'read', 'update', 'delete', 'manage', 'execute', 'approve', 'export', 'import'],
            isNullable: false,
          },
          {
            name: 'resource',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'isSystemPermission',
            type: 'boolean',
            default: false,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_permissions_name" ON "permissions" ("name")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_permissions_category" ON "permissions" ("category")
    `);

    // Insert system permissions
    await this.insertSystemPermissions(queryRunner);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('permissions');
  }

  private async insertSystemPermissions(queryRunner: QueryRunner): Promise<void> {
    const permissions = [
      // Organization permissions
      { name: 'organization:read', displayName: 'View Organization', category: 'organization', action: 'read' },
      { name: 'organization:update', displayName: 'Edit Organization', category: 'organization', action: 'update' },
      { name: 'organization:delete', displayName: 'Delete Organization', category: 'organization', action: 'delete' },

      // User management permissions
      { name: 'users:read', displayName: 'View Users', category: 'users', action: 'read' },
      { name: 'users:create', displayName: 'Invite Users', category: 'users', action: 'create' },
      { name: 'users:update', displayName: 'Edit Users', category: 'users', action: 'update' },
      { name: 'users:delete', displayName: 'Remove Users', category: 'users', action: 'delete' },

      // Marketing permissions
      { name: 'marketing:campaigns:read', displayName: 'View Campaigns', category: 'marketing', action: 'read', resource: 'campaigns' },
      { name: 'marketing:campaigns:create', displayName: 'Create Campaigns', category: 'marketing', action: 'create', resource: 'campaigns' },
      { name: 'marketing:campaigns:update', displayName: 'Edit Campaigns', category: 'marketing', action: 'update', resource: 'campaigns' },
      { name: 'marketing:campaigns:delete', displayName: 'Delete Campaigns', category: 'marketing', action: 'delete', resource: 'campaigns' },

      // AI permissions
      { name: 'ai:models:read', displayName: 'View AI Models', category: 'ai', action: 'read', resource: 'models' },
      { name: 'ai:models:create', displayName: 'Create AI Models', category: 'ai', action: 'create', resource: 'models' },
      { name: 'ai:models:execute', displayName: 'Use AI Features', category: 'ai', action: 'execute', resource: 'models' },

      // Email permissions
      { name: 'email:campaigns:read', displayName: 'View Email Campaigns', category: 'email', action: 'read', resource: 'campaigns' },
      { name: 'email:campaigns:create', displayName: 'Create Email Campaigns', category: 'email', action: 'create', resource: 'campaigns' },
      { name: 'email:campaigns:update', displayName: 'Edit Email Campaigns', category: 'email', action: 'update', resource: 'campaigns' },
      { name: 'email:campaigns:delete', displayName: 'Delete Email Campaigns', category: 'email', action: 'delete', resource: 'campaigns' },

      // Documents permissions
      { name: 'documents:read', displayName: 'View Documents', category: 'documents', action: 'read' },
      { name: 'documents:create', displayName: 'Create Documents', category: 'documents', action: 'create' },
      { name: 'documents:update', displayName: 'Edit Documents', category: 'documents', action: 'update' },
      { name: 'documents:delete', displayName: 'Delete Documents', category: 'documents', action: 'delete' },

      // Workflows permissions
      { name: 'workflows:read', displayName: 'View Workflows', category: 'workflows', action: 'read' },
      { name: 'workflows:create', displayName: 'Create Workflows', category: 'workflows', action: 'create' },
      { name: 'workflows:execute', displayName: 'Execute Workflows', category: 'workflows', action: 'execute' },

      // Settings permissions
      { name: 'settings:read', displayName: 'View Settings', category: 'settings', action: 'read' },
      { name: 'settings:manage', displayName: 'Manage Settings', category: 'settings', action: 'manage' },

      // Analytics permissions
      { name: 'analytics:read', displayName: 'View Analytics', category: 'analytics', action: 'read' },
      { name: 'analytics:export', displayName: 'Export Analytics', category: 'analytics', action: 'export' },
    ];

    for (const permission of permissions) {
      await queryRunner.query(`
        INSERT INTO permissions (name, "displayName", description, category, action, resource, "isActive", "isSystemPermission")
        VALUES ($1, $2, $3, $4, $5, $6, true, true)
      `, [
        permission.name,
        permission.displayName,
        `System permission: ${permission.displayName}`,
        permission.category,
        permission.action,
        permission.resource || null,
      ]);
    }
  }
}
