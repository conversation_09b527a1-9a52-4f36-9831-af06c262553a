@echo off
echo 🚀 Setting up Delify Platform...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file...
    copy .env.example .env
    echo ✅ .env file created. Please edit it with your configuration.
) else (
    echo ✅ .env file already exists
)

REM Install dependencies
echo 📦 Installing dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Create necessary directories
echo 📁 Creating directories...
if not exist uploads mkdir uploads
if not exist uploads\images mkdir uploads\images
if not exist uploads\documents mkdir uploads\documents
if not exist uploads\temp mkdir uploads\temp
if not exist logs mkdir logs
if not exist keys mkdir keys

echo ✅ Directories created

REM Start Docker containers
echo 🐳 Starting Docker containers...
docker-compose up -d postgres redis

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 10 /nobreak >nul

echo ✅ PostgreSQL should be ready

REM Start the application in development mode
echo 🚀 Starting application in development mode...
docker-compose up -d

REM Wait for application to start
echo ⏳ Waiting for application to start...
timeout /t 15 /nobreak >nul

echo ✅ Setup completed!
echo.
echo 📍 Application URLs:
echo    API: http://localhost:3000/api/v1
echo    Swagger Docs: http://localhost:3000/api/v1/docs
echo.
echo 🔧 Next steps:
echo    1. Edit .env file with your API keys and configuration
echo    2. Restart the application: docker-compose restart app
echo    3. Visit the Swagger docs to explore the API
echo.
echo 📚 Useful commands:
echo    docker-compose up -d     - Start development environment
echo    docker-compose logs app  - View application logs
echo    docker-compose down      - Stop all containers

pause
