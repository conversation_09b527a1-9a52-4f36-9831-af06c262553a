# RBAC System Documentation

## Overview

This comprehensive Role-Based Access Control (RBAC) system provides fine-grained permission management for the NestJS application. The system supports hierarchical roles, granular permissions, and seamless integration with the existing authentication system.

## 🚀 Features

- **Hierarchical Role System**: 12 predefined roles from MASTER_ACCOUNT to GUEST
- **Granular Permissions**: 8 permission types across 10 system modules (80 total permissions)
- **Permission Inheritance**: Higher roles automatically inherit permissions from lower roles
- **JWT Integration**: RBAC information embedded in access tokens
- **Redis Caching**: High-performance permission caching with 15-minute TTL
- **Flexible Decorators**: Easy-to-use decorators for endpoint protection
- **Comprehensive API**: Full REST API for role and permission management
- **Audit Logging**: Complete audit trail for all permission changes
- **Database Optimized**: Proper indexing and query optimization

## 📋 Quick Start

### 1. Database Setup

Run the migration to create RBAC tables:

```bash
npm run migration:run
```

Seed the system with default roles and permissions:

```bash
npm run seed:rbac
```

### 2. Basic Usage

Protect your endpoints with RBAC decorators:

```typescript
import { RequirePermission, AdminOnly, ManagerOnly } from '../rbac/decorators/rbac.decorators';

@Controller('users')
export class UsersController {
  
  // Require specific permission
  @RequirePermission('USER_MANAGEMENT_READ')
  @Get()
  async getUsers() {
    return this.usersService.findAll();
  }

  // Admin only access
  @AdminOnly()
  @Delete(':id')
  async deleteUser(@Param('id') id: string) {
    return this.usersService.delete(id);
  }

  // Manager and above
  @ManagerOnly()
  @Post('approve')
  async approveUser(@Param('id') id: string) {
    return this.usersService.approve(id);
  }
}
```

### 3. Role Assignment

Assign roles to users programmatically:

```typescript
// Inject RBAC service
constructor(private rbacService: RBACService) {}

// Assign role to user
await this.rbacService.assignRoleToUser(
  userId, 
  roleId, 
  assignedBy,
  expiresAt // optional
);

// Check user permission
const result = await this.rbacService.checkPermission(
  userId, 
  'USER_MANAGEMENT_READ'
);

if (result.allowed) {
  // User has permission
}
```

## 🏗️ System Architecture

### Role Hierarchy

```
MASTER_ACCOUNT (Level 0) - Highest authority
├── ADMIN (Level 1) - System administrator
│   ├── MANAGER (Level 2) - Department manager
│   │   ├── MARKETING_LEAD (Level 3) - Marketing team lead
│   │   │   └── MARKETING_STAFF (Level 4) - Marketing staff
│   │   ├── SALES_LEAD (Level 3) - Sales team lead
│   │   │   └── SALES_STAFF (Level 4) - Sales staff
│   │   ├── CONTENT_LEAD (Level 3) - Content team lead
│   │   │   └── CONTENT_EDITOR (Level 4) - Content editor
│   │   └── ANALYST (Level 3) - Data analyst
│   └── VIEWER (Level 5) - Read-only access
└── GUEST (Level 6) - Limited access
```

### Permission Matrix

| Role | User Mgmt | Content | Marketing | Sales | Analytics | Financial | System |
|------|-----------|---------|-----------|-------|-----------|-----------|--------|
| MASTER_ACCOUNT | MANAGE | MANAGE | MANAGE | MANAGE | MANAGE | MANAGE | MANAGE |
| ADMIN | MANAGE | MANAGE | MANAGE | MANAGE | READ,EXPORT | READ | MANAGE |
| MANAGER | READ,UPDATE | APPROVE | APPROVE | APPROVE | READ,EXPORT | READ | READ |
| MARKETING_LEAD | READ | CREATE,UPDATE | MANAGE | READ | READ | - | - |
| SALES_LEAD | READ | READ | READ | MANAGE | READ | READ | - |
| VIEWER | READ | READ | READ | READ | READ | - | - |

### System Modules

1. **USER_MANAGEMENT** - User account management
2. **ROLE_MANAGEMENT** - Role and permission management
3. **CONTENT_MANAGEMENT** - Content creation and editing
4. **MARKETING_CAMPAIGNS** - Marketing campaign management
5. **SALES_PIPELINE** - Sales process management
6. **ANALYTICS_REPORTS** - Data analytics and reporting
7. **FINANCIAL_DATA** - Financial information access
8. **SYSTEM_SETTINGS** - System configuration
9. **AI_FEATURES** - AI-powered features
10. **EXPORT_TOOLS** - Data export capabilities

### Permission Actions

- **READ** - View/access data
- **CREATE** - Add new records
- **UPDATE** - Edit existing records
- **DELETE** - Remove records
- **EXPORT** - Download/export data
- **APPROVE** - Workflow approval rights
- **MANAGE** - Full control (includes all other actions)
- **ASSIGN** - Grant permissions/roles

## 📚 Documentation

### Implementation Guides
- [Implementation Guide (English)](./RBAC_IMPLEMENTATION_GUIDE.md)
- [Implementation Guide (Vietnamese)](./RBAC_IMPLEMENTATION_GUIDE_vi.md)

### API Documentation
- [API Documentation (English)](./RBAC_API_DOCUMENTATION.md)
- [API Documentation (Vietnamese)](./RBAC_API_DOCUMENTATION_vi.md)

### User Guides
- [User Guide (English)](./RBAC_USER_GUIDE.md)
- [User Guide (Vietnamese)](./RBAC_USER_GUIDE_vi.md)

### Flow Diagrams
- [Authentication Flow Diagrams (English)](../authentication/AUTHENTICATION_DIAGRAMS.md)
- [Authentication Flow Diagrams (Vietnamese)](../authentication/AUTHENTICATION_DIAGRAMS_vi.md)

## 🔧 Configuration

### Environment Variables

```env
# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=15m

# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/database
```

### Cache Configuration

The system uses Redis for caching user permissions:

- **Cache Key Pattern**: `user_permissions:${userId}`
- **TTL**: 15 minutes
- **Invalidation**: Automatic on role/permission changes

## 🧪 Testing

### Run Tests

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# RBAC specific tests
npm run test -- --testPathPattern=rbac
```

### Test Coverage

The RBAC system includes comprehensive tests:

- Unit tests for all services
- Integration tests for API endpoints
- Permission checking tests
- Cache invalidation tests
- Role hierarchy tests

## 🚀 Deployment

### Production Checklist

1. **Database Migration**: Ensure all RBAC tables are created
2. **Seed Data**: Run seed script for default roles and permissions
3. **Redis Setup**: Configure Redis for permission caching
4. **Environment Variables**: Set all required environment variables
5. **Security Review**: Audit default permissions and roles
6. **Monitoring**: Set up logging and monitoring for RBAC operations

### Performance Optimization

- Database indexes are automatically created for optimal query performance
- Redis caching reduces database load for permission checks
- Batch operations for role assignments
- Optimized queries with proper relations

## 🔒 Security Considerations

### Best Practices

1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Regular Audits**: Review and cleanup permissions periodically
3. **Role Expiration**: Use temporary roles with expiration dates
4. **Audit Logging**: Monitor all permission changes
5. **Rate Limiting**: Protect role management endpoints

### Security Features

- Permission conflicts resolution
- Role level validation
- System role protection (cannot be deleted)
- Audit trail for all changes
- Rate limiting on sensitive endpoints

## 🤝 Contributing

### Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run migrations: `npm run migration:run`
5. Seed data: `npm run seed:rbac`
6. Start development server: `npm run start:dev`

### Code Standards

- Follow existing TypeScript/NestJS patterns
- Add comprehensive tests for new features
- Update documentation for any changes
- Use proper error handling and validation

## 📞 Support

For questions or issues:

1. Check the documentation in the `docs/rbac/` directory
2. Review the implementation guide
3. Check existing issues in the repository
4. Create a new issue with detailed description

## 📄 License

This RBAC system is part of the main application and follows the same license terms.

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Compatibility**: NestJS 10.x, TypeScript 5.x, PostgreSQL 14+
