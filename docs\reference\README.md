# 📖 Reference Materials

## 📋 Tổng quan

Reference documentation bao gồm **complete API documentation** và **troubleshooting guides** cho Delify Platform.

## 📚 Reference Guides

### 🔗 **API Documentation**
- **[API Reference](API_REFERENCE.md)** - Complete API documentation với examples

### 🔧 **Support & Troubleshooting**
- **[Troubleshooting](TROUBLESHOOTING.md)** - Common issues và solutions

## 🎯 Quick Reference

### API Endpoints
- **Authentication**: `/api/v1/auth/*`
- **Users**: `/api/v1/users/*`
- **Organizations**: `/api/v1/organizations/*`
- **AI Services**: `/api/v1/ai/*`
- **Health Check**: `/health`

### Common Commands
```bash
# Development
npm run start:dev
npm test
npm run lint

# Database
npm run migration:run
npm run migration:generate
npm run seed:run

# Production
npm run build
npm run start:prod
```

### Environment URLs
- **Development**: http://localhost:3000
- **API Docs**: http://localhost:3000/api/v1/docs
- **Health Check**: http://localhost:3000/health

**These references provide quick access to essential information cho development và troubleshooting.** 📖✨
