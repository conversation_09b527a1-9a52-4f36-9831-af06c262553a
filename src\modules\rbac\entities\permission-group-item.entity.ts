import {
  <PERSON>ti<PERSON>,
  PrimaryColumn,
  <PERSON><PERSON>oOne,
  JoinColumn,
} from 'typeorm';
import { PermissionGroup } from './permission-group.entity';
import { Permission } from './permission.entity';

/**
 * PermissionGroupItem Entity - Cá<PERSON> quyền trong nhóm quyền
 * PermissionGroupItem Entity - Permissions in permission groups
 */
@Entity('permission_group_items')
export class PermissionGroupItem {
  /**
   * ID nhóm quyền - Permission group ID
   */
  @PrimaryColumn({ type: 'uuid', name: 'group_id' })
  groupId: string;

  /**
   * ID quyền hạn - Permission ID
   */
  @PrimaryColumn({ type: 'uuid', name: 'permission_id' })
  permissionId: string;

  // Relations

  /**
   * Nhóm quyền - Permission group
   */
  @ManyToOne(() => PermissionGroup, (group) => group.permissionGroupItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'group_id' })
  group: PermissionGroup;

  /**
   * <PERSON>uyề<PERSON> hạn - Permission
   */
  @ManyToOne(() => Permission, (permission) => permission.permissionGroupItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;
}
