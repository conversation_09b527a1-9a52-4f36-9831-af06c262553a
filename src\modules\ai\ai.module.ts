import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { AiController } from './ai.controller';
import { OllamaController } from './controllers/ollama.controller';
import { AiService } from './ai.service';
import { OpenAiService } from './services/openai.service';
import { ContentOptimizationService } from './services/content-optimization.service';
import { CvScoringService } from './services/cv-scoring.service';
import { ChatbotService } from './services/chatbot.service';
import { AIProviderFactory } from './providers/ai-provider.factory';
import { AiModel } from './entities/ai-model.entity';
import { ChatSession } from './entities/chat-session.entity';
import { CvAnalysis } from './entities/cv-analysis.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiModel,
      ChatSession,
      CvAnalysis,
    ]),
  ],
  controllers: [AiController, OllamaController],
  providers: [
    AiService,
    OpenAiService,
    ContentOptimizationService,
    CvScoringService,
    ChatbotService,
    AIProviderFactory,
  ],
  exports: [
    AiService,
    OpenAiService,
    ContentOptimizationService,
    CvScoringService,
    ChatbotService,
    AIProviderFactory,
  ],
})
export class AiModule {}
