import { config } from 'dotenv';
import { join } from 'path';

// Load test environment variables
config({ path: join(__dirname, '.env.test') });

// Global test setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';

  // Increase timeout for integration tests
  jest.setTimeout(30000);

  // Mock console methods to reduce noise in tests
  if (process.env.SILENT_TESTS === 'true') {
    global.console = {
      ...console,
      log: jest.fn(),
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };
  }
});

// Global test teardown
afterAll(async () => {
  // Clean up any global resources
  await new Promise(resolve => setTimeout(resolve, 100));
});

// Mock external dependencies that shouldn't be called in tests
jest.mock('axios', () => {
  const mockAxiosInstance = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
  };

  return {
    default: {
      create: jest.fn(() => mockAxiosInstance),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
    },
    create: jest.fn(() => mockAxiosInstance),
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
  };
});

// Mock Redis for tests
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    keys: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    on: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
  }));
});

// Mock email service
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    verify: jest.fn().mockResolvedValue(true),
  })),
}));

// Global test utilities
global.testUtils = {
  // Wait for a specific amount of time
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate random test data
  randomString: (length = 10) => Math.random().toString(36).substring(2, length + 2),
  randomEmail: () => `test-${Math.random().toString(36).substring(2)}@example.com`,
  randomNumber: (min = 0, max = 100) => Math.floor(Math.random() * (max - min + 1)) + min,

  // Mock data generators
  mockUser: () => ({
    id: `user-${Math.random().toString(36).substring(2)}`,
    email: global.testUtils.randomEmail(),
    accountType: 'business',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }),

  mockOrganization: () => ({
    id: `org-${Math.random().toString(36).substring(2)}`,
    name: `Test Organization ${global.testUtils.randomString(5)}`,
    type: 'company',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }),

  // AI-specific mock data
  mockAIResponse: () => ({
    content: 'This is a test AI response',
    usage: {
      promptTokens: global.testUtils.randomNumber(10, 50),
      completionTokens: global.testUtils.randomNumber(20, 100),
      totalTokens: global.testUtils.randomNumber(30, 150),
    },
    model: 'test-model',
    finishReason: 'stop',
    responseTime: global.testUtils.randomNumber(500, 3000),
  }),

  mockOllamaModel: () => ({
    name: `test-model:${global.testUtils.randomString(3)}`,
    modified_at: new Date().toISOString(),
    size: global.testUtils.randomNumber(**********, ***********),
    digest: `sha256:${global.testUtils.randomString(64)}`,
    details: {
      format: 'gguf',
      family: 'llama',
      families: ['llama'],
      parameter_size: '7B',
      quantization_level: 'Q4_0',
    },
  }),
};

// Extend Jest matchers for better assertions
expect.extend({
  toBeValidAIResponse(received) {
    const pass = received &&
      typeof received.content === 'string' &&
      received.usage &&
      typeof received.usage.promptTokens === 'number' &&
      typeof received.usage.completionTokens === 'number' &&
      typeof received.usage.totalTokens === 'number' &&
      typeof received.model === 'string' &&
      typeof received.finishReason === 'string' &&
      typeof received.responseTime === 'number';

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid AI response`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid AI response`,
        pass: false,
      };
    }
  },

  toBeValidOllamaModel(received) {
    const pass = received &&
      typeof received.name === 'string' &&
      typeof received.modified_at === 'string' &&
      typeof received.size === 'number' &&
      typeof received.digest === 'string' &&
      received.details &&
      typeof received.details.format === 'string';

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid OLLAMA model`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid OLLAMA model`,
        pass: false,
      };
    }
  },
});

// Type declarations for global utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidAIResponse(): R;
      toBeValidOllamaModel(): R;
    }
  }

  var testUtils: {
    wait: (ms: number) => Promise<void>;
    randomString: (length?: number) => string;
    randomEmail: () => string;
    randomNumber: (min?: number, max?: number) => number;
    mockUser: () => any;
    mockOrganization: () => any;
    mockAIResponse: () => any;
    mockOllamaModel: () => any;
  };
}
