import { Injectable, UnauthorizedException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User, AccountType, SecurityMethod } from '../users/entities/user.entity';
import { UserDeviceService } from '../users/services/user-device.service';
import { UtilsService } from '../../common/services/utils.service';
import { LoggerService } from '../../common/services/logger.service';
import { VerificationService } from './services/verification.service';
import { EmailVerificationService } from './services/email-verification.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UpdateSecurityMethodDto, SecurityMethodResponse } from './dto/security-method.dto';
import { LoginVerificationDto, PartialLoginResponse, SendVerificationCodeDto } from './dto/login-verification.dto';
import { RefreshTokenDto, TokenResponse, RevokeTokenDto } from './dto/refresh-token.dto';

export interface JwtPayload {
  sub: string;
  email: string;
  username: string;
  role: string;
  sessionToken?: string;
  // RBAC fields
  roles?: string[];
  permissions?: string[];
  roleLevel?: number;
  isMasterAccount?: boolean;
  permissionVersion?: number;
  // SSO fields
  iss?: string; // Issuer domain
  aud?: string[]; // Audience (allowed subdomains)
  sessionId?: string; // Global session ID
  deviceId?: string; // Device fingerprint
  jti?: string; // JWT ID for revocation
  domain?: string; // Base domain
  nbf?: number; // Not before timestamp
  ssoEnabled?: boolean; // SSO enabled flag
  applications?: string[]; // Allowed applications
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private userDeviceService: UserDeviceService,
    private utilsService: UtilsService,
    private logger: LoggerService,
    private verificationService: VerificationService,
    private emailVerificationService: EmailVerificationService,
    @Inject(forwardRef(() => 'RBACService'))
    private rbacService?: any, // Use any to avoid circular dependency issues
    @Inject(forwardRef(() => 'SSOService'))
    private ssoService?: any, // SSO service for cross-domain authentication
  ) {}

  async validateUser(emailOrUsername: string, password: string): Promise<User | null> {
    const user = await this.usersService.findByEmailOrUsername(emailOrUsername);

    if (user && await this.utilsService.comparePassword(password, user.password)) {
      if (!user.isActive) {
        throw new UnauthorizedException('Account is not active');
      }
      return user;
    }

    return null;
  }

  async login(loginDto: LoginDto, deviceInfo: {
    ip: string;
    userAgent: string;
    deviceName?: string;
    location?: string;
  }): Promise<AuthResponse | PartialLoginResponse> {
    const { emailOrUsername, password } = loginDto;

    const user = await this.validateUser(emailOrUsername, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user has security method enabled
    if (user.hasSecurityMethodEnabled()) {
      // Create verification session
      const sessionId = this.verificationService.createVerificationSession(
        user.id,
        user.securityMethod,
        deviceInfo
      );

      // Send verification code if needed
      if (user.requiresEmailVerification()) {
        const verificationCode = this.verificationService.generateVerificationCode();

        // Store verification code in user record
        await this.usersService.setVerificationCode(user.id, verificationCode);

        // Send email
        await this.emailVerificationService.sendVerificationCode(user.email, verificationCode, 'login');
      }

      const message = user.requiresEmailVerification()
        ? 'Verification code sent to your email'
        : user.requiresTwoFactorAuth()
        ? 'Please enter your authenticator code'
        : 'Please enter your fixed verification code';

      return {
        sessionId,
        requiredVerification: user.securityMethod,
        message,
        expiresIn: 300, // 5 minutes
      };
    }

    // No additional verification required, complete login
    return this.completeLogin(user, deviceInfo);
  }

  /**
   * Complete the login process and issue tokens
   */
  private async completeLogin(user: User, deviceInfo: {
    ip: string;
    userAgent: string;
    deviceName?: string;
    location?: string;
  }): Promise<AuthResponse> {
    // Create device session
    const device = await this.userDeviceService.createDeviceSession(user.id, {
      userAgent: deviceInfo.userAgent,
      ipAddress: deviceInfo.ip,
      deviceName: deviceInfo.deviceName,
      location: deviceInfo.location,
    });

    // Update last login
    await this.usersService.updateLastLogin(user.id, deviceInfo.ip);

    // Generate tokens
    const { accessToken, refreshToken, expiresIn } = await this.generateTokens(user, device.sessionToken);

    this.logger.logWithContext(`User logged in: ${user.email} from ${deviceInfo.ip}`, 'AuthService');

    return {
      user,
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: 'Bearer',
    };
  }

  /**
   * Generate access and refresh tokens
   */
  private async generateTokens(user: User, sessionToken?: string): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    // Get RBAC information if service is available
    let rbacInfo: any = {};
    if (this.rbacService) {
      try {
        const userPermissions = await this.rbacService.getUserPermissions(user.id);
        rbacInfo = {
          roles: userPermissions.roles,
          permissions: userPermissions.permissions,
          roleLevel: userPermissions.roleLevel,
          isMasterAccount: userPermissions.isMasterAccount,
          permissionVersion: Date.now(), // For cache invalidation
        };
      } catch (error) {
        // If RBAC service fails, continue without RBAC info
        this.logger.logWithContext(`Failed to get RBAC info for user ${user.id}: ${error.message}`, 'AuthService');
      }
    }

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
      sessionToken,
      ...rbacInfo,
    };

    // Generate access token (short-lived: 15 minutes)
    const accessToken = this.jwtService.sign(payload, { expiresIn: '15m' });

    // Generate refresh token (long-lived: 7 days)
    const refreshTokenPayload = { sub: user.id, type: 'refresh' };
    const refreshToken = this.jwtService.sign(refreshTokenPayload, { expiresIn: '7d' });

    // Store refresh token in database
    const refreshTokenExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    await this.usersService.setRefreshToken(user.id, refreshToken, refreshTokenExpires);

    return {
      accessToken,
      refreshToken,
      expiresIn: 900, // 15 minutes in seconds
    };
  }

  /**
   * SSO Login - Enhanced login with SSO support
   */
  async ssoLogin(
    loginDto: LoginDto,
    deviceInfo?: any,
    application?: string,
  ): Promise<{
    accessToken: string;
    refreshToken: string;
    sessionId?: string;
    expiresIn: number;
    ssoEnabled: boolean;
    allowedApplications?: string[];
  }> {
    // Validate user credentials
    const user = await this.validateUser(loginDto.emailOrUsername, loginDto.password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if SSO is available and enabled
    if (this.ssoService) {
      try {
        // Get RBAC information
        let rbacInfo: any = {};
        if (this.rbacService) {
          const userPermissions = await this.rbacService.getUserPermissions(user.id);
          rbacInfo = {
            email: user.email,
            username: user.username,
            role: user.role,
            roles: userPermissions.roles,
            permissions: userPermissions.permissions,
            roleLevel: userPermissions.roleLevel,
            isMasterAccount: userPermissions.isMasterAccount,
          };
        }

        // Perform SSO login
        const ssoResult = await this.ssoService.ssoLogin({
          userId: user.id,
          deviceInfo: deviceInfo || {},
          application,
          deviceName: deviceInfo?.deviceName,
          location: deviceInfo?.location,
        }, rbacInfo);

        return {
          ...ssoResult,
          ssoEnabled: true,
        };
      } catch (error) {
        this.logger.logWithContext(
          `SSO login failed for user ${user.id}, falling back to regular login: ${error.message}`,
          'AuthService'
        );
        // Fall back to regular login if SSO fails
      }
    }

    // Regular login fallback
    const tokens = await this.generateTokens(user);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: tokens.expiresIn,
      ssoEnabled: false,
    };
  }

  /**
   * SSO Logout - Enhanced logout with SSO support
   */
  async ssoLogout(
    userId: string,
    sessionId?: string,
    globalLogout: boolean = false,
    deviceInfo?: any,
  ): Promise<{ message: string; sessionsTerminated?: number }> {
    if (this.ssoService && sessionId) {
      try {
        const result = await this.ssoService.ssoLogout(sessionId, userId, globalLogout, deviceInfo);
        return result;
      } catch (error) {
        this.logger.logWithContext(
          `SSO logout failed for user ${userId}: ${error.message}`,
          'AuthService'
        );
      }
    }

    // Regular logout fallback
    await this.usersService.clearRefreshToken(userId);

    return {
      message: 'Logout successful',
    };
  }

  async register(registerDto: RegisterDto & { accountType?: AccountType }, deviceInfo: {
    ip: string;
    userAgent: string;
    deviceName?: string;
    location?: string;
  }): Promise<AuthResponse> {
    const user = await this.usersService.create({
      ...registerDto,
      accountType: registerDto.accountType || AccountType.PERSONAL,
    });

    // Create initial device session
    const device = await this.userDeviceService.createDeviceSession(user.id, {
      userAgent: deviceInfo.userAgent,
      ipAddress: deviceInfo.ip,
      deviceName: deviceInfo.deviceName,
      location: deviceInfo.location,
    });

    // Generate tokens
    const { accessToken, refreshToken, expiresIn } = await this.generateTokens(user, device.sessionToken);

    this.logger.logWithContext(`User registered: ${user.email} (${user.accountType})`, 'AuthService');

    return {
      user,
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: 'Bearer',
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const { email } = forgotPasswordDto;

    try {
      const resetToken = await this.usersService.setPasswordResetToken(email);

      // TODO: Send email with reset token
      // await this.emailService.sendPasswordResetEmail(email, resetToken);

      this.logger.logWithContext(`Password reset requested for: ${email}`, 'AuthService');

      return { message: 'Password reset email sent' };
    } catch (error) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent' };
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { token, newPassword } = resetPasswordDto;

    const user = await this.usersService.resetPassword(token, newPassword);

    this.logger.logWithContext(`Password reset completed for: ${user.email}`, 'AuthService');

    return { message: 'Password reset successfully' };
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const { currentPassword, newPassword, confirmNewPassword, verificationCode } = changePasswordDto;

    const user = await this.usersService.findOne(userId);

    // Verify current password
    const isCurrentPasswordValid = await this.utilsService.comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Verify new password confirmation
    if (newPassword !== confirmNewPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Check if user has security method enabled and verify code
    if (user.hasSecurityMethodEnabled()) {
      if (!verificationCode) {
        throw new BadRequestException('Verification code is required');
      }

      let isValidCode = false;

      if (user.requiresEmailVerification()) {
        isValidCode = user.isVerificationCodeValid(verificationCode);
      } else if (user.requiresTwoFactorAuth()) {
        if (!user.twoFactorSecret) {
          throw new BadRequestException('Two-factor authentication not properly configured');
        }
        isValidCode = this.verificationService.verifyTwoFactorCode(user.twoFactorSecret, verificationCode);
      } else if (user.requiresFixedCode()) {
        if (!user.fixedCode) {
          throw new BadRequestException('Fixed code not configured');
        }
        isValidCode = await this.verificationService.verifyFixedCode(verificationCode, user.fixedCode);
      }

      if (!isValidCode) {
        throw new BadRequestException('Invalid verification code');
      }

      // Clear verification code if it was email verification
      if (user.requiresEmailVerification()) {
        await this.usersService.clearVerificationCode(user.id);
      }
    }

    // Update password
    await this.usersService.update(userId, { password: newPassword });

    // Send notification email
    await this.emailVerificationService.sendPasswordChangeNotification(user.email);

    this.logger.logWithContext(`Password changed for: ${user.email}`, 'AuthService');

    return { message: 'Password changed successfully' };
  }

  /**
   * Send verification code for password change
   */
  async sendPasswordChangeVerificationCode(userId: string): Promise<{ message: string }> {
    const user = await this.usersService.findOne(userId);

    if (!user.hasSecurityMethodEnabled()) {
      throw new BadRequestException('No security method enabled for this user');
    }

    if (user.requiresEmailVerification()) {
      const verificationCode = this.verificationService.generateVerificationCode();

      // Store verification code in user record
      await this.usersService.setVerificationCode(user.id, verificationCode);

      // Send email
      await this.emailVerificationService.sendVerificationCode(user.email, verificationCode, 'password_change');

      return { message: 'Verification code sent to your email' };
    }

    if (user.requiresTwoFactorAuth()) {
      return { message: 'Please enter your authenticator code' };
    }

    if (user.requiresFixedCode()) {
      return { message: 'Please enter your fixed verification code' };
    }

    throw new BadRequestException('Invalid security method configuration');
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    const user = await this.usersService.verifyEmail(token);

    this.logger.logWithContext(`Email verified for: ${user.email}`, 'AuthService');

    return { message: 'Email verified successfully' };
  }



  async validateJwtPayload(payload: JwtPayload): Promise<User> {
    const user = await this.usersService.findOne(payload.sub);

    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Validate session if sessionToken is present
    if (payload.sessionToken) {
      const isValidSession = await this.validateSession(payload.sessionToken);
      if (!isValidSession) {
        throw new UnauthorizedException('Session expired or invalid');
      }
    }

    return user;
  }

  async logout(sessionToken: string): Promise<{ message: string }> {
    await this.userDeviceService.logoutDevice(sessionToken);

    this.logger.logWithContext(`User logged out`, 'AuthService');

    return { message: 'Logged out successfully' };
  }

  async logoutAllDevices(userId: string, currentSessionToken?: string): Promise<{ message: string; devicesLoggedOut: number }> {
    const devicesLoggedOut = await this.userDeviceService.logoutAllDevices(userId, currentSessionToken);

    this.logger.logWithContext(`User logged out from ${devicesLoggedOut} devices`, 'AuthService');

    return {
      message: 'Logged out from all devices successfully',
      devicesLoggedOut,
    };
  }

  async validateSession(sessionToken: string): Promise<boolean> {
    const device = await this.userDeviceService.getDeviceByToken(sessionToken);

    if (!device || !device.isValid) {
      return false;
    }

    // Update device activity
    await this.userDeviceService.updateDeviceActivity(sessionToken);

    return true;
  }

  /**
   * Verify login with verification code
   */
  async verifyLogin(verificationDto: LoginVerificationDto, deviceInfo: {
    ip: string;
    userAgent: string;
    deviceName?: string;
    location?: string;
  }): Promise<AuthResponse> {
    const { sessionId, verificationCode } = verificationDto;

    // Get verification session
    const session = this.verificationService.getVerificationSession(sessionId);
    if (!session) {
      throw new UnauthorizedException('Invalid or expired verification session');
    }

    // Get user
    const user = await this.usersService.findOne(session.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Verify code based on security method
    let isValidCode = false;

    if (user.requiresEmailVerification()) {
      isValidCode = user.isVerificationCodeValid(verificationCode);
    } else if (user.requiresTwoFactorAuth()) {
      if (!user.twoFactorSecret) {
        throw new BadRequestException('Two-factor authentication not properly configured');
      }
      isValidCode = this.verificationService.verifyTwoFactorCode(user.twoFactorSecret, verificationCode);
    } else if (user.requiresFixedCode()) {
      if (!user.fixedCode) {
        throw new BadRequestException('Fixed code not configured');
      }
      isValidCode = await this.verificationService.verifyFixedCode(verificationCode, user.fixedCode);
    }

    if (!isValidCode) {
      throw new UnauthorizedException('Invalid verification code');
    }

    // Clear verification code if it was email verification
    if (user.requiresEmailVerification()) {
      await this.usersService.clearVerificationCode(user.id);
    }

    // Remove verification session
    this.verificationService.removeVerificationSession(sessionId);

    // Complete login
    return this.completeLogin(user, deviceInfo);
  }

  /**
   * Resend verification code
   */
  async resendVerificationCode(dto: SendVerificationCodeDto): Promise<{ message: string }> {
    const { sessionId } = dto;

    // Get verification session
    const session = this.verificationService.getVerificationSession(sessionId);
    if (!session) {
      throw new UnauthorizedException('Invalid or expired verification session');
    }

    // Get user
    const user = await this.usersService.findOne(session.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (user.requiresEmailVerification()) {
      const verificationCode = this.verificationService.generateVerificationCode();

      // Store verification code in user record
      await this.usersService.setVerificationCode(user.id, verificationCode);

      // Send email
      await this.emailVerificationService.sendVerificationCode(user.email, verificationCode, 'login');

      return { message: 'Verification code sent to your email' };
    }

    throw new BadRequestException('Verification code resend not supported for this security method');
  }

  /**
   * Get user's security method configuration
   */
  async getSecurityMethod(userId: string): Promise<SecurityMethodResponse> {
    const user = await this.usersService.findOne(userId);

    return {
      securityMethod: user.securityMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      hasFixedCode: user.hasFixedCodeSet(),
    };
  }

  /**
   * Update user's security method
   */
  async updateSecurityMethod(userId: string, dto: UpdateSecurityMethodDto): Promise<SecurityMethodResponse> {
    const { securityMethod, verificationCode, fixedCode } = dto;
    const user = await this.usersService.findOne(userId);

    // If enabling 2FA, generate secret and return setup info
    if (securityMethod === SecurityMethod.TWO_FACTOR_AUTH) {
      if (!verificationCode) {
        // First step: generate 2FA secret
        const { secret, qrCodeUrl, manualEntryKey } = this.verificationService.generateTwoFactorSecret(user.email);

        // Store secret temporarily (not enabled yet)
        await this.usersService.update(userId, { twoFactorSecret: secret });

        return {
          securityMethod: user.securityMethod, // Keep current until verified
          twoFactorEnabled: false,
          qrCodeUrl,
          manualEntryKey,
        };
      } else {
        // Second step: verify 2FA code and enable
        if (!user.twoFactorSecret) {
          throw new BadRequestException('Two-factor setup not initiated');
        }

        const isValidCode = this.verificationService.verifyTwoFactorCode(user.twoFactorSecret, verificationCode);
        if (!isValidCode) {
          throw new BadRequestException('Invalid verification code');
        }

        // Enable 2FA
        await this.usersService.update(userId, {
          securityMethod: SecurityMethod.TWO_FACTOR_AUTH,
          twoFactorEnabled: true,
        });

        this.logger.logWithContext(`Two-factor authentication enabled for user: ${user.email}`, 'AuthService');
      }
    } else if (securityMethod === SecurityMethod.EMAIL_VERIFICATION) {
      // Enable email verification
      await this.usersService.update(userId, {
        securityMethod: SecurityMethod.EMAIL_VERIFICATION,
        twoFactorEnabled: false,
        twoFactorSecret: null,
      });

      this.logger.logWithContext(`Email verification enabled for user: ${user.email}`, 'AuthService');
    } else if (securityMethod === SecurityMethod.FIXED_CODE) {
      // Enable fixed code verification
      if (!fixedCode) {
        throw new BadRequestException('Fixed code is required when enabling FIXED_CODE method');
      }

      // Validate fixed code format
      const validation = this.verificationService.validateFixedCodeFormat(fixedCode);
      if (!validation.isValid) {
        throw new BadRequestException(validation.error);
      }

      // Hash and store the fixed code
      const hashedFixedCode = await this.verificationService.hashFixedCode(fixedCode);

      await this.usersService.update(userId, {
        securityMethod: SecurityMethod.FIXED_CODE,
        twoFactorEnabled: false,
        twoFactorSecret: null,
        fixedCode: hashedFixedCode,
      });

      this.logger.logWithContext(`Fixed code verification enabled for user: ${user.email}`, 'AuthService');
    } else {
      // Disable security method
      await this.usersService.update(userId, {
        securityMethod: SecurityMethod.DISABLED,
        twoFactorEnabled: false,
        twoFactorSecret: null,
        fixedCode: null,
      });

      this.logger.logWithContext(`Security method disabled for user: ${user.email}`, 'AuthService');
    }

    // Send notification email
    await this.emailVerificationService.sendSecurityMethodChangeNotification(user.email, securityMethod);

    const updatedUser = await this.usersService.findOne(userId);
    return {
      securityMethod: updatedUser.securityMethod,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      hasFixedCode: updatedUser.hasFixedCodeSet(),
    };
  }

  /**
   * Change user's fixed code
   */
  async changeFixedCode(userId: string, currentCode: string, newCode: string): Promise<{ message: string }> {
    const user = await this.usersService.findOne(userId);

    // Check if user has fixed code enabled
    if (!user.requiresFixedCode()) {
      throw new BadRequestException('Fixed code verification is not enabled for this user');
    }

    // Verify current fixed code
    if (!user.fixedCode) {
      throw new BadRequestException('No fixed code is currently set');
    }

    const isCurrentCodeValid = await this.verificationService.verifyFixedCode(currentCode, user.fixedCode);
    if (!isCurrentCodeValid) {
      throw new BadRequestException('Current fixed code is incorrect');
    }

    // Validate new fixed code format
    const validation = this.verificationService.validateFixedCodeFormat(newCode);
    if (!validation.isValid) {
      throw new BadRequestException(validation.error);
    }

    // Check if new code is different from current code
    const isSameCode = await this.verificationService.verifyFixedCode(newCode, user.fixedCode);
    if (isSameCode) {
      throw new BadRequestException('New fixed code must be different from the current one');
    }

    // Hash and update the fixed code
    const hashedNewCode = await this.verificationService.hashFixedCode(newCode);
    await this.usersService.setFixedCode(userId, hashedNewCode);

    // Send notification email
    await this.emailVerificationService.sendSecurityMethodChangeNotification(user.email, 'fixed_code_changed');

    this.logger.logWithContext(`Fixed code changed for user: ${user.email}`, 'AuthService');

    return { message: 'Fixed code changed successfully' };
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<TokenResponse> {
    const { refreshToken } = refreshTokenDto;

    try {
      // Verify refresh token
      const decoded = this.jwtService.verify(refreshToken);

      if (decoded.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Find user by refresh token
      const user = await this.usersService.findByRefreshToken(refreshToken);
      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Verify token matches stored token
      if (!user.isRefreshTokenValid(refreshToken)) {
        throw new UnauthorizedException('Refresh token expired or invalid');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      this.logger.logWithContext(`Tokens refreshed for user: ${user.email}`, 'AuthService');

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn,
        tokenType: 'Bearer',
      };
    } catch (error) {
      this.logger.logWithContext(`Refresh token error: ${error.message}`, 'AuthService');
      throw new UnauthorizedException('Invalid or expired refresh token');
    }
  }

  /**
   * Revoke refresh token (logout)
   */
  async revokeToken(revokeTokenDto: RevokeTokenDto): Promise<{ message: string }> {
    const { refreshToken } = revokeTokenDto;

    try {
      // Verify refresh token
      const decoded = this.jwtService.verify(refreshToken);

      if (decoded.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Find user by refresh token
      const user = await this.usersService.findByRefreshToken(refreshToken);
      if (user) {
        // Clear refresh token from database
        await this.usersService.clearRefreshToken(user.id);
        this.logger.logWithContext(`Refresh token revoked for user: ${user.email}`, 'AuthService');
      }

      return { message: 'Token revoked successfully' };
    } catch (error) {
      // Even if token is invalid, we return success for security
      this.logger.logWithContext(`Token revocation attempt: ${error.message}`, 'AuthService');
      return { message: 'Token revoked successfully' };
    }
  }

  /**
   * Revoke all refresh tokens for a user
   */
  async revokeAllTokens(userId: string): Promise<{ message: string }> {
    await this.usersService.clearRefreshToken(userId);

    const user = await this.usersService.findOne(userId);
    this.logger.logWithContext(`All tokens revoked for user: ${user.email}`, 'AuthService');

    return { message: 'All tokens revoked successfully' };
  }
}
