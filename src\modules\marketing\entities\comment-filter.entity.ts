import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SocialPlatform } from './social-post.entity';

export enum FilterType {
  CONTAINS_ADDRESS = 'contains_address',
  CONTAINS_PHONE = 'contains_phone',
  CONTAINS_EMAIL = 'contains_email',
  CONSULTATION_REQUEST = 'consultation_request',
  BUSINESS_INQUIRY = 'business_inquiry',
  CUSTOM_KEYWORD = 'custom_keyword',
}

export enum FilterAction {
  NOTIFY = 'notify',
  AUTO_REPLY = 'auto_reply',
  FORWARD_TO_SALES = 'forward_to_sales',
  CREATE_LEAD = 'create_lead',
  IGNORE = 'ignore',
}

@Entity('comment_filters')
@Index(['userId', 'platform'])
@Index(['isActive', 'platform'])
export class CommentFilter {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SocialPlatform,
  })
  platform: SocialPlatform;

  @Column({
    type: 'enum',
    enum: FilterType,
  })
  filterType: FilterType;

  @Column({ type: 'jsonb' })
  keywords: string[]; // Keywords to match

  @Column({ type: 'jsonb', nullable: true })
  patterns: string[]; // Regex patterns for advanced matching

  @Column({
    type: 'enum',
    enum: FilterAction,
  })
  action: FilterAction;

  @Column({ type: 'text', nullable: true })
  autoReplyMessage: string;

  @Column({ type: 'jsonb', nullable: true })
  notificationSettings: {
    email?: boolean;
    webhook?: string;
    slackChannel?: string;
  };

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  priority: number; // Higher priority filters are checked first

  @Column({ default: 0 })
  matchCount: number; // Number of times this filter has matched

  @Column({ nullable: true })
  lastMatchAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    caseSensitive?: boolean;
    wholeWordsOnly?: boolean;
    includeReplies?: boolean;
    minConfidence?: number; // For AI-based filtering
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isKeywordFilter(): boolean {
    return this.filterType === FilterType.CUSTOM_KEYWORD;
  }

  get isAIFilter(): boolean {
    return [
      FilterType.CONTAINS_ADDRESS,
      FilterType.CONTAINS_PHONE,
      FilterType.CONTAINS_EMAIL,
      FilterType.CONSULTATION_REQUEST,
      FilterType.BUSINESS_INQUIRY,
    ].includes(this.filterType);
  }

  get shouldNotify(): boolean {
    return this.action === FilterAction.NOTIFY;
  }

  get shouldAutoReply(): boolean {
    return this.action === FilterAction.AUTO_REPLY && !!this.autoReplyMessage;
  }
}
