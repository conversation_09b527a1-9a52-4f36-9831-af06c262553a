import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * SSOApplication Entity - Quản lý các ứng dụng hỗ trợ SSO
 * SSOApplication Entity - Manages SSO-enabled applications
 */
@Entity('sso_applications')
@Index(['subdomain'])
@Index(['isActive'])
export class SSOApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên ứng dụng (unique) - Application name (unique)
   */
  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  /**
   * Subdomain (unique) - Subdomain (unique)
   */
  @Column({ type: 'varchar', length: 100, unique: true })
  subdomain: string;

  /**
   * Tên hiển thị - Display name
   */
  @Column({ type: 'varchar', length: 200, name: 'display_name' })
  displayName: string;

  /**
   * <PERSON>ô tả ứng dụng - Application description
   */
  @Column({ type: 'text', nullable: true })
  description?: string;

  /**
   * URL gốc của ứng dụng - Base URL of application
   */
  @Column({ type: 'varchar', length: 500, name: 'base_url' })
  baseUrl: string;

  /**
   * Danh sách origins được phép - Allowed origins list
   */
  @Column({ type: 'text', array: true, nullable: true, name: 'allowed_origins' })
  allowedOrigins?: string[];

  /**
   * Trạng thái hoạt động - Active status
   */
  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Virtual properties

  /**
   * Lấy URL đầy đủ - Get full URL
   */
  get fullUrl(): string {
    return this.baseUrl;
  }

  /**
   * Lấy domain pattern cho cookie - Get domain pattern for cookie
   */
  get cookieDomain(): string {
    try {
      const url = new URL(this.baseUrl);
      const hostParts = url.hostname.split('.');
      if (hostParts.length >= 2) {
        return `.${hostParts.slice(-2).join('.')}`;
      }
      return url.hostname;
    } catch {
      return this.subdomain;
    }
  }

  /**
   * Kiểm tra origin có được phép không - Check if origin is allowed
   */
  isOriginAllowed(origin: string): boolean {
    if (!this.allowedOrigins || this.allowedOrigins.length === 0) {
      return origin === this.baseUrl;
    }
    
    return this.allowedOrigins.some(allowedOrigin => {
      // Exact match
      if (allowedOrigin === origin) return true;
      
      // Wildcard match
      if (allowedOrigin.includes('*')) {
        const pattern = allowedOrigin.replace(/\*/g, '.*');
        const regex = new RegExp(`^${pattern}$`);
        return regex.test(origin);
      }
      
      return false;
    });
  }

  /**
   * Kiểm tra subdomain có hợp lệ không - Check if subdomain is valid
   */
  static isValidSubdomain(subdomain: string): boolean {
    const subdomainRegex = /^[a-z0-9]([a-z0-9-]*[a-z0-9])?$/;
    return subdomainRegex.test(subdomain) && subdomain.length <= 63;
  }

  /**
   * Tạo base URL từ subdomain và domain - Generate base URL from subdomain and domain
   */
  static generateBaseUrl(subdomain: string, baseDomain: string, useHttps: boolean = true): string {
    const protocol = useHttps ? 'https' : 'http';
    return `${protocol}://${subdomain}.${baseDomain}`;
  }

  /**
   * Parse subdomain từ URL - Parse subdomain from URL
   */
  static parseSubdomain(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const hostParts = urlObj.hostname.split('.');
      
      if (hostParts.length >= 3) {
        return hostParts[0];
      }
      
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Validate application configuration - Validate application configuration
   */
  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate name
    if (!this.name || this.name.trim().length === 0) {
      errors.push('Application name is required');
    }

    // Validate subdomain
    if (!this.subdomain || !SSOApplication.isValidSubdomain(this.subdomain)) {
      errors.push('Valid subdomain is required');
    }

    // Validate base URL
    try {
      new URL(this.baseUrl);
    } catch {
      errors.push('Valid base URL is required');
    }

    // Validate allowed origins
    if (this.allowedOrigins) {
      for (const origin of this.allowedOrigins) {
        if (origin !== '*' && !origin.includes('*')) {
          try {
            new URL(origin);
          } catch {
            errors.push(`Invalid origin: ${origin}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Cập nhật allowed origins - Update allowed origins
   */
  updateAllowedOrigins(origins: string[]): void {
    this.allowedOrigins = origins.filter(origin => origin && origin.trim().length > 0);
  }

  /**
   * Thêm allowed origin - Add allowed origin
   */
  addAllowedOrigin(origin: string): void {
    if (!this.allowedOrigins) {
      this.allowedOrigins = [];
    }
    
    if (!this.allowedOrigins.includes(origin)) {
      this.allowedOrigins.push(origin);
    }
  }

  /**
   * Xóa allowed origin - Remove allowed origin
   */
  removeAllowedOrigin(origin: string): void {
    if (this.allowedOrigins) {
      this.allowedOrigins = this.allowedOrigins.filter(o => o !== origin);
    }
  }

  /**
   * Kích hoạt ứng dụng - Activate application
   */
  activate(): void {
    this.isActive = true;
  }

  /**
   * Vô hiệu hóa ứng dụng - Deactivate application
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * Lấy thông tin tóm tắt - Get summary information
   */
  get summary(): string {
    return `${this.displayName} (${this.subdomain})`;
  }
}
