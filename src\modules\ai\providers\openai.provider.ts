import axios, { AxiosInstance } from 'axios';
import { BaseAIProvider, AIMessage, AIResponse, AIProviderConfig } from './base-ai.provider';

export class OpenAIProvider extends BaseAIProvider {
  private client: AxiosInstance;

  constructor(config: AIProviderConfig) {
    super(config);
    this.client = axios.create({
      baseURL: config.baseURL || 'https://api.openai.com/v1',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });
  }

  async generateText(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.model,
        messages: messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 1000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
      });

      const responseTime = Date.now() - startTime;
      const data = response.data;

      return {
        content: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        },
        model: data.model,
        finishReason: data.choices[0].finish_reason,
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }

  async *generateStream(
    messages: AIMessage[],
    options?: Partial<AIProviderConfig>
  ): AsyncGenerator<string, void, unknown> {
    const config = this.mergeConfig(options);

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.model,
        messages: messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 1000,
        stream: true,
      }, {
        responseType: 'stream',
      });

      for await (const chunk of response.data) {
        const lines = chunk.toString().split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt: string,
    options?: Partial<AIProviderConfig>
  ): Promise<AIResponse> {
    const startTime = Date.now();
    const config = this.mergeConfig(options);

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.model || 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: imageUrl } },
            ],
          },
        ],
        max_tokens: config.maxTokens || 1000,
      });

      const responseTime = Date.now() - startTime;
      const data = response.data;

      return {
        content: data.choices[0].message.content,
        usage: {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        },
        model: data.model,
        finishReason: data.choices[0].finish_reason,
        responseTime,
      };
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }

  async embedText(text: string): Promise<number[]> {
    try {
      const response = await this.client.post('/embeddings', {
        model: 'text-embedding-ada-002',
        input: text,
      });

      return response.data.data[0].embedding;
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      return false;
    }
  }

  // OpenAI specific methods
  async listModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data.map((model: any) => model.id);
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }

  async moderateContent(text: string): Promise<{
    flagged: boolean;
    categories: Record<string, boolean>;
    categoryScores: Record<string, number>;
  }> {
    try {
      const response = await this.client.post('/moderations', {
        input: text,
      });

      const result = response.data.results[0];
      return {
        flagged: result.flagged,
        categories: result.categories,
        categoryScores: result.category_scores,
      };
    } catch (error) {
      this.handleError(error, 'OpenAI');
    }
  }
}
