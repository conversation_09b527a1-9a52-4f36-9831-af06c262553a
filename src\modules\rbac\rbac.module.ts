import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { RolePermission } from './entities/role-permission.entity';
import { UserRole } from './entities/user-role.entity';
import { PermissionGroup } from './entities/permission-group.entity';
import { PermissionGroupItem } from './entities/permission-group-item.entity';
import { RoleHierarchy } from './entities/role-hierarchy.entity';

// Services
import { RBACService } from './services/rbac.service';
import { RoleService } from './services/role.service';
import { PermissionService } from './services/permission.service';

// Controllers
import { RoleController } from './controllers/role.controller';
import { PermissionController } from './controllers/permission.controller';

// Guards
import { RBACGuard, PermissionGuard, RoleGuard, RoleLevelGuard } from './guards/rbac.guard';

// Other modules
import { AuthModule } from '../auth/auth.module';
import { CacheModule } from '../cache/cache.module';

/**
 * RBAC Module - Module quản lý hệ thống phân quyền dựa trên vai trò
 * RBAC Module - Role-Based Access Control system module
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      Role,
      Permission,
      RolePermission,
      UserRole,
      PermissionGroup,
      PermissionGroupItem,
      RoleHierarchy,
    ]),
    
    // Forward reference to avoid circular dependency
    forwardRef(() => AuthModule),
    
    // Cache module for permission caching
    CacheModule,
  ],
  
  controllers: [
    RoleController,
    PermissionController,
  ],
  
  providers: [
    // Core services
    RBACService,
    RoleService,
    PermissionService,
    
    // Guards
    RBACGuard,
    PermissionGuard,
    RoleGuard,
    RoleLevelGuard,
  ],
  
  exports: [
    // Export services for use in other modules
    RBACService,
    RoleService,
    PermissionService,
    
    // Export guards for use in other modules
    RBACGuard,
    PermissionGuard,
    RoleGuard,
    RoleLevelGuard,
    
    // Export TypeORM repositories for advanced usage
    TypeOrmModule,
  ],
})
export class RBACModule {
  /**
   * Cấu hình module cho root application
   * Configure module for root application
   */
  static forRoot() {
    return {
      module: RBACModule,
      global: true, // Make RBAC available globally
    };
  }

  /**
   * Cấu hình module cho feature modules
   * Configure module for feature modules
   */
  static forFeature() {
    return {
      module: RBACModule,
    };
  }
}
